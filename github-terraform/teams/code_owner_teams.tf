locals {
  code_owner_teams = {
    "analytics-engineering-code-owners" : "Analytics Engineering Code Owners"
    "appraisals-engineering-code-owners" : "Appraisals Engineering Code Owners"
    "webshop-engineering-code-owners" : "Webshop Engineering Code Owners"
    "data-pipeline-engineering-code-owners" : "Data Pipeline Engineering Code Owners"
    "erp-onboarding-engineering-code-owners" : "ERP Onboarding Engineering Code Owners"
    "sql-dba-engineering-code-owners" : "SQL DBA Engineering Code Owners"
    "fleet-manager-backend-engineering-code-owners" : "FM backend Engineering Code Owners"
    "identity-engineering-code-owners" : "Identity Engineering Code Owners"
    "infrastructure-engineering-code-owners" : "Infrastructure Engineering Code Owners"
    "infrastructure-enterprise-engineering-code-owners" : "Infrastructure Enterprise Engineering Code Owners"
    "mobile-engineering-code-owners" : "Mobile Engineering Code Owners"
    "rfm-services-engineering-code-owners" : "RFM Services Engineering Code Owners"
    "rfm-services-test-engineering-code-owners" : "RFM Services Test Engineering Code Owners"
    "analytics-business-code-owners" : "Analytics Business Code Owners"
    "valuations-business-code-owners" : "Valuations Business Code Owners"
    "sales-qa-code-owners" : "Sales QA Code Owners"
    "rfm-engineering-code-owners" : "RFM Engineering Code Owners"
    "valuations-engineering-code-owners" : "Valuations Engineering Code Owners"
    "rfm-product-code-owners" : "RFM Product Code Owners"
    "data-science-engineering-code-owners" : "Data Science Engineering Code Owners"
    "data-engineering-code-owners" : "Data Engineering Code Owners"
    "ietl-code-owners" : "IETL Code Owners"
    "fleet-manager-services-engineering-code-owners" : "Fleet Manager Services Code Owners"
  }
}

resource "github_team" "code_owner_teams" {
  for_each    = local.code_owner_teams
  name        = each.key
  description = each.value
  privacy     = "closed"
}

resource "github_team_members" "analytics-engineering-code-owners" {
  team_id = github_team.code_owner_teams["analytics-engineering-code-owners"].id
  members {
    username = "JohnSparksRouse"
    role     = "member"
  }
  members {
    username = "c-dagostino"
    role     = "member"
  }
  members {
    username = "kbarton-rouse"
    role     = "member"
  }
  members {
    username = "logrouse" // Luke Grannis: ITDESK-8649
    role     = "member"
  }
  members {
    username = "enesrates"
    role     = "member"
  }
  members {
    username = "luigrs"
    role     = "member"
  }
}

resource "github_team_members" "appraisals-engineering-code-owners" {
  team_id = github_team.code_owner_teams["appraisals-engineering-code-owners"].id
  members {
    username = "JohnSparksRouse"
    role     = "member"
  }
  members {
    username = "c-dagostino"
    role     = "member"
  }
  members {
    username = "kbarton-rouse"
    role     = "member"
  }
}

resource "github_team_members" "webshop-engineering-code-owners" {
  team_id = github_team.code_owner_teams["webshop-engineering-code-owners"].id
  members {
    username = "lux-capacitor"
    role     = "maintainer"
  }
  members {
    username = "rouse-fernandoherrera"
    role     = "member"
  }
  members {
    username = "twesk"
    role     = "member"
  }
}

resource "github_team_members" "data-pipeline-engineering-code-owners" {
  team_id = github_team.code_owner_teams["data-pipeline-engineering-code-owners"].id
  members {
    username = "JohnSparksRouse"
    role     = "member"
  }
  members {
    username = "DaveEllendRouse"
    role     = "maintainer"
  }
  members {
    username = "zen4ever"
    role     = "maintainer"
  }
  members {
    username = "c-dagostino"
    role     = "member"
  }
  members {
    username = "kbarton-rouse"
    role     = "member"
  }
  members {
    username = "rouse-darren-fix"
    role     = "member"
  }
  members {
    username = "rouse-brett-malone"
    role     = "member"
  }
  members {
    role     = "maintainer"
    username = "lux-capacitor"
  }
}

resource "github_team_members" "data-engineering-code-owners" {
  team_id = github_team.code_owner_teams["data-engineering-code-owners"].id
  members {
    username = "JohnSparksRouse"
    role     = "member"
  }
  members {
    username = "rouse-anton-kotenko"
    role     = "member"
  }
}

resource "github_team_members" "ietl-code-owners" {
  team_id = github_team.code_owner_teams["ietl-code-owners"].id
  members {
    username = "DaveEllendRouse"
    role     = "maintainer"
  }
  members {
    username = "shawnfarleyrouse"
    role     = "member"
  }
}

resource "github_team_members" "sql-dba-engineering-code-owners" {
  team_id = github_team.code_owner_teams["sql-dba-engineering-code-owners"].id
  members {
    username = "JohnSparksRouse"
    role     = "member"
  }
  members {
    username = "DaveEllendRouse"
    role     = "maintainer"
  }
  members {
    username = "zen4ever"
    role     = "maintainer"
  }
}

resource "github_team_members" "erp-onboarding-engineering-code-owners" {
  team_id = github_team.code_owner_teams["erp-onboarding-engineering-code-owners"].id
  members {
    username = "DaveEllendRouse"
    role     = "maintainer"
  }
  members {
    username = "shawnfarleyrouse"
    role     = "member"
  }
  // Missing John, Andrew
}

resource "github_team_members" "fleet-manager-backend-engineering-code-owners" {
  team_id = github_team.code_owner_teams["fleet-manager-backend-engineering-code-owners"].id
  members {
    username = "DaveEllendRouse"
    role     = "maintainer"
  }
  members {
    username = "rouse-billy-herren"
    role     = "member"
  }
  members {
    username = "rouse-brett-malone"
    role     = "member"
  }
  members {
    role     = "member"
    username = "jhiga-rouse"
  }

  members {
    username = "rouse-anthonykleiser"
    role     = "member"
  }

  members {
    username = "rouseservices-jeremycummins"
    role     = "member"
  }

}

resource "github_team_members" "identity-engineering-code-owners" {
  team_id = github_team.code_owner_teams["identity-engineering-code-owners"].id
  members {
    username = "DaveEllendRouse"
    role     = "maintainer"
  }
  members {
    username = "zen4ever"
    role     = "maintainer"
  }
  members {
    username = "rouseservices-jeremycummins"
    role     = "member"
  }
  members {
    username = "rouse-jacobschaum"
    role     = "member"
  }
  members {
    username = "rouse-anthonykleiser"
    role     = "member"
  }
  members {
    username = "jhiga-rouse"
    role     = "member"
  }
}

resource "github_team_members" "infrastructure-engineering-code-owners" {
  team_id = github_team.code_owner_teams["infrastructure-engineering-code-owners"].id
  members {
    username = "dustincox-rouse"
    role     = "maintainer"
  }
  members {
    username = "zen4ever"
    role     = "maintainer"
  }
  members {
    username = "joelrouse"
    role     = "member"
  }
}

resource "github_team_members" "infrastructure-enterprise-engineering-code-owners" {
  team_id = github_team.code_owner_teams["infrastructure-enterprise-engineering-code-owners"].id
  members {
    username = "dustincox-rouse"
    role     = "maintainer"
  }
  members {
    username = "zen4ever"
    role     = "maintainer"
  }
  members {
    username = "DaveEllendRouse"
    role     = "maintainer"
  }
}

resource "github_team_members" "mobile-engineering-code-owners" {
  team_id = github_team.code_owner_teams["mobile-engineering-code-owners"].id
  members {
    username = "lux-capacitor"
    role     = "maintainer"
  }
  members {
    username = "mitchellthomas-rs"
    role     = "member"
  }
  members {
    role     = "member"
    username = "rouse-lawrence-leach"
  }
  members {
    role     = "member"
    username = "c-dagostino" // Chris D'Agostino
  }
}

resource "github_team_members" "rfm-services-engineering-code-owners" {
  team_id = github_team.code_owner_teams["rfm-services-engineering-code-owners"].id
  members {
    username = "DaveEllendRouse"
    role     = "maintainer"
  }
  members {
    username = "rouse-jacobschaum"
    role     = "member"
  }
  members {
    username = "jhiga-rouse"
    role     = "member"
  }

  members {
    role     = "maintainer"
    username = "lux-capacitor"
  }
}

resource "github_team_members" "analytics-business-code-owners" {
  team_id = github_team.code_owner_teams["analytics-business-code-owners"].id
  members {
    username = "JohnSparksRouse"
    role     = "member"
  }
  members {
    username = "c-dagostino"
    role     = "member"
  }
  members {
    username = "ehilderman"
    role     = "member"
  }
  members {
    username = "EvilTuring"
    role     = "member"
  }
  members {
    username = "DmitriGurkins"
    role     = "member"
  }
  members {
    username = "sqlagentgilmore" // Joel Gilmore
    role     = "member"
  }
}

resource "github_team_members" "valuations-business-code-owners" {
  team_id = github_team.code_owner_teams["valuations-business-code-owners"].id
  members {
    username = "DaveEllendRouse"
    role     = "maintainer"
  }
  members {
    username = "rouse-darren-fix"
    role     = "member"
  }
  members {
    username = "JoydeepBhadraRouse"
    role     = "member"
  }
  members {
    username = "dustincox-rouse" // ITDESK-7008 Remove with a new ITDESK access request
    role     = "maintainer"
  }
  members {
    username = "zen4ever" // ITDESK-7008 Remove with a new ITDESK access request
    role     = "maintainer"
  }
}

resource "github_team_members" "rfm-engineering-code-owners" {
  team_id = github_team.code_owner_teams["rfm-engineering-code-owners"].id

  members {
    username = "lux-capacitor" // Brad McGlasson
    role     = "maintainer"
  }
  members {
    username = "rouse-gregoryluong" // Greg Luong
    role     = "member"
  }
  members {
    username = "rouse-hardikpatel" // Hardik Patel
    role     = "member"
  }
  members {
    username = "jose-at-rouse" // Jose Quintanilla
    role     = "member"
  }
  members {
    username = "crash83k" // Matt Kernes
    role     = "member"
  }
}

resource "github_team_members" "valuations-engineering-code-owners" {
  team_id = github_team.code_owner_teams["valuations-engineering-code-owners"].id
  members {
    username = "DaveEllendRouse"
    role     = "maintainer"
  }
  members {
    username = "rouse-darren-fix"
    role     = "member"
  }
  members {
    username = "rouse-anton-kotenko"
    role     = "member"
  }
  members {
    username = "william-brehm-rouse"
    role     = "member"
  }

  members {
    username = "dustincox-rouse" // ITDESK-7008 Remove with a new ITDESK access request
    role     = "maintainer"
  }
  members {
    username = "zen4ever" // ITDESK-7008 Remove with a new ITDESK access request
    role     = "maintainer"
  }
}

resource "github_team_members" "rfm-product-code-owners" {
  team_id = github_team.code_owner_teams["rfm-product-code-owners"].id
  members {
    username = "shulmansj"
    role     = "member"
  }
}


resource "github_team_members" "sales-qa-code-owners" {
  team_id = github_team.code_owner_teams["sales-qa-code-owners"].id
  members {
    username = "lux-capacitor" // Brad McGlasson
    role     = "maintainer"
  }
  members {
    username = "rouse-gregoryluong" // Greg Luong
    role     = "member"
  }
  members {
    username = "rouse-hardikpatel" // Hardik Patel
    role     = "member"
  }
  members {
    username = "jose-at-rouse" // Jose Quintanilla
    role     = "member"
  }
  members {
    username = "c-dagostino" // Chris D'agostino
    role     = "member"
  }
  members {
    username = "JohnSparksRouse" // John Sparks
    role     = "maintainer"
  }
  members {
    username = "rouse-lawrence-leach" // Lawrence Leach
    role     = "member"
  }
}

resource "github_team_members" "data-science-engineering-code-owners" {
  team_id = github_team.code_owner_teams["data-science-engineering-code-owners"].id
  members {
    username = "rouse-darren-fix"
    role     = "member"
  }
  members {
    username = "william-brehm-rouse"
    role     = "member"
  }
  members {
    username = "leandroohf"
    role     = "member"
  }
  members {
    username = "JoydeepBhadraRouse"
    role     = "member"
  }
}

resource "github_team_members" "rfm-services-test-engineering-code-owners" {
  team_id = github_team.code_owner_teams["rfm-services-test-engineering-code-owners"].id
  members {
    username = "rouseservices-jeremycummins"
    role     = "member"
  }
  members {
    username = "DaveEllendRouse"
    role     = "maintainer"
  }
  members {
    username = "tfinleyre"
    role     = "member"
  }
  members {
    username = "jhiga-rouse"
    role     = "member"
  }
}

resource "github_team_members" "fleet-manager-services-engineering-code-owners" {
  team_id = github_team.code_owner_teams["fleet-manager-services-engineering-code-owners"].id
  members {
    username = "DaveEllendRouse"
    role     = "maintainer"
  }
  members {
    username = "rouse-anthonykleiser"
    role     = "member"
  }
  members {
    username = "rouseservices-jeremycummins"
    role     = "member"
  }
  members {
    role     = "maintainer"
    username = "lux-capacitor"
  }
}

output "code_owner_teams" {
  value = github_team.code_owner_teams
}
