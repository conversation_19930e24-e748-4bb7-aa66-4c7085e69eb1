import os
import pendulum
import importlib
import datetime as dt
import logging as log
from pathlib import Path
from google.cloud import storage


from airflow import DAG
from airflow.kubernetes import secret
from airflow.hooks.base_hook import BaseHook
try:
    from airflow.hooks.postgres_hook import PostgresHook
except ImportError:
    from airflow.providers.postgres.hooks.postgres import PostgresHook
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import PythonOperator
from airflow.operators.python_operator import BranchPythonOperator
from airflow.contrib.operators.slack_webhook_operator import SlackWebhookOperator
from airflow.providers.google.cloud.operators.kubernetes_engine import (
    GKEStartPodOperator,
)
from pager_duty_plugin import PagerDutyOperator
from shared_libs.default_args import get_default_args
from shared_libs.kubernetes import get_image_pull_policy
from shared_libs.slack_callback import task_fail_slack_alert
from shared_libs.image_versions import get_full_image_name, get_gcr_registry 
from kubernetes.client import models as k8s

# logging
log.basicConfig(format="%(asctime)s %(levelname)7s %(message)s")
log.getLogger().setLevel(log.INFO)

airflow_db_conn_id = "airflow_db"
environment = os.environ.get("COMPOSER_ENVIRONMENT", "dev")
gcr_registry = get_gcr_registry(environment)
image_pull_policy = get_image_pull_policy(environment)

PWD = Path(os.path.dirname(os.path.realpath(__file__)))
SQL_FOLDER = PWD / "dag_file_dependencies"

PIPELINE_PREFIX = "airflow-monitor-sys-valuations"
version_id = "{{ execution_date.strftime('%Y%m%d%H%M%S') }}"
local_tz = pendulum.timezone("America/Los_Angeles")
cluster_zone = "us-central1-b"
resources = k8s.V1ResourceRequirements(
    requests={"cpu": "300m", "memory": "0.5Gi"},
)

namespace = "airflow-monitoring-system-valuations"
service_account_name = namespace

table_config = importlib.import_module(
    "airflow-monitoring-system-valuations.dag_file_dependencies.table_config"
)

# get table configuration
bq_table_expiration_days = "30"
table_info = table_config.table_info

if environment != "prod":
    schedule_interval = None  # schedule_interval = "15 5-20 * * *"
    project_name = "management-dev-d6ba4d"
    cluster_name = "composer-jobs-v3-dev"
    slack_channel = "#data-alerts-dev"
    url_report = "https://datastudio.google.com/u/0/reporting/a00deaf5-4e0a-494f-8f21-4bb89e755398/page/lJu1C"
    bucket_name = "management-dev-d6ba4d-staging"
    bq_project = "management-dev-d6ba4d"
    db_secret_name = "de-mssql-airflow-monitor-system-valuations-dev"
    bq_project_id = "appraisals-data-dev-c55fa4"
    bq_dataset_version_id = "monitor_valuations_version_dev"
    bq_dataset_id = "monitor_valuations_dev"
    dataset_env_suffix = "_dev"
    mssql_server_ip = "{{ var.value.dev_mssql_gfdev03 }}"

else:
    schedule_interval = "15 7,9,10 * * *"
    project_name = "management-prod-837a97"
    cluster_name = "composer-jobs-v3-prod"
    slack_channel = "#data-alerts"
    url_report = "https://datastudio.google.com/u/0/reporting/9ed5029c-26c6-4609-8691-ab5b1b49a311/page/lJu1C"
    bucket_name = "management-prod-837a97-staging"
    bq_project = "management-prod-837a97"
    db_secret_name = "de-mssql-airflow-monitor-system-valuations-prod"
    pagerduty_key = (
        "{{ var.value.airflow_monitoring_system_valuations_pagerduty_api_key }}"
    )
    bq_project_id = "appraisals-data-prod-707493"
    bq_dataset_version_id = "monitor_valuations_version"
    bq_dataset_id = "monitor_valuations"
    dataset_env_suffix = ""
    mssql_server_ip = "***********"  # gdb02.rasgcp.net

custom_args = {
    "start_date": dt.datetime(2021, 6, 30, tzinfo=local_tz),
    "retries": 2,
    "retry_delay": dt.timedelta(minutes=2),
    "startup_timeout_seconds": 600,
    "image": get_full_image_name('airflow-monitoring-system-valuations', gcr_registry),
    "on_failure_callback": task_fail_slack_alert,
    "namespace": namespace,
    "service_account_name": service_account_name,
    "owner": "<EMAIL>",
    "depends_on_past": False,
}
default_args = get_default_args(custom_args)

# secrets/passwords
secret_db_username = secret.Secret(
    deploy_type="env",
    deploy_target="WIN_USER",
    secret=db_secret_name,
    key="username",
)

secret_db_password = secret.Secret(
    deploy_type="env",
    deploy_target="WIN_PASSWORD",
    secret=db_secret_name,
    key="password",
)

variable_dict = {
    "VERSION_ID": version_id,
    "PROJECT_ID": bq_project_id,
    "DATASET_ENV_SUFFIX": dataset_env_suffix,
    "GCS_BUCKET": f"{bq_project_id}-monitor",
}


def replace_vars_in_dict(source_dict, variable_dict):
    for key in source_dict.keys():
        if isinstance(source_dict[key], str):
            source_dict[key] = source_dict[key].format(**variable_dict)
    return source_dict


def get_airflow_db_hook():
    postgres_hook = PostgresHook(postgres_conn_id=airflow_db_conn_id)
    return postgres_hook


def save_run_dags_to_gc(**kwargs):
    sql_query = kwargs["ti"].xcom_pull(task_ids="render-sql-query")["query"]
    batch_id = kwargs["ti"].xcom_pull(task_ids="get-batch-id")

    print("updated")
    log.info(f"saving dags {sql_query}")
    log.info(f"saving dagbatch_id {batch_id}")

    hook = get_airflow_db_hook()
    df = hook.get_pandas_df(sql=sql_query)
    uri_template = (
        "pipeline/airflow-monitoring-system-valuations/"
        "{batch_id}/export.report.raw_{batch_id}.csv"
    )
    versioned_path = uri_template.format(batch_id=batch_id)
    current_path = uri_template.format(batch_id="current")
    uri_versioned = save_to_gcs(df, bq_project, bucket_name, versioned_path)
    uri_current = save_to_gcs(df, bq_project, bucket_name, current_path)
    return {"versioned": uri_versioned, "current": uri_current}


def save_to_gcs(df, project, bucket_name, name):
    storage_client = storage.Client(project=project)
    bucket = storage_client.bucket(bucket_name)
    blob_schema = storage.Blob(
        name=name,
        bucket=bucket,
    )
    blob_schema.upload_from_string(
        data=df.to_csv(index=False), content_type="text/csv", client=storage_client
    )
    return f"gs://{bucket_name}/{name}"


table_info = replace_vars_in_dict(table_info, variable_dict)

with DAG(
    dag_id=PIPELINE_PREFIX,
    description="Retrieve data to power Valuations monitoring dashboard",
    max_active_runs=1,
    default_args=default_args,
    schedule_interval=schedule_interval,
    catchup=False,
) as dag:
    end = DummyOperator(task_id="end")

    env_variables = {
        "DB_SERVER": mssql_server_ip,
        "DB_NAME": table_info["source_db_name"],
        "QUERY": table_info["query"],
        "GCP_PROJECT": bq_project_id,
        "GCS_PATH": table_info["avro_export_file_name"],
        "WRITE_LOGS": "False",
        "QA_COLUMNS": str(table_info["qa_columns"]),
    }

    mssql_to_gcs_id = "valuations-monitoring-mssql-gcs"
    mssql_to_gcs = GKEStartPodOperator(
        task_id=mssql_to_gcs_id,
        name=mssql_to_gcs_id,
        image=get_full_image_name("ss-to-gcs", gcr_registry),
        do_xcom_push=True,
        resources=resources,
        arguments=["python3", "main.py", "load-from-query"],
        secrets=[secret_db_username, secret_db_password],
        env_vars=env_variables,
        dag=dag,
    )

    gcs_to_bq_id = "valuations-monitoring-gcs-bq"
    gcs_to_bq = GKEStartPodOperator(
        task_id=gcs_to_bq_id,
        name=gcs_to_bq_id,
        dag=dag,
        image=get_full_image_name("avro-to-bq", gcr_registry),
        arguments=["python3", "main.py", "load-in-bq"],
        do_xcom_push=True,
        execution_timeout=dt.timedelta(minutes=30),
        env_vars={
            "SOURCE_URIS": env_variables["GCS_PATH"],
            "WRITE_DISPOSITION": "WRITE_TRUNCATE",
            "GCP_PROJECT_ID": bq_project_id,
            "DESTINATION": f"{bq_dataset_version_id}."
            f"{table_info['table_name']}_{version_id}",
            "XCOM_PUSH": "True",
            "CREATE_DATASETS": "True",
            "EXPIRATION_DAYS": bq_table_expiration_days,
            "VIEW_DESTINATION": f"{bq_project_id}"
            f".{bq_dataset_id}.{table_info['table_name']}",
        },
    )

    mssql_to_bq_qa_id = "valuations-monitoring-mssq-to-bq-qa"
    mssql_to_bq_qa = GKEStartPodOperator(
        task_id=mssql_to_bq_qa_id,
        name=mssql_to_bq_qa_id,
        dag=dag,
        image=get_full_image_name("ss-to-bq-qa", gcr_registry),
        resources=resources,
        execution_timeout=dt.timedelta(minutes=30),
        arguments=["python3", "-m", "src.cli", "qa"],
        secrets=[secret_db_username, secret_db_password],
        env_vars={
            "PROJECT_ID": bq_project_id,
            "DATASET_ID": bq_dataset_version_id,
            "VERSION": version_id,
            "TABLE_NAME": f"{table_info['table_name']}_{version_id}",
            "QA_COLUMNS": str(table_info["qa_columns"]),
            "METRICS": f"{{{{ task_instance.xcom_pull("
            f'task_ids="{mssql_to_gcs_id}"'
            f')["metrics"] }}}}',
        },
        get_logs=True,
    )

    render_sql_query_id = "render-sql-query"
    render_sql_query = GKEStartPodOperator(
        task_id=render_sql_query_id,
        name=render_sql_query_id,
        do_xcom_push=True,
        arguments=[
            "python",
            "-m",
            "airflow_monitoring_system_valuations.cli",
            "built-sql-from-settings",
        ],
    )

    dag_run_id = "save-run-dags-to-gc"
    run_dags = PythonOperator(
        task_id=dag_run_id, python_callable=save_run_dags_to_gc, provide_context=True
    )
    save_results_id = "save-results-to-bq"
    save_results_to_bq = GKEStartPodOperator(
        task_id=save_results_id,
        name=save_results_id,
        do_xcom_push=True,
        arguments=[
            "python",
            "-m",
            "airflow_monitoring_system_valuations.cli",
            "save-results-to-bq",
        ],
        env_vars={
            "ENV": environment,
            "INPUT": '{{ task_instance.xcom_pull(task_ids="save-run-dags-to-gc", key="return_value")["current"] }}',
            "VERSION": version_id,
        },
    )

    # alerts
    send_slack_alert_id = "send-slack-alert"
    send_slack_alert = SlackWebhookOperator(
        task_id=send_slack_alert_id,
        dag=dag,
        http_conn_id="slack",
        channel=slack_channel,
        webhook_token=BaseHook.get_connection("slack_client_projects").password,
        message=':warning: {{ task_instance.xcom_pull(task_ids="save-results-to-bq")["Slack"] }} \n'
        f"Report Url: {url_report}:",
        username="airflow",
        on_failure_callback=task_fail_slack_alert,
    )

    def is_list_empty(**kwargs):
        ti = kwargs["ti"]
        type_of_alert = kwargs["type"]
        next_task_id = kwargs["next_task_id"]
        other_task_id = kwargs["other_task_id"]
        upstream_task_id = kwargs["upstream_task_id"]
        result = ti.xcom_pull(task_ids=upstream_task_id)[type_of_alert]
        return next_task_id if result else other_task_id

    branch_slack_task = BranchPythonOperator(
        task_id="slack-list-is-not-empty",
        python_callable=is_list_empty,
        dag=dag,
        provide_context=True,
        op_kwargs={
            "type": "Slack",
            "upstream_task_id": "save-results-to-bq",
            "next_task_id": send_slack_alert_id,
            "other_task_id": "end",
        },
        on_failure_callback=task_fail_slack_alert,
    )
    notify_pager_duty_task_id = "notify-pager-duty"
    if environment != "prod":
        notify_pager_duty = DummyOperator(task_id=notify_pager_duty_task_id, dag=dag)
    else:
        notify_pager_duty = PagerDutyOperator(
            task_id=notify_pager_duty_task_id,
            dag=dag,
            retries=10,
            api_key=pagerduty_key,
            event_action="trigger",
            payload={
                "summary": '{{ task_instance.xcom_pull(task_ids="save-results-to-bq")["PagerDuty"] }} \n'
                f"Report Url: {url_report}",
                "source": "Airflow Monitoring System valuations",
                "severity": "critical",
            },
        )

    branch_pagerduty_task = BranchPythonOperator(
        task_id="pagerduty-list-is-not-empty",
        python_callable=is_list_empty,
        dag=dag,
        provide_context=True,
        op_kwargs={
            "type": "PagerDuty",
            "upstream_task_id": "save-results-to-bq",
            "next_task_id": "notify-pager-duty",
            "other_task_id": "end",
        },
        on_failure_callback=task_fail_slack_alert,
    )

    (
        render_sql_query
        >> run_dags
        >> save_results_to_bq
        >> [branch_slack_task, branch_pagerduty_task]
    )
    mssql_to_gcs >> gcs_to_bq >> mssql_to_bq_qa
    branch_slack_task >> [send_slack_alert, end]
    branch_pagerduty_task >> [notify_pager_duty, end]
    mssql_to_bq_qa >> notify_pager_duty
