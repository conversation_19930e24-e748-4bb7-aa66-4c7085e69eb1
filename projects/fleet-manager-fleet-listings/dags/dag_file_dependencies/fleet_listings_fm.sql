SET ROLE ROOT;

DROP VIEW IF EXISTS {PG_SCHEMA_VERSION}.fleet_listings_fm_{VERSION_ID};

CREATE VIEW {PG_SCHEMA_VERSION}.fleet_listings_fm_{VERSION_ID} AS
SELECT
  ((
    fa.is_listed 
    OR fa.channel_ritchie_list_is_listed
    OR fa.channel_mascus_is_listed
  )
  AND fa.selling_status = 'Listed For Sale') AS is_listed_for_sale,
  fa.channel_ritchie_list_is_featured,
  fa.channel_mascus_is_featured,
  fa.is_featured,
  CASE
    WHEN ((fa.is_listed OR fa.channel_ritchie_list_is_listed) AND fa.selling_status = 'Listed For Sale') THEN COALESCE(fa.approved_photo_count,0) =0
    ELSE
      NULL
  END AS no_photo,
  CASE
    WHEN CAST(1 + EXTRACT(DAY FROM CURRENT_DATE - DATE_TRUNC('day', LEAST( 
      CASE
          WHEN fa.is_listed
        AND fa.selling_status = 'Listed For Sale' THEN last_listed_date
        ELSE
        NULL
      END,
      CASE
        WHEN fa.channel_ritchie_list_is_listed AND fa.selling_status = 'Listed For Sale' THEN channel_ritchie_list_listing_date
        ELSE
          NULL
      END))) AS INT) BETWEEN 1 and 30 THEN '1-30'
    WHEN CAST(1 + EXTRACT(DAY FROM CURRENT_DATE - DATE_TRUNC('day', LEAST( 
      CASE
          WHEN fa.is_listed
        AND fa.selling_status = 'Listed For Sale' THEN last_listed_date
        ELSE
        NULL
      END,
      CASE
        WHEN fa.channel_ritchie_list_is_listed AND fa.selling_status = 'Listed For Sale' THEN channel_ritchie_list_listing_date
        ELSE
          NULL
      END))) AS INT) BETWEEN 31 and 60 THEN '31-60'
    WHEN CAST(1 + EXTRACT(DAY FROM CURRENT_DATE - DATE_TRUNC('day', LEAST( 
      CASE
          WHEN fa.is_listed
        AND fa.selling_status = 'Listed For Sale' THEN last_listed_date
        ELSE
        NULL
      END,
      CASE
        WHEN fa.channel_ritchie_list_is_listed AND fa.selling_status = 'Listed For Sale' THEN channel_ritchie_list_listing_date
        ELSE
          NULL
      END))) AS INT) BETWEEN 61 and 90 THEN '61-90'
    WHEN CAST(1 + EXTRACT(DAY FROM CURRENT_DATE - DATE_TRUNC('day', LEAST( 
      CASE
          WHEN fa.is_listed
        AND fa.selling_status = 'Listed For Sale' THEN last_listed_date
        ELSE
        NULL
      END,
      CASE
        WHEN fa.channel_ritchie_list_is_listed AND fa.selling_status = 'Listed For Sale' THEN channel_ritchie_list_listing_date
        ELSE
          NULL
      END))) AS INT) BETWEEN 91 and 120 THEN '91-120'
    ELSE
      '121+'
  END AS selling_days_listed,
  CASE
    WHEN CAST(1 + EXTRACT(DAY FROM CURRENT_DATE - DATE_TRUNC('day', LEAST( CASE
            WHEN fa.is_listed
          AND fa.selling_status = 'Listed For Sale' THEN last_listed_date
          ELSE
          NULL
        END
          ,
          CASE
            WHEN fa.channel_ritchie_list_is_listed AND fa.selling_status = 'Listed For Sale' THEN channel_ritchie_list_listing_date
          ELSE
          NULL
        END
          ))) AS INT) > 120 THEN TRUE
    ELSE
      FALSE
  END AS last_listed_date_greater_than_120,
  CASE
    WHEN (fa.is_listed OR fa.channel_ritchie_list_is_listed)
      AND fa.selling_status = 'Listed For Sale'
      AND CAST(1 + EXTRACT(DAY
      FROM
        CURRENT_DATE - DATE_TRUNC('day', CASE
          WHEN fa.list_price_source = 'manual'
            THEN list_price_manual_fx_date
          WHEN fa.list_price_source = 'system'
            THEN list_price_system_fx_date
          WHEN fa.list_price_source = 'custom'
            THEN list_price_customer_asking_fx_date
        END)) AS INT) BETWEEN 0 AND 30 THEN '0-30'
    WHEN (fa.is_listed OR fa.channel_ritchie_list_is_listed)
      AND fa.selling_status = 'Listed For Sale'
      AND CAST(1 + EXTRACT(DAY
      FROM
        CURRENT_DATE - DATE_TRUNC('day', CASE
          WHEN fa.list_price_source = 'manual'
            THEN list_price_manual_fx_date
          WHEN fa.list_price_source = 'system'
            THEN list_price_system_fx_date
          WHEN fa.list_price_source = 'custom'
            THEN list_price_customer_asking_fx_date
        END)) AS INT) BETWEEN 31 AND 60 THEN '31-60'
    WHEN (fa.is_listed OR fa.channel_ritchie_list_is_listed)
      AND fa.selling_status = 'Listed For Sale'
      AND CAST(1 + EXTRACT(DAY
      FROM
        CURRENT_DATE - DATE_TRUNC('day', CASE
          WHEN fa.list_price_source = 'manual'
            THEN list_price_manual_fx_date
          WHEN fa.list_price_source = 'system'
            THEN list_price_system_fx_date
          WHEN fa.list_price_source = 'custom'
            THEN list_price_customer_asking_fx_date
        END)) AS INT) BETWEEN 61 AND 90 THEN '61-90'
    ELSE
      	'91+'
  END AS days_since_price_updated,
  COALESCE(is_listed
    AND fa.selling_status = 'Listed For Sale', FALSE) AS channel_webshop_is_listed,
  CASE
    WHEN is_listed AND fa.selling_status = 'Listed For Sale' THEN CAST(1 + EXTRACT(DAY FROM CURRENT_DATE - DATE_TRUNC('day', last_listed_date)) AS INT)
  END AS channel_webshop_days_listed,
  COALESCE(channel_ritchie_list_is_listed
    AND fa.selling_status = 'Listed For Sale', FALSE) AS channel_ritchie_list_is_listed,
  COALESCE(channel_mascus_is_listed
    AND fa.selling_status = 'Listed For Sale', FALSE) AS channel_mascus_is_listed,
  CASE
    WHEN channel_ritchie_list_is_listed AND selling_status = 'Listed For Sale' THEN CAST(1 + EXTRACT(DAY FROM CURRENT_DATE - DATE_TRUNC('day', channel_ritchie_list_listing_date)) AS INT)
  END AS channel_ritchie_list_days_listed,
  CASE
    WHEN channel_mascus_is_listed AND selling_status = 'Listed For Sale' THEN CAST(1 + EXTRACT(DAY FROM CURRENT_DATE - DATE_TRUNC('day', channel_mascus_listing_date)) AS INT)
  END AS channel_mascus_days_listed,
  ((fa.is_listed
      AND webshop_priced_within_market = FALSE)
    OR (fa.channel_ritchie_list_is_listed
      AND ritchie_list_priced_within_market = FALSE)
    AND fa.selling_status = 'Listed For Sale') Priced_above_market,
  ((fa.is_listed
      AND webshop_outdated_pricing)
    OR (fa.channel_ritchie_list_is_listed
      AND ritchie_list_outdated_pricing)
    AND fa.selling_status = 'Listed For Sale') outdated_pricing,
  fa.selling_channels,
  fa.selling_is_featured,
  fa.display_meter_uom,
  fa.display_meter_value_precise,
  fa.equipment_status,
  fa.is_available_for_quoting,
  fa.option_01_bit,
  fa.option_02_bit,
  fa.pending_photo_count,
  fa.ratio_marketable_life_max,
  fa.selling_status,
  fa.serial_number,
  fa.web_description,
  fa.approved_photo_count > 0 or fa.pending_photo_count > 0 as has_photo,
  fa.approved_photo_count,
  fa.list_price,
  CASE
    WHEN fa.list_price_source = 'manual'
      THEN list_price_manual_fx_date
    WHEN fa.list_price_source = 'system'
      THEN list_price_system_fx_date
    WHEN fa.list_price_source = 'custom'
      THEN list_price_customer_asking_fx_date
  END AS list_price_fx_date,
  NULLIF(TRIM(fa.web_description),'') IS NOT NULL AS has_description,
  regexp_replace(fa.price_target_range_chart, ' - \d+$', '') AS price_target_range_chart_filter,
  fa.price_target_range_chart = 'priced low - 1' AS price_target_range_chart_low_1,
  fa.price_target_range_chart = 'priced low - 2' AS price_target_range_chart_low_2,
  fa.price_target_range_chart = 'within market range - 1' AS price_target_range_chart_mid_1,
  fa.price_target_range_chart = 'within market range - 2' AS price_target_range_chart_mid_2,
  fa.price_target_range_chart = 'within market range - 3' AS price_target_range_chart_mid_3,
  fa.price_target_range_chart = 'priced high - 1' AS price_target_range_chart_high_1,
  fa.price_target_range_chart = 'priced high - 2' AS price_target_range_chart_high_2,
  fa.consignment_date,
  fa.consignment_listing_number,
  fa.consignment_inspection_status,
  fa.consignment_sold_time,
  fa.consignment_sold_price,
  fa.consignment_minimum_price,
  fa.consignment_sale_event_site_name,
  fa.consignment_sale_event_name,
  fa.consignment_lot_number,
  CASE
    WHEN fa.channel_ritchie_list_is_featured IS TRUE THEN
      CAST(1 + EXTRACT(DAY FROM CURRENT_DATE - DATE_TRUNC('day', fa.channel_ritchie_list_last_featured_date)) AS INT)
    ELSE
      0
  END channel_ritchie_list_days_featured,
  CASE
    WHEN fa.channel_mascus_is_featured IS TRUE THEN
      CAST(1 + EXTRACT(DAY FROM CURRENT_DATE - DATE_TRUNC('day', fa.channel_mascus_last_featured_date)) AS INT)
    ELSE
      0
  END channel_mascus_days_featured,
  CASE
    WHEN CAST(1 + EXTRACT(DAY FROM CURRENT_DATE - DATE_TRUNC('day', LEAST( 
      CASE
          WHEN fa.channel_ritchie_list_is_featured THEN fa.channel_ritchie_list_last_featured_date
        ELSE
        NULL
      END,
      CASE
        WHEN fa.channel_mascus_is_featured THEN fa.channel_mascus_last_featured_date
        ELSE
          NULL
      END))) AS INT) BETWEEN 1 and 30 THEN '1-30'
    WHEN CAST(1 + EXTRACT(DAY FROM CURRENT_DATE - DATE_TRUNC('day', LEAST( 
      CASE
          WHEN fa.channel_ritchie_list_is_featured THEN fa.channel_ritchie_list_last_featured_date
        ELSE
        NULL
      END,
      CASE
        WHEN fa.channel_mascus_is_featured THEN fa.channel_mascus_last_featured_date
        ELSE
          NULL
      END))) AS INT) BETWEEN 31 and 60 THEN '31-60'
    WHEN CAST(1 + EXTRACT(DAY FROM CURRENT_DATE - DATE_TRUNC('day', LEAST( 
      CASE
          WHEN fa.channel_ritchie_list_is_featured THEN fa.channel_ritchie_list_last_featured_date
        ELSE
        NULL
      END,
      CASE
        WHEN fa.channel_mascus_is_featured THEN fa.channel_mascus_last_featured_date
        ELSE
          NULL
      END))) AS INT) BETWEEN 61 and 90 THEN '61-90'
    WHEN CAST(1 + EXTRACT(DAY FROM CURRENT_DATE - DATE_TRUNC('day', LEAST( 
      CASE
          WHEN fa.channel_ritchie_list_is_featured THEN fa.channel_ritchie_list_last_featured_date
        ELSE
        NULL
      END,
      CASE
        WHEN fa.channel_mascus_is_featured THEN fa.channel_mascus_last_featured_date
        ELSE
          NULL
      END))) AS INT) BETWEEN 91 and 120 THEN '91-120'
    ELSE
      '121+'
  END AS selling_days_featured,
  fa.channel_mpe_is_listed,
  m.*
FROM
  {PG_SCHEMA_VERSION}.fleet_listings_base_fm_{VERSION_ID} m
INNER JOIN
  fleet_manager.fleet_assets_p{PARTITION_ID} fa
ON
      m.fleet_asset_id = fa.fleet_asset_id
  AND m.fleet_customer_id = fa.fleet_customer_id
  AND fa.partition_id = {PARTITION_ID};
