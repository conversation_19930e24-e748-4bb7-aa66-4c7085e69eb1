from kubernetes.client import models as k8s

PG_FM_SCHEMA = "fleet_manager"
PG_MULTI_SCHEMA = "rfm_multi"

RESOURCES_BIG_TASK = k8s.V1ResourceRequirements(
    requests={"cpu": "500m", "memory": "1.8Gi"},
    limits={"cpu": "2000m", "memory": "16Gi"},
)
RESOURCES_SMALL_TASK = k8s.V1ResourceRequirements(
    requests={"cpu": "500m", "memory": "0.3Gi"},
)
RESOURCES_MEDIUM_TASK = k8s.V1ResourceRequirements(
    requests={"cpu": "500m", "memory": "1.8Gi"},
    limits={"cpu": "1000m", "memory": "6Gi"},
)
RESOURCES_DEFAULT = k8s.V1ResourceRequirements(
    requests={"cpu": "500m", "memory": "1.5Gi"},
    limits={"cpu": "800m", "memory": "2.5Gi"},
)

BQ_TABLE_EXPIRATION_DAYS = 4

QUERY_EXPORT_TABLE_CONFIG = {
    "proposal_history": {
        "query": """
            SELECT 
                p.*, fc.fleet_customer_id, fc.partition_id 
            FROM {RFM_SCHEMA}.proposal_history p
            JOIN fleet_manager.fleet_customers fc 
                ON fc.client_id_rouse_sales = p.client_id
            WHERE partition_id = {PARTITION_ID} 
              AND fleet_customer_id = {FLEET_CUSTOMER_ID} 
              AND p.id > {MAX_KEY_ID}
        """,
        "pk": "id",
    },
    "proposal_history_details": {
        "query": """
            SELECT 
                p.*, fc.fleet_customer_id, fc.partition_id 
            FROM {RFM_SCHEMA}.proposal_history_details p
            JOIN fleet_manager.fleet_customers fc 
                ON fc.client_id_rouse_sales = p.client_id
            WHERE partition_id = {PARTITION_ID} 
              AND fleet_customer_id = {FLEET_CUSTOMER_ID} 
        """,
        # Add back incremental export after backfilling work is done
        #   AND p.id > {MAX_KEY_ID}
        "pk": "id",
    },
}

pg_export_table_list_exclusions_default = [
    "auth_%",
    "django_%",
    # Evicting Backups
    "fleet_%_1",
    "fleet_%_2",
    "fleet_%_3",
    "fleet_%_4",
    "fleet_%_5",
    "%_mt",
    "%_old",
    "z_%",
    "%_copy%",
    "%_bk_%",
    "\\_%",
    "%delete%",
    "%_backup",
    "%_tmp_%",
    "%_temp_%",
    "temp_%",
    "tmp_%",
    "%_tmp",
    "%_temp",
    "pet_%",
    "pgbench_%",
    "spatial_ref_sys",
    # Tables without fleet_customer_id
    "csmm_relevancy",
    "fleet_manager_classification",
    "fleet_manager_cat_product_group",
    "fleet_manager_cat_product_group_tmp",
    "manualpriceimport",
    "attribute_generator_rules",
    "reversion_%",
    "fleet_assets_filters",  # temporary excluded, since this table can not be exported incrementally
    # excluded by https://rouseservices.atlassian.net/browse/SFMC-621
    "featured_history",
    "fleet_manager_update_controlhistory",
    "fleet_manager_update_reconditionedhistory",
    "fleet_manager_update_soldhistory",
    "legacy_rpo_history",
    "rental_insights_%",
    "equipment_values_history_fm",  
]

_current_valuations = {
    "type": "map",
    "values": ["null", "long"],
}

_adjusters_type = {
    "type": "map",
    "values": ["null", "float"],
}
_equipment_options_type = {
    "type": "map",
    "values": ["null", "int", "float", "string"],
}
_equipment_options_filter = {
    "type": "map",
    "values": ["null", "int", "boolean", "string"],
}
_internal_equipment_options_display_type = {
    "type": "map",
    "values": ["null", "int", "boolean", "string"],
}
_equipment_options_display_type = {
    "type": "array",
    "items": _internal_equipment_options_display_type,
}
_display_child_equipment_numbers_map_type = {
    "type": "array",
    "items": {
        "type": "map",
        "values": ["null", "boolean", "string"],
    },
}

_locality_comparable_adjustments = {
    "type": "array",
    "items": {
        "type": "map",
        "values": [
            "null",
            "int",
            "float",
            "string",
            {"type": "map", "values": ["null", "int", "float", "string"]},
            {"type": "array", "items": ["null", "int", "float", "string"]},
        ],
    },
}
_stack_error = {
    "type": "map",
    "values": [
        "null",
        "int",
        "string",
        {
            "type": "map",
            "values": ["null", "string", {"type": "map", "values": ["null", "string"]}],
        },
    ],
}

_value_trends_last_month_type = [
    "null",
    {
        "type": "bytes",
        "logicalType": "decimal",
        "precision": 18,
        "scale": 9,
    },
]

_fleet_assets_overrides = {
    "display_child_equipment_numbers_map": [
        "null",
        _display_child_equipment_numbers_map_type,
    ],
    "equipment_options": [
        "null",
        _equipment_options_type,
    ],
    "equipment_options_filter": [
        "null",
        _equipment_options_filter,
    ],
    "equipment_options_display": [
        "null",
        _equipment_options_display_type,
    ],
    "current_valuations": [
        "null",
        _current_valuations,
    ],
}

_fleet_classifications_overrides = {
    "cms_performance": [
        "null",
        {
            "type": "map",
            "values": ["null", "boolean", "float", "string"],
        },
    ]
}

_provisioning_history_overrides = {
    "provisioning_request": [
        "null",
        {"type": "map", "values": ["null", "int", "float", "string"]},
    ],
    "provisioning_response": [
        "null",
        {
            "type": "map",
            "values": ["null", "int", "float", "string", _stack_error],
        },
    ],
}

_fleet_valuations_overrides = {
    "adjusters": ["null", _adjusters_type],
    "equipment_options": [
        "null",
        _equipment_options_type,
    ],
    "equipment_options_display": [
        "null",
        _equipment_options_display_type,
    ],
    "locality_comparable_adjustments": [
        "null",
        _locality_comparable_adjustments,
    ],
}

_fleet_customers_overrides = {
    "channel_iron_planet_contract_ids": [
        "null",
        {
            "type": "map",
            "values": ["int", "float", "string"],
        },
    ],
    "channel_rb_live_auction_contract_ids": [
        "null",
        "string",
        {
            "type": "map",
            "values": ["int", "float", "string"],
        },
    ],
}

pg_tables_config = {
    f"{PG_FM_SCHEMA}.proposal_history_details": {
        "parse_json_fields_as_string": True,
    },
    f"{PG_FM_SCHEMA}.proposal_history": {
        "parse_json_fields_as_string": True,
    },
    f"{PG_FM_SCHEMA}.list_price_history": {
        "resources": RESOURCES_BIG_TASK,
    },
    f"{PG_FM_SCHEMA}.legacy_meter_history": {
        "resources": RESOURCES_BIG_TASK,
    },
    f"{PG_FM_SCHEMA}.remarketing_history": {
        "resources": RESOURCES_BIG_TASK,
    },
    f"{PG_FM_SCHEMA}.asset_change_history": {
        "resources": RESOURCES_BIG_TASK,
    },
    f"{PG_FM_SCHEMA}.fleet_classifications": {
        "resources": RESOURCES_BIG_TASK,
        "avro_schema_override_field_types": {
            "cms_performance": [
                "null",
                {
                    "type": "map",
                    "values": ["null", "boolean", "float", "string"],
                },
            ]
        },
    },
    f"{PG_FM_SCHEMA}.fleet_assets": {
        "resources": RESOURCES_BIG_TASK,
        "avro_schema_override_field_types": {
            "display_child_equipment_numbers_map": [
                "null",
                _display_child_equipment_numbers_map_type,
            ],
            "equipment_options": [
                "null",
                _equipment_options_type,
            ],
            "equipment_options_filter": [
                "null",
                _equipment_options_filter,
            ],
            "equipment_options_display": [
                "null",
                _equipment_options_display_type,
            ],
            "current_valuations": [
                "null",
                _current_valuations,
            ],
        },
    },
    f"{PG_FM_SCHEMA}.sold_assets": {
        "resources": RESOURCES_BIG_TASK,
        "avro_schema_override_field_types": _fleet_assets_overrides,
    },
    f"{PG_FM_SCHEMA}.fleet_valuations": {
        "resources": RESOURCES_BIG_TASK,
        "avro_schema_override_field_types": _fleet_valuations_overrides,
    },
    f"{PG_FM_SCHEMA}.sold_valuations": {
        "resources": RESOURCES_BIG_TASK,
        "avro_schema_override_field_types": _fleet_valuations_overrides,
    },
    f"{PG_FM_SCHEMA}.provisioning_history": {
        "avro_schema_override_field_types": {
            "provisioning_request": [
                "null",
                {"type": "map", "values": ["null", "int", "float", "string"]},
            ],
            "provisioning_response": [
                "null",
                {
                    "type": "map",
                    "values": ["null", "int", "float", "string", _stack_error],
                },
            ],
        },
    },
    f"{PG_FM_SCHEMA}.fleet_customers": {
        "avro_schema_override_field_types": {
            "channel_iron_planet_contract_ids": [
                "null",
                {
                    "type": "map",
                    "values": ["int", "float", "string"],
                },
            ],
            "channel_rb_live_auction_contract_ids": [
                "null",
                "string",
                {
                    "type": "map",
                    "values": ["int", "float", "string"],
                },
            ],
        },
    },
    f"{PG_FM_SCHEMA}.legacy_default_price_history": {
        "resources": RESOURCES_BIG_TASK,
    },
    f"{PG_FM_SCHEMA}.fleet_manager_update_meterhourshistory": {
        "resources": RESOURCES_BIG_TASK,
    },
    f"{PG_MULTI_SCHEMA}.equipment_base_fm": {
        "resources": RESOURCES_BIG_TASK,
        "avro_schema_override_field_types": {
            "display_child_equipment_numbers_map": [
                "null",
                _display_child_equipment_numbers_map_type,
            ],
            "current_valuations": [
                "null",
                _current_valuations,
            ],
        },
    },
    f"{PG_MULTI_SCHEMA}.equipment_catalog_fm": {
        "resources": RESOURCES_BIG_TASK,
        "avro_schema_override_field_types": {
            "display_child_equipment_numbers_map": [
                "null",
                _display_child_equipment_numbers_map_type,
            ],
            "current_valuations": [
                "null",
                _current_valuations,
            ],
        },
    },
    f"{PG_MULTI_SCHEMA}.equipment_fm": {
        "resources": RESOURCES_BIG_TASK,
        "avro_schema_override_field_types": {
            "display_child_equipment_numbers_map": [
                "null",
                _display_child_equipment_numbers_map_type,
            ],
            "current_valuations": [
                "null",
                _current_valuations,
            ],
        },
    },
    f"{PG_MULTI_SCHEMA}.fleet_listings_fm": {
        "resources": RESOURCES_BIG_TASK,
        "avro_schema_override_field_types": {
            "display_child_equipment_numbers_map": [
                "null",
                _display_child_equipment_numbers_map_type,
            ],
            "current_valuations": [
                "null",
                _current_valuations,
            ],
        },
    },
    f"{PG_MULTI_SCHEMA}.equipment_values_fm": {
        "resources": RESOURCES_BIG_TASK,
        "avro_schema_override_field_types": {
            "value_trends_flv_last_1_month": _value_trends_last_month_type,
            "value_trends_flv_last_12_month": _value_trends_last_month_type,
            "value_trends_flv_last_3_month": _value_trends_last_month_type,
            "value_trends_fmv_last_1_month": _value_trends_last_month_type,
            "value_trends_fmv_last_12_month": _value_trends_last_month_type,
            "value_trends_fmv_last_3_month": _value_trends_last_month_type,
            "value_trends_olv_last_1_month": _value_trends_last_month_type,
            "value_trends_olv_last_12_month": _value_trends_last_month_type,
            "value_trends_olv_last_3_month": _value_trends_last_month_type,
            "cumulative_percentile_low": [
                "null",
                {
                    "type": "bytes",
                    "logicalType": "decimal",
                    "precision": 38,
                    "scale": 10,
                },
            ],
            "cumulative_percentile_high": [
                "null",
                {
                    "type": "bytes",
                    "logicalType": "decimal",
                    "precision": 38,
                    "scale": 10,
                },
            ],
            "actual_meter_precise": [
                "null",
                {
                    "type": "bytes",
                    "logicalType": "decimal",
                    "precision": 24,
                    "scale": 7,
                },
            ],
            "actual_meter_miles_precise": [
                "null",
                {
                    "type": "bytes",
                    "logicalType": "decimal",
                    "precision": 24,
                    "scale": 7,
                },
            ],
        },
    },
    f"{PG_MULTI_SCHEMA}.sales_txn_fm": {
        "avro_schema_override_field_types": {
            "meter": [
                "null",
                {
                    "type": "bytes",
                    "logicalType": "decimal",
                    "precision": 38,
                    "scale": 10,
                },
            ],
            "meter_precise": [
                "null",
                {
                    "type": "bytes",
                    "logicalType": "decimal",
                    "precision": 38,
                    "scale": 10,
                },
            ],
        }
    },
    f"{PG_MULTI_SCHEMA}.sales_txn_historical_fm": {
        "avro_schema_override_field_types": {
            "meter": [
                "null",
                {
                    "type": "bytes",
                    "logicalType": "decimal",
                    "precision": 38,
                    "scale": 10,
                },
            ],
            "meter_precise": [
                "null",
                {
                    "type": "bytes",
                    "logicalType": "decimal",
                    "precision": 38,
                    "scale": 10,
                },
            ],
        }
    },
    f"{PG_MULTI_SCHEMA}.web_leads_aggregate": {
        "avro_schema_override_field_types": {
            "list_price": [
                "null",
                {
                    "type": "bytes",
                    "logicalType": "decimal",
                    "precision": 38,
                    "scale": 10,
                },
            ],
            "display_meter_value_precise": [
                "null",
                {
                    "type": "bytes",
                    "logicalType": "decimal",
                    "precision": 24,
                    "scale": 7,
                },
            ],
        }
    },
}

PG_EXPORT_TABLE_LIST_CONFIG = {
    "fleet_valuations": {
        "ignored_columns": ["entitlement_status"],
        "key": "fleet_asset_valuation_id",
        "incremental": {"key": "fleet_asset_valuation_id"},
        "avro_schema_override_field_types": _fleet_valuations_overrides,
    },
    "sold_valuations": {
        "key": "sold_valuation_id",
        "incremental": {"key": "sold_valuation_id"},
        "avro_schema_override_field_types": _fleet_valuations_overrides,
    },
    "fleet_asset_files": {
        "key": "file_id",
        "incremental": {"datetime_range": ["date_modified", "date_created"]},
    },
    "fleet_asset_photos": {
        "key": "photo_id",
        "incremental": {"datetime_range": ["date_modified", "date_created"]},
    },
    "fleet_assets": {
        "key": "fleet_asset_id",
        "incremental": {
            "datetime_range": [
                "asset_synced_date",
                "asset_refreshed_date",
                "asset_updated_date",
                "asset_dropped_date",
                "asset_created_date",
            ]
        },
        "avro_schema_override_field_types": _fleet_assets_overrides,
    },
    "fleet_customers": {"avro_schema_override_field_types": _fleet_customers_overrides},
    "fleet_customer_financing_programs": {
        "key": "fleet_customer_financing_program_term_id",
        "incremental": {"datetime_range": ["date_created", "date_updated"]},
    },
    "sold_assets": {
        "key": "sold_asset_id",
        "incremental": {"key": "sold_asset_id"},
        "avro_schema_override_field_types": _fleet_assets_overrides,
    },
    "fleet_entitlements": {
        "key": "fleet_entitlement_id",
        "incremental": {"datetime_range": ["date_modified", "date_created"]},
    },
    "fleet_classifications": {
        "key": "fleet_asset_classification_id",
        "incremental": {"key": "fleet_asset_classification_id"},
        "avro_schema_override_field_types": _fleet_classifications_overrides,
    },
    "asset_change_history": {
        "key": "id",
        "incremental": {"key": "id"},
    },
    "asset_notes_history": {"key": "id", "incremental": {"key": "id"}},
    "fleet_asset_files_assetfilehistory": {"key": "id", "incremental": {"key": "id"}},
    "list_price_history": {"key": "id", "incremental": {"key": "id"}},
    "remarketing_history": {"key": "id", "incremental": {"key": "id"}},
    "fleet_asset_photos_history": {"key": "id", "incremental": {"key": "id"}},
    "fleet_manager_update_branchhistory": {"key": "id", "incremental": {"key": "id"}},
    "fleet_manager_update_cmmdahistory": {"key": "id", "incremental": {"key": "id"}},
    "fleet_manager_update_controlhistory": {"key": "id", "incremental": {"key": "id"}},
    "fleet_manager_update_meterhourshistory": {
        "key": "id",
        "incremental": {"key": "id"},
    },
    "fleet_manager_update_metermileshistory": {
        "key": "id",
        "incremental": {"key": "id"},
    },
    "fleet_manager_update_reconditionedhistory": {
        "key": "id",
        "incremental": {"key": "id"},
    },
    "fleet_manager_update_soldhistory": {"key": "id", "incremental": {"key": "id"}},
    "option_bit_history": {"key": "id", "incremental": {"key": "id"}},
    "list_price_customer_asking_history": {"key": "id", "incremental": {"key": "id"}},
    "legacy_default_price_history": {
        "key": "legacy_default_price_id",
        "incremental": {"key": "legacy_default_price_id"},
    },
    "legacy_in_portal_history": {
        "key": "legacy_in_portal_id",
        "incremental": {"key": "legacy_in_portal_id"},
    },
    "legacy_valuation_eligibility_history": {
        "key": "legacy_valuation_eligibility_id",
        "incremental": {"key": "legacy_valuation_eligibility_id"},
    },
    "featured_history": {"key": "id", "incremental": {"key": "id"}},
    "legacy_meter_history": {
        "key": "legacy_meter_history_id",
        "incremental": {"key": "legacy_meter_history_id"},
    },
    "legacy_rpo_history": {
        "key": "legacy_rpo_history_id",
        "incremental": {"key": "legacy_rpo_history_id"},
    },
    "listed_history": {"key": "id", "incremental": {"key": "id"}},
    "provisioning_history": {
        "key": "id",
        "incremental": {"key": "id"},
        "avro_schema_override_field_types": _provisioning_history_overrides,
    },
    "sales_classifications": {
        "key": "sales_classification_id",
        "incremental": {"key": "sales_classification_id"},
    },
    "ras_sas_customer_asset": {"key": "CustomerAssetId"},
    "ras_sas_customer_asset_dimension_value": {"key": "CustomerAssetId, DimensionID"},
    "sfdc_account": {"key": "Sales_Client_ID__c"},
    "proposal_history_details": {
        "key": "id",
        # Add back incremental export after backfilling work is done
        # "incremental": {"key": "id"},
    },
    "proposal_history": {
        "key": "id",
        "incremental": {"key": "id"},
    },
    "fleet_asset_photos_logs": {
        "key": "photo_id",
        "incremental": {"key": "photo_id"},
    },
    "external_event_history": {
        "key": "external_event_history_id",
        "incremental": {"key": "external_event_history_id"},
    },
}
