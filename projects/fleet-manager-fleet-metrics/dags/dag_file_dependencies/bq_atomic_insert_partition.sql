BEGIN

  BEGIN TRANSACTION;

  INSERT INTO
    `{SALES_DATA_PROJECT_ID}.fleet_manager_fleet_metrics{BQ_DATASET_ENV_SUFFIX}.fleet_metrics_fm_historic`  
  (
    approved_photo_count,
    pending_photo_count,
    has_photo,
    selling_status,
    equipment_status,
    selling_channels,
    selling_is_featured,
    is_featured,
    is_available_for_quoting,
    option_01_bit,
    option_02_bit,
    channel_ritchie_list_is_featured,
    id,
    fleet_asset_id,
    fleet_customer_id,
    client_id,
    client_code,
    super_category,
    category,
    is_classified,
    classification_type,
    classification_status,
    make,
    model_year,
    is_within_marketable_life,
    cost,
    customer_cost_fx_date,
    nbv,
    nbv_fx_date,
    valuation_eligible,
    mpe_price,
    free_flv,
    rfm_fmv,
    rfm_wlv,
    rfm_flv,
    valuation_fx_date,
    subcategory,
    insights_type,
    insights_type_id,
    insights_category,
    insights_category_id,
    insights_subcategory,
    insights_subcategory_id,
    insights_localization,
    display_customer_level1_name,
    display_customer_level2_name,
    display_make,
    display_make_dashboard,
    customer_make_name,
    customer_model_name,
    display_model,
    machine_type,
    branch_country,
    branch_state,
    branch_region,
    branch_district,
    display_branch_name,
    branch_code,
    division_code,
    company_code,
    equipment_type,
    option_01_string,
    option_02_string,
    equipment_number,
    rdo_equipment_id,
    display_equipment_number,
    equipment_options_filter,
    display_meter_value_precise,
    serial_number,
    exchange_rates,
    updated_date,
    ab_cost,
    web_description,
    _version,
    ratio_marketable_life_max,
    display_meter_uom,
    price_target_range_chart_filter,
    price_target_range_chart_low_1,
    price_target_range_chart_low_2,
    price_target_range_chart_mid_1,
    price_target_range_chart_mid_2,
    price_target_range_chart_mid_3,
    price_target_range_chart_high_1,
    price_target_range_chart_high_2,
    days_since_price_updated,
    selling_days_listed,
    channel_ritchie_list_days_listed,
    channel_webshop_days_listed,
    has_description,
    cost_with_attachments,
    nbv_with_attachments,
    auction_ratio_marketable_life_max,
    retail_ratio_marketable_life_max,
    midpoint_ratio_marketable_life_max,
    auction_ratio_marketable_life_meter,
    retail_ratio_marketable_life_meter,
    midpoint_ratio_marketable_life_meter,
    auction_ratio_marketable_life_age,
    retail_ratio_marketable_life_age,
    midpoint_ratio_marketable_life_age,
    retail_bottom_quartile_3_month,
    retail_top_quartile_3_month,
    channel_ritchie_list_is_list_price_hidden,
    is_list_price_hidden,
    list_price,
    retail_3_month_expected_value_loss,
    retail_3_month_expected_value_loss_percentage,
    auction_3_month_expected_value_loss,
    auction_3_month_expected_value_loss_percentage,
    is_below_physical_ut_branch_benchmark,
    is_below_financial_ut_branch_benchmark,
    is_below_physical_ut_asset_benchmark,
    is_below_financial_ut_asset_benchmark,
    rdo_rental_status,
    rdo_last_rental_date,
    rdo_physical_ut_asset,
    rdo_physical_ut_bench,
    rdo_physical_ut_branch,
    rdo_physical_ut_company,
    rdo_financial_ut_asset,
    rdo_financial_ut_bench,
    rdo_financial_ut_branch,
    rdo_financial_ut_company,
    rdo_cat_class,
    rdo_branch_location,
    rdo_district,
    rdo_region,
    rdo_location_code,
    is_high_value_loss_auction,
    is_high_value_loss_retail,
    client_id_rouse_analytics,
    is_high_value_loss,
    rdo_month_id,
    rdo_rental_month,
    consignment_date,
    consignment_listing_number,
    consignment_inspection_status,
    consignment_sold_time,
    consignment_sold_price,
    consignment_minimum_price,
    consignment_sale_event_site_name,
    consignment_sale_event_name,
    consignment_lot_number,
    channel_ritchie_list_days_featured,
    channel_mascus_days_featured,
    rb_expected_price,
    selling_days_featured,
    option_01_date,
    option_02_date,
    option_01_financial,
    option_02_financial,
    option_03_financial,
    option_04_financial,
    option_01_numeric,
    option_02_numeric,
    option_03_string,
    channel_mpe_is_listed
  )
  SELECT
    approved_photo_count,
    pending_photo_count,
    has_photo,
    selling_status,
    equipment_status,
    selling_channels,
    selling_is_featured,
    is_featured,
    is_available_for_quoting,
    option_01_bit,
    option_02_bit,
    channel_ritchie_list_is_featured,
    id,
    fleet_asset_id,
    fleet_customer_id,
    client_id,
    client_code,
    super_category,
    category,
    is_classified,
    classification_type,
    classification_status,
    make,
    model_year,
    is_within_marketable_life,
    cost,
    customer_cost_fx_date,
    nbv,
    nbv_fx_date,
    valuation_eligible,
    mpe_price,
    free_flv,
    rfm_fmv,
    rfm_wlv,
    rfm_flv,
    valuation_fx_date,
    subcategory,
    insights_type,
    insights_type_id,
    insights_category,
    insights_category_id,
    insights_subcategory,
    insights_subcategory_id,
    insights_localization,
    display_customer_level1_name,
    display_customer_level2_name,
    display_make,
    display_make_dashboard,
    customer_make_name,
    customer_model_name,
    display_model,
    machine_type,
    branch_country,
    branch_state,
    branch_region,
    branch_district,
    display_branch_name,
    branch_code,
    division_code,
    company_code,
    equipment_type,
    option_01_string,
    option_02_string,
    equipment_number,
    rdo_equipment_id,
    display_equipment_number,
    equipment_options_filter,
    display_meter_value_precise,
    serial_number,
    exchange_rates,
    updated_date,
    ab_cost,
    web_description,
    {VERSION_ID} _version,
    ratio_marketable_life_max,
    display_meter_uom,
    price_target_range_chart_filter,
    price_target_range_chart_low_1,
    price_target_range_chart_low_2,
    price_target_range_chart_mid_1,
    price_target_range_chart_mid_2,
    price_target_range_chart_mid_3,
    price_target_range_chart_high_1,
    price_target_range_chart_high_2,
    days_since_price_updated,
    selling_days_listed,
    channel_ritchie_list_days_listed,
    channel_webshop_days_listed,
    has_description,
    cost_with_attachments,
    nbv_with_attachments,
    auction_ratio_marketable_life_max,
    retail_ratio_marketable_life_max,
    midpoint_ratio_marketable_life_max,
    auction_ratio_marketable_life_meter,
    retail_ratio_marketable_life_meter,
    midpoint_ratio_marketable_life_meter,
    auction_ratio_marketable_life_age,
    retail_ratio_marketable_life_age,
    midpoint_ratio_marketable_life_age,
    retail_bottom_quartile_3_month,
    retail_top_quartile_3_month,
    channel_ritchie_list_is_list_price_hidden,
    is_list_price_hidden,
    list_price,
    retail_3_month_expected_value_loss,
    retail_3_month_expected_value_loss_percentage,
    auction_3_month_expected_value_loss,
    auction_3_month_expected_value_loss_percentage,
    is_below_physical_ut_branch_benchmark,
    is_below_financial_ut_branch_benchmark,
    is_below_physical_ut_asset_benchmark,
    is_below_financial_ut_asset_benchmark,
    rdo_rental_status,
    rdo_last_rental_date,
    rdo_physical_ut_asset,
    rdo_physical_ut_bench,
    rdo_physical_ut_branch,
    rdo_physical_ut_company,
    rdo_financial_ut_asset,
    rdo_financial_ut_bench,
    rdo_financial_ut_branch,
    rdo_financial_ut_company,
    rdo_cat_class,
    rdo_branch_location,
    rdo_district,
    rdo_region,
    rdo_location_code,
    is_high_value_loss_auction,
    is_high_value_loss_retail,
    client_id_rouse_analytics,
    is_high_value_loss,
    rdo_month_id,
    rdo_rental_month,
    consignment_date,
    consignment_listing_number,
    consignment_inspection_status,
    consignment_sold_time,
    consignment_sold_price,
    consignment_minimum_price,
    consignment_sale_event_site_name,
    consignment_sale_event_name,
    consignment_lot_number,
    channel_ritchie_list_days_featured,
    channel_mascus_days_featured,
    rb_expected_price,
    selling_days_featured,
    option_01_date,
    option_02_date,
    option_01_financial,
    option_02_financial,
    option_03_financial,
    option_04_financial,
    option_01_numeric,
    option_02_numeric,
    option_03_string,
    channel_mpe_is_listed,
  FROM
    `{PROJECT_ID}.fleet_manager_fleet_metrics{BQ_DATASET_ENV_SUFFIX}.fleet_metrics_fm`
  ;

  COMMIT TRANSACTION;

EXCEPTION WHEN ERROR THEN
  ROLLBACK TRANSACTION;
  RAISE USING MESSAGE = @@error.message;
END;