CREATE SCHEMA IF NOT EXISTS
  `{SALES_DATA_PROJECT_ID}.fleet_manager_fleet_metrics{BQ_DATASET_ENV_SUFFIX}`
OPTIONS(
  location="us"
);
CREATE TABLE IF NOT EXISTS
  `{SALES_DATA_PROJECT_ID}.fleet_manager_fleet_metrics{BQ_DATASET_ENV_SUFFIX}.fleet_metrics_fm_historic`
(
  approved_photo_count INT64,
  pending_photo_count INT64,
  has_photo BOOLEAN,
  selling_status STRING,
  equipment_status STRING,
  selling_channels ARRAY<STRING>,
  selling_is_featured BOOLEAN,
  is_featured BOOLEAN,
  is_available_for_quoting <PERSON><PERSON><PERSON><PERSON><PERSON>,
  option_01_bit BOOLEAN,
  option_02_bit BOOLEAN,
  channel_ritchie_list_is_featured BOOLEAN,
  id INT64,
  fleet_asset_id INT64,
  fleet_customer_id INT64,
  client_id INT64,
  client_code STRING,
  super_category STRING,
  category STRING,
  is_classified BOOLEAN,
  classification_type STRING,
  classification_status STRING,
  make STRING,
  model_year INT64,
  is_within_marketable_life BOOLEAN,
  cost NUMERIC,
  customer_cost_fx_date DATE,
  nbv NUMERIC,
  nbv_fx_date DATE,
  valuation_eligible BOOLEAN,
  mpe_price INT64,
  free_flv INT64,
  rfm_fmv INT64,
  rfm_wlv INT64,
  rfm_flv INT64,
  valuation_fx_date DATE,
  subcategory STRING,
  display_customer_level1_name STRING,
  display_customer_level2_name STRING,
  display_make STRING,
  display_make_dashboard STRING,
  display_model STRING,
  machine_type STRING,
  branch_country STRING,
  branch_state STRING,
  branch_region STRING,
  branch_district STRING,
  display_branch_name STRING,
  branch_code STRING,
  division_code STRING,
  company_code STRING,
  equipment_type STRING,
  option_01_string STRING,
  option_02_string STRING,
  equipment_number STRING,
  display_equipment_number STRING,
  equipment_options_filter STRING,
  display_meter_value_precise NUMERIC,
  serial_number STRING,
  exchange_rates STRING,
  updated_date TIMESTAMP,
  ab_cost INT64,
  web_description STRING,
  _version INT64,
  ratio_marketable_life_max NUMERIC,
  display_meter_uom STRING
)
PARTITION BY
  DATE(updated_date)
CLUSTER BY
  fleet_customer_id,
  fleet_asset_id
OPTIONS (
  partition_expiration_days = 90
  )
;
ALTER TABLE
  `{SALES_DATA_PROJECT_ID}.fleet_manager_fleet_metrics{BQ_DATASET_ENV_SUFFIX}.fleet_metrics_fm_historic`
ADD COLUMN IF NOT EXISTS ratio_marketable_life_max NUMERIC,
ADD COLUMN IF NOT EXISTS exchange_rates STRING,
ADD COLUMN IF NOT EXISTS display_meter_uom STRING,
ADD COLUMN IF NOT EXISTS price_target_range_chart_filter STRING,
ADD COLUMN IF NOT EXISTS price_target_range_chart_low_1 BOOLEAN,
ADD COLUMN IF NOT EXISTS price_target_range_chart_low_2 BOOLEAN,
ADD COLUMN IF NOT EXISTS price_target_range_chart_mid_1 BOOLEAN,
ADD COLUMN IF NOT EXISTS price_target_range_chart_mid_2 BOOLEAN,
ADD COLUMN IF NOT EXISTS price_target_range_chart_mid_3 BOOLEAN,
ADD COLUMN IF NOT EXISTS price_target_range_chart_high_1 BOOLEAN,
ADD COLUMN IF NOT EXISTS price_target_range_chart_high_2 BOOLEAN,
ADD COLUMN IF NOT EXISTS days_since_price_updated STRING,
ADD COLUMN IF NOT EXISTS selling_days_listed STRING,
ADD COLUMN IF NOT EXISTS channel_ritchie_list_days_listed INT64,
ADD COLUMN IF NOT EXISTS channel_webshop_days_listed INT64,
ADD COLUMN IF NOT EXISTS has_description BOOLEAN,
ADD COLUMN IF NOT EXISTS has_photo BOOLEAN,
ADD COLUMN IF NOT EXISTS cost_with_attachments NUMERIC,
ADD COLUMN IF NOT EXISTS nbv_with_attachments NUMERIC,
ADD COLUMN IF NOT EXISTS auction_ratio_marketable_life_max NUMERIC,
ADD COLUMN IF NOT EXISTS retail_ratio_marketable_life_max NUMERIC,
ADD COLUMN IF NOT EXISTS midpoint_ratio_marketable_life_max NUMERIC,
ADD COLUMN IF NOT EXISTS auction_ratio_marketable_life_meter NUMERIC,
ADD COLUMN IF NOT EXISTS retail_ratio_marketable_life_meter NUMERIC,
ADD COLUMN IF NOT EXISTS midpoint_ratio_marketable_life_meter NUMERIC,
ADD COLUMN IF NOT EXISTS auction_ratio_marketable_life_age NUMERIC,
ADD COLUMN IF NOT EXISTS retail_ratio_marketable_life_age NUMERIC,
ADD COLUMN IF NOT EXISTS midpoint_ratio_marketable_life_age NUMERIC,
ADD COLUMN IF NOT EXISTS retail_bottom_quartile_3_month NUMERIC,
ADD COLUMN IF NOT EXISTS retail_top_quartile_3_month NUMERIC,
ADD COLUMN IF NOT EXISTS channel_ritchie_list_is_list_price_hidden BOOLEAN,
ADD COLUMN IF NOT EXISTS is_list_price_hidden BOOLEAN,
ADD COLUMN IF NOT EXISTS list_price NUMERIC,
ADD COLUMN IF NOT EXISTS insights_type STRING,
ADD COLUMN IF NOT EXISTS insights_type_id INT64,
ADD COLUMN IF NOT EXISTS insights_category STRING,
ADD COLUMN IF NOT EXISTS insights_category_id INT64,
ADD COLUMN IF NOT EXISTS insights_subcategory STRING,
ADD COLUMN IF NOT EXISTS insights_subcategory_id INT64,
ADD COLUMN IF NOT EXISTS insights_localization STRING,
ADD COLUMN IF NOT EXISTS retail_3_month_expected_value_loss NUMERIC,
ADD COLUMN IF NOT EXISTS retail_3_month_expected_value_loss_percentage NUMERIC,
ADD COLUMN IF NOT EXISTS auction_3_month_expected_value_loss NUMERIC,
ADD COLUMN IF NOT EXISTS auction_3_month_expected_value_loss_percentage NUMERIC,
ADD COLUMN IF NOT EXISTS rdo_location_code STRING,
ADD COLUMN IF NOT EXISTS customer_make_name STRING,
ADD COLUMN IF NOT EXISTS customer_model_name STRING,
ADD COLUMN IF NOT EXISTS channel_mascus_is_featured BOOLEAN,
ADD COLUMN IF NOT EXISTS channel_mascus_days_listed INT64,
ADD COLUMN IF NOT EXISTS channel_mascus_is_list_price_hidden BOOLEAN,
DROP COLUMN IF EXISTS retail_3_month_holding_cost,
DROP COLUMN IF EXISTS retail_3_month_holding_cost_percentage,
DROP COLUMN IF EXISTS auction_3_month_holding_cost,
DROP COLUMN IF EXISTS auction_3_month_holding_cost_percentage,
ADD COLUMN IF NOT EXISTS rdo_equipment_id STRING,
DROP COLUMN IF EXISTS is_within_physical_ut_branch,
DROP COLUMN IF EXISTS is_within_financial_ut_branch,
DROP COLUMN IF EXISTS is_within_physical_ut_benchmark,
DROP COLUMN IF EXISTS is_within_financial_ut_benchmark,
DROP COLUMN IF EXISTS is_within_physical_ut_branch_benchmark,
DROP COLUMN IF EXISTS is_within_financial_ut_branch_benchmark,
DROP COLUMN IF EXISTS is_within_physical_ut_asset_benchmark,
DROP COLUMN IF EXISTS is_within_financial_ut_asset_benchmark,
ADD COLUMN IF NOT EXISTS is_below_physical_ut_branch_benchmark BOOL,
ADD COLUMN IF NOT EXISTS is_below_financial_ut_branch_benchmark BOOL,
ADD COLUMN IF NOT EXISTS is_below_physical_ut_asset_benchmark BOOL,
ADD COLUMN IF NOT EXISTS is_below_financial_ut_asset_benchmark BOOL,
ADD COLUMN IF NOT EXISTS rdo_rental_status STRING,
ADD COLUMN IF NOT EXISTS rdo_last_rental_date DATE,
ADD COLUMN IF NOT EXISTS rdo_physical_ut_asset NUMERIC,
ADD COLUMN IF NOT EXISTS rdo_physical_ut_bench NUMERIC,
ADD COLUMN IF NOT EXISTS rdo_physical_ut_branch NUMERIC,
ADD COLUMN IF NOT EXISTS rdo_physical_ut_company STRING,
ADD COLUMN IF NOT EXISTS rdo_financial_ut_asset NUMERIC,
ADD COLUMN IF NOT EXISTS rdo_financial_ut_bench NUMERIC,
ADD COLUMN IF NOT EXISTS rdo_financial_ut_branch NUMERIC,
ADD COLUMN IF NOT EXISTS rdo_financial_ut_company STRING,
ADD COLUMN IF NOT EXISTS rdo_cat_class STRING,
ADD COLUMN IF NOT EXISTS rdo_branch_location STRING,
ADD COLUMN IF NOT EXISTS rdo_district STRING,
ADD COLUMN IF NOT EXISTS rdo_region STRING,
ADD COLUMN IF NOT EXISTS is_high_value_loss_auction BOOLEAN,
ADD COLUMN IF NOT EXISTS is_high_value_loss_retail BOOLEAN,
ADD COLUMN IF NOT EXISTS client_id_rouse_analytics INT64,
ADD COLUMN IF NOT EXISTS is_high_value_loss BOOLEAN,
ADD COLUMN IF NOT EXISTS rdo_month_id INT64,
ADD COLUMN IF NOT EXISTS rdo_rental_month DATE,
ADD COLUMN IF NOT EXISTS consignment_date TIMESTAMP,
ADD COLUMN IF NOT EXISTS consignment_listing_number INT64,
ADD COLUMN IF NOT EXISTS consignment_inspection_status STRING,
ADD COLUMN IF NOT EXISTS consignment_sold_time TIMESTAMP,
ADD COLUMN IF NOT EXISTS consignment_sold_price INT64,
ADD COLUMN IF NOT EXISTS consignment_minimum_price INT64,
ADD COLUMN IF NOT EXISTS consignment_sale_event_site_name STRING,
ADD COLUMN IF NOT EXISTS consignment_sale_event_name STRING,
ADD COLUMN IF NOT EXISTS consignment_lot_number STRING,
ADD COLUMN IF NOT EXISTS channel_ritchie_list_days_featured INT64,
ADD COLUMN IF NOT EXISTS channel_mascus_days_featured INT64,
ADD COLUMN IF NOT EXISTS rb_expected_price INT64,
ADD COLUMN IF NOT EXISTS selling_days_featured STRING,
ADD COLUMN IF NOT EXISTS option_01_date TIMESTAMP,
ADD COLUMN IF NOT EXISTS option_02_date TIMESTAMP,
ADD COLUMN IF NOT EXISTS option_01_financial NUMERIC,
ADD COLUMN IF NOT EXISTS option_02_financial NUMERIC,
ADD COLUMN IF NOT EXISTS option_03_financial NUMERIC,
ADD COLUMN IF NOT EXISTS option_04_financial NUMERIC,
ADD COLUMN IF NOT EXISTS option_01_numeric NUMERIC,
ADD COLUMN IF NOT EXISTS option_02_numeric NUMERIC,
ADD COLUMN IF NOT EXISTS option_03_string STRING,
ADD COLUMN IF NOT EXISTS channel_mpe_is_listed BOOLEAN
;
