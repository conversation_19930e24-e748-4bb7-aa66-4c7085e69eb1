import ssl
from equipment_values_history_etl import settings
from equipment_values_history_etl import qa_check
import os
import ast
import json
import pytz
import datetime
from urllib.parse import urlparse
import gcsfs
import asyncpg
import asyncio
import fastavro
import sqlalchemy
import psycopg2
import subprocess
import numpy as np
import pandas as pd
import logging as log
import pandavro as pdx
from fastavro import writer
from decimal import Decimal
from google.cloud import storage
from google.cloud import bigquery
from google.cloud.exceptions import NotFound
from urllib.parse import quote_plus

# logging
log.basicConfig(format=settings.formatting)
log.getLogger().setLevel(log.INFO)
log.getLogger("sqlalchemy.dialects.postgresql").setLevel(log.INFO)


def write_xcom(**kwargs):
    with open("/airflow/xcom/return.json", "w") as fh:
        json.dump(kwargs, fh)
    log.info(kwargs)


def write_ssl_files(ssl_dict):
    """
    Create ssl files and check permissions
    @param ssl_dict: dictionary, path with data to create
    @return: None
    """

    for path, value in ssl_dict.items():
        f = open(path, "w")
        f.write(value)
        f.close()
        os.chmod(path, 0o600)


def setup_pg_engine(db_config):
    ssl_config = ""
    if db_config["use_cert"]:
        temp_folder = "/tmp"
        ssl_root_cert_file_name = f"{temp_folder}/server-ca.pem"
        ssl_cert_file_name = f"{temp_folder}/client-cert.pem"
        ssl_key_file_name = f"{temp_folder}/client-key.pem"

        write_ssl_files(
            {
                ssl_root_cert_file_name: db_config["ssl_root_cert"],
                ssl_cert_file_name: db_config["ssl_cert"],
                ssl_key_file_name: db_config["ssl_key"],
            }
        )
        ssl_config = (
            f"?sslmode=verify-ca&sslrootcert={ssl_root_cert_file_name}"
            f"&sslcert={ssl_cert_file_name}"
            f"&sslkey={ssl_key_file_name}"
        )

    conn_string = (
        f"postgresql+psycopg2://{db_config['username']}:"
        f"{db_config['password']}@{db_config['hostname']}"
        f"/{db_config['database']}"
    ) + ssl_config

    schema = db_config.get("schema", None)
    if schema:
        connect_args = {"options": f"-csearch_path={schema}"}
        engine = sqlalchemy.create_engine(conn_string, connect_args=connect_args)
    else:
        engine = sqlalchemy.create_engine(conn_string)

    conn = engine.connect().execution_options(autocommit=True)
    return engine, conn


def get_min_max_window(upper_window, timedelta_lower_window):
    """Calculate lower bound"""
    upper_window = datetime.datetime.strptime(upper_window, "%Y-%m-%d %H:%M:%S")
    lower_window = upper_window - timedelta_lower_window
    lower_window = datetime.datetime.combine(lower_window.date(), datetime.time.min)
    lower_window = lower_window.strftime("%Y-%m-%d %H:%M:%S")

    log.info(f"Lower window: {lower_window} :: Upper window: {upper_window}")
    return upper_window, lower_window


def read_from_gcs(uris, columns, columns_float=None):
    """Read file from GCS and returns it as pandas dataframe"""
    dtypes = dict()
    if columns_float is not None:
        for column in columns_float:
            dtypes[column] = str

    dfs = []
    for uri in uris:
        log.info(f"Processing blob {uri}")
        df = pd.read_csv(uri, header=0, names=columns, dtype=dtypes)
        dfs.append(df)
    return pd.concat(dfs)


def create_table_from_query(project_id, dataset_id, table_id, query, expiration=False):
    """Creates a persistent table by running a query"""
    client = bigquery.Client(project=project_id)
    dataset_ref = client.dataset(dataset_id)
    dataset_ref.location = "US"
    try:
        dataset_ref = client.create_dataset(dataset_ref)
    except Exception as e:
        log.info(e)
        log.info(f"Dataset {dataset_id} already exists")

    job_config = bigquery.QueryJobConfig()
    job_config.write_disposition = "WRITE_TRUNCATE"
    job_config.range_partitioning = bigquery.table.RangePartitioning(
        field="partition_year",
        range_=bigquery.PartitionRange(start=2000, end=4000, interval=1),
    )
    # Start the query, passing in the extra configuration.
    job_config.destination = f"{project_id}.{dataset_id}.{table_id}"

    query_job = client.query(query, job_config=job_config)
    query_job.result()  # Wait for the job to complete.

    # Set table expiration time
    if expiration:
        destination_table = client.get_table(dataset_ref.table(table_id))
        destination_table.expires = datetime.datetime.now(
            pytz.utc
        ) + datetime.timedelta(days=settings.days_expire)
        client.update_table(destination_table, ["expires"])

    log.info(f"Table created {dataset_id}.{table_id}")
    return dataset_id, table_id


def create_temp_table_from_query(client, sql):
    """Creates temporary table from query"""
    job_config = bigquery.QueryJobConfig()

    # Start the query, passing in the extra configuration.
    query_job = client.query(sql, job_config=job_config)
    query_job.result()  # Wait for the job to complete.
    try:
        destination_table = query_job.destination
        temp_dataset_id = destination_table.dataset_id
        temp_table_id = destination_table.table_id
        log.info(f"Temporal table created {temp_dataset_id}.{temp_table_id}")
    except AttributeError:
        temp_dataset_id = None
        temp_table_id = None
        log.info("No temporal tables created")
    return temp_dataset_id, temp_table_id


def create_view_bigquery(
    project_id, dataset_id, dataset_id_view, table_id, table_name, query
):
    """Runs query that creates a view on BigQuery"""
    client = bigquery.Client(project=project_id)
    shared_dataset_ref = client.dataset(dataset_id_view)
    view_ref = shared_dataset_ref.table(table_name)
    try:
        shared_dataset_ref = client.create_dataset(shared_dataset_ref)
    except Exception as e:
        log.info(e)
        log.info(f"Dataset {dataset_id_view} already exists")

    view = bigquery.Table(view_ref)
    # query the table to create the view
    view.view_query = query.format(
        PROJECT_ID=project_id, DATASET_ID=dataset_id, TABLE_ID=table_id
    )
    try:
        view = client.update_table(view, ["view_query"])
        log.info(f"Successfully updated view at {view.full_table_id}")

    except NotFound:
        view = client.create_table(view)
        log.info(f"Successfully created view at {view.full_table_id}")


def bq_table_to_gcs(client, project_id, uri, dataset_id, table_id):
    """Extracts table and saves it in GCS"""
    log.info("Deleting previous blobs before exporting")
    modified_uri = uri.replace(".avro", "_*.avro")
    storage_client = storage.Client(project=project_id)
    url = urlparse(modified_uri, allow_fragments=False)
    bucket = storage_client.bucket(url.netloc)
    full_prefix = url.path.lstrip("/")

    blobs = bucket.list_blobs(prefix="/".join(full_prefix.split("/")[:-1]))
    for blob in blobs:
        blob.delete()

    dataset_ref = client.dataset(dataset_id)
    table_ref = dataset_ref.table(table_id)

    job_config = bigquery.ExtractJobConfig()
    job_config.destination_format = bigquery.DestinationFormat.AVRO
    job_config.compression = bigquery.Compression.SNAPPY
    job_config.use_avro_logical_types = True
    log.info(f"Extracting data from BQ: {table_id}")
    extract_job = client.extract_table(
        table_ref,
        modified_uri,
        job_config=job_config,
        # Location must match that of the source table.
        location="US",
    )
    extract_job.result()  # Waits for job to complete.

    log.info(f"Exported:{dataset_id}.{table_id} to {modified_uri}")


def gcs_to_bigquery(project_id, dataset_id, table_id, uri, expiration=None):
    """Takes an avro file from GCS and loads it into BigQuery"""
    uri = uri.replace(".avro", "*.avro")
    client = bigquery.Client(project=project_id)
    dataset_ref = client.dataset(dataset_id)

    dataset_ref.location = "US"
    try:
        dataset_ref = client.create_dataset(dataset_ref)
    except Exception as e:
        log.info(e)
        log.info(f"Dataset {dataset_id} already exists")

    # Set deafult table expiration for the dataset
    dataset = client.get_dataset(dataset_ref)
    dataset.default_table_expiration_ms = settings.table_expire_ms
    dataset = client.update_dataset(dataset, ["default_table_expiration_ms"])

    job_config = bigquery.LoadJobConfig()
    job_config.source_format = bigquery.SourceFormat.AVRO
    job_config.use_avro_logical_types = True
    job_config.write_disposition = "WRITE_TRUNCATE"
    job_config.autodetect = True

    load_job = client.load_table_from_uri(
        uri, dataset_ref.table(table_id), job_config=job_config
    )  # API request
    log.info("Starting upload to BigQuery {}".format(load_job.job_id))

    load_job.result()  # Waits for table load to complete.
    log.info("Upload to BigQuery finished.")

    destination_table = client.get_table(dataset_ref.table(table_id))
    log.info("Loaded {} rows.".format(destination_table.num_rows))

    # Set table expiration time
    if expiration:
        destination_table.expires = datetime.datetime.now(
            pytz.utc
        ) + datetime.timedelta(days=settings.days_expire)
        client.update_table(destination_table, ["expires"])


def write_gcloud(input_file, output_bucket):
    """Uplodads local file to gcloud storage"""
    url = urlparse(output_bucket, allow_fragments=False)
    client = storage.Client()
    bucket = client.bucket(url.netloc)
    blob = bucket.blob(url.path.lstrip("/"))
    blob.upload_from_filename(input_file)
    log.info(f"Uploading to bucket: {output_bucket}")


def format_writer(df, output_path, av_schema):
    """Creates file with a certain format, defaults to csv"""
    pd.set_option("display.max_rows", 80)
    if not av_schema:
        av_schema = pdx.__schema_infer(df)

    _dir = os.path.dirname(output_path)
    if not os.path.exists(_dir):
        os.makedirs(_dir)

    cast_ints(df, av_schema)
    df = df.replace({np.nan: None})
    pdx.to_avro(output_path, df, schema=av_schema)


def cast_ints(df, av_schema):
    """Cast ints to Int to support None values without turning them into Floats"""
    fields_to_cast = []
    for field in av_schema["fields"]:
        if field["type"][1] == "int":
            fields_to_cast.append(field["name"])

    cast_dict = {
        "int16": pd.Int16Dtype(),
        "int32": pd.Int32Dtype(),
        "int64": pd.Int64Dtype(),
        "float64": pd.Int64Dtype(),
    }

    for column_name in df:
        if column_name in fields_to_cast:
            df[column_name] = df[column_name].replace({None: np.nan})
            _type = str(df[column_name].dtype)
            df[column_name] = df[column_name].astype(cast_dict[_type])


def check_table_exists(client, dataset_id, table_id):
    """Check if a table exists in BigQuery"""
    dataset = client.dataset(dataset_id)
    table_ref = dataset.table(table_id)
    exists = True
    try:
        client.get_table(table_ref)
        log.info(f"Table {dataset_id}.{table_id} exists")
    except NotFound:
        exists = False
        log.info(f"Table {dataset_id}.{table_id} not found")
    return exists


def check_schema_exists(client, dataset_id):
    """Check if a schema exists in BigQuery"""
    exists = True
    try:
        client.get_dataset(dataset_id)
        log.info(f"Dataset {dataset_id} exists")
    except NotFound:
        exists = False
        log.info(f"Table {dataset_id} not found")
    return exists


def run_query_sets(client, project_id, dataset_id, table_id, sets, variable_dict):
    """Run a sequence of BigQuery queries"""
    version = variable_dict["VERSION_ID"]
    dataset_id_version = variable_dict["DATASET_ID"]
    dataset_id_view = variable_dict["DATASET_VIEW_ID"]

    for _set in sets:
        queries, out, table_name_check = _set
        table_exists = True
        log.info(f"Working on queries: {out}")
        if table_name_check:
            table_check = f"{table_name_check}_{version}"
            dataset_check = dataset_id_version
            if table_name_check == "equipment_values_history":
                dataset_check = dataset_id_view
                table_check = table_name_check
            table_exists = check_table_exists(client, dataset_check, table_check)
        if table_exists:
            for query in queries:
                query = query.format(**variable_dict)
                temp_ds_id, temp_table_id = create_temp_table_from_query(client, query)
                variable_dict["TEMP_DS_ID"] = temp_ds_id
                variable_dict["TEMP_TABLE_ID"] = temp_table_id

            if out is not None:
                variable_dict[f"TEMP_DS_ID_{out}"] = temp_ds_id
                variable_dict[f"TEMP_TABLE_ID_{out}"] = temp_table_id

    query = """
    SELECT
      *
    FROM
      `{PROJECT_ID}.{TEMP_DS_ID}.{TEMP_TABLE_ID}`
    """
    query = query.format(**variable_dict)
    create_table_from_query(project_id, dataset_id, table_id, query, expiration=False)


def bq_to_df(client, query):
    """Runs query in BigQuery and outputs it as a pandas df"""
    dataframe = client.query(query).result().to_dataframe(create_bqstorage_client=False)
    return dataframe


def bq_to_df_iterable(client, query):
    """Runs query in BigQuery and outputs it as a pandas df"""
    dataframe = client.query(query).result().to_dataframe_iterable()
    return dataframe


def bq_extract_schema(client, project_id, dataset_id, table_id):
    """Extract the schema from certain BigQuery table"""
    dataset_ref = client.dataset(dataset_id, project=project_id)
    table_ref = dataset_ref.table(table_id)
    table = client.get_table(table_ref)

    columns = [(schema.name, schema.field_type) for schema in table.schema]
    return columns


def construct_select(
    client,
    project_id,
    dataset_id,
    table_id,
    query,
    query_custom_columns,
    dataset_id_cust_eqpmnt,
    table_settings,
    columns_excluded,
    columns_to_select,
):
    """Adds columns to query in a statement"""
    has_custom_data = check_table_exists(client, dataset_id_cust_eqpmnt, table_settings)
    all_columns = "*"
    value_alt = []
    value_alt_name = []
    columns_dict = dict(bq_extract_schema(client, project_id, dataset_id, table_id))

    if has_custom_data and query_custom_columns:
        custom_columns = bq_to_df(client, query_custom_columns)
        value_alt = custom_columns["value_alt"].tolist()
        value_alt_name = custom_columns["value_alt_name"].tolist()

    select_columns = []
    for column in columns_to_select:
        _type = columns_dict[column]
        if (
            _type != "RECORD"
            and column not in value_alt
            and column not in value_alt_name
            and "_history" not in column
            and column not in columns_excluded
        ):
            if _type == "NUMERIC":
                column = f"CAST({column} AS FLOAT64) {column}"
            select_columns.append(column)
    all_columns = ",\n".join(select_columns)

    query_formatted = query.format(COLUMN_LIST=all_columns)
    return query_formatted


def copy_gcs(input_bucket, output_bucket, project_id, dataset_id, table_id):
    """Copies a blob from one bucket to another with a new name."""
    url_input = urlparse(input_bucket, allow_fragments=False)
    url_output = urlparse(output_bucket, allow_fragments=False)

    client = bigquery.Client(project_id)

    table_exists = check_table_exists(client, dataset_id, table_id)
    if table_exists:
        storage_client = storage.Client(project=project_id)

        source_bucket = storage_client.bucket(url_input.netloc)
        source_blob = source_bucket.blob(url_input.path.lstrip("/"))
        destination_bucket = storage_client.bucket(url_output.netloc)

        blob_copy = source_bucket.copy_blob(
            source_blob, destination_bucket, url_output.path.lstrip("/")
        )

        log.info(
            "Blob {} in bucket {} copied to blob {} in bucket {}.".format(
                source_blob.name,
                source_bucket.name,
                blob_copy.name,
                destination_bucket.name,
            )
        )


def run_shell(command_list):
    """Runs any OS command"""
    subprocess.run(command_list)
    log.info("Proxy started")


def drop_pg_versions(
    conn,
    pg_schema,
    pg_query_tables_catalog,
    pg_version_table,
    retention_num_versions,
    pg_query_drop,
):
    """Drops older PostgreSQL views or tables, based on the 'retention_num_versions' specified"""
    log.info(f"Cleaning older versions based on: {pg_schema}.{pg_version_table}")
    log.info(f"Query for PG Catalog: {pg_query_tables_catalog}")
    pg_table_list = pd.read_sql(pg_query_tables_catalog, conn)["tablename"].tolist()

    retention_num_versions = int(retention_num_versions)

    log.info(
        f"Tables PG: {pg_table_list} using retention number for {retention_num_versions} versions"
    )

    if pg_version_table in pg_table_list:
        pg_table_list.remove(pg_version_table)

    if len(pg_table_list) <= 0:
        log.info("No tables found to drop")
        return

    pg_table_list.sort()

    if len(pg_table_list) <= 0:
        log.info("No tables to drop")
        return

    log.info(f"Dropping previous versioned tables {pg_table_list}")
    tables = ",".join([f"{pg_schema}.{table}" for table in pg_table_list])
    query_drop = pg_query_drop.format(TABLES=tables)
    log.info(f"Tables to drop: {tables}")
    conn.execute(query_drop)
    log.info("Tables dropped")


def drop_version_tables(
    client,
    conn,
    pg_query_tables_catalog,
    bq_query_tables_schema,
    pg_query_drop,
    pg_schema_version,
    pg_table,
):
    """Drops older PostgreSQL tables, based on the correspondent BigQuery existing tables"""
    pg_table_list = pd.read_sql(sqlalchemy.text(pg_query_tables_catalog), conn)[
        "tablename"
    ].tolist()
    bq_table_list = bq_to_df(client, bq_query_tables_schema)["table_name"].tolist()

    pg_table_list_no_partition = pg_table_list.copy()
    for table in pg_table_list:
        delimiter = table[-5]
        year = int(table[-4:])
        if delimiter == "_" and year >= 2018:
            pg_table_list_no_partition.remove(table)

    tables_to_drop = set(pg_table_list_no_partition) - set(bq_table_list)
    if pg_table in tables_to_drop:
        tables_to_drop.remove(pg_table)

    if len(tables_to_drop) > 0:
        log.info("Dropping previous versioned tables")
        tables = ",".join([f"{pg_schema_version}.{table}" for table in tables_to_drop])
        query_drop = pg_query_drop.format(TABLES=tables)
        log.info(f"Tables to drop: {tables}")
        conn.execute(query_drop)
        log.info("Tables dropped")
    else:
        log.info("No tables found to drop")


def list_blobs(project_id, avro_path):
    """Lists blob from the bucket"""
    storage_client = storage.Client(project=project_id)
    url = urlparse(avro_path, allow_fragments=False)
    bucket = storage_client.bucket(url.netloc)
    full_prefix = url.path.lstrip("/")
    blobs = bucket.list_blobs(prefix="/".join(full_prefix.split("/")[:-1]))
    return blobs


async def avro_to_pg(db_config, pg_schema, pg_table, bq_export_columns, blobs):
    """Export dataframe list into PostgreSQL"""
    log.info("Starting import")

    conn_params = {
        "user": db_config["username"],
        "password": db_config["password"],
        "host": db_config["hostname"],
        "database": db_config["database"],
    }

    if db_config["use_cert"]:
        temp_folder = "/tmp"
        ssl_root_cert_file_name = f"{temp_folder}/server-ca.pem"
        ssl_cert_file_name = f"{temp_folder}/client-cert.pem"
        ssl_key_file_name = f"{temp_folder}/client-key.pem"

        write_ssl_files(
            {
                ssl_root_cert_file_name: db_config["ssl_root_cert"],
                ssl_cert_file_name: db_config["ssl_cert"],
                ssl_key_file_name: db_config["ssl_key"],
            }
        )

        log.info("Using connection pool SSL certs")
        sslctx = ssl.create_default_context(
            ssl.Purpose.SERVER_AUTH, cafile=ssl_root_cert_file_name
        )

        sslctx.check_hostname = False  # sslmode=verify-ca.

        sslctx.load_cert_chain(
            ssl_cert_file_name,
            keyfile=ssl_key_file_name,
        )
        conn_params["ssl"] = sslctx

    con = await asyncpg.connect(**conn_params)
    num_rows = 0
    chunk = 200000
    for blob in blobs:
        batch = []
        with blob.open("rb") as f:
            reader = fastavro.reader(f)
            for p in reader:
                len_batch = len(batch)
                if len_batch >= chunk:
                    log.info(f"Processed {num_rows} rows")
                    await con.copy_records_to_table(
                        pg_table,
                        schema_name=pg_schema,
                        records=batch,
                        columns=bq_export_columns,
                    )
                    num_rows += len_batch
                    batch.clear()
                batch.append(tuple(p.values()))
        if len(batch) > 0:
            await con.copy_records_to_table(
                pg_table,
                schema_name=pg_schema,
                records=batch,
                columns=bq_export_columns,
            )
            num_rows += len(batch)

    log.info(f"Exported {num_rows}")
    await con.close()
    log.info("Import succesful")
    return num_rows


def bq_to_pg(
    client,
    db_config,
    pg_schema_version,
    pg_table,
    bq_query,
    bq_query_tables_schema,
    pg_query_create,
    pg_query_drop,
    pg_query_truncate,
    pg_query_tables_catalog,
    pg_query_create_view,
    bq_export_columns,
    pg_schema_view,
    pg_view,
    pg_table_partition,
    pg_query_create_partition,
    project_id,
    avro_path,
):
    """Export BigQuery table into a postgress DB"""
    # log.info(f"Export {pg_table} started")
    engine, conn = setup_pg_engine(db_config)

    if not engine.dialect.has_table(conn, pg_table_partition, schema=pg_schema_version):
        log.info(f"Partition does not exist, creating {pg_table_partition}...")
        conn.execute(pg_query_create_partition)
    else:
        log.info("Table exists, truncating...")
        conn.execute(pg_query_truncate)

    temp_ds_id, temp_table_id = create_temp_table_from_query(client, bq_query)
    bq_table_to_gcs(client, project_id, avro_path, temp_ds_id, temp_table_id)

    blobs = list_blobs(project_id, avro_path)

    try:
        log.info(f"Populating PostgreSQL partition {pg_table_partition}")
        bq_rows = asyncio.get_event_loop().run_until_complete(
            avro_to_pg(
                db_config,
                pg_schema_version,
                pg_table_partition,
                bq_export_columns,
                blobs,
            )
        )
        log.info(f"Number of rows exported: {bq_rows}")
        qa_check.pg_import_check(conn, bq_rows, pg_schema_version, pg_table_partition)

    finally:
        conn.close()
        engine.dispose()
    log.info(f"Export {pg_table} done")


def mssql_to_gcs(
    domain,
    username,
    password,
    sql_host,
    sql_db,
    project_id,
    gcs_uri,
    tmpdir,
    query,
    av_schema,
    qa_columns,
    year,
):
    """Retrieve data from MSSQL and upload it to GCS"""

    engine = sqlalchemy.create_engine(
        f"mssql+pymssql://{username}:{quote_plus(password)}@{sql_host}/{sql_db}"
    )
    conn = engine.connect()
    results_proxy = conn.execute(query)

    metrics = export_to_gcs(results_proxy, gcs_uri, tmpdir, av_schema, qa_columns, year)
    if metrics == qa_check.generate_metrics_template(qa_columns):
        log.info("No data has been found")
    return metrics


def export_to_gcs(results_proxy, output_path, tmpdir, av_schema, qa_columns, year):
    """Exports pandas dataframe to gcloud storage"""
    url = urlparse(output_path, allow_fragments=False)
    metrics = qa_check.generate_metrics_template(qa_columns)
    if url.scheme != "":
        count = 0
        while True:
            log.info(f"Exporting part {count}")
            tmp_path = f"{tmpdir}{url.path}"
            partial_results = results_proxy.fetchmany(500000)
            if not partial_results:
                break
            records = [dict(row) for row in partial_results]
            df = pd.DataFrame(records)
            qa_check.get_data_metrics_source(metrics, df, qa_columns)
            parsed_schema = fastavro.parse_schema(av_schema)
            _dir = os.path.dirname(tmp_path)
            if not os.path.exists(_dir):
                os.makedirs(_dir)
            with open(tmp_path, "wb+") as out:
                writer(out, parsed_schema, records, codec="snappy")
            del df
            write_gcloud(
                tmp_path, output_path.replace(".avro", f"_{year}_{count}.avro")
            )
            os.remove(tmp_path)
            count += 1

    for key, val in metrics.items():
        if type(val) is Decimal:
            metrics[key] = str(val)

    write_xcom(metrics=str(metrics))
    return metrics


def gcs_to_local(bucket):
    """Read file from GCS and returns it as pandas dataframe"""
    fs = gcsfs.GCSFileSystem(token="google_default")
    local_file = bucket.split("/")[-1]
    log.info(f"Downloading {bucket} to {local_file}...")
    fs.get(bucket, local_file)
    return local_file


def pg_to_df(query, db_config):
    """Read from PostgreSQL and exports to dataframe"""
    engine, conn = setup_pg_engine(db_config)
    df = pd.read_sql(query, conn)
    return df


def pg_prepare_env(pg_table, db_config, pg_schema_version, pg_query_create):
    """Creates schema, table in Cloud SQL Postgres"""
    log.info(f"Preparing environment for {pg_table} started")

    engine, conn = setup_pg_engine(db_config)

    if not engine.dialect.has_table(conn, pg_table, schema=pg_schema_version):
        log.info("Main table does not exist, creating...")
        conn.execute(pg_query_create)
    else:
        log.info("Main table exists")
    conn.close()
    engine.dispose()


def run_bq_query(project_id, query):
    """Runs arbitrary query in BigQuery"""
    client = bigquery.Client(project=project_id)
    query_job = client.query(query)
    return query_job


def create_partitioned_table_from_query(
    project_id, dataset_id, dataset_id_view, table_id, table_name, query, partition
):
    """Creates a persistent partitioned table by running a query"""
    client = bigquery.Client(project=project_id)
    dataset_ref = client.dataset(dataset_id_view)
    dataset_ref.location = "US"
    try:
        dataset_ref = client.create_dataset(dataset_ref)
    except Exception as e:
        log.info(e)
        log.info(f"Dataset {dataset_id_view} already exists")

    job_config = bigquery.QueryJobConfig()
    job_config.write_disposition = bigquery.WriteDisposition.WRITE_APPEND

    job_config.schema_update_options = [
        bigquery.SchemaUpdateOption.ALLOW_FIELD_ADDITION
    ]

    job_config.range_partitioning = bigquery.table.RangePartitioning(
        field=partition,
        range_=bigquery.PartitionRange(start=2000, end=4000, interval=1),
    )

    # Start the query, passing in the extra configuration.
    job_config.destination = f"{project_id}.{dataset_id_view}.{table_name}"

    query = query.format(
        PROJECT_ID=project_id, DATASET_ID=dataset_id, TABLE_ID=table_id
    )
    query_job = client.query(query, job_config=job_config)
    query_job.result()  # Wait for the job to complete.

    log.info(f"Table created {dataset_id}.{table_name}")


def delete_partition_in_table(
    project_id, dataset_id, table_id, query, dataset_id_partition, table_id_partition
):
    """Deletes a partition in a table by running a query"""
    client = bigquery.Client(project=project_id)

    query = query.format(
        PROJECT_ID=project_id,
        DATASET_ID=dataset_id,
        TABLE_ID=table_id,
        DATASET_ID_PARTITION=dataset_id_partition,
        TABLE_ID_PARTITION=table_id_partition,
    )
    query_job = client.query(query)
    try:
        query_job.result()  # Wait for the job to complete.
        log.info("Partitions deleted")
    except NotFound:
        log.info(f"Table not found: {project_id}.{dataset_id}.{table_id}")


def df_to_bq(client, bq_df, dataset_id, table_id, table_ref, expiration=False):
    """Create a table from a dataframe"""
    dataset_ref = client.dataset(dataset_id)
    dataset_ref.location = "US"
    try:
        dataset_ref = client.create_dataset(dataset_ref)
    except Exception as e:
        log.info(e)
        log.info(f"Dataset {dataset_id} already exists")

    job_config = bigquery.LoadJobConfig()
    job_config.write_disposition = "WRITE_TRUNCATE"
    job_config.autodetect = True

    job = client.load_table_from_dataframe(bq_df, table_ref, job_config=job_config)
    job.result()  # Wait for the job to complete.

    if expiration:
        destination_table = client.get_table(dataset_ref.table(table_id))
        destination_table.expires = datetime.datetime.now(
            pytz.utc
        ) + datetime.timedelta(days=settings.days_expire)
        client.update_table(destination_table, ["expires"])


def pg_to_pg(
    client,
    db_config,
    pg_schema_version,
    pg_table,
    year,
    pg_query_drop,
    pg_query_truncate,
    bq_query_partition,
    pg_query_create_view,
    pg_schema_view,
    pg_view,
    pg_table_partition,
    pg_query_create_partition,
    pg_query_find_latest_version,
    pg_insert_data,
    variable_dict,
):
    """Export BigQuery table into a postgress DB"""
    log.info(f"Export for modified year on {pg_table} started")
    partitions = bq_to_df(client, bq_query_partition)["partition_year"].values.tolist()
    log.info(f"Partitions found in BQ: {partitions}")
    if int(year) in partitions:
        engine, conn = setup_pg_engine(db_config)

        if not engine.dialect.has_table(
            conn, pg_table_partition, schema=pg_schema_version
        ):
            log.info(f"Partition does not exist, creating {pg_table_partition}...")
            conn.execute(pg_query_create_partition)
        else:
            log.info("Table exists, truncating...")
            conn.execute(pg_query_truncate)

        try:
            results_proxy = conn.execute(sqlalchemy.text(pg_query_find_latest_version))
            sql_results = results_proxy.fetchone()
            if sql_results:
                pg_table_previous_ver = list(sql_results)[0]
                log.info(f"Previous table found: {pg_table_previous_ver}")

                possible_year = pg_table_previous_ver.split("_")[-1]
                if len(possible_year) == 4:
                    pg_table_previous_ver = pg_table_previous_ver[:-5]

                variable_dict["PG_TABLE_PREV"] = pg_table_previous_ver

                pg_insert_data = pg_insert_data.format(**variable_dict)
                conn.execute(pg_insert_data)
                log.info("Data copied done")

            else:
                log.info("No previous table with data available")
        finally:
            conn.close()
            engine.dispose()
    else:
        log.info(f"Partition {year} does not exist, skipping")


def pg_check_previous_partition_exists(
    db_config,
    year,
    pg_query_find_latest_version,
    partitions,
):
    """Check if data in BQ is in PG"""
    previous_table_found = False
    if int(year) in partitions:
        engine, conn = setup_pg_engine(db_config)
        try:
            results_proxy = conn.execute(sqlalchemy.text(pg_query_find_latest_version))
            sql_results = results_proxy.fetchone()
            if sql_results:
                pg_table_previous_ver = list(sql_results)[0]
                log.info(f"Previous table found: {pg_table_previous_ver}")
                possible_year = pg_table_previous_ver.split("_")[-1]
                if int(possible_year) == int(year):
                    previous_table_found = True
            else:
                log.info("No previous table with data available")
        finally:
            conn.close()
            engine.dispose()
    return previous_table_found


def pg_postprepare_env(
    engine,
    conn,
    pg_table,
    pg_schema_version,
    pg_query_create_view,
    pg_query_tables_catalog,
    bq_query_tables_schema,
    pg_query_drop,
    client,
):
    """Creates view and drop tables in Cloud SQL Postgres"""
    log.info(f"Preparing environment for {pg_table} started")

    if engine.dialect.has_table(conn, pg_table, schema=pg_schema_version):
        log.info(f"Recreating view...: {pg_query_create_view}")
        conn.execute(pg_query_create_view)
        log.info("View created/updated")

        drop_version_tables(
            client,
            conn,
            pg_query_tables_catalog,
            bq_query_tables_schema,
            pg_query_drop,
            pg_schema_version,
            pg_table,
        )
    else:
        log.info("Main table does not exist")


def pg_postprepare_env_fm(
    engine,
    conn,
    pg_schema_version,
    pg_query_create_view_fm,
    table_drop_config_list_fm,
):
    """Creates view and drop tables in Cloud SQL Postgres"""
    log.info(f"Preparing environment for fleet_manager tables started")

    log.info(f"Recreating view FM version...: {pg_query_create_view_fm}")
    conn.execute(pg_query_create_view_fm)
    log.info("View created/updated for FM version")

    for table_drop in table_drop_config_list_fm:
        drop_pg_versions(conn, **table_drop)


def mssql_query(username, password, sql_host, sql_db, query):
    """Retrieve data from MSSQL"""
    engine = sqlalchemy.create_engine(
        f"mssql+pymssql://{username}:{quote_plus(password)}@{sql_host}/{sql_db}"
    )
    conn = engine.connect()
    try:
        results_proxy = conn.execute(query)
        records = results_proxy.fetchone()
    finally:
        conn.close()
        engine.dispose()

    return records


# Pipelines


def get_modified_years(
    project_id,
    dataset_view_id,
    dataset_stage_id,
    username,
    password,
    sql_host,
    sql_db,
    query_count_years,
    view_counter_id,
    table_counter_id,
    bq_query_counter_table,
):
    """Obtain modified years to be exported"""
    client = bigquery.Client(project_id)

    engine = sqlalchemy.create_engine(
        f"mssql+pymssql://{username}:{quote_plus(password)}@{sql_host}/{sql_db}"
    )
    conn = engine.connect()
    results_proxy = conn.execute(query_count_years)
    sql_results = results_proxy.fetchall()
    if not sql_results:
        log.info("No data available")
    records = [dict(row) for row in sql_results]
    sql_df = pd.DataFrame(records)
    log.info(sql_df)
    if not sql_df.empty:
        view_exists = check_table_exists(client, dataset_view_id, view_counter_id)
        table_exists = check_table_exists(client, dataset_stage_id, table_counter_id)
        log.info(
            f"Checking... view_exists: {view_exists} for dataset_view_id: {dataset_view_id}, view_counter_id: {view_counter_id}, project_id: {project_id}"
        )
        log.info(
            f"Checking... table_exists: {table_exists} for dataset_stage_id: {dataset_stage_id}, table_counter_id: {table_counter_id}, project_id: {project_id}"
        )
        if not view_exists or table_exists:
            log.info("Counter table does not exist")
            bq_df = sql_df.copy().truncate(before=-1, after=-1)
        else:
            query_count = bq_query_counter_table.format(
                PROJECT_ID=project_id,
                DATASET_ID=dataset_view_id,
                TABLE_ID=view_counter_id,
            )
            bq_df = bq_to_df(client, query_count)

        log.info(bq_df)

        df_diff = pd.concat([sql_df, bq_df]).drop_duplicates(keep=False)
        found_2018 = any(i < 2018 for i in df_diff["year"])
        year_set = set(df_diff["year"])
        if found_2018:
            year_set.add(2018)
        years = list(year_set)
        log.info(f"Years to export {years}")
        write_xcom(years=str(years))

        # Create and update counter table
        table_ref = f"{project_id}.{dataset_stage_id}.{table_counter_id}"
        df_to_bq(client, sql_df, dataset_stage_id, table_counter_id, table_ref)
        log.info(f"Table created {table_ref}")

        # Create main view
        view_query = bq_query_counter_table.format(
            PROJECT_ID=project_id,
            DATASET_ID=dataset_stage_id,
            TABLE_ID=table_counter_id,
        )
        create_view_bigquery(
            project_id,
            dataset_stage_id,
            dataset_view_id,
            table_counter_id,
            view_counter_id,
            view_query,
        )

    else:
        log.info("Skipping, no historic data found")
        write_xcom(years=str([]))


def mssql2gcs(
    project_id,
    dataset_id,
    table_id,
    output_path_mssql,
    tmpdir,
    domain,
    username,
    password,
    sql_host,
    sql_db,
    query,
    av_schema,
    qa_columns,
    year,
    years_found,
    query_year_filter,
    dataset_view_id,
    evh_table_name,
    table_name,
    ss_query_count,
):
    """Export from MSSQL Database to GCS"""
    run = True
    if years_found != "":
        years_to_run = ast.literal_eval(years_found)
        if year != "":
            _log = year
            if int(year) not in years_to_run:
                run = False
        else:
            _log = table_id
        if len(years_to_run) > 0:
            query_year_filter = query_year_filter.format(YEARS=years_found[1:-1])
            log.info(f"Applied filters: {years_found[1:-1]}")
        else:
            run = False
            query_year_filter = ""

    log.info(f"Working in {table_id}")
    if "no_version" in table_id:
        client = bigquery.Client(project=project_id)
        view_exists = check_table_exists(client, dataset_view_id, evh_table_name)
        if view_exists:
            run = False
            _log = table_id

    query = query.format(YEARS=query_year_filter)

    if run:
        metrics = mssql_to_gcs(
            domain,
            username,
            password,
            sql_host,
            sql_db,
            project_id,
            output_path_mssql,
            tmpdir,
            query,
            av_schema,
            qa_columns,
            year,
        )
        if table_name == "equipment_values_history":
            log.info(ss_query_count)
            sql_count = mssql_query(
                username, password, sql_host, sql_db, ss_query_count
            )[0]
            metric_count = metrics["num_rows"]
            if sql_count != metric_count:
                raise ValueError(
                    f"""Not all rows have been pulled.
                       Extracted: {metric_count}
                       Rows in db: {sql_count}
                    """
                )
            else:
                log.info("Pre check passed")
    else:
        log.info(f"Export for {_log} skipped")
        write_xcom(metrics=str(qa_check.generate_metrics_template(qa_columns)))


def gcs2bq(project_id, dataset_id, table_id, output_path_mssql, qa_columns, metrics):
    """Export from GCS to BigQuery"""
    metric = qa_check.process_metrics(metrics, qa_columns)

    if metric != qa_check.generate_metrics_template(qa_columns):
        # Create version table
        gcs_to_bigquery(
            project_id, dataset_id, table_id, output_path_mssql, expiration=False
        )
    else:
        log.info("No data has been found")


def bqquery2bq(
    project_id,
    dataset_id,
    dataset_view_id,
    dataset_stage_id,
    table_id,
    sets,
    table_adj_factors,
    table_history_no_version,
    table_initial_valuations,
    table_field_version,
    table_history_details,
    table_name,
    query_view,
    version,
    query_delete_partition,
    client_id,
    sales_code,
):
    """Runs queries in BQ and persits result in a table"""

    variable_dict = {
        "VERSION_ID": version,
        "PROJECT_ID": project_id,
        "DATASET_ID": dataset_id,
        "TABLE_ID": table_id,
        "DATASET_VIEW_ID": dataset_view_id,
        "TABLE_ID_NAME": table_name,
        "DATASET_ID_ADJ_FACTORS": dataset_id,
        "TABLE_ID_ADJ_FACTORS": table_adj_factors,
        "DATASET_ID_FIELD_VERSION": dataset_id,
        "TABLE_ID_FIELD_VERSION": table_field_version,
        "DATASET_ID_HISTORY_NO_VERSION": dataset_id,
        "TABLE_ID_HISTORY_NO_VERSION": table_history_no_version,
        "DATASET_ID_INIT_VALUATIONS": dataset_id,
        "TABLE_ID_INIT_VALUATIONS": table_initial_valuations,
        "DATASET_ID_HISTORY_DETAILS": dataset_id,
        "TABLE_ID_HISTORY_DETAILS": table_history_details,
        "CLIENT_ID": client_id,
        "SALES_CODE": sales_code,
    }
    client = bigquery.Client(project=project_id)

    table_exists = check_table_exists(client, dataset_id, table_id)
    if table_exists:
        run_query_sets(
            client, project_id, dataset_stage_id, table_id, sets, variable_dict
        )

        # Delete partition
        delete_partition_in_table(
            project_id,
            dataset_view_id,
            table_name,
            query_delete_partition,
            dataset_stage_id,
            table_id,
        )

        # Create main view
        create_partitioned_table_from_query(
            project_id,
            dataset_stage_id,
            dataset_view_id,
            table_id,
            table_name,
            query_view,
            "partition_year",
        )


def pg_env(
    project_id,
    dataset_id,
    table_id,
    db_config,
    pg_schema_version,
    pg_table,
    pg_query_create,
    pg_view,
    pg_schema_view,
):
    """Creates schema, table in Cloud SQL Postgres"""
    client = bigquery.Client(project_id)
    table_exists = True
    if not ("_fm" in table_id):
        table_exists = check_table_exists(client, dataset_id, table_id)
    engine, conn = setup_pg_engine(db_config)
    final_view_exists = True
    try:
        final_view_exists = engine.dialect.has_table(
            conn, pg_view, schema=pg_schema_view
        )
    finally:
        conn.close()
        engine.dispose()
    if table_exists or not (final_view_exists):
        # Start proxy
        pg_prepare_env(
            pg_table,
            db_config,
            pg_schema_version,
            pg_query_create,
        )


def bq2pg(
    project_id,
    dataset_id,
    table_id,
    db_config,
    pg_schema,
    pg_table,
    query,
    pg_query_create,
    pg_query_drop,
    pg_query_truncate,
    bq_query_tables_schema,
    pg_query_tables_catalog,
    pg_query_create_view,
    pg_schema_view,
    pg_view,
    query_export,
    bq_export_columns,
    pg_table_partition,
    pg_query_create_partition,
    year,
    years_found,
    pg_query_find_latest_version,
    pg_insert_data,
    variable_dict,
    avro_path,
    bq_query_partition,
):
    """Export from BigQuery to Cloud SQL Postgres"""
    client = bigquery.Client(project_id)
    years_to_run = ast.literal_eval(years_found)
    table_exists = True
    if "_fm" not in table_id:
        log.info(table_id)
        table_exists = check_table_exists(client, dataset_id, table_id)

    engine, conn = setup_pg_engine(db_config)
    final_view_exists = True
    try:
        final_view_exists = engine.dialect.has_table(
            conn, pg_view, schema=pg_schema_view
        )
    finally:
        conn.close()
        engine.dispose()
    if table_exists or not (final_view_exists):
        log.info(f"Modified years {years_found}")
        partitions = bq_to_df(client, bq_query_partition)[
            "partition_year"
        ].values.tolist()
        log.info(f"Partitions found in BQ: {partitions}")

        if "_fm" not in table_id and not (final_view_exists):
            # if the view doesn't exist then previous versions doesn't exist and
            # we want to download the data from BQ
            years_found = str(partitions)
            years_to_run = partitions

        if len(years_to_run) > 0:
            export_from_bq = False
            pg_previous_table_found = pg_check_previous_partition_exists(
                db_config,
                year,
                pg_query_find_latest_version,
                partitions,
            )
            if year in years_found or (
                int(year) in partitions and not (pg_previous_table_found)
            ):
                export_from_bq = True

            if export_from_bq:
                bq_to_pg(
                    client,
                    db_config,
                    pg_schema,
                    pg_table,
                    query_export,
                    bq_query_tables_schema,
                    pg_query_create,
                    pg_query_drop,
                    pg_query_truncate,
                    pg_query_tables_catalog,
                    pg_query_create_view,
                    bq_export_columns,
                    pg_schema_view,
                    pg_view,
                    pg_table_partition,
                    pg_query_create_partition,
                    project_id,
                    avro_path,
                )
            else:
                pg_to_pg(
                    client,
                    db_config,
                    pg_schema,
                    pg_table,
                    year,
                    pg_query_drop,
                    pg_query_truncate,
                    bq_query_partition,
                    pg_query_create_view,
                    pg_schema_view,
                    pg_view,
                    pg_table_partition,
                    pg_query_create_partition,
                    pg_query_find_latest_version,
                    pg_insert_data,
                    variable_dict,
                )
        else:
            log.info("Export skipped")


def pg_env_post(
    fleet_customer_id,
    db_config,
    pg_schema_version,
    pg_table,
    pg_query_create_view,
    pg_query_create_view_fm,
    project_id,
    dataset_id,
    table_id,
    pg_query_tables_catalog,
    bq_query_tables_schema,
    pg_query_drop,
    table_drop_config_list_fm,
    pg_view,
    pg_schema_view,
):
    """Drops tables and updates view in Cloud SQL Postgres"""
    client = bigquery.Client(project_id)
    table_exists = check_table_exists(client, dataset_id, table_id)
    final_view_exists = True
    try:
        engine, conn = setup_pg_engine(db_config)
        final_view_exists = engine.dialect.has_table(
            conn, pg_view, schema=pg_schema_view
        )
    finally:
        conn.close()
        engine.dispose()

    engine, conn = setup_pg_engine(db_config)

    try:
        with conn.begin():
            if table_exists or not (final_view_exists):
                pg_postprepare_env(
                    engine,
                    conn,
                    pg_table,
                    pg_schema_version,
                    pg_query_create_view,
                    pg_query_tables_catalog,
                    bq_query_tables_schema,
                    pg_query_drop,
                    client,
                )

            if fleet_customer_id:
                pg_postprepare_env_fm(
                    engine,
                    conn,
                    pg_schema_version,
                    pg_query_create_view_fm,
                    table_drop_config_list_fm,
                )
            else:
                log.info(
                    f"Client with fleet_customer_id: {fleet_customer_id} is not enabled for IMS"
                )
    finally:
        conn.close()
        engine.dispose()

    log.info("Create production views on Postgres finished")


def pg_execute_ddl_list(db_config, pg_ddl_list):
    """Create addtional views in PostgreSQL"""
    log.info("Additional views creation started")
    engine, conn = setup_pg_engine(db_config)

    try:
        for pg_dll in pg_ddl_list:
            start_query = datetime.datetime.now()
            log.info(f"Recreating...: {pg_dll}")
            conn.execute(pg_dll)
            end_query = datetime.datetime.now()
            log.info(f"Running Postgres Query Time Took: {end_query - start_query}")
    finally:
        conn.close()
        engine.dispose()


def pg_count(
    db_config,
    query_count,
):
    """Create addtional views in PostgreSQL"""
    log.info("Additional views creation started")

    engine, conn = setup_pg_engine(db_config)

    try:
        df = qa_check.run_pg_queries(conn, [query_count], {})[0]
        count_result = df.iloc[0]["count"]
        log.info(f"Count result...: {count_result}")

        write_xcom(
            metrics={
                "num_rows": int(count_result),
                "is_data_empty": bool(count_result <= 0),
            }
        )
    except sqlalchemy.exc.ProgrammingError as sql_error:
        try:
            raise sql_error.orig
        except psycopg2.errors.UndefinedTable:
            log.warning(
                "Attempted to count table but were not found... considering '0' as count"
            )
            write_xcom(
                metrics={
                    "num_rows": int(0),
                    "is_data_empty": True,
                }
            )

    finally:
        conn.close()
        engine.dispose()


def get_modified_years_fm(
    project_id,
    dataset_view_id,
    table_name,
    bq_query_counter_table,
):
    """Obtain modified years to be exported"""

    client = bigquery.Client(project_id)
    view_exists = check_table_exists(client, dataset_view_id, table_name)
    if not view_exists:
        log.info("Counter table does not exist")
        write_xcom(years=str([]))

    else:
        query_count = bq_query_counter_table.format(
            PROJECT_ID=project_id,
            DATASET_ID=dataset_view_id,
            TABLE_ID=table_name,
        )
        bq_df = bq_to_df(client, query_count)

        log.info(bq_df)
        found_2018 = any(i < 2018 for i in bq_df["year"])
        year_set = set(bq_df["year"])
        if found_2018:
            year_set.add(2018)
        years = list(year_set)
        log.info(f"Years to export {years}")
        write_xcom(years=str(years))
