from equipment_values_history_etl.tables import (
    equipment_values_history_no_version,
    equipment_values_history,
    equipment_values_history_fm,
    adjustment_factors,
    field_version,
    initial_valuations,
    equipment_history_detailed,
)
from datetime import timedelta

# Logging
formatting = "%(asctime)s %(levelname)7s %(message)s"

# GC

project_id = "rs-client-{}-{CLIENT_VERSION}"

project_id_app_envs = {
    "local": "dev-c55fa4",
    "dev": "dev-c55fa4",
    "prod": "prod-707493",
    "ppe": "prod-707493",
}
project_id_app = "appraisals-data-{}"

output_filename_publish_envs = {
    "local": "dev-1e0721",
    "dev": "dev-1e0721",
    "prod": "prod-5f68c8",
    "ppe": "prod-5f68c8",
}

# BQ
dataset_id_envs = {"local": "_dev", "dev": "_dev", "prod": "", "ppe": "_ppe"}
dataset_id = "equipment_values_history_version{}"

dataset_id_view_envs = {"local": "_dev", "dev": "_dev", "prod": "", "ppe": "_ppe"}
dataset_id_view = "equipment_values_history{}"

dataset_id_stage_envs = {"local": "_dev", "dev": "_dev", "prod": "", "ppe": "_ppe"}
dataset_id_stage = "equipment_values_history_stage{}"

table_id_adj_factors = "adjustment_factors"
table_id_field_version = "field_version"
table_id_history_no_version = "equipment_values_history_no_version"
table_id_initial_valuations = "initial_valuations"
table_id_history_details = "equipment_history_detailed"

# Cloud SQL
pg_schema_view = "rfm_{CLIENT_CODE}"
pg_schema_version = "rfm_{CLIENT_CODE}_version"

pg_timedelta_lower_window = timedelta(days=5)

# BigQuery
table_expire_ms = 30 * 24 * 60 * 60 * 1000
days_expire = 30

# Tables
table_dict = {
    "equipment_values_history_no_version": equipment_values_history_no_version,
    "equipment_values_history": equipment_values_history,
    "equipment_values_history_fm": equipment_values_history_fm,
    "adjustment_factors": adjustment_factors,
    "field_version": field_version,
    "initial_valuations": initial_valuations,
    "equipment_history_detailed": equipment_history_detailed,
}
