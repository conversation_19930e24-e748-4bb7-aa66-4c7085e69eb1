import ast
import logging

import click
from equipment_values_history_etl import etl
from equipment_values_history_etl import settings
from equipment_values_history_etl import qa_check
from equipment_values_history_etl import __version__


@click.group()
@click.version_option(version=__version__)
def cli():
    pass


@cli.command()
@click.option(
    "--username", type=str, envvar="PG_USER", help="Postgres username", required=True
)
@click.option(
    "--password",
    type=str,
    envvar="PG_PASSWORD",
    help="Postgres password",
    required=True,
)
@click.option("--pg-host", type=str, envvar="DB_SERVER", help="Postgres hostname")
@click.option(
    "--table-name",
    type=str,
    envvar="TABLE_NAME",
    help="Table name to process",
    required=True,
)
@click.option("--env", type=str, envvar="ENV", help="Environment")
@click.option(
    "--client-code", type=str, envvar="CLIENT_CODE", help="Client Code", required=True
)
@click.option(
    "--fleet-customer-id",
    type=str,
    default=None,
    envvar="FLEET_CUSTOMER_ID",
    help="Fleet customer id",
)
@click.option("--version", type=str, envvar="VERSION", help="Version ID", required=True)
@click.option("--upper-window", type=str, envvar="UPPER_WINDOW", help="Datetime window")
@click.option(
    "--use-cert",
    type=bool,
    envvar="USE_PG_CERT",
    required=True,
    help="Flag to use or not pg ssl",
)
@click.option(
    "--ssl-root-cert",
    type=str,
    envvar="SSL_ROOT_CERT",
    help="contains SSL certificate authority (CA) certificate(s)",
)
@click.option(
    "--ssl-cert",
    type=str,
    envvar="SSL_CERT",
    help="the client SSL certificate",
)
@click.option(
    "--ssl-key",
    type=str,
    envvar="SSL_KEY",
    help="the secret key used for the client certificate",
)
@click.option(
    "--pg-db", type=str, envvar="PG_DB", help="DB name", required=False, default=""
)
def pg_create_materialized_tables(
    username,
    password,
    pg_host,
    table_name,
    env,
    client_code,
    fleet_customer_id,
    version,
    upper_window,
    use_cert,
    ssl_root_cert,
    ssl_cert,
    ssl_key,
    pg_db,
):
    """Run queries for create version views/tables for Fleet Manager flow."""
    table = settings.table_dict[table_name]
    pg_table = table.pg_table
    pg_schema_view = settings.pg_schema_view.format(CLIENT_CODE=client_code)
    pg_schema_version = settings.pg_schema_version.format(CLIENT_CODE=client_code)

    variable_dict = {
        "CLIENT_CODE": client_code,
        "PG_SCHEMA_VIEW": pg_schema_view,
        "PG_SCHEMA_VERSION": pg_schema_version,
        "PG_TABLE": pg_table,
        "VERSION_ID": version,
        "MIN_WINDOW": "NULL",
        "MAX_WINDOW": "NULL",
    }

    if upper_window:
        pg_timedelta_lower_window = settings.pg_timedelta_lower_window
        pg_upper_window, pg_lower_window = etl.get_min_max_window(
            upper_window, pg_timedelta_lower_window
        )
        variable_dict["MIN_WINDOW"] = "'{}'".format(pg_lower_window)
        variable_dict["MAX_WINDOW"] = "'{}'".format(pg_upper_window)

    if fleet_customer_id:
        variable_dict["FLEET_CUSTOMER_ID"] = fleet_customer_id

    pg_ddl_list = []
    for pg_ddl_view_definition in table.pg_ddl_list:
        pg_ddl_list.append(pg_ddl_view_definition.format(**variable_dict))

    db_config = {
        "username": username,
        "password": password,
        "hostname": pg_host,
        "database": pg_db,
        "use_cert": use_cert,
        "ssl_root_cert": ssl_root_cert,
        "ssl_cert": ssl_cert,
        "ssl_key": ssl_key,
    }
    etl.pg_execute_ddl_list(db_config, pg_ddl_list)


@cli.command()
@click.option("--username", type=str, envvar="PG_USER", help="Postgres username")
@click.option("--password", type=str, envvar="PG_PASSWORD", help="Postgres password")
@click.option("--pg-host", type=str, envvar="DB_SERVER", help="Postgres hostname")
@click.option(
    "--table-name",
    type=str,
    envvar="TABLE_NAME",
    help="Table name to process",
)
@click.option("--env", type=str, envvar="ENV", help="Environment")
@click.option(
    "--client-code",
    type=str,
    envvar="CLIENT_CODE",
    help="Client Code",
)
@click.option(
    "--fleet-customer-id",
    type=str,
    default=None,
    envvar="FLEET_CUSTOMER_ID",
    help="Fleet customer id",
)
@click.option("--version", type=str, envvar="VERSION", help="Version ID")
@click.option("--metrics", type=str, envvar="METRICS", help="QA metrics from source")
@click.option(
    "--use-cert",
    type=bool,
    envvar="USE_PG_CERT",
    required=True,
    help="Flag to use or not pg ssl",
)
@click.option(
    "--ssl-root-cert",
    type=str,
    envvar="SSL_ROOT_CERT",
    help="contains SSL certificate authority (CA) certificate(s)",
)
@click.option(
    "--ssl-cert",
    type=str,
    envvar="SSL_CERT",
    help="the client SSL certificate",
)
@click.option(
    "--ssl-key",
    type=str,
    envvar="SSL_KEY",
    help="the secret key used for the client certificate",
)
@click.option(
    "--pg-db", type=str, envvar="PG_DB", help="DB name", required=False, default=""
)
def pg_qa(
    username,
    password,
    pg_host,
    table_name,
    env,
    client_code,
    fleet_customer_id,
    version,
    metrics,
    use_cert,
    ssl_root_cert,
    ssl_cert,
    ssl_key,
    pg_db,
):
    """QA checks for views/tables."""
    table = settings.table_dict[table_name]
    pg_table = table.pg_table
    pg_schema_version = settings.pg_schema_version.format(CLIENT_CODE=client_code)
    if client_code != "multi":
        pg_qa_queries = table.pg_qa_queries
    else:
        pg_qa_queries = table.pg_qa_queries_multi

    db_config = {
        "username": username,
        "password": password,
        "hostname": pg_host,
        "database": pg_db,
        "use_cert": use_cert,
        "ssl_root_cert": ssl_root_cert,
        "ssl_cert": ssl_cert,
        "ssl_key": ssl_key,
    }
    qa_check.fm_quality_check(
        db_config,
        pg_table,
        pg_schema_version,
        pg_qa_queries,
        version,
        fleet_customer_id,
        metrics,
    )


@cli.command()
@click.option("--project-id", type=str, envvar="PROJECT_ID", help="Google project ID")
@click.option(
    "--table-id-list",
    type=str,
    envvar="TABLE_ID_LIST",
    help="List of BigQuery table ID with dataset ID",
)
def bq_check_if_table_exists(project_id, table_id_list):
    """Check if a table exists"""
    table_id_list = ast.literal_eval(table_id_list)
    qa_check.quality_check_table_exists(
        project_id,
        table_id_list,
    )


@cli.command()
@click.option("--project-id", type=str, envvar="PROJECT_ID", help="Google project ID")
@click.option(
    "--dataset-id", type=str, default=settings.dataset_id, help="BigQuery dataset ID"
)
@click.option(
    "--dataset-view-id",
    type=str,
    default=settings.dataset_id_view,
    help="BigQuery dataset ID",
)
@click.option(
    "--table-name",
    type=str,
    default="equipment_values_history",
    help="Table name to process",
)
@click.option(
    "--version", type=str, envvar="VERSION", default="1234", help="Version ID"
)
@click.option(
    "--client-code", type=str, envvar="CLIENT_CODE", default="xyz", help="Client Code"
)
@click.option("--metrics", type=str, envvar="METRICS", help="QA metrics from source")
@click.option("--env", type=str, envvar="ENV", default="dev", help="Environment")
@click.option(
    "--client-version",
    type=str,
    envvar="CLIENT_VERSION",
    default="1",
    help="Client Version",
)
def qa_table(
    project_id,
    dataset_id,
    dataset_view_id,
    table_name,
    version,
    client_code,
    metrics,
    env,
    client_version,
):
    """Run data quality checks on BigQuery tables"""

    dataset_id = dataset_id.format(settings.dataset_id_envs[env])
    dataset_id_stage = settings.dataset_id_stage.format(
        settings.dataset_id_stage_envs[env]
    )
    dataset_id_view = dataset_view_id.format(settings.dataset_id_view_envs[env])

    table_id = f"{table_name}_{version}"

    table = settings.table_dict[table_name]

    bq_queries = table.bq_qa_queries

    qa_check.quality_check_table(
        project_id,
        dataset_id_stage,
        table_id,
        bq_queries,
        dataset_id_view,
        table_name,
        dataset_id,
    )


@cli.command()
@click.option("--project-id", type=str, envvar="PROJECT_ID", help="Google project ID")
@click.option(
    "--dataset-id", type=str, default=settings.dataset_id, help="BigQuery dataset ID"
)
@click.option(
    "--table-name",
    type=str,
    default="equipment_values_history",
    help="Table name to process",
)
@click.option(
    "--version", type=str, envvar="VERSION", default="1234", help="Version ID"
)
@click.option(
    "--client-code", type=str, envvar="CLIENT_CODE", default="xyz", help="Client Code"
)
@click.option("--metrics", type=str, envvar="METRICS", help="SQL Server metrics")
@click.option("--env", type=str, envvar="ENV", default="dev", help="Environment")
@click.option(
    "--client-version",
    type=str,
    envvar="CLIENT_VERSION",
    default="1",
    help="Client Version",
)
def qa(
    project_id,
    dataset_id,
    table_name,
    version,
    client_code,
    metrics,
    env,
    client_version,
):
    """Run data quality checks on comparing from MSSQL and
    output from BigQuery
    """
    table = settings.table_dict[table_name]

    dataset_id = dataset_id.format(settings.dataset_id_envs[env])
    dataset_view_id = settings.dataset_id_view.format(
        settings.dataset_id_view_envs[env]
    )
    table_id = f"{table_name}_{version}"
    bq_queries = table.bq_qa_queries
    if hasattr(table, "mssql_bq_qa_queries"):
        bq_queries = table.mssql_bq_qa_queries
    qa_columns = table.qa_columns
    float_columns = []
    if hasattr(table, "float_columns"):
        float_columns = table.float_columns

    qa_check.quality_check(
        project_id,
        dataset_id,
        table_id,
        bq_queries,
        metrics,
        qa_columns,
        dataset_view_id,
        table_name,
        float_columns,
    )


@cli.command()
@click.option("--project-id", type=str, envvar="PROJECT_ID", help="Google project ID")
@click.option(
    "--table-name",
    type=str,
    envvar="TABLE_NAME",
    help="Table name to process",
)
@click.option("--username", type=str, envvar="PG_USER", help="Postgres username")
@click.option("--password", type=str, envvar="PG_PASSWORD", help="Postgres password")
@click.option("--pg-host", type=str, envvar="DB_SERVER", help="Postgres hostname")
@click.option(
    "--version", type=str, envvar="VERSION", default="1234", help="Version ID"
)
@click.option(
    "--client-code", type=str, envvar="CLIENT_CODE", default="xyz", help="Client Code"
)
@click.option("--env", type=str, envvar="ENV", default="dev", help="Environment")
@click.option(
    "--client-version",
    type=str,
    envvar="CLIENT_VERSION",
    default="1",
    help="Client Version",
)
@click.option(
    "--fm-table-exists",
    type=bool,
    default=False,
    envvar="FM_TABLE_EXISTS",
    help="Check if table dependencies exists",
)
@click.option(
    "--use-cert",
    type=bool,
    envvar="USE_PG_CERT",
    required=True,
    help="Flag to use or not pg ssl",
)
@click.option(
    "--ssl-root-cert",
    type=str,
    envvar="SSL_ROOT_CERT",
    help="contains SSL certificate authority (CA) certificate(s)",
)
@click.option(
    "--ssl-cert",
    type=str,
    envvar="SSL_CERT",
    help="the client SSL certificate",
)
@click.option(
    "--ssl-key",
    type=str,
    envvar="SSL_KEY",
    help="the secret key used for the client certificate",
)
@click.option(
    "--pg-db", type=str, envvar="PG_DB", help="DB name", required=False, default=""
)
def qa_partitions(
    project_id,
    table_name,
    username,
    password,
    pg_host,
    version,
    client_code,
    env,
    client_version,
    fm_table_exists,
    use_cert,
    ssl_root_cert,
    ssl_cert,
    ssl_key,
    pg_db,
):
    """Compare if partitions are the same between PostgreSQL and BigQuery"""
    if "_fm" in table_name and not (fm_table_exists):
        logging.info("Skipping task, the fm tables doesn't exists")
        return None
    table = settings.table_dict[table_name]

    dataset_view_id = settings.dataset_id_view.format(
        settings.dataset_id_view_envs[env]
    )
    bq_query_partition = table.bq_query_get_partitions.format(
        PROJECT_ID=project_id, DATASET_ID=dataset_view_id, TABLE_ID=table_name
    )

    if client_code != "multi":
        pg_query_partition = table.pg_query_get_partitions.format(
            CLIENT_CODE=client_code, TABLE_ID=table_name
        )
    else:
        pg_query_partition = table.pg_query_get_partitions_multi.format(
            CLIENT_CODE=client_code, TABLE_ID=table_name, VERSION=version
        )

    db_config = {
        "username": username,
        "password": password,
        "hostname": pg_host,
        "database": pg_db,
        "use_cert": use_cert,
        "ssl_root_cert": ssl_root_cert,
        "ssl_cert": ssl_cert,
        "ssl_key": ssl_key,
    }
    qa_check.quality_check_partitions(
        project_id,
        bq_query_partition,
        pg_query_partition,
        db_config,
        dataset_view_id,
        table_name,
    )


@cli.command()
@click.option("--project-id", type=str, envvar="PROJECT_ID", help="Google project ID")
@click.option(
    "--table-name",
    type=str,
    default="equipment_values_history",
    help="Table name to process",
)
@click.option(
    "--dataset-id", type=str, default=settings.dataset_id, help="BigQuery dataset ID"
)
@click.option(
    "--dataset-view-id",
    type=str,
    default=settings.dataset_id_view,
    help="BigQuery dataset ID",
)
@click.option(
    "--client-code", type=str, envvar="CLIENT_CODE", default="xyz", help="Client Code"
)
@click.option(
    "--client-id", type=str, envvar="CLIENT_ID", default="15", help="Client Id"
)
@click.option("--env", type=str, envvar="ENV", default="dev", help="Environment")
@click.option(
    "--version", type=str, envvar="VERSION", default="1234", help="Version ID"
)
@click.option(
    "--client-version",
    type=str,
    envvar="CLIENT_VERSION",
    default="1",
    help="Client Version",
)
@click.option(
    "--sales-code",
    type=str,
    envvar="SALES_CODE",
    default="xyzs",
    help="Sales Client Code",
)
def bqquery2bq(
    project_id,
    dataset_id,
    dataset_view_id,
    table_name,
    client_code,
    client_id,
    env,
    version,
    client_version,
    sales_code,
):
    """Runs BigQuery query and persists the result in a table"""

    dataset_id = dataset_id.format(settings.dataset_id_envs[env])

    dataset_view_id = dataset_view_id.format(settings.dataset_id_view_envs[env])
    dataset_id_stage = settings.dataset_id_stage.format(
        settings.dataset_id_stage_envs[env]
    )
    table_id = f"{table_name}_{version}"

    table = settings.table_dict[table_name]
    sets = table.sets

    table_adj_factors = f"{settings.table_id_adj_factors}_{version}"
    table_field_version = f"{settings.table_id_field_version}_{version}"
    table_no_version = f"{settings.table_id_history_no_version}_{version}"
    table_initial_valuations = f"{settings.table_id_initial_valuations}" f"_{version}"
    table_history_details = f"{settings.table_id_history_details}_{version}"
    query_view = table.select_all
    bq_query_delete_partition = table.bq_query_delete_partition

    etl.bqquery2bq(
        project_id,
        dataset_id,
        dataset_view_id,
        dataset_id_stage,
        table_id,
        sets,
        table_adj_factors,
        table_no_version,
        table_initial_valuations,
        table_field_version,
        table_history_details,
        table_name,
        query_view,
        version,
        bq_query_delete_partition,
        client_id,
        sales_code,
    )


@cli.command()
@click.option("--project-id", type=str, envvar="PROJECT_ID", help="Google project ID")
@click.option(
    "--dataset-id", type=str, default=settings.dataset_id, help="BigQuery dataset ID"
)
@click.option(
    "--dataset-view-id",
    type=str,
    default=settings.dataset_id_view,
    help="BigQuery dataset ID",
)
@click.option("--username", type=str, envvar="PG_USER", help="Postgres username")
@click.option("--password", type=str, envvar="PG_PASSWORD", help="Postgres password")
@click.option("--pg-host", type=str, envvar="DB_SERVER", help="Postgres hostname")
@click.option(
    "--table-name",
    type=str,
    envvar="TABLE_NAME",
    help="Table name to process",
)
@click.option("--env", type=str, envvar="ENV", default="dev", help="Environment")
@click.option(
    "--version", type=str, envvar="VERSION", default="1234", help="Version ID"
)
@click.option(
    "--client-code", type=str, envvar="CLIENT_CODE", default="xyz", help="Client Code"
)
@click.option(
    "--client-id", type=str, envvar="CLIENT_ID", default="15", help="Client ID"
)
@click.option("--year", type=str, envvar="YEAR", default="", help="Client Code")
@click.option(
    "--years-found",
    type=str,
    envvar="YEARS_FOUND",
    default="",
    help="Years found with modifications",
)
@click.option(
    "--client-version",
    type=str,
    envvar="CLIENT_VERSION",
    default="1",
    help="Client Version",
)
@click.option(
    "--retention-period",
    type=str,
    envvar="RETENTION_PERIOD",
    help="PostgreSQL table retention period",
)
@click.option(
    "--use-cert",
    type=bool,
    envvar="USE_PG_CERT",
    required=True,
    help="Flag to use or not pg ssl",
)
@click.option(
    "--ssl-root-cert",
    type=str,
    envvar="SSL_ROOT_CERT",
    help="contains SSL certificate authority (CA) certificate(s)",
)
@click.option(
    "--ssl-cert",
    type=str,
    envvar="SSL_CERT",
    help="the client SSL certificate",
)
@click.option(
    "--ssl-key",
    type=str,
    envvar="SSL_KEY",
    help="the secret key used for the client certificate",
)
@click.option(
    "--sales-code",
    type=str,
    envvar="SALES_CODE",
    default="xyzs",
    help="Sales Client Code",
)
@click.option(
    "--pg-db", type=str, envvar="PG_DB", help="DB name", required=False, default=""
)
def bq2pg(
    project_id,
    dataset_id,
    dataset_view_id,
    username,
    password,
    pg_host,
    table_name,
    env,
    version,
    client_code,
    client_id,
    year,
    years_found,
    client_version,
    retention_period,
    use_cert,
    ssl_root_cert,
    ssl_cert,
    ssl_key,
    sales_code,
    pg_db,
):
    """Export from BigQuery to Postgres with intermediate steps on
    Google Cloud Storage
    """

    dataset_id = dataset_id.format(settings.dataset_id_envs[env])
    dataset_view_id = dataset_view_id.format(settings.dataset_id_view_envs[env])
    dataset_id_stage = settings.dataset_id_stage.format(
        settings.dataset_id_stage_envs[env]
    )

    table = settings.table_dict[table_name]

    pg_table = table.pg_table
    pg_table_id = f"{pg_table}_{version}"
    pg_schema_version = settings.pg_schema_version.format(CLIENT_CODE=client_code)
    pg_schema_view = settings.pg_schema_view.format(CLIENT_CODE=client_code)
    year_start = year
    if year == "2018":
        year_start = "2000"
    years_range = range(int(year_start), int(year) + 1)
    variable_dict = {
        "CLIENT_ID": client_id,
        "SALES_CODE": sales_code,
        "CLIENT_CODE": client_code,
        "PG_SCHEMA_VERSION": pg_schema_version,
        "PG_SCHEMA_VIEW": pg_schema_view,
        "PG_TABLE": pg_table,
        "VERSION_ID": version,
        "PROJECT_ID": project_id,
        "DATASET_VIEW_ID": dataset_view_id,
        "DATASET_VERSION_ID": dataset_id,
        "TABLE_ID": table_name,
        "DATASET_STAGE_ID": dataset_id_stage,
        "YEAR": year,
        "YEAR_START": year_start,
        "YEAR_END": year,
        "YEARS": ",".join([f"{year}" for year in years_range]),
        "YEAR_END_PLUS_ONE": int(year) + 1,
        "RETENTION_PERIOD": retention_period,
        "GCS_PATH_ENV": table.output_filename_envs[env],
        "CLIENT_VERSION": client_version,
        "TABLE_NAME": table_name,
    }
    pg_query_create = table.pg_query_create.format(**variable_dict)
    pg_query_drop = table.pg_query_drop
    pg_query_truncate = table.pg_query_truncate.format(**variable_dict)
    pg_query_tables_catalog = table.pg_query_tables_catalog.format(**variable_dict)
    pg_query_create_view = table.pg_query_create_view.format(**variable_dict)
    pg_table_partition = f"{pg_table_id}_{year}"
    pg_query_create_partition = table.pg_query_create_partition.format(**variable_dict)

    bq_query_export = table.bq_query_export.format(**variable_dict)

    bq_query_tables_schema = table.bq_query_tables_schema.format(**variable_dict)

    query_export = table.bq_query_export.format(**variable_dict)
    bq_export_columns = table.bq_export_columns
    pg_query_find_latest_version = table.pg_query_find_latest_version.format(
        **variable_dict
    )
    pg_insert_data = table.pg_insert_data
    if client_code != "multi":
        avro_path = table.avro_export_file_name.format(**variable_dict)
    else:
        avro_path = table.avro_export_file_name_multi.format(**variable_dict)

    bq_query_partition = table.bq_query_get_partitions.format(
        PROJECT_ID=project_id, DATASET_ID=dataset_view_id, TABLE_ID=table_name
    )

    db_config = {
        "username": username,
        "password": password,
        "hostname": pg_host,
        "database": pg_db,
        "use_cert": use_cert,
        "ssl_root_cert": ssl_root_cert,
        "ssl_cert": ssl_cert,
        "ssl_key": ssl_key,
    }
    etl.bq2pg(
        project_id,
        dataset_id_stage,
        pg_table_id,
        db_config,
        pg_schema_version,
        pg_table_id,
        bq_query_export,
        pg_query_create,
        pg_query_drop,
        pg_query_truncate,
        bq_query_tables_schema,
        pg_query_tables_catalog,
        pg_query_create_view,
        pg_schema_view,
        pg_table,
        query_export,
        bq_export_columns,
        pg_table_partition,
        pg_query_create_partition,
        year,
        years_found,
        pg_query_find_latest_version,
        pg_insert_data,
        variable_dict,
        avro_path,
        bq_query_partition,
    )


@cli.command()
@click.option("--project-id", type=str, envvar="PROJECT_ID", help="Google project ID")
@click.option(
    "--dataset-id", type=str, default=settings.dataset_id, help="BigQuery dataset ID"
)
@click.option(
    "--dataset-view-id",
    type=str,
    default=settings.dataset_id_view,
    help="BigQuery dataset ID",
)
@click.option("--username", type=str, envvar="PG_USER", help="Postgres username")
@click.option("--password", type=str, envvar="PG_PASSWORD", help="Postgres password")
@click.option("--pg-host", type=str, envvar="DB_SERVER", help="Postgres hostname")
@click.option(
    "--table-name",
    type=str,
    envvar="TABLE_NAME",
    help="Table name to process",
)
@click.option("--env", type=str, envvar="ENV", default="dev", help="Environment")
@click.option(
    "--version", type=str, envvar="VERSION", default="1234", help="Version ID"
)
@click.option(
    "--client-code", type=str, envvar="CLIENT_CODE", default="xyz", help="Client Code"
)
@click.option(
    "--client-id", type=str, envvar="CLIENT_ID", default="15", help="Client ID"
)
@click.option(
    "--client-version",
    type=str,
    envvar="CLIENT_VERSION",
    default="1",
    help="Client Version",
)
@click.option(
    "--use-cert",
    type=bool,
    envvar="USE_PG_CERT",
    required=True,
    help="Flag to use or not pg ssl",
)
@click.option(
    "--ssl-root-cert",
    type=str,
    envvar="SSL_ROOT_CERT",
    help="contains SSL certificate authority (CA) certificate(s)",
)
@click.option(
    "--ssl-cert",
    type=str,
    envvar="SSL_CERT",
    help="the client SSL certificate",
)
@click.option(
    "--ssl-key",
    type=str,
    envvar="SSL_KEY",
    help="the secret key used for the client certificate",
)
@click.option(
    "--pg-db", type=str, envvar="PG_DB", help="DB name", required=False, default=""
)
def pg_env(
    project_id,
    dataset_id,
    dataset_view_id,
    username,
    password,
    pg_host,
    table_name,
    env,
    version,
    client_code,
    client_id,
    client_version,
    use_cert,
    ssl_root_cert,
    ssl_cert,
    ssl_key,
    pg_db,
):
    """Export from BigQuery to Postgres"""

    dataset_id = dataset_id.format(settings.dataset_id_envs[env])
    dataset_view_id = dataset_view_id.format(settings.dataset_id_view_envs[env])
    dataset_id_stage = settings.dataset_id_stage.format(
        settings.dataset_id_stage_envs[env]
    )

    table = settings.table_dict[table_name]

    pg_table = table.pg_table
    pg_table_id = f"{pg_table}_{version}"

    pg_schema_version = settings.pg_schema_version.format(CLIENT_CODE=client_code)
    pg_schema_view = settings.pg_schema_view.format(CLIENT_CODE=client_code)
    variable_dict = {
        "CLIENT_ID": client_id,
        "CLIENT_CODE": client_code,
        "PG_SCHEMA_VERSION": pg_schema_version,
        "PG_SCHEMA_VIEW": pg_schema_view,
        "PG_TABLE": pg_table,
        "VERSION_ID": version,
        "PROJECT_ID": project_id,
        "DATASET_VIEW_ID": dataset_view_id,
        "DATASET_VERSION_ID": dataset_id,
        "TABLE_ID": table_name,
        "DATASET_STAGE_ID": dataset_id_stage,
    }
    pg_query_create = table.pg_query_create.format(**variable_dict)

    db_config = {
        "username": username,
        "password": password,
        "hostname": pg_host,
        "database": pg_db,
        "use_cert": use_cert,
        "ssl_root_cert": ssl_root_cert,
        "ssl_cert": ssl_cert,
        "ssl_key": ssl_key,
    }
    etl.pg_env(
        project_id,
        dataset_id_stage,
        pg_table_id,
        db_config,
        pg_schema_version,
        pg_table_id,
        pg_query_create,
        pg_table,
        pg_schema_view,
    )


@cli.command()
@click.option("--project-id", type=str, envvar="PROJECT_ID", help="Google project ID")
@click.option(
    "--dataset-id", type=str, default=settings.dataset_id, help="BigQuery dataset ID"
)
@click.option(
    "--tmpdir",
    type=str,
    default="/tmp",
    envvar="TMPDIR",
    help="Export file to temporal path",
)
@click.option(
    "--domain",
    type=str,
    envvar="WIN_USER_DOMAIN",
    help="Windows domain. Must be able to resolve into an IP",
)
@click.option("--username", type=str, envvar="WIN_USER", help="Windows username")
@click.option("--password", type=str, envvar="WIN_PASSWORD", help="Windows password")
@click.option("--sql-host", type=str, envvar="SQL_HOST", help="SQL Server hostname")
@click.option(
    "--table-name",
    type=str,
    default="equipment_values_history",
    help="Table name to process",
)
@click.option("--env", type=str, envvar="ENV", default="dev", help="Environment")
@click.option(
    "--version", type=str, envvar="VERSION", default="1234", help="Version ID"
)
@click.option(
    "--client-id", type=str, envvar="CLIENT_ID", default="15", help="Sales client ID"
)
@click.option(
    "--client-code", type=str, envvar="CLIENT_CODE", default="xyz", help="Client Code"
)
@click.option("--year", type=str, envvar="YEAR", default="", help="Year to extract")
@click.option(
    "--years-found",
    type=str,
    envvar="YEARS_FOUND",
    default="",
    help="Years found with modifications",
)
@click.option(
    "--client-version",
    type=str,
    envvar="CLIENT_VERSION",
    default="1",
    help="Client Version",
)
@click.option(
    "--sales-code",
    type=str,
    envvar="SALES_CODE",
    default="xyzs",
    help="Sales Client Code",
)
def mssql2gcs(
    project_id,
    dataset_id,
    tmpdir,
    domain,
    username,
    password,
    sql_host,
    table_name,
    env,
    version,
    client_id,
    client_code,
    year,
    years_found,
    client_version,
    sales_code,
):
    """Export from MSSQL to BigQuery with intermediate steps on
    Google Cloud Storage
    """

    dataset_id = dataset_id.format(settings.dataset_id_envs[env])
    dataset_view_id = settings.dataset_id_view.format(
        settings.dataset_id_view_envs[env]
    )
    table = settings.table_dict[table_name]

    table_id = f"{table_name}_{version}"

    output_path_mssql = table.output_filename_mssql_gcs.format(
        table.output_filename_envs[env],
        VERSION_ID=version,
        TABLE_NAME=table_name,
        CLIENT_CODE=client_code,
        CLIENT_VERSION=client_version,
    )
    if hasattr(table, "sql_host"):
        sql_host = table.sql_host.format(table.sql_host_envs[env])

    sql_db = table.sql_db.format(CLIENT_ID=client_id, SALES_CODE=sales_code)
    av_schema = table.av_schema

    query_year_filter = ""
    if hasattr(table, "query_year_filter"):
        query_year_filter = table.query_year_filter

    year_operator = f"={year}"
    if year != "":
        if int(year) <= 2018:
            year_operator = f"<={year}"

    query = table.query.format(
        VERSION_ID=version,
        CLIENT_ID=client_id,
        SALES_CODE=sales_code,
        YEAR_FILTER=f"AND YEAR(ValueTime){year_operator}",
    )

    qa_columns = table.qa_columns
    ss_query_count = None
    if hasattr(table, "ss_query_count"):
        ss_query_count = table.ss_query_count.format(
            VERSION_ID=version,
            CLIENT_ID=client_id,
            SALES_CODE=sales_code,
            YEAR_FILTER=f"AND YEAR(ValueTime){year_operator}",
        )

    etl.mssql2gcs(
        project_id,
        dataset_id,
        table_id,
        output_path_mssql,
        tmpdir,
        domain,
        username,
        password,
        sql_host,
        sql_db,
        query,
        av_schema,
        qa_columns,
        year,
        years_found,
        query_year_filter,
        dataset_view_id,
        "equipment_values_history",
        table_name,
        ss_query_count,
    )


@cli.command()
@click.option("--project-id", type=str, envvar="PROJECT_ID", help="Google project ID")
@click.option(
    "--dataset-id", type=str, default=settings.dataset_id, help="BigQuery dataset ID"
)
@click.option(
    "--table-name",
    type=str,
    default="equipment_values_history",
    help="Table name to process",
)
@click.option("--env", type=str, envvar="ENV", default="dev", help="Environment")
@click.option(
    "--version", type=str, envvar="VERSION", default="1234", help="Version ID"
)
@click.option(
    "--client-id", type=str, envvar="CLIENT_ID", default="15", help="Sales client ID"
)
@click.option(
    "--client-code", type=str, envvar="CLIENT_CODE", default="xyz", help="Client Code"
)
@click.option("--metrics", type=str, envvar="METRICS", help="SQL Server metrics")
@click.option(
    "--client-version",
    type=str,
    envvar="CLIENT_VERSION",
    default="1",
    help="Client Version",
)
def gcs2bq(
    project_id,
    dataset_id,
    table_name,
    env,
    version,
    client_id,
    client_code,
    metrics,
    client_version,
):
    """Export from MSSQL to BigQuery with intermediate steps on
    Google Cloud Storage
    """

    dataset_id = dataset_id.format(settings.dataset_id_envs[env])

    table = settings.table_dict[table_name]

    table_id = f"{table_name}_{version}"

    output_path_mssql = table.output_filename_mssql_gcs.format(
        table.output_filename_envs[env],
        VERSION_ID=version,
        TABLE_NAME=table_name,
        CLIENT_CODE=client_code,
        CLIENT_VERSION=client_version,
    )

    qa_columns = table.qa_columns

    etl.gcs2bq(project_id, dataset_id, table_id, output_path_mssql, qa_columns, metrics)


@cli.command()
@click.option("--project-id", type=str, envvar="PROJECT_ID", help="Google project ID")
@click.option(
    "--dataset-id", type=str, default=settings.dataset_id, help="BigQuery dataset ID"
)
@click.option("--username", type=str, envvar="WIN_USER", help="Windows username")
@click.option("--password", type=str, envvar="WIN_PASSWORD", help="Windows password")
@click.option("--sql-host", type=str, envvar="SQL_HOST", help="SQL Server hostname")
@click.option(
    "--table-name",
    type=str,
    default="equipment_values_history",
    help="Table name to process",
)
@click.option("--env", type=str, envvar="ENV", default="dev", help="Environment")
@click.option(
    "--version", type=str, envvar="VERSION", default="1234", help="Version ID"
)
@click.option(
    "--client-id", type=str, envvar="CLIENT_ID", default="15", help="Sales client ID"
)
@click.option(
    "--client-code", type=str, envvar="CLIENT_CODE", default="xyz", help="Client Code"
)
@click.option(
    "--client-version",
    type=str,
    envvar="CLIENT_VERSION",
    default="1",
    help="Client Version",
)
def get_modified_years(
    project_id,
    dataset_id,
    username,
    password,
    sql_host,
    table_name,
    env,
    version,
    client_id,
    client_code,
    client_version,
):
    """Compares query results from MSSQL and BigQuery"""

    dataset_id = dataset_id.format(settings.dataset_id_envs[env])
    dataset_id_stage = settings.dataset_id_stage.format(
        settings.dataset_id_stage_envs[env]
    )
    dataset_view_id = settings.dataset_id_view.format(
        settings.dataset_id_view_envs[env]
    )

    table = settings.table_dict[table_name]

    sql_db = table.sql_db.format(CLIENT_ID=client_id)

    query_count_years = table.query_count_years.format(CLIENT_ID=client_id)
    view_counter_id = table.view_counter_id
    table_counter_id = f"{view_counter_id}_{version}"
    bq_query_counter_table = table.bq_query_counter_table

    etl.get_modified_years(
        project_id,
        dataset_view_id,
        dataset_id_stage,
        username,
        password,
        sql_host,
        sql_db,
        query_count_years,
        view_counter_id,
        table_counter_id,
        bq_query_counter_table,
    )


@cli.command()
@click.option("--project-id", type=str, envvar="PROJECT_ID", help="Google project ID")
@click.option(
    "--table-name",
    type=str,
    default="equipment_values_history_fm",
    help="Table name to process",
)
@click.option("--env", type=str, envvar="ENV", default="dev", help="Environment")
@click.option(
    "--client-code", type=str, envvar="CLIENT_CODE", default="xyz", help="Client Code"
)
@click.option(
    "--client-version",
    type=str,
    envvar="CLIENT_VERSION",
    default="1",
    help="Client Version",
)
def get_modified_years_fm(
    project_id,
    table_name,
    env,
    client_code,
    client_version,
):
    """Compares query results from MSSQL and BigQuery"""

    dataset_view_id = settings.dataset_id_view.format(
        settings.dataset_id_view_envs[env]
    )

    table = settings.table_dict[table_name]

    bq_query_counter_table = table.bq_query_counter_table

    etl.get_modified_years_fm(
        project_id,
        dataset_view_id,
        table_name,
        bq_query_counter_table,
    )


@cli.command()
@click.option("--project-id", type=str, envvar="PROJECT_ID", help="Google project ID")
@click.option(
    "--dataset-id", type=str, default=settings.dataset_id, help="BigQuery dataset ID"
)
@click.option(
    "--dataset-view-id",
    type=str,
    default=settings.dataset_id_view,
    help="BigQuery dataset ID",
)
@click.option("--username", type=str, envvar="PG_USER", help="Postgres username")
@click.option("--password", type=str, envvar="PG_PASSWORD", help="Postgres password")
@click.option("--pg-host", type=str, envvar="DB_SERVER", help="Postgres hostname")
@click.option("--env", type=str, envvar="ENV", default="dev", help="Environment")
@click.option(
    "--version", type=str, envvar="VERSION", default="1234", help="Version ID"
)
@click.option(
    "--client_code",
    type=str,
    envvar="CLIENT_CODE",
    default="",
    help="Rouse client code",
)
@click.option(
    "--client-version",
    type=str,
    envvar="CLIENT_VERSION",
    default="1",
    help="Client Version",
)
@click.option(
    "--retention-period",
    type=str,
    envvar="RETENTION_PERIOD",
    help="PostgreSQL table retention period",
)
@click.option(
    "--fleet-customer-id",
    type=str,
    default=None,
    envvar="FLEET_CUSTOMER_ID",
    help="Fleet customer id",
)
@click.option(
    "--fm-table-exists",
    type=bool,
    default=False,
    envvar="FM_TABLE_EXISTS",
    help="Check if table dependencies exists",
)
@click.option(
    "--use-cert",
    type=bool,
    envvar="USE_PG_CERT",
    required=True,
    help="Flag to use or not pg ssl",
)
@click.option(
    "--ssl-root-cert",
    type=str,
    envvar="SSL_ROOT_CERT",
    help="contains SSL certificate authority (CA) certificate(s)",
)
@click.option(
    "--ssl-cert",
    type=str,
    envvar="SSL_CERT",
    help="the client SSL certificate",
)
@click.option(
    "--ssl-key",
    type=str,
    envvar="SSL_KEY",
    help="the secret key used for the client certificate",
)
@click.option(
    "--pg-db", type=str, envvar="PG_DB", help="DB name", required=False, default=""
)
def pg_env_post(
    project_id,
    dataset_id,
    dataset_view_id,
    username,
    password,
    pg_host,
    env,
    version,
    client_code,
    client_version,
    retention_period,
    fleet_customer_id,
    fm_table_exists,
    use_cert,
    ssl_root_cert,
    ssl_cert,
    ssl_key,
    pg_db,
):
    if not (fm_table_exists):
        fleet_customer_id = None

    """
    Drops older tables/views and updates views in Cloud SQL Postgres for legacy and IMS versions at the same transaction
    """

    dataset_id = dataset_id.format(settings.dataset_id_envs[env])
    dataset_view_id = dataset_view_id.format(settings.dataset_id_view_envs[env])
    dataset_id_stage = settings.dataset_id_stage.format(
        settings.dataset_id_stage_envs[env]
    )

    pg_schema_version = settings.pg_schema_version.format(CLIENT_CODE=client_code)
    pg_schema_view = settings.pg_schema_view.format(CLIENT_CODE=client_code)

    variable_dict = {
        "PG_SCHEMA_VERSION": pg_schema_version,
        "PG_SCHEMA_VIEW": pg_schema_view,
        "VERSION_ID": version,
        "PROJECT_ID": project_id,
        "DATASET_VIEW_ID": dataset_view_id,
        "DATASET_VERSION_ID": dataset_id,
        "DATASET_STAGE_ID": dataset_id_stage,
        "CLIENT_CODE": client_code,
        "RETENTION_PERIOD": retention_period,
    }

    # Legacy Table version
    table_name = "equipment_values_history"
    table = settings.table_dict[table_name]

    pg_table = table.pg_table
    pg_table_id = f"{pg_table}_{version}"

    variable_dict["TABLE_ID"] = table_name
    variable_dict["PG_TABLE"] = pg_table

    pg_query_drop = table.pg_query_drop
    pg_query_tables_catalog = table.pg_query_tables_catalog.format(**variable_dict)
    pg_query_create_view = table.pg_query_create_view.format(**variable_dict)
    bq_query_tables_schema = table.bq_query_tables_schema.format(**variable_dict)

    # FM Table version
    table_name_fm = "equipment_values_history_fm"
    table_fm = settings.table_dict[table_name_fm]

    pg_table_fm = table_fm.pg_table

    variable_dict["TABLE_ID"] = table_name_fm
    variable_dict["PG_TABLE"] = pg_table_fm

    if client_code != "multi":
        pg_query_create_view_fm = table_fm.pg_query_create_view.format(**variable_dict)
    else:
        pg_query_create_view_fm = table_fm._pg_query_create_view_multi_sql.format(
            **variable_dict
        )

    # Build drops for FM tables
    table_drop_config_list_fm = _build_pg_fm_drops(
        pg_schema_version,
        retention_period,
        variable_dict.copy(),
        version,
        [table_name_fm],
    )

    db_config = {
        "username": username,
        "password": password,
        "hostname": pg_host,
        "database": pg_db,
        "use_cert": use_cert,
        "ssl_root_cert": ssl_root_cert,
        "ssl_cert": ssl_cert,
        "ssl_key": ssl_key,
    }
    etl.pg_env_post(
        fleet_customer_id,
        db_config,
        pg_schema_version,
        pg_table_id,
        pg_query_create_view,
        pg_query_create_view_fm,
        project_id,
        dataset_id_stage,
        pg_table_id,
        pg_query_tables_catalog,
        bq_query_tables_schema,
        pg_query_drop,
        table_drop_config_list_fm,
        pg_table,
        pg_schema_view,
    )


@cli.command()
@click.option("--username", type=str, envvar="PG_USER", help="Postgres username")
@click.option("--password", type=str, envvar="PG_PASSWORD", help="Postgres password")
@click.option("--pg-host", type=str, envvar="DB_SERVER", help="Postgres hostname")
@click.option(
    "--table-name",
    type=str,
    default="equipment_values_history_fm",
    help="Table name to process",
)
@click.option("--env", type=str, envvar="ENV", default="dev", help="Environment")
@click.option(
    "--version", type=str, envvar="VERSION", default="1234", help="Version ID"
)
@click.option(
    "--client-code", type=str, envvar="CLIENT_CODE", default="xyz", help="Client Code"
)
@click.option(
    "--use-cert",
    type=bool,
    envvar="USE_PG_CERT",
    required=True,
    help="Flag to use or not pg ssl",
)
@click.option(
    "--ssl-root-cert",
    type=str,
    envvar="SSL_ROOT_CERT",
    help="contains SSL certificate authority (CA) certificate(s)",
)
@click.option(
    "--ssl-cert",
    type=str,
    envvar="SSL_CERT",
    help="the client SSL certificate",
)
@click.option(
    "--ssl-key",
    type=str,
    envvar="SSL_KEY",
    help="the secret key used for the client certificate",
)
@click.option(
    "--pg-db", type=str, envvar="PG_DB", help="DB name", required=False, default=""
)
def pg_count(
    username,
    password,
    pg_host,
    table_name,
    env,
    version,
    client_code,
    use_cert,
    ssl_root_cert,
    ssl_cert,
    ssl_key,
    pg_db,
):
    """Returns as XCOM value the count for provided table or zero if table does not exist"""

    table = settings.table_dict[table_name]
    pg_table = table.pg_table

    pg_schema_view = settings.pg_schema_view.format(CLIENT_CODE=client_code)
    variable_dict = {
        "CLIENT_CODE": client_code,
        "PG_SCHEMA_VIEW": pg_schema_view,
        "PG_TABLE": pg_table,
    }

    query_count = "SELECT COUNT(*) as count FROM {PG_SCHEMA_VIEW}.{PG_TABLE}".format(
        **variable_dict
    )

    db_config = {
        "username": username,
        "password": password,
        "hostname": pg_host,
        "database": pg_db,
        "use_cert": use_cert,
        "ssl_root_cert": ssl_root_cert,
        "ssl_cert": ssl_cert,
        "ssl_key": ssl_key,
    }

    etl.pg_count(
        db_config,
        query_count,
    )


def _build_pg_fm_drops(
    pg_schema_version, retention_period, variable_dict, version, fm_tables
):
    table_dict_drop_list = []
    for fm_table in fm_tables:
        table_fm = settings.table_dict[fm_table]
        pg_table_fm = table_fm.pg_table
        pg_table_id_fm = f"{pg_table_fm}_{version}"

        variable_dict["PG_TABLE"] = pg_table_fm
        variable_dict["RETENTION_PERIOD"] = retention_period
        pg_query_drop_fm = table_fm.pg_query_drop
        pg_query_tables_catalog_fm = table_fm.pg_query_tables_to_drop.format(
            **variable_dict
        )

        table_dict_drop = {
            "pg_query_tables_catalog": pg_query_tables_catalog_fm,
            "pg_schema": pg_schema_version,
            "pg_version_table": pg_table_id_fm,
            "retention_num_versions": retention_period,
            "pg_query_drop": pg_query_drop_fm,
        }
        table_dict_drop_list.append(table_dict_drop)

    return table_dict_drop_list


@cli.command()
@click.option(
    "--project-id",
    type=str,
    envvar="PROJECT_ID",
    help="Google project ID",
)
@click.option(
    "--dataset-id",
    type=str,
    default=settings.dataset_id_view,
    help="BigQuery dataset ID",
)
@click.option("--table-name-ims", type=str, default="", help="Table name to process")
@click.option("--table-name-legacy", type=str, default="", help="Table name to process")
@click.option("--env", type=str, envvar="ENV", help="Environment")
@click.option("--client-code", type=str, envvar="CLIENT_CODE", help="Client Code")
@click.option(
    "--client-version",
    type=str,
    envvar="CLIENT_VERSION",
    help="Client version",
)
def qa_ims_vs_sales(
    project_id,
    dataset_id,
    table_name_ims,
    table_name_legacy,
    env,
    client_code,
    client_version,
):
    """Run data quality checks comparing queries between MSSQL and BigQuery"""

    table = settings.table_dict[table_name_ims]

    dataset_id = dataset_id.format(settings.dataset_id_envs[env])

    bq_queries = table.bq_qa_ims_vs_sales_queries

    qa_check.qa_ims_vs_sales(
        project_id,
        dataset_id,
        table_name_ims,
        table_name_legacy,
        bq_queries,
    )


if __name__ == "__main__":
    cli()
