import operator

# GCS
output_filename_envs = {"local": "-dev", "dev": "-dev", "prod": "", "ppe": ""}

avro_export_file_name = (
    "gs://rs-client-{CLIENT_CODE}-{CLIENT_VERSION}-"
    "stage{GCS_PATH_ENV}/pipeline"
    "/equipment-values-history/"
    "{VERSION_ID}/avro/bq-to-gcs/{TABLE_NAME}/"
    "{YEAR}/"
    "{TABLE_NAME}_{CLIENT_CODE}_{VERSION_ID}.avro"
)

avro_export_file_name_multi = (
    "gs://{PROJECT_ID}-data/pipeline"
    "/equipment-values-history/"
    "{VERSION_ID}/avro/bq-to-gcs/{TABLE_NAME}/"
    "{YEAR}/"
    "{TABLE_NAME}_{CLIENT_CODE}_{VERSION_ID}.avro"
)

# PostgreSQL
pg_table = "equipment_values_history_fm"


_pg_query_create_view_sql = """
CREATE SCHEMA IF NOT EXISTS {PG_SCHEMA_VIEW};
CREATE OR REPLACE VIEW {PG_SCHEMA_VIEW}.{PG_TABLE} AS
SELECT
  *
FROM
  {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID};

ALTER SCHEMA {PG_SCHEMA_VERSION} OWNER TO root;
ALTER TABLE {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID} OWNER TO root;

ALTER SCHEMA {PG_SCHEMA_VIEW} OWNER TO root;
ALTER VIEW {PG_SCHEMA_VIEW}.{PG_TABLE} OWNER TO root;

--indexes and primary key

CREATE INDEX IF NOT EXISTS {PG_TABLE}_{VERSION_ID}_eq_nb_index
    ON {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID} (equipment_number);

--grant permissions
--versioned table
GRANT SELECT ON {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID} TO {PG_TABLE}_r;

--view
GRANT SELECT ON {PG_SCHEMA_VIEW}.{PG_TABLE} TO {PG_TABLE}_r;

"""

pg_query_create_view = f"""
DO
$$
BEGIN
  {_pg_query_create_view_sql}
EXCEPTION
  WHEN invalid_table_definition THEN
    RAISE WARNING 'Invalid table definition, view {{PG_SCHEMA_VIEW}}.{{PG_TABLE}} will be dropped';
    DROP VIEW IF EXISTS {{PG_SCHEMA_VIEW}}.{{PG_TABLE}};
    {_pg_query_create_view_sql}
END
$$
"""

_pg_query_create_view_multi_sql = """
ALTER SCHEMA {PG_SCHEMA_VERSION} OWNER TO root;
ALTER TABLE {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID} OWNER TO root;

--indexes and primary key

CREATE INDEX IF NOT EXISTS {PG_TABLE}_{VERSION_ID}_eq_nb_index
    ON {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID} (equipment_number);

--grant permissions
--versioned table
GRANT SELECT ON {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID} TO {PG_TABLE}_r;

"""

pg_query_drop = "DROP TABLE {TABLES}"

pg_query_truncate = """
TRUNCATE TABLE {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID}_{YEAR}
"""

pg_query_tables_catalog = """
SELECT
  tablename
FROM
  pg_catalog.pg_tables
WHERE
  schemaname != 'pg_catalog'
  AND schemaname != 'information_schema'
  AND tablename LIKE '{PG_TABLE}_2%'
  AND schemaname = '{PG_SCHEMA_VERSION}'
"""

pg_query_catalog = """
SELECT
  tablename
FROM
  pg_catalog.pg_tables
WHERE
  schemaname != 'pg_catalog'
  AND schemaname != 'information_schema'
  AND tablename LIKE '{PG_TABLE}_2%%'
  AND schemaname = '{PG_SCHEMA_VERSION}'
"""

pg_query_tables_to_drop = """
    SELECT
    tablename
  FROM (
    SELECT
      tablename,
      to_TIMESTAMP(reverse(split_part(reverse(tablename), '_', 1)), 'YYYYMMDDHH24MISS') AS execution_time,
      reverse(split_part(reverse(tablename), '_', 1)) split_part
    FROM
      pg_catalog.pg_tables
    WHERE
      schemaname != 'pg_catalog'
      AND schemaname != 'information_schema'
      AND tablename LIKE '{PG_TABLE}_2%%'
      AND schemaname = '{PG_SCHEMA_VERSION}' ) t
  WHERE
    execution_time < NOW() - INTERVAL '{RETENTION_PERIOD} DAY'
    AND LENGTH(split_part) >4
"""


# QA

_qa_not_null_fields = [
    "client_id",
    "client_code",
    "fleet_customer_id",
    "category",
    "category_scid",
    "equipment_id",
    "equipment_number",
    "make",
    "make_scid",
    "model",
    "model_year",
    "scid",
    "subcategory",
    "subcategory_scid",
    "fleet_customer_id",
    "category_id",
    "subcategory_id",
    "make_id",
    "model_id",
    "fleet_asset_id",
    "fleet_entitlement_date_created",
]

_pg_query_qa_check_null_count = """
SELECT
  COUNT(*) count
FROM
  {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID}
WHERE
    fleet_customer_id != {FLEET_CUSTOMER_ID}
    OR
""" + " OR \n".join(
    [f + " IS NULL" for f in _qa_not_null_fields]
)

_pg_query_qa_check_null_multi_count = """
SELECT
  COUNT(*) count
FROM
  {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID}
WHERE
    
""" + " OR \n".join(
    [f + " IS NULL" for f in _qa_not_null_fields]
)

pg_qa_queries = [
    (operator.gt, _pg_query_qa_check_null_count, 0),
]

pg_qa_queries_multi = [
    (operator.gt, _pg_query_qa_check_null_multi_count, 0),
]

ims_vs_sales_comparison = """
  WITH ims AS (SELECT *
    FROM (
          SELECT m.*, row_number() over (partition by equipment_number order by valuation_date desc) as rn
          FROM `{PROJECT_ID}.{DATASET_ID}.{BQ_TABLE_IMS}` m
        ) m2
    WHERE m2.rn = 1),
  legacy AS (SELECT *
    from (
          SELECT m.*, row_number() over (partition by equipment_number order by valuation_date desc) as rn
          FROM `{PROJECT_ID}.{DATASET_ID}.{BQ_TABLE}` m
        ) m2
    WHERE m2.rn = 1)
  SELECT
    CAST((
      ROUND(SUM(COALESCE(CAST(lower(legacy.equipment_number) = lower(ims.equipment_number) AS INTEGER), 0)) / count(1), 2) +
      ROUND(SUM(COALESCE(CAST(legacy.category = ims.category AS INTEGER), 0)) / count(1), 2) +
      ROUND(SUM(COALESCE(CAST(legacy.make = ims.make AS INTEGER), 0)) / count(1), 2) +
      ROUND(SUM(COALESCE(CAST(legacy.model = ims.model AS INTEGER), 0)) / count(1), 2) +
      ROUND(SUM(COALESCE(CAST(legacy.subcategory = ims.subcategory AS INTEGER), 0)) / count(1), 2)
      --1 - SUM(COALESCE(ABS(ims.meter - legacy.meter) / NULLIF(ims.meter, 0), 0) )/ count(1) +
      --ROUND(SUM(COALESCE(CAST(legacy.meter_type = ims.meter_type AS INTEGER), 0)) / count(1), 2)
      --ROUND(SUM(COALESCE(CAST(legacy.default_currency = ims.default_currency AS INTEGER), 0)) / count(1), 2) +
      --1 - SUM(COALESCE(ABS(ims.rfm_fmv - legacy.fmv) / NULLIF(ims.rfm_fmv, 0), 0) )/ count(1) +
      --1 - SUM(COALESCE(ABS(ims.rfm_olv - legacy.olv) / NULLIF(ims.rfm_olv, 0), 0) )/ count(1) +
      --1 - SUM(COALESCE(ABS(ims.rfm_flv - legacy.flv) / NULLIF(ims.rfm_flv, 0), 0) )/ count(1) +
      --1 - SUM(COALESCE(ABS(ims.rfm_wlv - legacy.wlv) / NULLIF(ims.rfm_wlv, 0), 0) )/ count(1)
    ) / 5 AS NUMERIC) AS count
  FROM
    ims
  inner join
    legacy
  ON
    lower(legacy.equipment_number) = lower(ims.equipment_number)
    AND lower(legacy.make) != 'n/a' --Do not include assets which were not classified in legacy
"""

bq_qa_ims_vs_sales_queries = [
    (operator.lt, ims_vs_sales_comparison, 0.9),
]

# CREATE SCHEMA IF NOT EXISTS {PG_SCHEMA};
pg_query_create = """
CREATE SCHEMA IF NOT EXISTS {PG_SCHEMA_VERSION};
CREATE TABLE IF NOT EXISTS {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID}
(
    category VARCHAR,
    category_id INTEGER,
    category_scid INTEGER,
    client_id INTEGER,
    client_code VARCHAR,
    fleet_customer_id INTEGER,
    rfm_configuration_adjustment numeric,
    default_currency VARCHAR,
    equipment_id BIGINT,
    equipment_number VARCHAR,
    rfm_flv INTEGER,
    rfm_fmv INTEGER,
    make VARCHAR,
    make_id INTEGER,
    make_scid INTEGER,
    meter INTEGER,
    rfm_meter_adjustment numeric,
    meter_type VARCHAR,
    model VARCHAR,
    model_id INTEGER,
    scid INTEGER,
    rfm_olv INTEGER,
    rfm_recondition_adjustment numeric,
    rfm_region_adjustment numeric,
    subcategory VARCHAR,
    subcategory_id INTEGER,
    subcategory_scid INTEGER,
    valuation_date timestamp with time zone,
    valuation_log_id VARCHAR,
    rfm_wlv INTEGER,
    model_year INTEGER,
    valuation_fx_date DATE,
    retail_top_quartile_3_month numeric,
    retail_bottom_quartile_3_month numeric,
    auction_top_quartile_3_month numeric,
    auction_bottom_quartile_3_month numeric,
    free_flv INTEGER,
    free_mpe INTEGER,
    free_flv_top_quartile_3_month numeric,
    free_flv_bottom_quartile_3_month numeric,
    free_configuration_adjustment numeric,
    free_recondition_adjustment numeric,
    free_meter_adjustment numeric,
    free_region_adjustment numeric,
    id INTEGER,
    rfm_condition_adjustment numeric,
    meter_precise numeric,
    insights_category VARCHAR,
    insights_category_id INTEGER,
    insights_subcategory VARCHAR,
    insights_subcategory_id INTEGER
) PARTITION BY RANGE (valuation_date);
ALTER TABLE {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID} OWNER TO root;
"""

pg_query_create_partition = """
CREATE TABLE
  {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID}_{YEAR}
PARTITION OF
  {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID}
FOR VALUES FROM ('{YEAR_START}-01-01 00:00:00') TO ('{YEAR_END_PLUS_ONE}-01-01 00:00:00');
CREATE INDEX IF NOT EXISTS
  {PG_TABLE}_{VERSION_ID}_{YEAR}_id_index
ON
  {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID}_{YEAR}
(equipment_number);

ALTER TABLE {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID}_{YEAR} OWNER TO root;
"""

bq_query_export = """
SELECT
  category,
  category_id,
  category_scid,
  client_id,
  client_code,
  fleet_customer_id,
  rfm_configuration_adjustment,
  default_currency,
  equipment_id,
  equipment_number,
  rfm_flv,
  rfm_fmv,
  make,
  make_id,
  make_scid,
  meter,
  rfm_meter_adjustment,
  meter_type,
  model,
  model_id,
  scid,
  rfm_olv,
  rfm_recondition_adjustment,
  rfm_region_adjustment,
  subcategory,
  subcategory_id,
  subcategory_scid,
  valuation_date,
  valuation_log_id,
  rfm_wlv,
  model_year,
  valuation_fx_date,
  retail_top_quartile_3_month,
  retail_bottom_quartile_3_month,
  auction_top_quartile_3_month,
  auction_bottom_quartile_3_month,
  free_flv,
  free_mpe,
  free_flv_top_quartile_3_month,
  free_flv_bottom_quartile_3_month,
  free_configuration_adjustment,
  free_recondition_adjustment,
  free_meter_adjustment,
  free_region_adjustment,
  ROW_NUMBER() OVER (
  ORDER BY
    equipment_number,
    valuation_log_id ASC
  ) AS id,
  rfm_condition_adjustment,
  meter_precise,
  insights_category,
  insights_category_id,
  insights_subcategory,
  insights_subcategory_id
FROM
  `{PROJECT_ID}.{DATASET_VIEW_ID}.{TABLE_ID}`
WHERE
  valuation_date >='{YEAR_START}-01-01'
  AND valuation_date <'{YEAR_END_PLUS_ONE}-01-01'
"""


bq_export_columns = [
    "category",
    "category_id",
    "category_scid",
    "client_id",
    "client_code",
    "fleet_customer_id",
    "rfm_configuration_adjustment",
    "default_currency",
    "equipment_id",
    "equipment_number",
    "rfm_flv",
    "rfm_fmv",
    "make",
    "make_id",
    "make_scid",
    "meter",
    "rfm_meter_adjustment",
    "meter_type",
    "model",
    "model_id",
    "scid",
    "rfm_olv",
    "rfm_recondition_adjustment",
    "rfm_region_adjustment",
    "subcategory",
    "subcategory_id",
    "subcategory_scid",
    "valuation_date",
    "valuation_log_id",
    "rfm_wlv",
    "model_year",
    "valuation_fx_date",
    "retail_top_quartile_3_month",
    "retail_bottom_quartile_3_month",
    "auction_top_quartile_3_month",
    "auction_bottom_quartile_3_month",
    "free_flv",
    "free_mpe",
    "free_flv_top_quartile_3_month",
    "free_flv_bottom_quartile_3_month",
    "free_configuration_adjustment",
    "free_recondition_adjustment",
    "free_meter_adjustment",
    "free_region_adjustment",
    "id",
    "rfm_condition_adjustment",
    "meter_precise",
    "insights_category",
    "insights_category_id",
    "insights_subcategory",
    "insights_subcategory_id",
]

pg_query_find_latest_version = """
SELECT
  tablename
FROM
  pg_catalog.pg_tables
WHERE
  schemaname != 'pg_catalog'
  AND schemaname != 'information_schema'
  AND schemaname = 'rfm_{CLIENT_CODE}_version'
  AND tablename LIKE '{PG_TABLE}_%_{YEAR}'
  AND tablename NOT LIKE '{PG_TABLE}_{VERSION_ID}%'
ORDER BY
  tablename DESC
LIMIT 1;
"""

pg_insert_data = """
ALTER TABLE {PG_SCHEMA_VERSION}.{PG_TABLE_PREV}_{YEAR}
  ADD COLUMN IF NOT EXISTS free_flv INTEGER,
  ADD COLUMN IF NOT EXISTS free_mpe INTEGER,
  ADD COLUMN IF NOT EXISTS free_flv_top_quartile_3_month NUMERIC,
  ADD COLUMN IF NOT EXISTS free_flv_bottom_quartile_3_month NUMERIC,
  ADD COLUMN IF NOT EXISTS free_configuration_adjustment NUMERIC,
  ADD COLUMN IF NOT EXISTS free_recondition_adjustment NUMERIC,
  ADD COLUMN IF NOT EXISTS free_meter_adjustment NUMERIC,
  ADD COLUMN IF NOT EXISTS free_region_adjustment NUMERIC,
  ADD COLUMN IF NOT EXISTS id SERIAL PRIMARY KEY,
  ADD COLUMN IF NOT EXISTS rfm_condition_adjustment NUMERIC,
  ADD COLUMN IF NOT EXISTS meter_precise NUMERIC,
  ADD COLUMN IF NOT EXISTS insights_category VARCHAR,
  ADD COLUMN IF NOT EXISTS insights_category_id INTEGER,
  ADD COLUMN IF NOT EXISTS insights_subcategory VARCHAR,
  ADD COLUMN IF NOT EXISTS insights_subcategory_id INTEGER,
  ;
INSERT INTO
  {PG_SCHEMA_VERSION}.{PG_TABLE}_{VERSION_ID}_{YEAR}
SELECT
  *
FROM
  {PG_SCHEMA_VERSION}.{PG_TABLE_PREV}_{YEAR};
"""

bq_query_get_partitions = """
SELECT
  DISTINCT EXTRACT(YEAR FROM valuation_date) AS partition_year
FROM
  `{PROJECT_ID}.{DATASET_ID}.{TABLE_ID}`
ORDER BY 1 DESC
"""

bq_query_tables_schema = """
DECLARE RETENTION_PERIOD DATE DEFAULT
  DATE_SUB(CURRENT_DATE(), INTERVAL {RETENTION_PERIOD} DAY);
SELECT
 table_name
FROM
 `{PROJECT_ID}.{DATASET_STAGE_ID}.INFORMATION_SCHEMA.TABLES`
WHERE
  DATE(creation_time) >= RETENTION_PERIOD
  AND table_name LIKE '{TABLE_ID}_2%'
"""

bq_query_counter_table = """
SELECT
  EXTRACT(YEAR FROM valuation_date) AS year, COUNT(valuation_date) year_count
FROM
  `{PROJECT_ID}.{DATASET_ID}.{TABLE_ID}`
GROUP BY year
ORDER BY 1 DESC
"""

pg_query_get_partitions = """
SELECT
  DISTINCT CAST(date_part('year', valuation_date) AS INT) AS partition_year
FROM
  rfm_{CLIENT_CODE}.{TABLE_ID}
ORDER BY 1 DESC
"""

pg_query_get_partitions_multi = """
SELECT
  DISTINCT CAST(date_part('year', valuation_date) AS INT) AS partition_year
FROM
  rfm_{CLIENT_CODE}_version.{TABLE_ID}_{VERSION}
ORDER BY 1 DESC
"""
