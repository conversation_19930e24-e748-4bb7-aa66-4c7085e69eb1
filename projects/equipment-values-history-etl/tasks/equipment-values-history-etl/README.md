## Equipment Values History Pipeline

Data pipeline that creates the `equipment_values_history` table,
which will enable a history on asset values, e.g. FMV, FLV, OLV, WLV, for each version/valuation_log_id without removing
duplicates. Furthermore,
the table produced by this pipeline will replace the current Asset Value History module.

Also this data pipeline creates the `equipment_values_history_fm` materialized table (per client), only if client has
`fleet_customer_id` on the
`client_mappings` file so that the RFM API can use to show the corresponding values history changes.

**Pipeline Author/Engineer**: <PERSON> <<EMAIL>>

**Responsible Engineer**: <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON> <<EMAIL>>


---

### Business Impact

_In the event that failure of this pipeline might negatively impact our clients you should notify any internal or
interested parties at Rouse or Ritchie Bros that would be responsible for customer support. It is important that the
appropriate internal resources at our company are brought up-to-date on the issue and its business impacts before our
clients might be impacted by the issue so that we can be pro-active in our client support._

On delay or failure of this pipeline the following clients and business functions may be impacted:

* The values provided by the Sales API will be stale and potentially inaccurate.
* The values provided by the RFM API will be stale and potentially inaccurate.
* RFM API will not have a correspondent table for a new client.

On delay or failure of this pipeline the following dependent pipelines may be impacted:

* None

---

### Pipeline Trigger

This pipeline is triggered after the Nightly from a corresponding client finishes.

---

#### Dependencies

None

---

### Pipeline Diagram

![Pipeline diagram](docs/equipment_values_history_simple.svg)

![Pipeline diagram](docs/equipment_values_history_simple_fm.svg)

[Editable version of this Diagram](https://drive.google.com/file/d/1lGJLQsDL9Hp-W-YjsUuNFUouTtaIW1hr/view?usp=sharing)

---

### Detailed Pipeline Diagram

![Detailed pipeline diagram](docs/equipment_values_history_detailed.svg)

![Detailed pipeline diagram](docs/equipment_values_history_detailed_fm.svg)

[Editable version of this Diagram](https://drive.google.com/file/d/1lGJLQsDL9Hp-W-YjsUuNFUouTtaIW1hr/view?usp=sharing)

---

### Sources

For legacy version of the Pipeline:

| SQL Server tables (Shards)                                                                           | BigQuery destination dataset | BigQuery destination table          | Extraction type | Purpose                                                                                                                                                                                                                                                                                                                                                                                      |
|------------------------------------------------------------------------------------------------------|------------------------------|-------------------------------------|-----------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Assets.Version, vod.ValuationLog_Adjustment                                                          | version                      | adjustment_factors                  | Delta by year   | Retrieves adjustment factors for each valuation_log_id of an asset.                                                                                                                                                                                                                                                                                                                          |
| Assets.ValueLog, ExpressionsEngine.Currency, Assets.Asset, Assets.Version, vod.valuation_log_details | version                      | equipment_history_detailed          | Delta by year   | Retrieves extra features for each valuation_log_id of an asset, such as meter, meter_type, currency. This is the only table that extracts from vod.valuation_log_details.                                                                                                                                                                                                                    |
| Assets.Field, Assets.ValueLog, Assets.Asset, common.Country                                          | version                      | equipment_values_history_no_version | Full            | Looks for FLV, FMV, OLV and WLV in the Assets.ValueLog table only for assets that don't have versionIds associated with them. This is useful to get the values for assets back when versionId was not being used while also avoiding queries to KVPs. It only pulls the whole table when there is no EVH tables detected in the BigQuery dataset view, subsequent runs skip this extraction. |
| Assets.Field, Assets.ValueLog                                                                        | version                      | equipment_values_history            | Delta by year   | With a query very similar to the previous one, it looks for FLV, FMV, OLV and WLV in the Assets.ValueLog table for assets that have versionIds associated with them while avoiding queries to KVPs.                                                                                                                                                                                          |
| Assets.Field, Assets.Field_Version                                                                   | version                      | field_version                       | Full            | Queries the KVPs in order to get a more complete view on the MeterType. It used to pull some other fields from there but those were no longer needed.                                                                                                                                                                                                                                        |
| Assets.ValueLog, Assets.Version, vod.ValuationLog                                                    | version                      | initial_valuations                  | Delta by year   | Looks for FLV, FMV, OLV and WLV in the vod.ValuationLog table only for the first or inital valuations done to an assets in order to give a more complete view of when the asset was first created.                                                                                                                                                                                           |
| Assets.ValueLog, Assets.Field                                                                        | stage                        | asset_valuelog_counter              | Full            | Extracts the number of rows that exist for FLV, FMV, OLV and WLV in the Assets.ValueLog table for each year. If it detects that an asset_valuelog_counter view exists, it compares the most recent extraction with latest version available in BigQuery in order to detect which years have been modified.                                                                                   |

Tables used to generate the final equipment_values_history in BigQuery:

| BigQuery source dataset          | BigQuery source table                                                                                                                            | BigQuery destination table                              | Load type                    | Purpose                                                                                                                              |
|----------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------|------------------------------|--------------------------------------------------------------------------------------------------------------------------------------|
| equipment_values_history_version | adjustment_factors, equipment_history_detailed, equipment_values_history_no_version, equipment_values_history, field_version, initial_valuations | equipment_values_history_stage.equipment_values_history | Full                         | Can contain full history of each version/valuation_log_id for each asset the first run, subsequent runs contains only certain years. |
| equipment_values_history_stage   | equipment_values_history                                                                                                                         | equipment_history.equipment_values_history              | Trunc/Load on year partition | Keep complete history of each version/valuation_log_id for each asset.                                                               |

For IMS version of the Pipeline:

| Source | ProjectID                   | View                                              |
|--------|-----------------------------|---------------------------------------------------|
| BQ     | CLIENT_PROJECT_ID           | equipment_values_history.equipment_values_history |
| BQ     | CLIENT_PROJECT_ID           | fleet_valuations_history.fleet_valuations_history |
| BQ     | appraisals-data-prod-707493 | equipment_classification.equipment_classification |

For Multi-tenant clients' version of the Pipeline:

| Source | ProjectID                   | View                                              |
|--------|-----------------------------|---------------------------------------------------|
| BQ     | rfm-prod-5f68c8             | fleet_manager{ENV_SUFFIX}.fleet_valuations        |
| BQ     | rfm-prod-5f68c8             | fleet_manager{ENV_SUFFIX}.fleet_customers         |
| BQ     | rfm-prod-5f68c8             | fleet_manager{ENV_SUFFIX}.fleet_entitlements      |
| BQ     | appraisals-data-prod-707493 | equipment_classification.equipment_classification |

---

### Transformations Performed

The pipeline applies a series of queries using all the tables the previously extracted tables, stored in the version
dataset in BigQuery, resulting in a partitioned table in the stage dataset. If this is the first time the pipeline runs,
the stage table will be copied into the "view" dataset. For subsequent runs, only the extracted years (partitions) will
replace those from the "view" dataset.

The main SQL Server table used in this pipeline is `Assets.ValueLog`, called `equipment_values_history` in BigQuery,
which logs changes on asset values (FMV, FLV, OLV, WLV). In addition to that, we need to retrieve records without
version_ids, usually old records, these are obtained from the `equipment_values_history_no_version` table, this table is
only pulled once, because if the records would get updated, we would expect them to come with version_ids and also helps
with pipeline performance on future runs. Once these two tables are merged, we will get the complete history that
`Assets.ValueLog` provide but we noticed that the table only logs one record for only the value that has changed not the
whole group of values (FMV, FLV, OLV, WLV) for a certain version. In consequence, after the table is pivoted there will
be many versions with null values.

The first approach we took to overcome the null issue was to look at the initial values for each asset, obtained in
`initial_valuations`. But since not all the values in `Assets.ValueLog` are in the same currency we need to convert them
to the right one before joining them to our pivoted set.

The second approach to overcome the null issue was to use the LAST_VALUE function, which is applied to fill missing
values for the FMV, FLV and WLV values by taking the oldest value from the oldest version and replicate it into newer
versions without values. Complementary to that, the OLV value is calculated where needed by averaging the FMV and FLV.

Additionally, we enrich the dataset by adding the adjustment factors (Option, Region and Recondition) for each
valuation_log_id of each asset obtained by the `adjustment_factors` table. The pipeline also required to gather a
historic record on the MeterType field from the KVPs, as this was the only place where such a record is kept. However,
the versionId ranges can be noncontiguous, therefore custom ranges were created for each asset and meter type, starting
from 0 up to the highest available versionId, trying to remove gaps in the versionIds as much as possible. Finally, some
extra fields such as the meter, meter_type, currency_code and valuation_log_id from the table
`equipment_values_history_detailed` (sourced from `vod.valuation_log_detail`) by simply joining the tables.

Due to the considerable amounts of data being pulled from SQL Server and exported into PostgreSQL a delta functionality
was added, where a table in BigQuery holds the count of the number of rows per year from the SQL Server table
`Assets.ValueLog`. During a new run it will compare the number of rows per year between the output from SQL Server and
BigQuery, if it detects any difference in a certain year it will pull from SQL Server only that year, also it will only
export that year into PostgreSQL and the rest of the years will just be copied to the new table from the old version
table.

In delta extractions, we perform all the steps mentioned above but there is one additional step we add to remove
possible gap, where we merge the recently extracted table and the table from the "view" dataset without the partitions
from the delta table to apply the LAST_VALUE function.

---

### Quality Assurance Checks

The following quality assurance checks are in place:

* For flows that comprehends exporting data from MSSQL to BigQuery:
    * Checks if the number of rows are greater than 0.
    * Checks if the number of rows are equal in both ends.
* For exporting `adjustment_factors` data from MSSQL to BigQuery:
    * Checks if a sum of `asset_id` and `multiplier` columns match in both ends.
    * Checks if `asset_id`, `valuation_log_id` and `type` columns contain nulls.
* For exporting `equipment_values_history` data from MSSQL to BigQuery:
    * Checks if a sum of `equipment_id`, `meter` and `value` columns match in both ends.
    * Checks if `equipment_number`, `equipment_id`, `version_id` and `currency_code` columns contain nulls.
* For exporting `equipment_values_history_no_version` data from MSSQL to BigQuery:
    * Checks if a sum of `equipment_id` and `value` columns match in both ends.
    * Checks if `equipment_id` and `currency_code` columns contain nulls.
* For exporting `field_version` data from MSSQL to BigQuery:
    * Checks if a sum of `asset_id`, `version_id_range_start` and `version_id_range_end` columns match in both ends.
    * Checks if `asset_id`, `version_id_range_start`, `version_id_range_end` and `value` columns contain nulls.
    * Checks if the extracted `client_id` is the same as in the `equipment_values_history` table.
* For exporting `initial_values` data from MSSQL to BigQuery:
    * Checks if a sum of `valuation_log_id`, `fmv`, `flv`, `olv` and `wlv` columns match in both ends.
    * Checks if `valuation_log_id`, `fmv`, `flv`, `olv` and `wlv` columns contain nulls.
* For the final table `equipment_values_history`:
    * Checks if `equipment_id`, `default_currency`, `fmv`, `flv` and `olv` columns contain nulls for assets that are
      appraised and have a `valuation_log_id`.
    * Checks if there is only one `client_id`.
* For the final table `equipment_values_history_fm`:
    * Checks if the number of rows did not reduce based on the current production view.
    * Checks if `category`, `category_scid`, `equipment_id`, `equipment_number`, `make`, `make_scid`, `model`,
      `model_year`, `scid`, `subcategory`, `subcategory_scid`, `fleet_customer_id`, `category_id`, `subcategory_id`,
      `make_id`, `model_id` and `fleet_asset_id` columns contain nulls.
    * Checks if the new view has only records to the corresponding `fleet_customer_id`,
      based on the DAG running `client_mapping` configuration.

---

### Troubleshooting

The most likely cause of failure could be from quality checks. In that case, make sure all dependencies executed
correctly.

---

### Usage Examples

To run the examples below you will need to change directory (`cd`) to the directory above the
`equipment_values_history_etl` directory and run the commands.

---

Show help:

```
python -m equipment_values_history_etl.cli --help
```

Output:

```
Usage: cli.py [OPTIONS] COMMAND [ARGS]...

Options:
  --version  Show the version and exit.
  --help     Show this message and exit.

Commands:
  bq2pg                             Export from BigQuery to Postgres with intermediate steps on GCS
  bqquery2bq                        Runs BigQuery query and persists the result in a table
  gcs2bq                            Export from GCS to BigQuery
  get-modified-years                Compares query results from MSSQL and BigQuery
  mssql2gcs                         Export from MSSQL to GCS
  pg-count                          Returns as XCOM value the count for provided table or zero if table does not exist
  pg-create-materialized-tables     Run queries for create version views/tables for Fleet Manager flow
  pg-env                            Export from BigQuery to Postgres
  pg-env-post                       Drops older tables/views and updates views in Cloud SQL Postgres for legacy and IMS versions at the same transaction
  pg-qa                             QA checks for views/tables.
  qa                                Run data quality checks comparing output from MSSQL and BigQuery
  qa-partitions                     Compare if partitions are the same between PostgreSQL and BigQuery
  qa-table                          Run data quality checks on BigQuery tables 
```

---

### Test In Docker Container

Make sure that Docker is installed and running on your machine.

The first time you invoke `./run_docker.sh` it will create a Docker container based on the information in the
`Dockerfile` file. Once the container image has been created subsequent runs of the script should run more quickly as
they can used the previously created image.

`run_docker.sh` must pass one of three flags:

* `-b` run BASH shell.

```
./run_docker.sh -b
```

---
