WITH
  rfm_valuations AS(
  SELECT
    fvh.fleet_customer_id,
    fc.client_code_rouse_sales as client_code,
    fvh.classification_rouse_category AS category,
    fvh.classification_rouse_category_id AS category_id,
    ec.category_scid AS category_scid,
    fc.client_id_rouse_sales AS client_id,
    fvh.equipment_options_adjustment AS rfm_configuration_adjustment,
    IF(fvh.country != 'GBUK', 'USD', fvh.country) AS default_currency,
    fvh.fleet_asset_id AS equipment_id,
    fvh.equipment_number AS equipment_number,
    fvh.flv AS rfm_flv,
    fvh.fmv AS rfm_fmv,
    fvh.classification_rouse_make AS make,
    fvh.classification_rouse_make_id AS make_id,
    ec.make_scid AS make_scid,
    CASE
      WHEN fvh.meter_uom = 'H' THEN fvh.meter_hours
    ELSE
      COALESCE(fvh.meter_miles_precise, fvh.meter_miles)
    END
    AS meter,
    fvh.meter_adjustment AS rfm_meter_adjustment,
    fvh.meter_uom AS meter_type,
    fvh.classification_rouse_model AS model,
    fvh.classification_rouse_model_id AS model_id,
    ec.scid AS scid,
    fvh.olv AS rfm_olv,
    fvh.reconditioned_date_adjustment AS rfm_recondition_adjustment,
    fvh.condition_adjustment AS rfm_condition_adjustment,
    fvh.locality_adjustment AS rfm_region_adjustment,
    fvh.classification_rouse_subcategory AS subcategory,
    fvh.classification_rouse_subcategory_id AS subcategory_id,
    ec.subcategory_scid AS subcategory_scid,
    fvh.asset_valuation_created_date AS valuation_date,
    fvh.fleet_asset_valuation_set_id AS valuation_log_id,
    fvh.wlv AS rfm_wlv,
    fvh.model_year,
    fvh.fx_date AS valuation_fx_date,
    fvh.retail_top_quartile_3_month,
    fvh.retail_bottom_quartile_3_month,
    fvh.auction_top_quartile_3_month,
    fvh.auction_bottom_quartile_3_month,
    LAG(fvh.flv, 1) OVER(PARTITION BY fvh.fleet_asset_id ORDER BY  fvh.asset_valuation_created_date) lag_rfm_flv,
    LAG(fvh.fmv, 1) OVER(PARTITION BY fvh.fleet_asset_id ORDER BY  fvh.asset_valuation_created_date) lag_rfm_fmv,
    LAG(fvh.olv, 1) OVER(PARTITION BY fvh.fleet_asset_id ORDER BY  fvh.asset_valuation_created_date) lag_rfm_olv,
    LAG(fvh.wlv, 1) OVER(PARTITION BY fvh.fleet_asset_id ORDER BY  fvh.asset_valuation_created_date) lag_rfm_wlv,
    LAG(fvh.model_year, 1) OVER(PARTITION BY fvh.fleet_asset_id ORDER BY  fvh.asset_valuation_created_date) lag_model_year,
    LAG(fvh.classification_rouse_category_id, 1) OVER(PARTITION BY fvh.fleet_asset_id ORDER BY  fvh.asset_valuation_created_date) lag_category_id,
    LAG(fvh.classification_rouse_subcategory_id, 1) OVER(PARTITION BY fvh.fleet_asset_id ORDER BY  fvh.asset_valuation_created_date) lag_subcategory_id,
    LAG(fvh.classification_rouse_make_id, 1) OVER(PARTITION BY fvh.fleet_asset_id ORDER BY  fvh.asset_valuation_created_date) lag_make_id,
    LAG(fvh.classification_rouse_model_id, 1) OVER(PARTITION BY fvh.fleet_asset_id ORDER BY  fvh.asset_valuation_created_date) lag_model_id,
    LAG(fvh.equipment_options_adjustment, 1) OVER(PARTITION BY fvh.fleet_asset_id ORDER BY  fvh.asset_valuation_created_date) lag_rfm_configuration_adjustment,
    LAG(fvh.reconditioned_date_adjustment, 1) OVER(PARTITION BY fvh.fleet_asset_id ORDER BY  fvh.asset_valuation_created_date) lag_rfm_recondition_adjustment,
    LAG(fvh.condition_adjustment, 1) OVER(PARTITION BY fvh.fleet_asset_id ORDER BY  fvh.asset_valuation_created_date) lag_rfm_condition_adjustment,
    LAG(fvh.meter_adjustment, 1) OVER(PARTITION BY fvh.fleet_asset_id ORDER BY  fvh.asset_valuation_created_date) lag_rfm_meter_adjustment,
    LAG(fvh.locality_adjustment, 1) OVER(PARTITION BY fvh.fleet_asset_id ORDER BY  fvh.asset_valuation_created_date) lag_rfm_region_adjustment,
    LAG(CASE
      WHEN fvh.meter_uom = 'H' THEN fvh.meter_hours
    ELSE
      COALESCE(fvh.meter_miles_precise,fvh.meter_miles)
    END, 1) OVER(PARTITION BY fvh.fleet_asset_id ORDER BY  fvh.asset_valuation_created_date) lag_meter,
    CAST(null AS INTEGER) free_flv,
    CAST(null AS INTEGER) free_mpe,
    CAST(null AS FLOAT64) free_flv_top_quartile_3_month,
    CAST(null AS FLOAT64) free_flv_bottom_quartile_3_month,
    CAST(null AS FLOAT64) AS free_configuration_adjustment,
    CAST(null AS FLOAT64) AS free_meter_adjustment,
    CAST(null AS FLOAT64) AS free_recondition_adjustment,
    CAST(null AS FLOAT64) AS free_region_adjustment,
    fvh.classification_insights_category AS insights_category,
    fvh.classification_insights_category_id AS insights_category_id,
    fvh.classification_insights_subcategory AS insights_subcategory,
    fvh.classification_insights_subcategory_id AS insights_subcategory_id,
  FROM
    `{CLIENT_PROJECT_ID}.fleet_manager{ENV_SUFFIX}.fleet_valuations` fvh
  INNER JOIN `{CLIENT_PROJECT_ID}.fleet_manager{ENV_SUFFIX}.fleet_customers` fc
    ON fvh.partition_id = fc.partition_id
        AND fvh.fleet_customer_id = fc.fleet_customer_id
  INNER JOIN `{CLIENT_PROJECT_ID}.fleet_manager{ENV_SUFFIX}.fleet_assets` fa
    ON fvh.partition_id = fa.partition_id
        AND fvh.fleet_customer_id = fa.fleet_customer_id
        AND fvh.fleet_asset_id = fa.fleet_asset_id
  INNER JOIN `{CLIENT_PROJECT_ID}.fleet_manager{ENV_SUFFIX}.fleet_entitlements` fe
     ON fvh.partition_id = fe.partition_id
         AND fvh.fleet_customer_id = fe.fleet_customer_id
         AND fvh.fleet_asset_id = fe.fleet_asset_id
         AND fvh.fleet_asset_valuation_id = fe.fleet_asset_valuation_id
  LEFT OUTER JOIN
    `{APPRAISALS_PROJECT_ID}.equipment_classification{MAIN_ENV_SUFFIX}.equipment_classification` ec
  ON
    ec.model_id = fvh.classification_rouse_model_id
    AND ec.make_id = fvh.classification_rouse_make_id
    AND ec.subcategory_id = fvh.classification_rouse_subcategory_id
  WHERE
    fvh.valuation_type = 'rfm'
    AND fvh.equipment_number NOT LIKE '__rouse_test__%')
SELECT
  category,
  category_id,
  category_scid,
  client_id,
  client_code,
  fleet_customer_id,
  CAST(rfm_configuration_adjustment AS FLOAT64) AS rfm_configuration_adjustment,
  default_currency,
  equipment_id,
  equipment_number,
  rfm_flv,
  rfm_fmv,
  make,
  make_id,
  make_scid,
  CAST(ROUND(meter, 2) AS FLOAT64) AS meter,
  CAST(ROUND(meter, 7) AS NUMERIC) meter_precise,
  CAST(rfm_meter_adjustment AS FLOAT64) AS rfm_meter_adjustment,
  meter_type,
  model,
  model_id,
  scid,
  rfm_olv,
  CAST(rfm_recondition_adjustment AS FLOAT64) AS rfm_recondition_adjustment,
  CAST(rfm_condition_adjustment AS FLOAT64) AS rfm_condition_adjustment,
  CAST(rfm_region_adjustment AS FLOAT64) AS rfm_region_adjustment,
  subcategory,
  subcategory_id,
  subcategory_scid,
  valuation_date,
  valuation_log_id,
  rfm_wlv,
  model_year,
  valuation_fx_date,
  CAST(retail_top_quartile_3_month AS FLOAT64) AS retail_top_quartile_3_month,
  CAST(retail_bottom_quartile_3_month AS FLOAT64) AS retail_bottom_quartile_3_month,
  CAST(auction_top_quartile_3_month AS FLOAT64) AS auction_top_quartile_3_month,
  CAST(auction_bottom_quartile_3_month AS FLOAT64) AS auction_bottom_quartile_3_month,
  CAST(free_flv AS INTEGER) AS free_flv,
  CAST(free_mpe AS INTEGER) AS free_mpe,
  CAST(free_flv_top_quartile_3_month AS FLOAT64) AS free_flv_top_quartile_3_month,
  CAST(free_flv_bottom_quartile_3_month AS FLOAT64) AS free_flv_bottom_quartile_3_month,
  CAST(free_configuration_adjustment AS FLOAT64) AS free_configuration_adjustment,
  CAST(free_recondition_adjustment AS FLOAT64) AS free_recondition_adjustment,
  CAST(free_meter_adjustment AS FLOAT64) AS free_meter_adjustment,
  CAST(free_region_adjustment AS FLOAT64) AS free_region_adjustment,
  insights_category,
  insights_category_id,
  insights_subcategory,
  insights_subcategory_id,
  CAST({VERSION_ID} AS BIGINT) _version_id,
  CURRENT_TIMESTAMP() AS sync_date
FROM
  rfm_valuations
WHERE NOT(IFNULL(rfm_flv,0)= IFNULL(lag_rfm_flv,0)
  AND IFNULL(rfm_fmv,0) = IFNULL(lag_rfm_fmv,0)
  AND IFNULL(rfm_olv,0) = IFNULL(lag_rfm_olv,0)
  AND IFNULL(rfm_wlv,0) = IFNULL(lag_rfm_wlv,0)
  AND IFNULL(model_year,0) = IFNULL(lag_model_year,0)
  AND IFNULL(category_id,0) = IFNULL(lag_category_id,0)
  AND IFNULL(subcategory_id,0) = IFNULL(lag_subcategory_id,0)
  AND IFNULL(make_id,0) = IFNULL(lag_make_id,0)
  AND IFNULL(model_id,0) = IFNULL(lag_model_id,0)
  AND IFNULL(meter,0) = IFNULL(lag_meter,0)
  AND IFNULL(ROUND(rfm_configuration_adjustment,3),1) = IFNULL(ROUND(lag_rfm_configuration_adjustment,3),1)
  AND IFNULL(ROUND(rfm_recondition_adjustment,3),1) = IFNULL(ROUND(lag_rfm_recondition_adjustment,3),1)
  AND IFNULL(ROUND(rfm_condition_adjustment,3),1) = IFNULL(ROUND(lag_rfm_condition_adjustment,3),1)
  AND IFNULL(ROUND(rfm_meter_adjustment,3),1) = IFNULL(ROUND(lag_rfm_meter_adjustment,3),1)
  AND IFNULL(ROUND(rfm_region_adjustment,3),1) = IFNULL(ROUND(lag_rfm_region_adjustment,3),1))