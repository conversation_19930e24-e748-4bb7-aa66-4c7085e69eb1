WITH values_history_cte AS (
  SELECT 
    equipment_number, 
    TIMESTAMP_ADD(valuation_date, INTERVAL 1 SECOND) AS valuation_date,
    valuation_log_id,
    rfm_flv,
    rfm_fmv,
    rfm_olv,
    rfm_wlv,
    model_year,
    category_id,
    subcategory_id,
    make_id,
    model_id,
    rfm_configuration_adjustment,
    rfm_recondition_adjustment,
    rfm_condition_adjustment,
    rfm_meter_adjustment,
    rfm_region_adjustment,
    meter,
    insights_category,
    insights_category_id,
    insights_subcategory,
    insights_subcategory_id,
    ROW_NUMBER() OVER (PARTITION BY equipment_number ORDER BY valuation_date DESC) as rownumber
  FROM `{CLIENT_PROJECT_ID}.{STAGE_DATASET_NAME}.{TABLE_NAME}`
  ),
values_to_insert_cte AS(
  SELECT
    vhv.category,
    vhv.category_id,
    vhv.category_scid,
    vhv.client_id,
    vhv.client_code,
    vhv.fleet_customer_id,
    vhv.rfm_configuration_adjustment,
    vhv.default_currency,
    vhv.equipment_id,
    vhv.equipment_number,
    vhv.rfm_flv,
    vhv.rfm_fmv,
    vhv.make,
    vhv.make_id,
    vhv.make_scid,
    vhv.meter,
    vhv.meter_precise,
    vhv.rfm_meter_adjustment,
    vhv.meter_type,
    vhv.model,
    vhv.model_id,
    vhv.scid,
    vhv.rfm_olv,
    vhv.rfm_recondition_adjustment,
    vhv.rfm_condition_adjustment,
    vhv.rfm_region_adjustment,
    vhv.subcategory,
    vhv.subcategory_id,
    vhv.subcategory_scid,
    vhv.valuation_date,
    IFNULL(vhv.valuation_log_id,'') AS valuation_log_id,
    vhv.rfm_wlv,
    vhv.model_year,
    vhv.valuation_fx_date,
    vhv.sync_date,
    vhv.retail_top_quartile_3_month,
    vhv.retail_bottom_quartile_3_month,
    vhv.auction_top_quartile_3_month,
    vhv.auction_bottom_quartile_3_month,
    vhv.free_flv,
    vhv.free_mpe,
    vhv.free_flv_top_quartile_3_month,
    vhv.free_flv_bottom_quartile_3_month,
    vhv.free_configuration_adjustment,
    vhv.free_meter_adjustment ,
    vhv.free_recondition_adjustment,
    vhv.free_region_adjustment,
    vhv.insights_category,
    vhv.insights_category_id,
    vhv.insights_subcategory,
    vhv.insights_subcategory_id,
    ROW_NUMBER() OVER (PARTITION BY vhv.equipment_number ORDER BY vhv.valuation_date ASC) rownumber,
    CASE WHEN vhc_changes.equipment_number IS NULL THEN FALSE ELSE TRUE END data_matches
  FROM
    `{CLIENT_PROJECT_ID}.{VERSION_DATASET_NAME}.{TABLE_NAME}_{VERSION_ID}` vhv
  LEFT OUTER JOIN values_history_cte vhc
    ON vhc.equipment_number = vhv.equipment_number
    AND vhc.valuation_date > vhv.valuation_date
    AND vhc.rownumber = 1
  LEFT OUTER JOIN values_history_cte vhc_changes
    ON vhc_changes.equipment_number = vhv.equipment_number
    AND IFNULL(vhv.rfm_flv,0)= IFNULL(vhc_changes.rfm_flv,0)
    AND IFNULL(vhv.rfm_fmv,0) = IFNULL(vhc_changes.rfm_fmv,0)
    AND IFNULL(vhv.rfm_olv,0) = IFNULL(vhc_changes.rfm_olv,0)
    AND IFNULL(vhv.rfm_wlv,0) = IFNULL(vhc_changes.rfm_wlv,0)
    AND IFNULL(vhv.model_year,0) = IFNULL(vhc_changes.model_year,0)
    AND IFNULL(vhv.category_id,0) = IFNULL(vhc_changes.category_id,0)
    AND IFNULL(vhv.subcategory_id,0) = IFNULL(vhc_changes.subcategory_id,0)
    AND IFNULL(vhv.make_id,0) = IFNULL(vhc_changes.make_id,0)
    AND IFNULL(vhv.model_id,0) = IFNULL(vhc_changes.model_id,0)
    AND IFNULL(ROUND(vhv.rfm_configuration_adjustment,3),1) = IFNULL(ROUND(vhc_changes.rfm_configuration_adjustment,3),1)
    AND IFNULL(ROUND(vhv.rfm_recondition_adjustment,3),1) = IFNULL(ROUND(vhc_changes.rfm_recondition_adjustment,3),1)
    AND IFNULL(ROUND(vhv.rfm_condition_adjustment,3),1) = IFNULL(ROUND(vhc_changes.rfm_condition_adjustment,3),1)
    AND IFNULL(ROUND(vhv.rfm_meter_adjustment,3),1) = IFNULL(ROUND(vhc_changes.rfm_meter_adjustment,3),1)
    AND IFNULL(ROUND(vhv.rfm_region_adjustment,3),1) = IFNULL(ROUND(vhc_changes.rfm_region_adjustment,3),1)
    AND IFNULL(vhv.meter,0) = IFNULL(vhc_changes.meter,0)
    AND vhc_changes.rownumber = 1
  WHERE vhc.valuation_date IS NULL
)
SELECT 
  category,
  category_id,
  category_scid,
  client_id,
  client_code,
  fleet_customer_id,
  rfm_configuration_adjustment,
  default_currency,
  equipment_id,
  equipment_number,
  rfm_flv,
  rfm_fmv,
  make,
  make_id,
  make_scid,
  meter,
  meter_precise,
  rfm_meter_adjustment,
  meter_type,
  model,
  model_id,
  scid,
  rfm_olv,
  rfm_recondition_adjustment,
  rfm_condition_adjustment,
  rfm_region_adjustment,
  subcategory,
  subcategory_id,
  subcategory_scid,
  valuation_date,
  valuation_log_id,
  rfm_wlv,
  model_year,
  valuation_fx_date,
  sync_date,
  retail_top_quartile_3_month,
  retail_bottom_quartile_3_month,
  auction_top_quartile_3_month,
  auction_bottom_quartile_3_month,
  free_flv,
  free_mpe,
  free_flv_top_quartile_3_month,
  free_flv_bottom_quartile_3_month,
  free_configuration_adjustment,
  free_meter_adjustment ,
  free_recondition_adjustment,
  free_region_adjustment,
  insights_category,
  insights_category_id,
  insights_subcategory,
  insights_subcategory_id
FROM values_to_insert_cte
WHERE  NOT (rownumber=1 AND data_matches = TRUE )