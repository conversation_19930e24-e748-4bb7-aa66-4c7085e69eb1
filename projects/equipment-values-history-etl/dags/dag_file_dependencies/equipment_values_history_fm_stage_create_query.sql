CREATE TABLE IF NOT EXISTS `{CLIENT_PROJECT_ID}.{STAGE_DATASET_NAME}.{TABLE_NAME}` AS
SELECT
  category,
  category_id,
  category_scid,
  client_id,
  CAST(rfm_configuration_adjustment AS FLOAT64) AS rfm_configuration_adjustment,
  default_currency,
  equipment_id,
  equipment_number,
  CAST(rfm_flv AS INT) AS rfm_flv,
  CAST(rfm_fmv AS INT) AS rfm_fmv,
  make,
  make_id,
  make_scid,
  CAST(meter AS FLOAT64) AS meter,
  meter_precise,
  CAST(rfm_meter_adjustment AS FLOAT64) AS rfm_meter_adjustment,
  meter_type,
  model,
  model_id,
  scid,
  CAST(rfm_olv AS INT) AS rfm_olv,
  CAST(rfm_recondition_adjustment AS FLOAT64) AS rfm_recondition_adjustment,
  CAST(rfm_condition_adjustment AS FLOAT64) AS rfm_condition_adjustment,
  CAST(rfm_region_adjustment AS FLOAT64) AS rfm_region_adjustment,
  subcategory,
  subcategory_id,
  subcategory_scid,
  valuation_date,
  IFNULL(CAST(valuation_log_id AS STRING),'') AS valuation_log_id ,
  CAST(rfm_wlv AS INT) AS rfm_wlv,
  model_year,
  CAST(valuation_fx_date AS DATE) AS valuation_fx_date,
  sync_date,
  CAST(retail_top_quartile_3_month AS FLOAT64) AS retail_top_quartile_3_month,
  CAST(retail_bottom_quartile_3_month AS FLOAT64) AS retail_bottom_quartile_3_month,
  CAST(auction_top_quartile_3_month AS FLOAT64) AS auction_top_quartile_3_month,
  CAST(auction_bottom_quartile_3_month AS FLOAT64) AS auction_bottom_quartile_3_month,
  CAST(free_flv AS INTEGER) AS free_flv,
  CAST(free_mpe AS INTEGER) AS free_mpe,
  CAST(free_flv_top_quartile_3_month AS FLOAT64) AS free_flv_top_quartile_3_month,
  CAST(free_flv_bottom_quartile_3_month AS FLOAT64) AS free_flv_bottom_quartile_3_month,
  CAST(free_configuration_adjustment AS FLOAT64) AS free_configuration_adjustment,
  CAST(free_meter_adjustment AS FLOAT64) AS free_meter_adjustment,
  CAST(free_recondition_adjustment AS FLOAT64) AS free_recondition_adjustment,
  CAST(free_region_adjustment AS FLOAT64) AS free_region_adjustment,
  insights_category,
  insights_category_id,
  insights_subcategory,
  insights_subcategory_id,
  fleet_customer_id,
  client_code,

FROM
  `{CLIENT_PROJECT_ID}.{ORIGIN_DATASET_NAME}.{ORIGIN_TABLE_NAME}`;

ALTER TABLE `{CLIENT_PROJECT_ID}.{STAGE_DATASET_NAME}.{TABLE_NAME}`
    ADD COLUMN IF NOT EXISTS retail_top_quartile_3_month FLOAT64,
    ADD COLUMN IF NOT EXISTS retail_bottom_quartile_3_month FLOAT64,
    ADD COLUMN IF NOT EXISTS auction_top_quartile_3_month FLOAT64,
    ADD COLUMN IF NOT EXISTS auction_bottom_quartile_3_month FLOAT64,
    ADD COLUMN IF NOT EXISTS client_code STRING,
    ADD COLUMN IF NOT EXISTS fleet_customer_id INTEGER,
    ADD COLUMN IF NOT EXISTS free_flv INTEGER,
    ADD COLUMN IF NOT EXISTS free_mpe INTEGER,
    ADD COLUMN IF NOT EXISTS free_flv_top_quartile_3_month FLOAT64,
    ADD COLUMN IF NOT EXISTS free_flv_bottom_quartile_3_month FLOAT64,
    ADD COLUMN IF NOT EXISTS free_configuration_adjustment FLOAT64,
    ADD COLUMN IF NOT EXISTS free_meter_adjustment FLOAT64,
    ADD COLUMN IF NOT EXISTS free_recondition_adjustment FLOAT64,
    ADD COLUMN IF NOT EXISTS free_region_adjustment FLOAT64,
    ADD COLUMN IF NOT EXISTS rfm_condition_adjustment FLOAT64,
    ADD COLUMN IF NOT EXISTS meter_precise NUMERIC,
    ADD COLUMN IF NOT EXISTS insights_category STRING,
    ADD COLUMN IF NOT EXISTS insights_category_id INT64,
    ADD COLUMN IF NOT EXISTS insights_subcategory STRING,
    ADD COLUMN IF NOT EXISTS insights_subcategory_id INT64
    ;


UPDATE `{CLIENT_PROJECT_ID}.{STAGE_DATASET_NAME}.{TABLE_NAME}`
    SET client_code = '{SALES_CLIENT_CODE}',
        fleet_customer_id = {FLEET_CUSTOMER_ID}
WHERE fleet_customer_id IS NULL;

UPDATE `{CLIENT_PROJECT_ID}.{STAGE_DATASET_NAME}.{TABLE_NAME}`
    SET meter_precise = CAST(ROUND(meter, 7) AS NUMERIC)
    WHERE
      meter IS NOT NULL
      AND meter_precise IS NULL;