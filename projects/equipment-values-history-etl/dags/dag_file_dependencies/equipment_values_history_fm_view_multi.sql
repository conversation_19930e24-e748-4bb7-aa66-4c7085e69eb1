BEGIN;

SET search_path TO rfm_multi;

DROP VIEW IF EXISTS equipment_values_history_fm;

CREATE OR REPLACE VIEW equipment_values_history_fm
AS
    WITH
      latest_valuation AS(
      SELECT
        client_id,
        equipment_number,
        MAX(valuation_date) AS valuation_date
      FROM
        rfm_multi_version.equipment_values_history_fm_{VERSION_ID}
      WHERE
        valuation_date>= '{MINIMUM_VALUATION_DATE}'
      GROUP BY
        client_id,
        equipment_number )
    SELECT
      MIN(fv.fleet_asset_valuation_id) AS id,
      fv.classification_rouse_category AS category,
      fv.classification_rouse_category_id AS category_id,
      fa.category_scid,
      fc.client_id_rouse_sales AS client_id,
      fc.client_code_rouse_sales AS client_code,
      fv.fleet_customer_id,
      fv.equipment_options_adjustment AS rfm_configuration_adjustment,
      MIN(CASE
          WHEN fv.country <> 'GBUK' THEN 'USD'
          ELSE fv.country
      END
        ) AS default_currency,
      fv.fleet_asset_id AS equipment_id,
      fv.equipment_number,
      MIN( CAST(
          CASE
            WHEN LOWER(fv.mpe_method) = 'algo' AND fv.mpe IS NOT NULL THEN fv.algo_flv
            ELSE fv.flv
        END
          AS INT)) AS rfm_flv,
      fv.fmv AS rfm_fmv,
      fv.classification_rouse_make AS make,
      fv.classification_rouse_make_id AS make_id,
      fa.make_scid,
      MIN(CAST(CASE
            WHEN fv.meter_uom = 'H' THEN fv.meter_hours
            ELSE COALESCE(ROUND(fv.meter_miles_precise,2), fv.meter_miles)
        END
          AS INTEGER)) AS meter,
      MIN(CAST(CASE
            WHEN fv.meter_uom = 'H' THEN fv.meter_hours
            ELSE fv.meter_miles_precise
        END
          AS INTEGER)::NUMERIC) AS meter_precise,
      fv.meter_adjustment AS rfm_meter_adjustment,
      MIN(fv.meter_uom) AS meter_type,
      fv.classification_rouse_model AS model,
      fv.classification_rouse_model_id AS model_id,
      fa.scid,
      fv.olv AS rfm_olv,
      fv.reconditioned_date_adjustment AS rfm_recondition_adjustment,
      fv.condition_adjustment AS rfm_condition_adjustment,
      fv.locality_adjustment AS rfm_region_adjustment,
      fv.classification_rouse_subcategory AS subcategory,
      fv.classification_rouse_subcategory_id AS subcategory_id,
      fa.subcategory_scid,
      MIN(fv.classification_insights_category) AS insights_category,
      MIN(fv.classification_insights_category_id) AS insights_category_id,
      MIN(fv.classification_insights_subcategory) AS insights_subcategory,
      MIN(fv.classification_insights_subcategory_id) AS insights_subcategory_id,
      MIN(DATE_TRUNC('day',fv.asset_valuation_created_date)) AS valuation_date,
      MIN(CAST(fv.fleet_asset_valuation_set_id AS TEXT)) AS valuation_log_id,
      fv.wlv AS rfm_wlv,
      fv.model_year,
      MIN(fv.fx_date) AS valuation_fx_date,
      MIN(fv.retail_top_quartile_3_month) AS retail_top_quartile_3_month,
      MIN(fv.retail_bottom_quartile_3_month) AS retail_bottom_quartile_3_month,
      MIN(fv.auction_top_quartile_3_month) AS auction_top_quartile_3_month,
      MIN(fv.auction_bottom_quartile_3_month) AS auction_bottom_quartile_3_month,
      CAST(NULL AS INTEGER) AS free_flv,
      CAST(NULL AS INTEGER) AS free_mpe,
      CAST(NULL AS NUMERIC) AS free_flv_top_quartile_3_month,
      CAST(NULL AS NUMERIC) AS free_flv_bottom_quartile_3_month,
      CAST(NULL AS NUMERIC) AS free_configuration_adjustment,
      CAST(NULL AS NUMERIC) AS free_meter_adjustment,
      CAST(NULL AS NUMERIC) AS free_recondition_adjustment,
      CAST(NULL AS NUMERIC) AS free_region_adjustment
    FROM
      fleet_manager.fleet_valuations fv
    INNER JOIN
      fleet_manager.fleet_assets fa
    ON
      fv.partition_id = fa.partition_id
      AND fv.fleet_customer_id = fa.fleet_customer_id
      AND fv.fleet_asset_id = fa.fleet_asset_id
      AND fa.asset_deleted_date IS NULL
    INNER JOIN
      fleet_manager.fleet_customers fc
    ON
      fv.partition_id = fc.partition_id
      AND fv.fleet_customer_id = fc.fleet_customer_id
    LEFT OUTER JOIN
      latest_valuation lv
    ON
      fc.client_id_rouse_sales = lv.client_id
      AND fv.equipment_number = lv.equipment_number
    LEFT OUTER JOIN
      rfm_multi_version.equipment_values_history_fm_{VERSION_ID} evh
    ON
      lv.valuation_date = evh.valuation_date
      AND lv.client_id = evh.client_id
      AND lv.equipment_number = evh.equipment_number
      AND COALESCE(fv.fmv,0) = COALESCE(evh.rfm_fmv,0)
      AND COALESCE(fv.olv,0) = COALESCE(evh.rfm_olv,0)
      AND COALESCE(fv.wlv,0) = COALESCE(evh.rfm_wlv,0)
      AND COALESCE(fv.model_year,0) = COALESCE(evh.model_year,0)
      AND COALESCE(fv.classification_rouse_category_id,0) = COALESCE(evh.category_id,0)
      AND COALESCE(fv.classification_rouse_subcategory_id,0) = COALESCE(evh.subcategory_id,0)
      AND COALESCE(fv.classification_rouse_make_id,0) = COALESCE(evh.make_id,0)
      AND COALESCE(fv.classification_rouse_model_id,0) = COALESCE(evh.model_id,0)
      AND COALESCE(ROUND(fv.equipment_options_adjustment,3),1) = COALESCE(ROUND(evh.rfm_configuration_adjustment,3),1)
      AND COALESCE(ROUND(fv.reconditioned_date_adjustment,3),1) = COALESCE(ROUND(evh.rfm_recondition_adjustment,3),1)
      AND COALESCE(ROUND(fv.condition_adjustment,3),1) = COALESCE(ROUND(evh.rfm_condition_adjustment,3),1)
      AND COALESCE(ROUND(fv.meter_adjustment,3),1) = COALESCE(ROUND(evh.rfm_meter_adjustment,3),1)
      AND COALESCE(ROUND(fv.locality_adjustment,3),1) = COALESCE(ROUND(evh.rfm_region_adjustment,3),1)
    WHERE
      fv.valuation_type = 'rfm'
      AND fv.partition_id = 10000
      AND evh.id IS NULL
      AND (fv.asset_valuation_created_date > lv.valuation_date
        OR lv.valuation_date IS NULL)
      AND fv.asset_valuation_created_date > '{PREVIOUS_DAY}'
      AND fa.is_customer_entitled_to_value = TRUE
    GROUP BY
      fv.fmv,
      fv.olv,
      fv.wlv,
      fv.model_year,
      fv.classification_rouse_category_id,
      fv.classification_rouse_subcategory_id,
      fv.classification_rouse_make_id,
      fv.classification_rouse_model_id,
      fv.equipment_options_adjustment,
      fv.reconditioned_date_adjustment,
      fv.condition_adjustment,
      fv.meter_adjustment,
      fv.locality_adjustment,
      fv.classification_rouse_category,
      fa.category_scid,
      fc.client_id_rouse_sales,
      fc.client_code_rouse_sales,
      fv.fleet_customer_id,
      fv.fleet_asset_id,
      fv.equipment_number,
      fv.classification_rouse_make,
      fa.make_scid,
      fv.classification_rouse_model,
      fa.scid,
      fv.classification_rouse_subcategory,
      fa.subcategory_scid
    UNION ALL
    SELECT
      evh.id,
      evh.category,
      evh.category_id,
      evh.category_scid,
      evh.client_id,
      evh.client_code,
      evh.fleet_customer_id,
      evh.rfm_configuration_adjustment,
      evh.default_currency,
      evh.equipment_id,
      evh.equipment_number,
      evh.rfm_flv,
      evh.rfm_fmv,
      evh.make,
      evh.make_id,
      evh.make_scid,
      evh.meter,
      evh.meter_precise,
      evh.rfm_meter_adjustment,
      evh.meter_type,
      evh.model,
      evh.model_id,
      evh.scid,
      evh.rfm_olv,
      evh.rfm_recondition_adjustment,
      evh.rfm_condition_adjustment,
      evh.rfm_region_adjustment,
      evh.subcategory,
      evh.subcategory_id,
      evh.subcategory_scid,
      evh.insights_category,
      evh.insights_category_id,
      evh.insights_subcategory,
      evh.insights_subcategory_id,
      evh.valuation_date,
      evh.valuation_log_id,
      evh.rfm_wlv,
      evh.model_year,
      evh.valuation_fx_date,
      evh.retail_top_quartile_3_month,
      evh.retail_bottom_quartile_3_month,
      evh.auction_top_quartile_3_month,
      evh.auction_bottom_quartile_3_month,
      evh.free_flv,
      evh.free_mpe,
      evh.free_flv_top_quartile_3_month,
      evh.free_flv_bottom_quartile_3_month,
      evh.free_configuration_adjustment,
      evh.free_meter_adjustment,
      evh.free_recondition_adjustment,
      evh.free_region_adjustment
    FROM
      rfm_multi_version.equipment_values_history_fm_{VERSION_ID} evh
    INNER JOIN fleet_manager.fleet_assets fa
      ON fa.partition_id = 10000
      AND fa.fleet_asset_id = evh.equipment_id
    WHERE
      fa.is_customer_entitled_to_value = TRUE
    UNION ALL
    SELECT
      DISTINCT fv.fleet_asset_valuation_id AS id,
      fv.classification_rouse_category AS category,
      fv.classification_rouse_category_id AS category_id,
      fa.category_scid,
      fc.client_id_rouse_sales AS client_id,
      fc.client_code_rouse_sales AS client_code,
      fv.fleet_customer_id,
      fv.equipment_options_adjustment AS rfm_configuration_adjustment,
      CASE
        WHEN fv.country <> 'GBUK' THEN 'USD'
        ELSE fv.country
    END
      AS default_currency,
      fv.fleet_asset_id AS equipment_id,
      fv.equipment_number,
      CAST(
        CASE
          WHEN LOWER(fv.mpe_method) = 'algo' AND fv.mpe IS NOT NULL THEN fv.algo_flv
          ELSE fv.flv
      END
        AS INT) AS rfm_flv,
      fv.fmv AS rfm_fmv,
      fv.classification_rouse_make AS make,
      fv.classification_rouse_make_id AS make_id,
      fa.make_scid,
      CAST(CASE
          WHEN fv.meter_uom = 'H' THEN fv.meter_hours
          ELSE COALESCE(ROUND(fv.meter_miles_precise,2), fv.meter_miles)
      END
        AS INTEGER) AS meter,
      CAST(CASE
          WHEN fv.meter_uom = 'H' THEN fv.meter_hours
          ELSE fv.meter_miles_precise
      END
        AS INTEGER)::NUMERIC AS meter_precise,
      fv.meter_adjustment AS rfm_meter_adjustment,
      fv.meter_uom AS meter_type,
      fv.classification_rouse_model AS MODEL,
      fv.classification_rouse_model_id AS model_id,
      fa.scid,
      fv.olv AS rfm_olv,
      fv.reconditioned_date_adjustment AS rfm_recondition_adjustment,
      fv.condition_adjustment AS rfm_condition_adjustment,
      fv.locality_adjustment AS rfm_region_adjustment,
      fv.classification_rouse_subcategory AS subcategory,
      fv.classification_rouse_subcategory_id AS subcategory_id,
      fa.subcategory_scid,
      fv.classification_insights_category AS insights_category,
      fv.classification_insights_category_id AS insights_category_id,
      fv.classification_insights_subcategory AS insights_subcategory,
      fv.classification_insights_subcategory_id AS insights_subcategory_id,
      DATE_TRUNC('day',fv.asset_valuation_created_date) AS valuation_date,
      CAST(fv.fleet_asset_valuation_set_id AS TEXT) AS valuation_log_id,
      fv.wlv AS rfm_wlv,
      fv.model_year,
      fv.fx_date AS valuation_fx_date,
      fv.retail_top_quartile_3_month,
      fv.retail_bottom_quartile_3_month,
      fv.auction_top_quartile_3_month,
      fv.auction_bottom_quartile_3_month,
      CAST(NULL AS INTEGER) AS free_flv,
      CAST(NULL AS INTEGER) AS free_mpe,
      CAST(NULL AS NUMERIC) AS free_flv_top_quartile_3_month,
      CAST(NULL AS NUMERIC) AS free_flv_bottom_quartile_3_month,
      CAST(NULL AS NUMERIC) AS free_configuration_adjustment,
      CAST(NULL AS NUMERIC) AS free_meter_adjustment,
      CAST(NULL AS NUMERIC) AS free_recondition_adjustment,
      CAST(NULL AS NUMERIC) AS free_region_adjustment
    FROM
      fleet_manager.fleet_valuations fv
    JOIN
      fleet_manager.fleet_assets fa
    ON
      fv.partition_id = fa.partition_id
      AND fv.fleet_customer_id = fa.fleet_customer_id
      AND fv.fleet_asset_id = fa.fleet_asset_id
      AND fa.asset_deleted_date IS NULL
    JOIN
      fleet_manager.fleet_customers fc
    ON
      fv.partition_id = fc.partition_id
      AND fv.fleet_customer_id = fc.fleet_customer_id
    WHERE
      fv.valuation_type = 'free'
      AND fv.partition_id = 10000
      AND COALESCE(fa.is_customer_entitled_to_value, FALSE) = FALSE;



ALTER TABLE equipment_values_history_fm
    OWNER TO root;

GRANT SELECT ON equipment_values_history_fm TO equipment_catalog_api;
GRANT SELECT ON equipment_values_history_fm TO equipment_values_history_fm_r;
GRANT SELECT ON equipment_values_history_fm TO fleet_manager_pg_export_multi_tenant;
GRANT SELECT ON equipment_values_history_fm TO rfm_multi_all_tables_r;

COMMIT;