import collections
import datetime as dt
import itertools
import os
from datetime import <PERSON><PERSON><PERSON>

import pendulum
from airflow import DAG
from airflow.models import Variable
from airflow.operators.dummy import DummyOperator
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from airflow.utils.task_group import TaskGroup

from client_projects_constants import client_mappings
from operators.pubsub_ack_operator import PubS<PERSON>AckOperator
from operators.pubsub_custom_filter import PubSubExcludeOperator
from operators.pubsub_filter_message import PubSubPullFilterFromListOperator
from sensors.pubsub_retrieve_all_messages import PubSubRetrieveAllMessages
from sharding_constants import sharded_clients
from shared_libs.default_args import get_default_args
from shared_libs.image_versions import get_gcr_registry
from shared_libs.slack_callback import task_fail_slack_alert
from shared_libs.pagerduty_callback import dag_fail_pagerduty_alert
from airflow_datasets import RouseDatasets

VERSION_MAJOR = "1"
VERSION_MINOR = "4"
PIPELINE_VERSION = f"v{VERSION_MAJOR}.{VERSION_MINOR}"

LOCAL_TZ = pendulum.timezone("America/Los_Angeles")
ENVIRONMENT = os.environ.get("COMPOSER_ENVIRONMENT", "dev")
GCR_REGISTRY = get_gcr_registry(ENVIRONMENT)

NAMESPACE = "equipment-values-history-etl"
SERVICE_ACCOUNT_NAME = NAMESPACE

EXTERNAL_PIPELINE_PREFIX = f"equipment-values-history-etl"

EXTERNAL_DAG_ID = f"{EXTERNAL_PIPELINE_PREFIX}-{PIPELINE_VERSION}"

if ENVIRONMENT != "prod":
    CLUSTER_NAME = "composer-jobs-v3-dev"
    PROJECT_NAME = "management-dev-d6ba4d"
    PROJECT_NAME_IMS = "appraisals-data-dev-c55fa4"
    START_DATE = dt.datetime(2022, 3, 16, tzinfo=LOCAL_TZ)
    SCHEDULE_INTERVAL = timedelta(minutes=15)
    POKE_INTERVAL = int(timedelta(minutes=5).total_seconds())
    SUBSCRIPTION_NAME = "eqp-values-history-nightly{IMS_CONVERSION_STATUS}-dev"
    SUBSCRIPTION_NAME_IMS = (
        "fleet-values-history-subscription-fm-{IMS_CONVERSION_STATUS}-dev"
    )
else:
    CLUSTER_NAME = "composer-jobs-v3-prod"
    PROJECT_NAME = "management-prod-837a97"
    PROJECT_NAME_IMS = "appraisals-data-prod-707493"
    START_DATE = dt.datetime(2022, 3, 17, tzinfo=LOCAL_TZ)
    SCHEDULE_INTERVAL = timedelta(minutes=15)
    POKE_INTERVAL = int(timedelta(minutes=5).total_seconds())
    SUBSCRIPTION_NAME = "eqp-values-history-nightly{IMS_CONVERSION_STATUS}-prod"
    SUBSCRIPTION_NAME_IMS = (
        "fleet-values-history-subscription-fm-{IMS_CONVERSION_STATUS}-prod"
    )

IMS_STATUS_LEGACY = "legacy"
IMS_STATUS_IMS = "ims"
IMS_STATUS_IMS_PREFERRED = "ims_preferred"
# We will only wait for nightly PubSub message in production
STATUS_RUN_LEGACY = (
    [IMS_STATUS_LEGACY, IMS_STATUS_IMS_PREFERRED]
    if ENVIRONMENT == "prod"
    else [IMS_STATUS_LEGACY]
)
STATUS_RUN_IMS = [IMS_STATUS_IMS, IMS_STATUS_IMS_PREFERRED]

TASK_STATUS = "completed"
PROCESS_IMS = "fleet_manager_valuations_history"
PROCESS_LEGACY = "nightly"


def get_ims_conversion_status_by_env(value):
    status = value.get("ims_conversion_status", IMS_STATUS_LEGACY)

    if ENVIRONMENT == "prod":
        return status

    # We will update legacy clients with fleet_customer_id to a new status,
    #   so we will trigger then based on IMS Refresh PubSub message
    if status == "legacy" and value.get("fleet_customer_id"):
        status = IMS_STATUS_IMS_PREFERRED

    return status


# Client list

Client = collections.namedtuple(
    "Client",
    [
        "sales_code",
        "sales_client_id",
        "ras_code",
        "host_name",
        "is_sharded",
        "ims_conversion_status",
        "fleet_customer_id",
    ],
)

dev_mssql_db_server = "{{ var.value.dev_mssql_gfdev04 }}"

DICT_SHARD_SERVERS = {
    item["client_code"]: item["shard_server"] for item in sharded_clients
}
IMS_ACTIVE_CLIENTS = Variable.get("ims_active_clients", "[]", deserialize_json=True)
ALL_SALES_ACTIVE_CLIENTS = [
    Client(
        sales_code=value["sales_client_code"],
        sales_client_id=value["sales_client_id"],
        ras_code=ras_code,
        host_name=(
            DICT_SHARD_SERVERS.get(ras_code)
            if ENVIRONMENT == "prod"
            else dev_mssql_db_server
        ),
        is_sharded=ras_code in DICT_SHARD_SERVERS if ENVIRONMENT == "prod" else True,
        ims_conversion_status=get_ims_conversion_status_by_env(value),
        fleet_customer_id=str(value.get("fleet_customer_id", "")),
    )
    for ras_code, value in client_mappings.items()
    if "sales_client_code" in value
    and "sales_client_id" in value
    and value.get("sales_pipeline_type", None) == "nightly"
    and (
        DICT_SHARD_SERVERS.get(ras_code, None) is not None
        or ras_code in IMS_ACTIVE_CLIENTS
    )
    and get_ims_conversion_status_by_env(value) != IMS_STATUS_IMS
]

CLIENTS_GROUPED_BY_STATUS = {
    k: list(g)
    for k, g in itertools.groupby(
        sorted(ALL_SALES_ACTIVE_CLIENTS, key=lambda k: k.ims_conversion_status),
        lambda k: k.ims_conversion_status,
    )
}

# default dag args
CUSTOM_ARGS = {
    "owner": "<EMAIL>",
    "retries": 3,
    "retry_delay": timedelta(minutes=5),
    "namespace": NAMESPACE,
    "service_account_name": SERVICE_ACCOUNT_NAME,
    "on_failure_callback": task_fail_slack_alert,
    "weight_rule": "upstream",
}
DEFAULT_ARGS = get_default_args(CUSTOM_ARGS)


DEFAULT_DAG_TAGS = ["equipment", "equipment-values-history", "trigger"]

dag = DAG(
    dag_id=f"trigger-{EXTERNAL_PIPELINE_PREFIX}-dags-{PIPELINE_VERSION}",
    default_args=DEFAULT_ARGS,
    catchup=False,
    max_active_runs=1,
    schedule=SCHEDULE_INTERVAL,
    start_date=START_DATE,
    tags=DEFAULT_DAG_TAGS,
    dagrun_timeout=timedelta(minutes=40),
    on_failure_callback=dag_fail_pagerduty_alert(
        "sales_equipment_values_history_pagerduty_api_key"
    ),
)


def build_external_dag_id(ras_code):
    return f"{EXTERNAL_PIPELINE_PREFIX}-client-{ras_code}-{PIPELINE_VERSION}"


def dag_factory_for_status(ims_conversion_status, clients):
    with dag:

        subscription_name_legacy = SUBSCRIPTION_NAME.format(
            IMS_CONVERSION_STATUS=(
                f"-{ims_conversion_status}" if ims_conversion_status != "legacy" else ""
            )
        )
        subscription_name_ims = SUBSCRIPTION_NAME_IMS.format(
            IMS_CONVERSION_STATUS=ims_conversion_status
        )

        with TaskGroup(group_id=f"status-{ims_conversion_status}") as grp_for_status:

            join_pull_filter = DummyOperator(task_id="join-pull-filter")
            join_end = DummyOperator(task_id="join-end")

            if ims_conversion_status in STATUS_RUN_LEGACY:
                # I set the max_messages to be the amount of clients so that if all clients
                # are finished, the program only does one API call
                # and one dag run.
                # We use soft_fail so that if there are no messages, we don't receive
                # an alarm just because there weren't none.
                pubsub_pusher_task_id = "pubsub-pull-data"
                pubsub_pusher = PubSubRetrieveAllMessages(
                    task_id=pubsub_pusher_task_id,
                    project=PROJECT_NAME,
                    subscription=subscription_name_legacy,
                    max_messages=len(client_mappings) * 2,
                    poke_interval=POKE_INTERVAL,
                    mode="reschedule",
                    timeout=int(SCHEDULE_INTERVAL.total_seconds()),
                    execution_timeout=SCHEDULE_INTERVAL,
                    soft_fail=True,
                    ack_messages=(
                        True if ims_conversion_status == IMS_STATUS_LEGACY else False
                    ),
                )

                pubsub_filter_status_task_id = "pubsub-filter-status"
                pubsub_filter_status = PubSubPullFilterFromListOperator(
                    task_id=pubsub_filter_status_task_id,
                    filter_field="status",
                    value_list=[TASK_STATUS.upper()],
                    project=PROJECT_NAME,
                    subscription=subscription_name_legacy,
                    source_task_id=pubsub_pusher.task_id,
                    trigger_rule="one_success",
                )

                client_code_uppercase = [
                    client_code.upper() for client_code, *_ in clients
                ]

                pubsub_filter_from_list_task_id = "pubsub-filter-missing-clients"
                pubsub_filter_from_list = PubSubPullFilterFromListOperator(
                    task_id=pubsub_filter_from_list_task_id,
                    filter_field="clientcode",
                    value_list=client_code_uppercase,
                    project=PROJECT_NAME,
                    subscription=subscription_name_legacy,
                    source_task_id=pubsub_filter_status.task_id,
                    trigger_rule="one_success",
                )

                (
                    pubsub_pusher
                    >> pubsub_filter_status
                    >> pubsub_filter_from_list
                    >> join_pull_filter
                )

            if ims_conversion_status in STATUS_RUN_IMS:
                pubsub_pusher_ims_task_id = "pubsub-pull-data-ims"
                pubsub_pusher_ims = PubSubRetrieveAllMessages(
                    task_id=pubsub_pusher_ims_task_id,
                    project=PROJECT_NAME_IMS,
                    subscription=subscription_name_ims,
                    max_messages=len(client_mappings) * 2,
                    poke_interval=POKE_INTERVAL,
                    mode="reschedule",
                    timeout=int(SCHEDULE_INTERVAL.total_seconds()),
                    execution_timeout=SCHEDULE_INTERVAL,
                    soft_fail=True,
                )

                pubsub_filter_status_ims_task_id = "pubsub-filter-status-ims"
                pubsub_filter_status_ims = PubSubPullFilterFromListOperator(
                    task_id=pubsub_filter_status_ims_task_id,
                    filter_field="status",
                    value_list=[TASK_STATUS.upper()],
                    project=PROJECT_NAME_IMS,
                    subscription=subscription_name_ims,
                    source_task_id=pubsub_pusher_ims.task_id,
                    trigger_rule="one_success",
                )

                fleet_customer_ids = [
                    fleet_customer_id for *_, fleet_customer_id in clients
                ]

                pubsub_filter_from_list_ims_task_id = (
                    "pubsub-filter-missing-clients-ims"
                )
                pubsub_filter_from_list_ims = PubSubPullFilterFromListOperator(
                    task_id=pubsub_filter_from_list_ims_task_id,
                    filter_field="fleet_customer_id",
                    value_list=fleet_customer_ids,
                    project=PROJECT_NAME_IMS,
                    subscription=subscription_name_ims,
                    source_task_id=pubsub_filter_status_ims.task_id,
                    trigger_rule="one_success",
                )

                (
                    pubsub_pusher_ims
                    >> pubsub_filter_status_ims
                    >> pubsub_filter_from_list_ims
                )

                if ims_conversion_status == IMS_STATUS_IMS:
                    ack_message_ims = PubSubAckOperator(
                        task_id=f"ack-message-ims",
                        project_name=PROJECT_NAME_IMS,
                        subscription_id=subscription_name_ims,
                        source_task_id=pubsub_filter_from_list_ims.task_id,
                    )

                    pubsub_filter_from_list_ims >> ack_message_ims >> join_pull_filter
                else:
                    pubsub_filter_from_list_ims >> join_pull_filter

            def dag_factory(
                sales_code,
                client_id,
                ras_code,
                host_name,
                is_sharded,
                ims_conversion_status,
                fleet_customer_id,
            ):
                external_dag_id = build_external_dag_id(ras_code)

                with TaskGroup(group_id=f"group-{ras_code}") as grp_for_client:

                    trigger_pipeline = TriggerDagRunOperator(
                        task_id=f"launch-{external_dag_id}",
                        trigger_dag_id=external_dag_id,
                        execution_timeout=timedelta(minutes=10),
                    )

                    if ims_conversion_status in STATUS_RUN_LEGACY:
                        pubsub_filter_id = f"pubsub-filter-{ras_code}"
                        pubsub_filter = PubSubExcludeOperator(
                            task_id=pubsub_filter_id,
                            matches={
                                "clientcode": sales_code.upper(),
                                "status": TASK_STATUS,
                                "process": PROCESS_LEGACY,
                            },
                            no_matches={},
                            source_task_id=pubsub_filter_from_list.task_id,
                            execution_timeout=timedelta(minutes=10),
                        )

                        ack_message = PubSubAckOperator(
                            task_id=f"ack-message-nightly-{ras_code}",
                            project_name=PROJECT_NAME,
                            subscription_id=subscription_name_legacy,
                            source_task_id=pubsub_filter.task_id,
                        )

                        pubsub_filter >> trigger_pipeline >> ack_message

                    if ims_conversion_status in STATUS_RUN_IMS:
                        pubsub_filter_ims_id = f"pubsub-filter-ims-{ras_code}"
                        pubsub_filter_ims = PubSubExcludeOperator(
                            task_id=pubsub_filter_ims_id,
                            matches={
                                "fleet_customer_id": fleet_customer_id,
                                "status": TASK_STATUS,
                                "process": PROCESS_IMS,
                                "env": CLUSTER_NAME,
                            },
                            no_matches={},
                            source_task_id=pubsub_filter_from_list_ims.task_id,
                            execution_timeout=timedelta(minutes=10),
                        )
                        pubsub_filter_ims >> trigger_pipeline
                        if ims_conversion_status != IMS_STATUS_IMS:
                            ack_message_ims_client = PubSubAckOperator(
                                task_id=f"ack-message-ims-{ras_code}",
                                project_name=PROJECT_NAME_IMS,
                                subscription_id=subscription_name_ims,
                                source_task_id=pubsub_filter_ims.task_id,
                            )

                            trigger_pipeline >> ack_message_ims_client

                    join_pull_filter >> grp_for_client >> join_end

            # render shard DAGs
            for client in clients:
                dag_factory(*client)


for ims_conversion_status, clients in CLIENTS_GROUPED_BY_STATUS.items():
    dag_factory_for_status(ims_conversion_status, clients)


# ALL CLIENTS TRIGGER DAG
with DAG(
    dag_id=f"trigger-{EXTERNAL_PIPELINE_PREFIX}-all-clients-{PIPELINE_VERSION}",
    default_args=DEFAULT_ARGS,
    catchup=False,
    max_active_runs=1,
    schedule=None,
    start_date=START_DATE,
    tags=DEFAULT_DAG_TAGS,
) as trigger_all_clients_dag:

    def dag_factory(
        sales_code,
        client_id,
        ras_code,
        host_name,
        is_sharded,
        ims_conversion_status,
        fleet_customer_id,
    ):
        external_dag_id = build_external_dag_id(ras_code)
        TriggerDagRunOperator(
            task_id="trigger-" + external_dag_id,
            trigger_dag_id=external_dag_id,
            execution_date="{{ logical_date }}",
            dag=trigger_all_clients_dag,
            execution_timeout=dt.timedelta(minutes=180),
        )

    # DAGs
    for client in ALL_SALES_ACTIVE_CLIENTS:
        dag_factory(*client)
