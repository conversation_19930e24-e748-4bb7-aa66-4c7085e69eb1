import collections
import datetime as dt
import os
from pathlib import Path

import pendulum
from airflow import DAG
from airflow.kubernetes.secret import Secret
from airflow.models import Variable
from airflow.providers.google.cloud.operators.kubernetes_engine import (
    GKEStartPodOperator,
)
from airflow.operators.dummy import DummyOperator

from client_projects_constants import client_mappings
from sharding_constants import sharded_clients
from shared_libs.default_args import get_default_args, get_ssl_pgbouncer_secrets
from shared_libs.image_versions import get_full_image_name, get_gcr_registry
from shared_libs.kubernetes import get_image_pull_policy
from shared_libs.slack_callback import task_fail_slack_alert
from shared_libs.pagerduty_callback import dag_fail_pagerduty_alert
from postgres_config import pg_db_shard_servers
from fleet_manager_sharding_constants import sharded_clients_by_fleet_customer_id
from kubernetes.client import models as k8s
from airflow_datasets import RouseDatasets


environment = os.environ.get("COMPOSER_ENVIRONMENT", "dev")
gcr_registry = get_gcr_registry(environment)
image_pull_policy = get_image_pull_policy(environment)

VERSION_MAJOR = "1"
VERSION_MINOR = "4"
version_suffix = f"v{VERSION_MAJOR}.{VERSION_MINOR}"

IMS_STATUS_LEGACY = "legacy"
IMS_STATUS_IMS = "ims"
IMS_STATUS_IMS_PREFERRED = "ims_preferred"

# global environment settings
cluster_zone = "us-central1-b"
resources = k8s.V1ResourceRequirements(
    requests={"cpu": "500m", "memory": "1.8Gi"},
    limits={"cpu": "800m", "memory": "2.5Gi"},
)

RESOURCES_SMALL_TASK = k8s.V1ResourceRequirements(
    requests={"cpu": "500m", "memory": "0.3Gi"},
)

version = "{{ logical_date.strftime('%Y%m%d%H%M%S') }}"
upper_window = "{{ logical_date.strftime('%Y-%m-%d %H:%M:%S') }}"
upper_window_xcom_key = "upper_window"
local_tz = pendulum.timezone("America/Los_Angeles")

years = range(2018, dt.datetime.now().astimezone(local_tz).year + 1)
history_years = {}
history_detailed_years = {}
bq_to_pg_years = {}

namespace = "equipment-values-history-etl"
service_account_name = namespace

table_retention_days = Variable.get("rfm01_pg_retention_days")

# per envionment settings
# Development & Local# dev / local

if environment != "prod":
    project_name = "management-dev-d6ba4d"
    cluster_name = "composer-jobs-v3-dev"
    db_secret_name = "de-eqp-values-history-dev"
    pg_db_secret_name = "de-pg-eqp-values-history-dev"
    start_date = dt.datetime(2022, 1, 12, tzinfo=local_tz)
    schedule = None
    appraisals_project_id = "appraisals-data-dev-c55fa4"
    rfm_project = "rfm-dev-1e0721"

else:  # PRODUCTION
    project_name = "management-prod-837a97"
    cluster_name = "composer-jobs-v3-prod"
    db_secret_name = "de-eqp-values-history-prod"
    pg_db_secret_name = "de-pg-eqp-values-history-prod"
    start_date = dt.datetime(2022, 2, 7, tzinfo=local_tz)
    schedule = None
    appraisals_project_id = "appraisals-data-prod-707493"
    rfm_project = "rfm-prod-5f68c8"

pool_name = "equipment-values-history-cloud-sql"

default_server_name = "pg_bouncer"
engine_config = Variable.get("postgres_engine_configuration", {}, deserialize_json=True)
server_name = engine_config.get("server", default_server_name)

# client list
Client = collections.namedtuple(
    "Client",
    [
        "sales_code",
        "sales_client_id",
        "ras_code",
        "host_name",
        "client_version",
        "ims_conversion_status",
        "fleet_customer_id",
        "pg_db_server_name",
        "local_environment",
    ],
)

dev_mssql_db_server = "{{ var.value.dev_mssql_gfdev04 }}"

dict_shard_servers = {
    item["client_code"]: item["shard_server"] for item in sharded_clients
}
IMS_ACTIVE_CLIENTS = Variable.get("ims_active_clients", "[]", deserialize_json=True)
PPE_ACTIVE_CLIENTS = Variable.get("ppe_active_clients", "[]", deserialize_json=True)

all_sales_active_clients = [
    Client(
        sales_code=value["sales_client_code"],
        sales_client_id=value["sales_client_id"],
        ras_code=ras_code,
        host_name=(
            dict_shard_servers.get(ras_code)
            if environment == "prod"
            else dev_mssql_db_server
        ),
        client_version=value["client_version"],
        ims_conversion_status=value.get("ims_conversion_status", IMS_STATUS_LEGACY),
        fleet_customer_id=value.get("fleet_customer_id"),
        pg_db_server_name=(
            sharded_clients_by_fleet_customer_id.get(
                str(value.get("fleet_customer_id", "null")), {}
            ).get("shard", "rfm02")
            if server_name == default_server_name
            else server_name
        ),
        local_environment=environment,
    )
    for ras_code, value in client_mappings.items()
    if "sales_client_code" in value
    and "sales_client_id" in value
    and value.get("sales_pipeline_type", None) == "nightly"
    and (
        dict_shard_servers.get(ras_code, None) is not None
        or ras_code in IMS_ACTIVE_CLIENTS
    )
]

all_sales_active_clients.append(
    Client(
        sales_code="multi",
        sales_client_id=10000,
        ras_code="multi",
        host_name="",
        client_version=1,
        ims_conversion_status="ims",
        fleet_customer_id=10000,
        pg_db_server_name="rfm03",
        local_environment=environment,
    )
)

all_ppe_active_clients = [
    Client(
        sales_code=value["sales_client_code"],
        sales_client_id=value["sales_client_id"],
        ras_code=ras_code,
        host_name=(
            dict_shard_servers.get(ras_code)
            if environment == "prod"
            else dev_mssql_db_server
        ),
        client_version=value["client_version"],
        ims_conversion_status=value.get("ims_conversion_status", IMS_STATUS_LEGACY),
        fleet_customer_id=value.get("fleet_customer_id"),
        pg_db_server_name="rfm01_ppe",
        local_environment="ppe",
    )
    for ras_code, value in client_mappings.items()
    if "sales_client_code" in value
    and "sales_client_id" in value
    and value.get("sales_pipeline_type", None) == "nightly"
    and ras_code in PPE_ACTIVE_CLIENTS
    and environment == "prod"
]

DAG_PREFIX_NAME_FLEET_MANAGER = "equipment-values-history-fm"


# default dag args
custom_args = {
    "start_date": start_date,
    "retries": 3,
    "retry_delay": dt.timedelta(minutes=5),
    "retry_exponential_backoff": True,
    "max_retry_delay": dt.timedelta(minutes=30),
    "startup_timeout_seconds": 600,
    "namespace": namespace,
    "service_account_name": service_account_name,
    "depends_on_past": False,
    "image": get_full_image_name("equipment-values-history-etl", gcr_registry),
    "owner": "<EMAIL>",
    "cluster_name": cluster_name,
    "project_id": project_name,
    "on_failure_callback": task_fail_slack_alert,
}
default_args = get_default_args(custom_args)

# secrets/passwords
secret_db_username = Secret(
    deploy_type="env", deploy_target="WIN_USER", secret=db_secret_name, key="username"
)

secret_db_password = Secret(
    deploy_type="env",
    deploy_target="WIN_PASSWORD",
    secret=db_secret_name,
    key="password",
)

secret_pg_db_username = Secret(
    deploy_type="env", deploy_target="PG_USER", secret=pg_db_secret_name, key="username"
)

secret_pg_db_password = Secret(
    deploy_type="env",
    deploy_target="PG_PASSWORD",
    secret=pg_db_secret_name,
    key="password",
)

secrets_pg_db = [
    secret_pg_db_username,
    secret_pg_db_password,
] + get_ssl_pgbouncer_secrets(environment)


default_dag_tags = ["equipment", "equipment-values-history"]

# Datasets
dataset_id_envs = {"dev": "_dev", "local": "_dev", "prod": "", "ppe": "_ppe"}

FLEET_VALUATIONS_HISTORY_TABLE_NAME = "fleet_valuations_history"
LEGACY_TABLE_NAME = "equipment_values_history"
TABLE_NAME = "equipment_values_history_fm"
BACKFILL_TABLE_NAME = "equipment_values_history_backfill_fm"
ROLE_NAME = "equipment_values_history"
BQ_TABLE_EXPIRATION_DAYS = "null"
pg_table_retention_versions = "{{ var.value.rfm01_pg_retention_days }}"


# SQL statments
PWD = Path(os.path.dirname(os.path.realpath(__file__)))
SQL_FOLDER = os.path.join(PWD, "dag_file_dependencies")


def get_query(query_file, variable_dict):
    with open(os.path.join(SQL_FOLDER, query_file)) as f:
        query = f.read()
    query = query.format(**variable_dict)
    return query


def check_status_legacy(ims_conversion_status, fleet_customer_id):
    return (
        ims_conversion_status == IMS_STATUS_IMS_PREFERRED and fleet_customer_id
    ) or (ims_conversion_status == IMS_STATUS_LEGACY)


def check_status_ims(ims_conversion_status, fleet_customer_id):
    return (
        ims_conversion_status in [IMS_STATUS_IMS, IMS_STATUS_IMS_PREFERRED]
        and fleet_customer_id
    )


def create_dag(
    dag_id,
    ras_code,
    sales_code,
    client_id,
    host_name,
    client_version,
    ims_conversion_status,
    fleet_customer_id,
    pg_db_server,
    use_cert,
    pg_db_name,
    local_environment,
):
    if ras_code != "multi":
        client_project = f"rs-client-{ras_code}-{client_version}"
    else:
        client_project = rfm_project
    start_fm_task_id = "start_fm_tasks"

    VERSION_DATASET_NAME = (
        f"equipment_values_history_version{dataset_id_envs[local_environment]}"
    )
    STAGE_DATASET_NAME = (
        f"equipment_values_history_stage{dataset_id_envs[local_environment]}"
    )
    DATASET_NAME = f"equipment_values_history{dataset_id_envs[local_environment]}"
    FLEET_VALUATIONS_HISTORY_DATASET_NAME = (
        f"fleet_valuations_history{dataset_id_envs[local_environment]}"
    )
    extra_tags = [f"client-id-{client_id}", f"client-code-{sales_code}"]
    if fleet_customer_id:
        extra_tags.append(f"fleet-customer-id-{fleet_customer_id}")

    if ras_code == "multi":
        schedule_dataset = [
            RouseDatasets.fleet_manager_pg_export(fleet_customer_id=fleet_customer_id)
        ]
    elif local_environment == environment:
        schedule_dataset = [
            RouseDatasets.fleet_manager_valuations_history(
                fleet_customer_id=fleet_customer_id
            )
        ]
    else:
        schedule_dataset = [
            RouseDatasets.fleet_manager_valuations_history_ppe(
                fleet_customer_id=fleet_customer_id
            )
        ]

    dag = DAG(
        dag_id,
        default_args=default_args,
        description="Export data from SQL Server to PostgreSQL",
        schedule=schedule_dataset,
        catchup=False,
        dagrun_timeout=dt.timedelta(minutes=540),
        max_active_runs=1,
        tags=default_dag_tags + extra_tags,
        on_failure_callback=dag_fail_pagerduty_alert(
            "sales_equipment_values_history_pagerduty_api_key"
        ),
    )

    pg_env_post_id = "equipment-values-history-pg-env-post"
    pg_env_post_env = {
        "VERSION": version,
        "ENV": local_environment,
        "CLIENT_CODE": ras_code,
        "CLIENT_VERSION": client_version,
        "PROJECT_ID": client_project,
        "RETENTION_PERIOD": table_retention_days,
        "LEGACY_TABLE_EXISTS": (
            "False" if ims_conversion_status == IMS_STATUS_IMS else "True"
        ),
        "DB_SERVER": pg_db_server,
        "USE_PG_CERT": use_cert,
        "PG_DB": pg_db_name,
    }
    if fleet_customer_id:
        pg_env_post_env["FLEET_CUSTOMER_ID"] = str(fleet_customer_id)
        pg_env_post_env["FM_TABLE_EXISTS"] = str(True)

    pg_env_post = GKEStartPodOperator(
        task_id=pg_env_post_id,
        name=pg_env_post_id,
        dag=dag,
        resources=resources,
        execution_timeout=dt.timedelta(minutes=180),
        arguments=[
            "python3",
            "-m",
            "equipment_values_history_etl.cli",
            "pg-env-post",
        ],
        secrets=secrets_pg_db,
        env_vars=pg_env_post_env,
        get_logs=True,
        trigger_rule="none_failed_or_skipped",
        on_failure_callback=task_fail_slack_alert,
        outlets=(
            RouseDatasets.equipment_values_history_etl(fleet_customer_id)
            if ras_code == "etc" and local_environment == environment
            else None
        ),
    )

    if check_status_legacy(ims_conversion_status, fleet_customer_id):
        mod_years_history_id = "evh-equipment-history-mod-years"
        mod_years_history = GKEStartPodOperator(
            task_id=mod_years_history_id,
            name=mod_years_history_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=360),
            do_xcom_push=True,
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "get-modified-years",
                "--table-name=equipment_values_history",
            ],
            secrets=[secret_db_username, secret_db_password],
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_ID": str(client_id),
                "CLIENT_CODE": ras_code,
                "SQL_HOST": host_name,
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        mssql_to_gcs_adjustment_factors_id = "evh-adjustments-mssql2gcs"
        mssql_to_gcs_adjustment_factors = GKEStartPodOperator(
            task_id=mssql_to_gcs_adjustment_factors_id,
            name=mssql_to_gcs_adjustment_factors_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=120),
            do_xcom_push=True,
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "mssql2gcs",
                "--table-name=adjustment_factors",
            ],
            secrets=[secret_db_username, secret_db_password],
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_ID": str(client_id),
                "SALES_CODE": sales_code,
                "CLIENT_CODE": ras_code,
                "SQL_HOST": host_name,
                "YEARS_FOUND": f"{{{{ task_instance.xcom_pull("
                f'task_ids="{mod_years_history_id}"'
                f')["years"] }}}}',
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        gcs_to_bq_adjustment_factors_id = "evh-adjustments-gcs2bq"
        gcs_to_bq_adjustment_factors = GKEStartPodOperator(
            task_id=gcs_to_bq_adjustment_factors_id,
            name=gcs_to_bq_adjustment_factors_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=120),
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "gcs2bq",
                "--table-name=adjustment_factors",
            ],
            secrets=[secret_db_username, secret_db_password],
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_ID": str(client_id),
                "CLIENT_CODE": ras_code,
                "SQL_HOST": host_name,
                "METRICS": f"{{{{ task_instance.xcom_pull("
                f'task_ids="{mssql_to_gcs_adjustment_factors_id}"'
                f')["metrics"] }}}}',
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        mssql_qa_check_adjustment_factors_id = "evh-adjustment-factors-mssql-bq-qa"
        mssql_qa_check_adjustment_factors = GKEStartPodOperator(
            task_id=mssql_qa_check_adjustment_factors_id,
            name=mssql_qa_check_adjustment_factors_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=30),
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "qa",
                "--table-name=adjustment_factors",
            ],
            env_vars={
                "ENV": local_environment,
                "VERSION": version,
                "METRICS": f"{{{{ task_instance.xcom_pull("
                f'task_ids="{mssql_to_gcs_adjustment_factors_id}"'
                f')["metrics"] }}}}',
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        mssql_to_gcs_field_version_id = "evh-field-version-mssql2gcs"
        mssql_to_gcs_field_version = GKEStartPodOperator(
            task_id=mssql_to_gcs_field_version_id,
            name=mssql_to_gcs_field_version_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=120),
            do_xcom_push=True,
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "mssql2gcs",
                "--table-name=field_version",
            ],
            secrets=[secret_db_username, secret_db_password],
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_ID": str(client_id),
                "SALES_CODE": sales_code,
                "CLIENT_CODE": ras_code,
                "SQL_HOST": host_name,
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        gcs_to_bq_field_version_id = "evh-field-version-gcs2bq"
        gcs_to_bq_field_version = GKEStartPodOperator(
            task_id=gcs_to_bq_field_version_id,
            name=gcs_to_bq_field_version_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=120),
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "gcs2bq",
                "--table-name=field_version",
            ],
            secrets=[secret_db_username, secret_db_password],
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_ID": str(client_id),
                "CLIENT_CODE": ras_code,
                "SQL_HOST": host_name,
                "METRICS": f"{{{{ task_instance.xcom_pull("
                f'task_ids="{mssql_to_gcs_field_version_id}"'
                f')["metrics"] }}}}',
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        mssql_qa_check_field_version_id = "evh-field-version-mssql-bq-qa"
        mssql_qa_check_field_version = GKEStartPodOperator(
            task_id=mssql_qa_check_field_version_id,
            name=mssql_qa_check_field_version_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=30),
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "qa",
                "--table-name=field_version",
            ],
            env_vars={
                "ENV": local_environment,
                "VERSION": version,
                "METRICS": f"{{{{ task_instance.xcom_pull("
                f'task_ids="{mssql_to_gcs_field_version_id}"'
                f')["metrics"] }}}}',
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        for year in reversed(years):
            mssql_to_gcs_history_id = f"evh-equipment-history-mssql2gcs-{year}"
            mssql_to_gcs_history = GKEStartPodOperator(
                task_id=mssql_to_gcs_history_id,
                name=mssql_to_gcs_history_id,
                dag=dag,
                resources=resources,
                execution_timeout=dt.timedelta(minutes=360),
                do_xcom_push=True,
                arguments=[
                    "python3",
                    "-m",
                    "equipment_values_history_etl.cli",
                    "mssql2gcs",
                    "--table-name=equipment_values_history",
                ],
                secrets=[secret_db_username, secret_db_password],
                env_vars={
                    "VERSION": version,
                    "ENV": local_environment,
                    "CLIENT_ID": str(client_id),
                    "SALES_CODE": sales_code,
                    "CLIENT_CODE": ras_code,
                    "SQL_HOST": host_name,
                    "YEAR": str(year),
                    "YEARS_FOUND": f"{{{{ task_instance.xcom_pull("
                    f'task_ids="{mod_years_history_id}"'
                    f')["years"] }}}}',
                    "CLIENT_VERSION": client_version,
                    "PROJECT_ID": client_project,
                },
                get_logs=True,
                on_failure_callback=task_fail_slack_alert,
            )
            history_years[mssql_to_gcs_history_id] = mssql_to_gcs_history

            mssql_to_gcs_history_detailed_id = (
                "evh-history-detailed-" f"mssql2gcs-{year}"
            )
            mssql_to_gcs_history_detailed = GKEStartPodOperator(
                task_id=mssql_to_gcs_history_detailed_id,
                name=mssql_to_gcs_history_detailed_id,
                dag=dag,
                resources=resources,
                execution_timeout=dt.timedelta(minutes=360),
                do_xcom_push=True,
                arguments=[
                    "python3",
                    "-m",
                    "equipment_values_history_etl.cli",
                    "mssql2gcs",
                    "--table-name=equipment_history_detailed",
                ],
                secrets=[secret_db_username, secret_db_password],
                env_vars={
                    "VERSION": version,
                    "ENV": local_environment,
                    "CLIENT_ID": str(client_id),
                    "SALES_CODE": sales_code,
                    "CLIENT_CODE": ras_code,
                    "SQL_HOST": host_name,
                    "YEAR": str(year),
                    "YEARS_FOUND": f"{{{{ task_instance.xcom_pull("
                    f'task_ids="{mod_years_history_id}"'
                    f')["years"] }}}}',
                    "CLIENT_VERSION": client_version,
                    "PROJECT_ID": client_project,
                },
                get_logs=True,
                on_failure_callback=task_fail_slack_alert,
            )
            history_detailed_years[mssql_to_gcs_history_detailed_id] = (
                mssql_to_gcs_history_detailed
            )

            bq_to_pg_id = f"equipment-values-history-bq2pg-{year}"
            bq_to_pg = GKEStartPodOperator(
                task_id=bq_to_pg_id,
                name=bq_to_pg_id,
                dag=dag,
                resources=resources,
                execution_timeout=dt.timedelta(minutes=180),
                arguments=[
                    "python3",
                    "-m",
                    "equipment_values_history_etl.cli",
                    "bq2pg",
                ],
                secrets=secrets_pg_db,
                env_vars={
                    "VERSION": version,
                    "ENV": local_environment,
                    "CLIENT_CODE": ras_code,
                    "SALES_CODE": sales_code,
                    "YEAR": str(year),
                    "YEARS_FOUND": f"{{{{ task_instance.xcom_pull("
                    f'task_ids="{mod_years_history_id}"'
                    f')["years"] }}}}',
                    "CLIENT_VERSION": client_version,
                    "PROJECT_ID": client_project,
                    "RETENTION_PERIOD": table_retention_days,
                    "TABLE_NAME": "equipment_values_history",
                    "DB_SERVER": pg_db_server,
                    "USE_PG_CERT": use_cert,
                    "PG_DB": pg_db_name,
                },
                get_logs=True,
                on_failure_callback=task_fail_slack_alert,
            )
            bq_to_pg_years[bq_to_pg_id] = bq_to_pg

        gcs_to_bq_history_id = "evh-equipment-history-gcs2bq"
        gcs_to_bq_history = GKEStartPodOperator(
            task_id=gcs_to_bq_history_id,
            name=gcs_to_bq_history_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=360),
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "gcs2bq",
                "--table-name=equipment_values_history",
            ],
            secrets=[secret_db_username, secret_db_password],
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_ID": str(client_id),
                "CLIENT_CODE": ras_code,
                "SQL_HOST": host_name,
                "METRICS": f"{{{{ task_instance.xcom_pull("
                f"task_ids={list(history_years.keys())}"
                f") }}}}",
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        mssql_qa_check_history_id = "evh-equipment-history-mssql-bq-qa"
        mssql_qa_check_history = GKEStartPodOperator(
            task_id=mssql_qa_check_history_id,
            name=mssql_qa_check_history_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=30),
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "qa",
                "--table-name=equipment_values_history",
            ],
            env_vars={
                "ENV": local_environment,
                "VERSION": version,
                "METRICS": f"{{{{ task_instance.xcom_pull("
                f"task_ids={list(history_years.keys())}"
                f") }}}}",
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        gcs_to_bq_history_detailed_id = "evh-history-detailed-gcs2bq"
        gcs_to_bq_history_detailed = GKEStartPodOperator(
            task_id=gcs_to_bq_history_detailed_id,
            name=gcs_to_bq_history_detailed_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=120),
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "gcs2bq",
                "--table-name=equipment_history_detailed",
            ],
            secrets=[secret_db_username, secret_db_password],
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_ID": str(client_id),
                "CLIENT_CODE": ras_code,
                "SQL_HOST": host_name,
                "METRICS": f"{{{{ task_instance.xcom_pull("
                f"task_ids={list(history_detailed_years.keys())}"
                f")}}}}",
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        mssql_qa_check_history_detailed_id = "evh-history-detailed-mssql-bq-qa"
        mssql_qa_check_history_detailed = GKEStartPodOperator(
            task_id=mssql_qa_check_history_detailed_id,
            name=mssql_qa_check_history_detailed_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=30),
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "qa",
                "--table-name=equipment_history_detailed",
            ],
            env_vars={
                "ENV": local_environment,
                "VERSION": version,
                "METRICS": f"{{{{ task_instance.xcom_pull("
                f"task_ids={list(history_detailed_years.keys())}"
                f")}}}}",
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        mssql_to_gcs_history_no_version_id = "evh-history-no-version-mssql2gcs"
        mssql_to_gcs_history_no_version = GKEStartPodOperator(
            task_id=mssql_to_gcs_history_no_version_id,
            name=mssql_to_gcs_history_no_version_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=120),
            do_xcom_push=True,
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "mssql2gcs",
                "--table-name=equipment_values_history_no_version",
            ],
            secrets=[secret_db_username, secret_db_password],
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_ID": str(client_id),
                "SALES_CODE": sales_code,
                "CLIENT_CODE": ras_code,
                "SQL_HOST": host_name,
                "YEARS_FOUND": f"{{{{ task_instance.xcom_pull("
                f'task_ids="{mod_years_history_id}"'
                f')["years"] }}}}',
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        gcs_to_bq_history_no_version_id = "evh-history-no-version-gcs2bq"
        gcs_to_bq_history_no_version = GKEStartPodOperator(
            task_id=gcs_to_bq_history_no_version_id,
            name=gcs_to_bq_history_no_version_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=120),
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "gcs2bq",
                "--table-name=equipment_values_history_no_version",
            ],
            secrets=[secret_db_username, secret_db_password],
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_ID": str(client_id),
                "CLIENT_CODE": ras_code,
                "SQL_HOST": host_name,
                "METRICS": f"{{{{ task_instance.xcom_pull("
                f'task_ids="{mssql_to_gcs_history_no_version_id}"'
                f')["metrics"] }}}}',
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        mssql_qa_check_history_no_version_id = "evh-no-version-mssql-bq-qa"
        mssql_qa_check_history_no_version = GKEStartPodOperator(
            task_id=mssql_qa_check_history_no_version_id,
            name=mssql_qa_check_history_no_version_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=30),
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "qa",
                "--table-name=equipment_values_history_no_version",
            ],
            env_vars={
                "ENV": local_environment,
                "VERSION": version,
                "METRICS": f"{{{{ task_instance.xcom_pull("
                f'task_ids="{mssql_to_gcs_history_no_version_id}"'
                f')["metrics"] }}}}',
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        mssql_to_gcs_initial_valuations_id = "evh-initial-valuations-mssql2gcs"
        mssql_to_gcs_initial_valuations = GKEStartPodOperator(
            task_id=mssql_to_gcs_initial_valuations_id,
            name=mssql_to_gcs_initial_valuations_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=120),
            do_xcom_push=True,
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "mssql2gcs",
                "--table-name=initial_valuations",
            ],
            secrets=[secret_db_username, secret_db_password],
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_ID": str(client_id),
                "SALES_CODE": sales_code,
                "CLIENT_CODE": ras_code,
                "SQL_HOST": host_name,
                "YEARS_FOUND": f"{{{{ task_instance.xcom_pull("
                f'task_ids="{mod_years_history_id}"'
                f')["years"] }}}}',
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        gcs_to_bq_initial_valuations_id = "evh-initial-valuations-gcs2bq"
        gcs_to_bq_initial_valuations = GKEStartPodOperator(
            task_id=gcs_to_bq_initial_valuations_id,
            name=gcs_to_bq_initial_valuations_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=120),
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "gcs2bq",
                "--table-name=initial_valuations",
            ],
            secrets=[secret_db_username, secret_db_password],
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_ID": str(client_id),
                "CLIENT_CODE": ras_code,
                "SQL_HOST": host_name,
                "METRICS": f"{{{{ task_instance.xcom_pull("
                f'task_ids="{mssql_to_gcs_initial_valuations_id}"'
                f')["metrics"] }}}}',
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        mssql_qa_check_initial_valuations_id = "evh-initial-valuations-mssql-bq-qa"
        mssql_qa_check_initial_valuations = GKEStartPodOperator(
            task_id=mssql_qa_check_initial_valuations_id,
            name=mssql_qa_check_initial_valuations_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=30),
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "qa",
                "--table-name=initial_valuations",
            ],
            env_vars={
                "ENV": local_environment,
                "VERSION": version,
                "METRICS": f"{{{{ task_instance.xcom_pull("
                f'task_ids="{mssql_to_gcs_initial_valuations_id}"'
                f')["metrics"] }}}}',
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        bq_to_bq_id = "evh-equipment-values-history-bq2bq"
        bq_to_bq = GKEStartPodOperator(
            task_id=bq_to_bq_id,
            name=bq_to_bq_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=100),
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "bqquery2bq",
                "--table-name=equipment_values_history",
            ],
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_ID": str(client_id),
                "SALES_CODE": sales_code,
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        qa_check_id = "evh-equipment-values-history-qa"
        qa_check = GKEStartPodOperator(
            task_id=qa_check_id,
            name=qa_check_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=30),
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "qa-table",
            ],
            env_vars={
                "ENV": local_environment,
                "VERSION": version,
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        pg_env_id = "equipment-values-history-pg-env"
        pg_env = GKEStartPodOperator(
            task_id=pg_env_id,
            name=pg_env_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=180),
            arguments=["python3", "-m", "equipment_values_history_etl.cli", "pg-env"],
            secrets=secrets_pg_db,
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
                "TABLE_NAME": "equipment_values_history",
                "DB_SERVER": pg_db_server,
                "USE_PG_CERT": use_cert,
                "PG_DB": pg_db_name,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        qa_partition_id = "equipment-values-history-qa-partition"
        qa_partition = GKEStartPodOperator(
            task_id=qa_partition_id,
            name=qa_partition_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=180),
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "qa-partitions",
            ],
            secrets=secrets_pg_db,
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
                "TABLE_NAME": "equipment_values_history",
                "DB_SERVER": pg_db_server,
                "USE_PG_CERT": use_cert,
                "PG_DB": pg_db_name,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )
        (
            [
                mssql_qa_check_history,
                mssql_qa_check_adjustment_factors,
                mssql_qa_check_field_version,
                mssql_qa_check_history_no_version,
                mssql_qa_check_initial_valuations,
                mssql_qa_check_history_detailed,
            ]
            >> bq_to_bq
            >> qa_check
            >> pg_env
        )

        mod_years_history >> [
            mssql_to_gcs_history_no_version,
            mssql_to_gcs_initial_valuations,
            mssql_to_gcs_adjustment_factors,
        ]

        last_task = None
        previous_task = None
        for task_name in bq_to_pg_years.keys():
            current_task = bq_to_pg_years[task_name]
            if previous_task:
                current_task >> previous_task
            else:
                last_task = current_task
            previous_task = current_task

        pg_env >> previous_task
        last_task >> pg_env_post >> qa_partition
        (
            mod_years_history
            >> list(history_years.values())
            >> gcs_to_bq_history
            >> mssql_qa_check_history
        )
        (
            mssql_to_gcs_adjustment_factors
            >> gcs_to_bq_adjustment_factors
            >> mssql_qa_check_adjustment_factors
        )
        (
            mssql_to_gcs_field_version
            >> gcs_to_bq_field_version
            >> mssql_qa_check_field_version
        )
        (
            mssql_to_gcs_history_no_version
            >> gcs_to_bq_history_no_version
            >> mssql_qa_check_history_no_version
        )
        (
            mssql_to_gcs_initial_valuations
            >> gcs_to_bq_initial_valuations
            >> mssql_qa_check_initial_valuations
        )
        (
            mod_years_history
            >> list(history_detailed_years.values())
            >> gcs_to_bq_history_detailed
            >> mssql_qa_check_history_detailed
        )

    # Fleet manager tasks

    if fleet_customer_id is not None:
        start_fm_task = DummyOperator(dag=dag, task_id=start_fm_task_id)

        variable_dict = {
            "FLEET_CUSTOMER_ID": fleet_customer_id,
            "SALES_CLIENT_CODE": sales_code,
            "VERSION_DATASET_NAME": VERSION_DATASET_NAME,
            "VERSION_ID": version,
            "BQ_TABLE_EXPIRATION_DAYS": BQ_TABLE_EXPIRATION_DAYS,
            "CLIENT_PROJECT_ID": client_project,
            "TABLE_NAME": TABLE_NAME,
            "TABLE_NAME_BACKFILL": BACKFILL_TABLE_NAME,
            "ENV_SUFFIX": dataset_id_envs[local_environment],
            "MAIN_ENV_SUFFIX": dataset_id_envs[environment],
            "APPRAISALS_PROJECT_ID": appraisals_project_id,
            "STAGE_DATASET_NAME": STAGE_DATASET_NAME,
            "DATASET_NAME": DATASET_NAME,
            "PG_TABLE": f"{TABLE_NAME}_{version}",
            "PG_SCHEMA": f"rfm_{ras_code}_version",
            "PG_VIEW_SCHEMA": f"rfm_{ras_code}",
            "PG_VIEW": TABLE_NAME,
            "ROLE_NAME": ROLE_NAME,
            "RETENTION": pg_table_retention_versions,
            "REPLACE": "",
            "FILTER": "",
            "PROJECT_ID": client_project,
            "DATASET_ID": DATASET_NAME,
            "TABLE_ID": TABLE_NAME,
            "BQ_DATASET": DATASET_NAME,
            "BQ_TABLE": TABLE_NAME,
            "ORIGIN_DATASET_NAME": VERSION_DATASET_NAME,
            "ORIGIN_TABLE_NAME": f"{TABLE_NAME}_{version}",
            "MINIMUM_VALUATION_DATE": str(
                dt.datetime.now().date() - dt.timedelta(days=120)
            ),
            "PREVIOUS_DAY": str(dt.datetime.now().date() - dt.timedelta(days=1)),
        }

        bq_query_backfill = get_query(
            "equipment_values_history_backfill_query.sql", variable_dict
        )

        if ras_code != "multi":
            bq_query_version = get_query(
                "equipment_values_history_fm_version_query.sql", variable_dict
            )
        else:
            bq_query_version = get_query(
                "equipment_values_history_fm_version_multi_query.sql", variable_dict
            )

        bq_query_fill_stage_table = get_query(
            "equipment_values_history_fm_stage_query.sql", variable_dict
        )

        if check_status_legacy(ims_conversion_status, fleet_customer_id):
            variable_dict["ORIGIN_DATASET_NAME"] = STAGE_DATASET_NAME
            variable_dict["ORIGIN_TABLE_NAME"] = BACKFILL_TABLE_NAME

        bq_query_create_stage_table = get_query(
            "equipment_values_history_fm_stage_create_query.sql", variable_dict
        )

        create_version_dataset_id = (
            f"{DAG_PREFIX_NAME_FLEET_MANAGER}-{ras_code}-create-version-dataset"
        )
        create_version_dataset = GKEStartPodOperator(
            task_id=create_version_dataset_id,
            name=create_version_dataset_id,
            image=get_full_image_name("execute-bigquery", gcr_registry),
            arguments=["python3", "main.py", "execute-bigquery"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "QUERY": f"""
            CREATE SCHEMA IF NOT EXISTS {VERSION_DATASET_NAME};
            ALTER SCHEMA IF EXISTS {VERSION_DATASET_NAME}
                        SET OPTIONS(
                            default_table_expiration_days={BQ_TABLE_EXPIRATION_DAYS}
                        );
            """,
                "GCP_PROJECT_ID": client_project,
            },
            dag=dag,
        )

        create_stage_dataset_id = (
            f"{DAG_PREFIX_NAME_FLEET_MANAGER}-{ras_code}-create-stage-dataset"
        )
        create_stage_dataset = GKEStartPodOperator(
            task_id=create_stage_dataset_id,
            name=create_stage_dataset_id,
            image=get_full_image_name("execute-bigquery", gcr_registry),
            arguments=["python3", "main.py", "execute-bigquery"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "QUERY": f"""
                    CREATE SCHEMA IF NOT EXISTS {STAGE_DATASET_NAME};
                """,
                "GCP_PROJECT_ID": client_project,
            },
            dag=dag,
        )

        create_view_dataset_id = (
            f"{DAG_PREFIX_NAME_FLEET_MANAGER}-{ras_code}-create-view-dataset"
        )
        create_view_dataset = GKEStartPodOperator(
            task_id=create_view_dataset_id,
            name=create_view_dataset_id,
            image=get_full_image_name("execute-bigquery", gcr_registry),
            arguments=["python3", "main.py", "execute-bigquery"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "QUERY": f"""
                    CREATE SCHEMA IF NOT EXISTS {DATASET_NAME};
                """,
                "GCP_PROJECT_ID": client_project,
            },
            dag=dag,
        )

        fill_version_table_id = (
            f"{DAG_PREFIX_NAME_FLEET_MANAGER}-{ras_code}-fill-version"
        )
        fill_version_table = GKEStartPodOperator(
            task_id=fill_version_table_id,
            name=fill_version_table_id,
            image=get_full_image_name("execute-bigquery", gcr_registry),
            arguments=["python3", "main.py", "execute-bigquery"],
            resources=RESOURCES_SMALL_TASK,
            do_xcom_push=True,
            env_vars={
                "QUERY": bq_query_version,
                "GCP_PROJECT_ID": client_project,
                "DESTINATION_TABLE": f"{client_project}.{VERSION_DATASET_NAME}.{TABLE_NAME}_{version}",
                "VIEW_TO_REPLACE": f"{client_project}.{STAGE_DATASET_NAME}.{TABLE_NAME}_current",
                "WRITE_DISPOSITION": "WRITE_TRUNCATE",
                "XCOM_PUSH": "True",
            },
            dag=dag,
        )

        create_stage_table_id = (
            f"{DAG_PREFIX_NAME_FLEET_MANAGER}-{ras_code}-create-stage"
        )
        create_stage_table = GKEStartPodOperator(
            task_id=create_stage_table_id,
            name=create_stage_table_id,
            image=get_full_image_name("execute-bigquery", gcr_registry),
            arguments=["python3", "main.py", "execute-bigquery"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "QUERY": bq_query_create_stage_table,
                "GCP_PROJECT_ID": client_project,
            },
            trigger_rule="none_failed_or_skipped",
            dag=dag,
        )

        fill_stage_table_id = f"{DAG_PREFIX_NAME_FLEET_MANAGER}-{ras_code}-fill-stage"
        fill_stage_table = GKEStartPodOperator(
            task_id=fill_stage_table_id,
            name=fill_stage_table_id,
            image=get_full_image_name("execute-bigquery", gcr_registry),
            arguments=["python3", "main.py", "execute-bigquery"],
            resources=RESOURCES_SMALL_TASK,
            do_xcom_push=True,
            env_vars={
                "QUERY": bq_query_fill_stage_table,
                "GCP_PROJECT_ID": client_project,
                "DESTINATION_TABLE": f"{client_project}.{STAGE_DATASET_NAME}.{TABLE_NAME}",
                "VIEW_TO_REPLACE": f"{client_project}.{DATASET_NAME}.{TABLE_NAME}",
                "WRITE_DISPOSITION": "WRITE_APPEND",
                "XCOM_PUSH": "True",
            },
            trigger_rule="none_failed_or_skipped",
            dag=dag,
        )

        pg_env_fm_id = "equipment-values-history-pg-fm-env"
        pg_env_fm = GKEStartPodOperator(
            task_id=pg_env_fm_id,
            name=pg_env_fm_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=180),
            arguments=["python3", "-m", "equipment_values_history_etl.cli", "pg-env"],
            secrets=secrets_pg_db,
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
                "TABLE_NAME": TABLE_NAME,
                "DB_SERVER": pg_db_server,
                "USE_PG_CERT": use_cert,
                "PG_DB": pg_db_name,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        mod_years_fm_history_id = "evh-equipment-history-mod-years-fm"
        mod_years_fm_history = GKEStartPodOperator(
            task_id=mod_years_fm_history_id,
            name=mod_years_fm_history_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=360),
            do_xcom_push=True,
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "get-modified-years-fm",
            ],
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_ID": str(client_id),
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        for year in reversed(years):
            fm_bq_to_pg_id = (
                f"{DAG_PREFIX_NAME_FLEET_MANAGER}-{ras_code}-fm-bq2pg-{year}"
            )
            fm_bq_to_pg = GKEStartPodOperator(
                task_id=fm_bq_to_pg_id,
                name=fm_bq_to_pg_id,
                dag=dag,
                resources=resources,
                execution_timeout=dt.timedelta(minutes=180),
                arguments=[
                    "python3",
                    "-m",
                    "equipment_values_history_etl.cli",
                    "bq2pg",
                ],
                secrets=secrets_pg_db,
                env_vars={
                    "VERSION": version,
                    "ENV": local_environment,
                    "CLIENT_CODE": ras_code,
                    "YEAR": str(year),
                    "YEARS_FOUND": f"{{{{ task_instance.xcom_pull("
                    f'task_ids="{mod_years_fm_history_id}"'
                    f')["years"] }}}}',
                    "CLIENT_VERSION": client_version,
                    "PROJECT_ID": client_project,
                    "RETENTION_PERIOD": table_retention_days,
                    "TABLE_NAME": TABLE_NAME,
                    "DB_SERVER": pg_db_server,
                    "USE_PG_CERT": use_cert,
                    "PG_FIELD_TYPES": """{
                        'meter_precise': 'NUMERIC(24,7)'
                    }""",
                    "PG_DB": pg_db_name,
                },
                get_logs=True,
                on_failure_callback=task_fail_slack_alert,
            )
            pg_env_fm >> fm_bq_to_pg >> pg_env_post

        if check_status_legacy(ims_conversion_status, fleet_customer_id):
            fill_backfill_table_id = (
                f"{DAG_PREFIX_NAME_FLEET_MANAGER}-{ras_code}-fill-backfill"
            )
            fill_backfill_table = GKEStartPodOperator(
                task_id=fill_backfill_table_id,
                name=fill_backfill_table_id,
                image=get_full_image_name("execute-bigquery", gcr_registry),
                arguments=["python3", "main.py", "execute-bigquery"],
                resources=RESOURCES_SMALL_TASK,
                do_xcom_push=True,
                env_vars={
                    "QUERY": bq_query_backfill,
                    "GCP_PROJECT_ID": client_project,
                    "XCOM_PUSH": "True",
                },
                dag=dag,
            )

            review_legacy_data_id = f"{DAG_PREFIX_NAME_FLEET_MANAGER}-{ras_code}-check-legacy-valuation-history"
            review_legacy_data = GKEStartPodOperator(
                task_id=review_legacy_data_id,
                name=review_legacy_data_id,
                arguments=[
                    "python3",
                    "-m",
                    "equipment_values_history_etl.cli",
                    "bq-check-if-table-exists",
                ],
                resources=RESOURCES_SMALL_TASK,
                dag=dag,
                execution_timeout=dt.timedelta(minutes=20),
                env_vars={
                    "PROJECT_ID": client_project,
                    "TABLE_ID_LIST": f'["{DATASET_NAME}.{LEGACY_TABLE_NAME}"]',
                },
                get_logs=True,
                do_xcom_push=True,
                on_failure_callback=task_fail_slack_alert,
            )

        qa_partition_fm_id = "equipment-values-history-qa-partition-fm"
        qa_partition_fm = GKEStartPodOperator(
            task_id=qa_partition_fm_id,
            name=qa_partition_fm_id,
            dag=dag,
            resources=resources,
            execution_timeout=dt.timedelta(minutes=180),
            arguments=[
                "python3",
                "-m",
                "equipment_values_history_etl.cli",
                "qa-partitions",
            ],
            secrets=secrets_pg_db,
            env_vars={
                "VERSION": version,
                "ENV": local_environment,
                "CLIENT_CODE": ras_code,
                "CLIENT_VERSION": client_version,
                "PROJECT_ID": client_project,
                "TABLE_NAME": "equipment_values_history_fm",
                "FM_TABLE_EXISTS": str(True),
                "DB_SERVER": pg_db_server,
                "USE_PG_CERT": use_cert,
                "PG_DB": pg_db_name,
            },
            get_logs=True,
            on_failure_callback=task_fail_slack_alert,
        )

        if ras_code == "multi":
            create_final_view_multi_id = (
                f"{DAG_PREFIX_NAME_FLEET_MANAGER}-{ras_code}-create-pg-view"
            )
            create_final_view_multi = GKEStartPodOperator(
                task_id=create_final_view_multi_id,
                name=create_final_view_multi_id,
                image=get_full_image_name("execute-pg-task", gcr_registry),
                dag=dag,
                arguments=["python3", "-m" "src.pgsql_cmd_task", "execute-task"],
                resources=resources,
                secrets=secrets_pg_db,
                env_vars={
                    "DB_NAME": pg_db_name,
                    "ENV": environment,
                    "PG_QUERY_TASK_SCRIPT": get_query(
                        "equipment_values_history_fm_view_multi.sql", variable_dict
                    ),
                    "DB_SERVER": pg_db_server,
                    "USE_PG_CERT": use_cert,
                },
            )
            qa_partition_fm >> create_final_view_multi

        pg_env_post >> qa_partition_fm

        if check_status_legacy(ims_conversion_status, fleet_customer_id):
            if ims_conversion_status != IMS_STATUS_IMS:
                qa_check_ims_vs_sales_equipment_values_history = GKEStartPodOperator(
                    task_id=f"qa-ims-vs-sales-equipment-values-history-{ras_code}",
                    name=f"qa-ims-vs-sales-equipment-values-history-{ras_code}",
                    dag=dag,
                    resources=resources,
                    execution_timeout=dt.timedelta(minutes=30),
                    arguments=[
                        "python3",
                        "-m",
                        "equipment_values_history_etl.cli",
                        "qa-ims-vs-sales",
                        "--table-name-ims=equipment_values_history_fm",
                        "--table-name-legacy=equipment_values_history",
                        f"--client-code={ras_code}",
                        f"--client-version={client_version}",
                    ],
                    env_vars={
                        "ENV": local_environment,
                    },
                    get_logs=True,
                    on_failure_callback=task_fail_slack_alert,
                )
                [
                    qa_partition,
                    qa_partition_fm,
                ] >> qa_check_ims_vs_sales_equipment_values_history

            (
                start_fm_task
                >> review_legacy_data
                >> branch_backfill_selected_ouptput_task
                >> fill_backfill_table
                >> create_stage_table
            )
            branch_backfill_selected_ouptput_task >> create_stage_table

        (
            start_fm_task
            >> [
                create_version_dataset,
                create_stage_dataset,
                create_view_dataset,
            ]
            >> fill_version_table
            >> create_stage_table
            >> fill_stage_table
            >> mod_years_fm_history
            >> pg_env_fm
        )

    return dag


def dag_factory(
    sales_code,
    client_id,
    ras_code,
    host_name,
    client_version,
    ims_conversion_status,
    fleet_customer_id,
    pg_db_server_name,
    local_environment,
):
    if local_environment == environment:
        dag_id = f"equipment-values-history-etl-" f"client-{ras_code}-{version_suffix}"
    else:
        dag_id = (
            f"equipment-values-history-etl-" f"client-ppe-{ras_code}-{version_suffix}"
        )
    globals()[dag_id] = create_dag(
        dag_id,
        ras_code,
        sales_code,
        client_id,
        host_name,
        str(client_version),
        ims_conversion_status,
        fleet_customer_id,
        pg_db_server=pg_db_shard_servers[pg_db_server_name]["main"][environment],
        use_cert=str(pg_db_shard_servers[pg_db_server_name]["use_certificates"]),
        pg_db_name=pg_db_shard_servers[pg_db_server_name][
            "db_name_allow_prepared_statement"
        ]["main"],
        local_environment=local_environment,
    )


for client in all_sales_active_clients:
    dag_factory(*client)

for client in all_ppe_active_clients:
    dag_factory(*client)
