import argparse
import yaml
import re
from google.cloud import secretmanager
from google.api_core.exceptions import NotFound


def create_secret(project, secret_name, data):
    secret_request = secretmanager.CreateSecretRequest(
        parent=project,
        secret_id=secret_name,
        secret={"replication": {"automatic": {}}},
    )
    secret = sm_client.create_secret(request=secret_request)
    payload = {"data": data.encode('utf8')}
    response_version = sm_client.add_secret_version(request={'parent': secret.name, 'payload': payload})
    return response_version


def add_new_version(project, secret, data):
    payload = {"data": data.encode('utf8')}
    full_secret_path = f'{project}/secrets/{secret}'
    response_version = sm_client.add_secret_version(request={'parent': full_secret_path, 'payload': payload})
    return response_version


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Read create-db-users's database users, copy its secrets to a "
                                                 "new project")
    parser.add_argument("--users-file", action="store", required=True, type=str)
    parser.add_argument("--source-project", action="store", required=True, type=str)
    parser.add_argument("--target-project", action="store", required=True, type=str)
    parser.add_argument("--key", action="store", type=str, default="db_users")
    parser.add_argument("--dry-run", action="store_true")
    args = parser.parse_args()
    with open(args.users_file, 'rt') as dict_file:
        users_data = yaml.load(dict_file, yaml.Loader)

    re_secret = re.compile(r"lookup\('rouse\.gcp\.secret_manager', '(\w+)', project_id=cloud_sql_project\)")
    sm_client = secretmanager.SecretManagerServiceClient()
    source_secret_project = f'projects/{args.source_project}'
    target_secret_project = f'projects/{args.target_project}'
    count_new = 0
    count_updated = 0
    for user in users_data[args.key]:
        match_secret = re_secret.search(user['password'])
        if match_secret is None:
            print(f"No lookup.secret_manager found on item for account {user['user']}")
            continue
        gcp_secret_name = match_secret.group(1)
        target_secret_path = f"{target_secret_project}/secrets/{gcp_secret_name}"
        target_secret_versions_path = f"{target_secret_path}/versions/latest"
        source_secret_path = f"{source_secret_project}/secrets/{gcp_secret_name}"
        source_secret_versions_path = f"{source_secret_path}/versions/latest"

        try:
            get_secret_request = secretmanager.AccessSecretVersionRequest(name=target_secret_versions_path)
            response = sm_client.access_secret_version(request=get_secret_request)
            target_secret_data = response.payload.data.decode('utf8')
        except NotFound as snf:
            target_secret_data = None

        get_secret_request = secretmanager.AccessSecretVersionRequest(name=source_secret_versions_path)
        response = sm_client.access_secret_version(request=get_secret_request)
        secret_data = response.payload.data.decode('utf8')
        if target_secret_data is None:
            create_secret(target_secret_project, gcp_secret_name, secret_data)
            count_new += 1
        elif secret_data != target_secret_data:
            add_new_version(target_secret_project, gcp_secret_name, secret_data)
            count_updated += 1
    if count_new:
        print(f'{count_new} new secrets have been created on project {args.target_project}')
    if count_updated:
        print(f'{count_updated} secrets have been created on project {args.target_project}')
