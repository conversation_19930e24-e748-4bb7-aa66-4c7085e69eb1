# syntax=docker/dockerfile:experimental
FROM us-docker.pkg.dev/images-4a3fb6/gcr.io/golden-image-python

LABEL MAINTEINER="<EMAIL>"

RUN apt-get update -y && \
    apt-get install curl wget build-essential openssh-client net-tools postgresql-client jq git -y && \
    apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*
    
    # install google cloud sdk with tools
    RUN mkdir -p /opt
    RUN curl -sSL https://sdk.cloud.google.com > install.sh
    RUN bash install.sh --disable-prompts --install-dir=/opt ; /opt/google-cloud-sdk/bin/gcloud components install kubectl gke-gcloud-auth-plugin
    ENV PATH $PATH:/opt/google-cloud-sdk/bin
    
    USER root
    COPY . /root/job/
    
RUN pip3 install -r /root/job/requirements.txt

RUN wget https://dl.google.com/cloudsql/cloud_sql_proxy.linux.amd64 -O /bin/cloud_sql_proxy && \
    chmod +x /bin/cloud_sql_proxy

RUN wget https://storage.googleapis.com/alloydb-auth-proxy/v1.13.2/alloydb-auth-proxy.linux.amd64 -O /bin/alloydb_auth_proxy && chmod +x /bin/alloydb_auth_proxy


RUN chmod +x /root/job/*.sh; mkdir -p -m 0600 ~/.ssh && ssh-keyscan github.com >> ~/.ssh/known_hosts
RUN --mount=type=ssh,id=ssh_key ansible-galaxy collection install -r /root/job/ansible/runtime_playbooks/requirements.yml -p /root/job/ansible/collections

WORKDIR /root/job