database_objects:
  schemas:
    fleet_manager:
      authorization: root
  sequences:
    fleet_asset_id:
      table: fleet_manager.fleet_assets
      column: fleet_asset_id
      initial_value: "{{ shard_seed }}"
  
database_roles:
  - name: equipment_r
    where_get_tables: (table_name = 'equipment' and table_schema not like '%_version')  or (table_name similar to 'equipment_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_rw
    where_get_tables: (table_name = 'equipment' and table_schema not like '%_version')  or (table_name similar to 'equipment_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: equipment_marketing_r
    where_get_tables: (table_name = 'equipment_marketing' and table_schema not like '%_version')  or (table_name similar to 'equipment_marketing_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_marketing_rw
    where_get_tables: (table_name = 'equipment_marketing' and table_schema not like '%_version')  or (table_name similar to 'equipment_marketing_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: equipment_marketing_fm_r
    where_get_tables: (table_name = 'equipment_marketing_fm' and table_schema not like '%_version')  or (table_name similar to 'equipment_marketing_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_validation_fm_r
    where_get_tables: (table_name = 'equipment_validation_fm' and table_schema not like '%_version')  or (table_name similar to 'equipment_validation_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_channels_fm_r
    where_get_tables: (table_name = 'equipment_channels_fm' and table_schema not like '%_version')  or (table_name similar to 'equipment_channels_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_fm_r
    where_get_tables: (table_name in ('equipment_base_fm', 'equipment_fm', 'equipment_marketing_fm') and table_schema not like '%_version')  or (table_name similar to 'equipment_base_fm_\d{14}|equipment_marketing_fm_\d{14}|equipment_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: fleet_listings_fm_r
    where_get_tables: (table_name in ('fleet_listings_base_fm', 'fleet_listings_fm') and table_schema not like '%_version')  or (table_name similar to 'fleet_listings_base_fm_\d{14}|fleet_listings_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: fleet_metrics_fm_r
    where_get_tables: (table_name in ('fleet_metrics_base_fm', 'fleet_metrics_fm') and table_schema not like '%_version')  or (table_name similar to 'fleet_metrics_base_fm_\d{14}|fleet_metrics_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_photos_r
    where_get_tables: (table_name = 'equipment_photos' and table_schema not like '%_version')  or (table_name similar to 'equipment_photos_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_photos_rw
    where_get_tables: (table_name = 'equipment_photos' and table_schema not like '%_version')  or (table_name similar to 'equipment_photos_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: sold_asset_backfill_r
    where_get_tables: (table_name = 'sold_asset_backfill' and table_schema not like '%_version')  or (table_name similar to 'sold_asset_backfill_\d{14}' and table_schema like '%_version')
    grant: select

  - name: sold_asset_backfill_rw
    where_get_tables: (table_name = 'sold_asset_backfill' and table_schema not like '%_version')  or (table_name similar to 'sold_asset_backfill_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: equipment_photos_backfill_r
    where_get_tables: (table_name = 'equipment_photos_backfill' and table_schema not like '%_version')  or (table_name similar to 'equipment_photos_backfill_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_photos_backfill_rw
    where_get_tables: (table_name = 'equipment_photos_backfill' and table_schema not like '%_version')  or (table_name similar to 'equipment_photos_backfill_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: legacy_valuation_eligibility_history_backfill_r
    where_get_tables: (table_name = 'legacy_valuation_eligibility_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'legacy_valuation_eligibility_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select

  - name: legacy_valuation_eligibility_history_backfill_rw
    where_get_tables: (table_name = 'legacy_valuation_eligibility_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'legacy_valuation_eligibility_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: legacy_in_portal_history_backfill_r
    where_get_tables: (table_name = 'legacy_in_portal_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'legacy_in_portal_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select

  - name: legacy_in_portal_history_backfill_rw
    where_get_tables: (table_name = 'legacy_in_portal_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'legacy_in_portal_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: legacy_rpo_history_backfill_r
    where_get_tables: (table_name = 'legacy_rpo_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'legacy_rpo_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select

  - name: legacy_rpo_history_backfill_rw
    where_get_tables: (table_name = 'legacy_rpo_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'legacy_rpo_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: fleet_customer_financing_programs_backfill_r
    where_get_tables: (table_name = 'fleet_customer_financing_programs' and table_schema not like '%_version')  or (table_name similar to 'fleet_customer_financing_programs_backfill_\d{14}' and table_schema like '%_version')
    grant: select

  - name: fleet_customer_financing_programs_backfill_rw
    where_get_tables: (table_name = 'fleet_customer_financing_programs' and table_schema not like '%_version')  or (table_name similar to 'fleet_customer_financing_programs_backfill_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: equipment_photos_fm_r
    where_get_tables: (table_name = 'equipment_photos_fm' and table_schema not like '%_version')  or (table_name similar to 'equipment_photos_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_notes_history_fm_r
    where_get_tables: (table_name = 'equipment_notes_history_fm' and table_schema not like '%_version')  or (table_name similar to 'equipment_notes_history_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: change_history_fm_r
    where_get_tables: (table_name = 'change_history_fm' and table_schema not like '%_version')  or (table_name similar to 'change_history_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_catalog_configuration_rw
    where_get_tables: (table_name = 'equipment_catalog_configuration' and table_schema not like '%_version')  or (table_name similar to 'equipment_catalog_configuration_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: equipment_catalog_configuration_r
    where_get_tables: (table_name = 'equipment_catalog_configuration' and table_schema not like '%_version')  or (table_name similar to 'equipment_catalog_configuration_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_catalog_fm_r
    where_get_tables: (table_name = 'equipment_catalog_fm' and table_schema not like '%_version')  or (table_name similar to 'equipment_catalog_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_catalog_r
    where_get_tables: (table_name = 'equipment_catalog' and table_schema not like '%_version')  or (table_name similar to 'equipment_catalog_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_catalog_rw
    where_get_tables: (table_name = 'equipment_catalog' and table_schema not like '%_version')  or (table_name similar to 'equipment_catalog_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: equipment_classification_r
    where_get_tables: (table_name like 'equipment_classification%' and table_schema not like '%_version')  or ((table_name similar to 'equipment_classification_subcategory_\d{14}' or table_name similar to 'equipment_classification_category_\d{14}' or table_name similar to 'equipment_classification_\d{14}') and table_schema like '%_version')
    grant: select

  - name: equipment_classification_rw
    where_get_tables: (table_name like 'equipment_classification%' and table_schema not like '%_version')  or ((table_name similar to 'equipment_classification_subcategory_\d{14}' or table_name similar to 'equipment_classification_category_\d{14}' or table_name similar to 'equipment_classification_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: classification_photos_r
    where_get_tables: (table_name = 'classification_photos' and table_schema not like '%_version')  or (table_name similar to 'classification_photos_\d{14}' and table_schema like '%_version')
    grant: select

  - name: classification_photos_rw
    where_get_tables: (table_name = 'classification_photos' and table_schema not like '%_version')  or (table_name similar to 'classification_photos_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: equipment_classification_relevancy_rw
    where_get_tables: (table_name like 'equipment_classification_relevancy%' and table_schema not like '%_version')  or ((table_name similar to 'equipment_classification_relevancy_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: equipment_classification_relevancy_r
    where_get_tables: (table_name like 'equipment_classification_relevancy%' and table_schema not like '%_version')  or ((table_name similar to 'equipment_classification_relevancy_\d{14}') and table_schema like '%_version')
    grant: select

  - name: equipment_values_history_fm_r
    where_get_tables: (table_name = 'equipment_values_history_fm' and table_schema not like '%_version')  or (table_name similar to 'equipment_values_history_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_values_history_r
    where_get_tables: (table_name = 'equipment_values_history' and table_schema not like '%_version')  or (table_name similar to 'equipment_values_history_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_values_history_rw
    where_get_tables: (table_name = 'equipment_values_history' and table_schema not like '%_version')  or (table_name similar to 'equipment_values_history_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: mkt_metrics_login_r
    where_get_tables: (table_name = 'mkt_metrics_login' and table_schema not like '%_version')  or (table_name similar to 'mkt_metrics_login_\d{14}' and table_schema like '%_version')
    grant: select

  - name: mkt_metrics_login_rw
    where_get_tables: (table_name = 'mkt_metrics_login' and table_schema not like '%_version')  or (table_name similar to 'mkt_metrics_login_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: mkt_metrics_photo_r
    where_get_tables: (table_name = 'mkt_metrics_photo' and table_schema not like '%_version')  or (table_name similar to 'mkt_metrics_photo_\d{14}' and table_schema like '%_version')
    grant: select

  - name: mkt_metrics_photo_rw
    where_get_tables: (table_name = 'mkt_metrics_photo' and table_schema not like '%_version')  or (table_name similar to 'mkt_metrics_photo_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: mkt_metrics_photo_fm_r
    where_get_tables: (table_name = 'mkt_metrics_photo_fm' and table_schema not like '%_version')  or (table_name similar to 'mkt_metrics_photo_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: mkt_metrics_photo_fm_rw
    where_get_tables: (table_name = 'mkt_metrics_photo_fm' and table_schema not like '%_version')  or (table_name similar to 'mkt_metrics_photo_fm_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: mkt_metrics_asset_r
    where_get_tables: (table_name = 'mkt_metrics_asset' and table_schema not like '%_version')  or (table_name similar to 'mkt_metrics_asset_\d{14}' and table_schema like '%_version')
    grant: select

  - name: mkt_metrics_asset_rw
    where_get_tables: (table_name = 'mkt_metrics_asset' and table_schema not like '%_version')  or (table_name similar to 'mkt_metrics_asset_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: mkt_metrics_weblead_r
    where_get_tables: (table_name = 'mkt_metrics_weblead' and table_schema not like '%_version')  or (table_name similar to 'mkt_metrics_weblead_\d{14}' and table_schema like '%_version')
    grant: select

  - name: mkt_metrics_weblead_rw
    where_get_tables: (table_name = 'mkt_metrics_weblead' and table_schema not like '%_version')  or (table_name similar to 'mkt_metrics_weblead_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: mkt_metrics_weblead_fm_r
    where_get_tables: (table_name = 'mkt_metrics_weblead_fm' and table_schema not like '%_version')  or (table_name similar to 'mkt_metrics_weblead_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: mkt_metrics_weblead_fm_rw
    where_get_tables: (table_name = 'mkt_metrics_weblead_fm' and table_schema not like '%_version')  or (table_name similar to 'mkt_metrics_weblead_fm_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: mkt_metrics_proposal_r
    where_get_tables: (table_name = 'mkt_metrics_proposal' and table_schema not like '%_version')  or (table_name similar to 'mkt_metrics_proposal_\d{14}' and table_schema like '%_version')
    grant: select

  - name: mkt_metrics_proposal_rw
    where_get_tables: (table_name = 'mkt_metrics_proposal' and table_schema not like '%_version')  or (table_name similar to 'mkt_metrics_proposal_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: mkt_metrics_proposal_fm_r
    where_get_tables: (table_name = 'mkt_metrics_proposal_fm' and table_schema not like '%_version')  or (table_name similar to 'mkt_metrics_proposal_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: mkt_metrics_proposal_fm_rw
    where_get_tables: (table_name = 'mkt_metrics_proposal_fm' and table_schema not like '%_version')  or (table_name similar to 'mkt_metrics_proposal_fm_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: sales_txn_r
    where_get_tables: (table_name = 'sales_txn' and table_schema not like '%_version')  or (table_name similar to 'sales_txn_\d{14}' and table_schema like '%_version')
    grant: select

  - name: sales_txn_rw
    where_get_tables: (table_name = 'sales_txn' and table_schema not like '%_version')  or (table_name similar to 'sales_txn_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: sales_historical_txn_r
    where_get_tables: (table_name = 'sales_historical_txn' and table_schema not like '%_version')  or (table_name similar to 'sales_historical_txn_\d{14}' and table_schema like '%_version')
    grant: select

  - name: sales_historical_txn_rw
    where_get_tables: (table_name = 'sales_historical_txn' and table_schema not like '%_version')  or (table_name similar to 'sales_historical_txn_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: values_lookup_transactions_r
    where_get_tables: (table_name = 'values_lookup_transactions' and table_schema not like '%_version')  or (table_name similar to 'values_lookup_transactions_\d{14}' and table_schema like '%_version')
    grant: select

  - name: values_lookup_transactions_rw
    where_get_tables: (table_name = 'values_lookup_transactions' and table_schema not like '%_version')  or (table_name similar to 'values_lookup_transactions_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: values_lookup_configurations_r
    where_get_tables: (table_name = 'values_lookup_configurations' and table_schema not like '%_version')  or (table_name similar to 'values_lookup_configurations_\d{14}' and table_schema like '%_version')
    grant: select

  - name: values_lookup_configurations_rw
    where_get_tables: (table_name = 'values_lookup_configurations' and table_schema not like '%_version')  or (table_name similar to 'values_lookup_configurations_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_retail_c_fm_r
    where_get_tables: ((table_name in ('transaction_volume_retail_c_fm','transaction_volume_retail_insi_c_fm', 'transaction_volume_retail_eu_c_fm', 'transaction_volume_retail_insi_eu_c_fm')) and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_c_\d{14}' or table_name similar to 'transaction_volume_retail_insi_c_\d{14}' or table_name similar to 'transaction_volume_retail_eu_c_\d{14}' or table_name similar to 'transaction_volume_retail_insi_eu_c_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_retail_c_fm_rw
    where_get_tables: ((table_name in ('transaction_volume_retail_c_fm','transaction_volume_retail_insi_c_fm', 'transaction_volume_retail_eu_c_fm', 'transaction_volume_retail_insi_eu_c_fm')) and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_c_\d{14}' or table_name similar to 'transaction_volume_retail_insi_c_\d{14}' or table_name similar to 'transaction_volume_retail_eu_c_\d{14}' or table_name similar to 'transaction_volume_retail_insi_eu_c_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_retail_cs_fm_r
    where_get_tables: ((table_name in ('transaction_volume_retail_cs_fm','transaction_volume_retail_insi_c_fm', 'transaction_volume_retail_eu_cs_fm', 'transaction_volume_retail_insi_eu_cs_fm')) and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_cs_\d{14}' or table_name similar to 'transaction_volume_retail_insi_cs_\d{14}' or table_name similar to 'transaction_volume_retail_eu_cs_\d{14}' or table_name similar to 'transaction_volume_retail_insi_eu_cs_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_retail_cs_fm_rw
    where_get_tables: ((table_name in ('transaction_volume_retail_cs_fm','transaction_volume_retail_insi_c_fm', 'transaction_volume_retail_eu_cs_fm', 'transaction_volume_retail_insi_eu_cs_fm')) and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_cs_\d{14}' or table_name similar to 'transaction_volume_retail_insi_cs_\d{14}' or table_name similar to 'transaction_volume_retail_eu_cs_\d{14}' or table_name similar to 'transaction_volume_retail_insi_eu_cs_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_retail_csm_fm_r
    where_get_tables: ((table_name in ('transaction_volume_retail_csm_fm','transaction_volume_retail_insi_csm_fm', 'transaction_volume_retail_eu_csm_fm', 'transaction_volume_retail_insi_eu_csm_fm')) and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_csm_\d{14}' or table_name similar to 'transaction_volume_retail_insi_csm_\d{14}' or table_name similar to 'transaction_volume_retail_eu_csm_\d{14}' or table_name similar to 'transaction_volume_retail_insi_eu_csm_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_retail_csm_fm_rw
    where_get_tables: ((table_name in ('transaction_volume_retail_csm_fm','transaction_volume_retail_insi_csm_fm', 'transaction_volume_retail_eu_csm_fm', 'transaction_volume_retail_insi_eu_csm_fm')) and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_csm_\d{14}' or table_name similar to 'transaction_volume_retail_insi_csm_\d{14}' or table_name similar to 'transaction_volume_retail_eu_csm_\d{14}' or table_name similar to 'transaction_volume_retail_insi_eu_csm_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_retail_csmm_fm_r
    where_get_tables: ((table_name in ('transaction_volume_retail_csmm_fm','transaction_volume_retail_insi_csmm_fm', 'transaction_volume_retail_eu_csmm_fm', 'transaction_volume_retail_insi_eu_csmm_fm')) and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_csmm_\d{14}' or table_name similar to 'transaction_volume_retail_insi_csmm_\d{14}' or table_name similar to 'transaction_volume_retail_eu_csmm_\d{14}' or table_name similar to 'transaction_volume_retail_insi_eu_csmm_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_retail_csmm_fm_rw
    where_get_tables: ((table_name in ('transaction_volume_retail_csmm_fm','transaction_volume_retail_insi_csmm_fm', 'transaction_volume_retail_eu_csmm_fm', 'transaction_volume_retail_insi_eu_csmm_fm')) and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_csmm_\d{14}' or table_name similar to 'transaction_volume_retail_insi_csmm_\d{14}' or table_name similar to 'transaction_volume_retail_eu_csmm_\d{14}' or table_name similar to 'transaction_volume_retail_insi_eu_csmm_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_retail_csmmy_fm_r
    where_get_tables: ((table_name in ('transaction_volume_retail_csmmy_fm','transaction_volume_retail_insi_csmmy_fm', 'transaction_volume_retail_eu_csmmy_fm', 'transaction_volume_retail_insi_eu_csmmy_fm')) and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_csmmy_\d{14}' or table_name similar to 'transaction_volume_retail_insi_csmmy_\d{14}' or table_name similar to 'transaction_volume_retail_eu_csmmy_\d{14}' or table_name similar to 'transaction_volume_retail_insi_eu_csmmy_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_retail_csmmy_fm_rw
    where_get_tables: ((table_name in ('transaction_volume_retail_csmmy_fm','transaction_volume_retail_insi_csmmy_fm', 'transaction_volume_retail_eu_csmmy_fm', 'transaction_volume_retail_insi_eu_csmmy_fm')) and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_csmmy_\d{14}' or table_name similar to 'transaction_volume_retail_insi_csmmy_\d{14}' or table_name similar to 'transaction_volume_retail_eu_csmmy_\d{14}' or table_name similar to 'transaction_volume_retail_insi_eu_csmmy_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_retail_quartile_csmm_fm_r
    where_get_tables: ((table_name = 'transaction_volume_retail_quartile_csmm_fm' or table_name = 'transaction_volume_retail_eu_quartile_csmm_fm') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_quartile_csmm_\d{14}' or table_name similar to 'transaction_volume_retail_eu_quartile_csmm_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_retail_quartile_csmm_fm_rw
    where_get_tables: ((table_name = 'transaction_volume_retail_quartile_csmm_fm' or table_name = 'transaction_volume_retail_eu_quartile_csmm_fm') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_quartile_csmm_\d{14}' or table_name similar to 'transaction_volume_retail_eu_quartile_csmm_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: value_trends_r
    where_get_tables: ((table_name = 'value_trends' or table_name = 'value_trends_all' or table_name = 'value_trends_all_chart') and table_schema not like '%_version')  or ((table_name similar to 'value_trends_\d{14}' or table_name similar to 'value_trends_all_\d{14}' or table_name similar to 'value_trends_all_chart_\d{14}' ) and table_schema like '%_version')
    grant: select

  - name: value_trends_rw
    where_get_tables: ((table_name = 'value_trends' or table_name = 'value_trends_all' or table_name = 'value_trends_all_chart') and table_schema not like '%_version')  or ((table_name similar to 'value_trends_\d{14}' or table_name similar to 'value_trends_all_\d{14}' or table_name similar to 'value_trends_all_chart_\d{14}' ) and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: value_trends_chart_r
    where_get_tables: (table_name = 'value_trends_chart' and table_schema not like '%_version')  or (table_name similar to 'value_trends_chart_\d{14}' and table_schema like '%_version')
    grant: select

  - name: value_trends_chart_rw
    where_get_tables: (table_name = 'value_trends_chart' and table_schema not like '%_version')  or (table_name similar to 'value_trends_chart_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: value_trends_uk_r
    where_get_tables: (table_name = 'value_trends_uk' and table_schema not like '%_version')  or (table_name similar to 'value_trends_uk_\d{14}' and table_schema like '%_version')
    grant: select

  - name: value_trends_uk_rw
    where_get_tables: (table_name = 'value_trends_uk' and table_schema not like '%_version')  or (table_name similar to 'value_trends_uk_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: value_trends_uk_chart_r
    where_get_tables: (table_name = 'value_trends_uk_chart' and table_schema not like '%_version')  or (table_name similar to 'value_trends_uk_chart_\d{14}' and table_schema like '%_version')
    grant: select

  - name: value_trends_uk_chart_rw
    where_get_tables: (table_name = 'value_trends_uk_chart' and table_schema not like '%_version')  or (table_name similar to 'value_trends_uk_chart_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: equipment_values_residuals_r
    where_get_tables: (table_name = 'equipment_values_residuals' and table_schema not like '%_version')  or (table_name similar to 'equipment_values_residuals_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_values_residuals_rw
    where_get_tables: (table_name = 'equipment_values_residuals' and table_schema not like '%_version')  or (table_name similar to 'equipment_values_residuals_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: equipment_values_residuals_fm_r
    where_get_tables: (table_name = 'equipment_values_residuals_fm' and table_schema not like '%_version')  or (table_name similar to 'equipment_values_residuals_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_values_residuals_fm_rw
    where_get_tables: (table_name = 'equipment_values_residuals_fm' and table_schema not like '%_version')  or (table_name similar to 'equipment_values_residuals_fm_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: equipment_values_r
    where_get_tables: (table_name = 'equipment_values' and table_schema not like '%_version')  or (table_name similar to 'equipment_values_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_values_rw
    where_get_tables: (table_name = 'equipment_values' and table_schema not like '%_version')  or (table_name similar to 'equipment_values_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: equipment_values_configuration_r
    where_get_tables: (table_name = 'equipment_values_configuration' and table_schema not like '%_version')  or (table_name similar to 'equipment_values_configuration_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_values_configuration_rw
    where_get_tables: (table_name = 'equipment_values_configuration' and table_schema not like '%_version')  or (table_name similar to 'equipment_values_configuration_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: equipment_values_fm_r
    where_get_tables: (table_name = 'equipment_values_fm' and table_schema not like '%\_version')  or (table_name similar to 'equipment\_values\_fm\_\d{14}' and table_schema like '%\_version')
    grant: select

  - name: equipment_values_fm_rw
    where_get_tables: (table_name = 'equipment_values_fm' and table_schema not like '%\_version')  or (table_name similar to 'equipment\_values\_fm\_\d{14}' and table_schema like '%\_version')
    grant: select, insert, update, delete

  - name: equipment_values_configuration_fm_r
    where_get_tables: (table_name = 'equipment_values_configuration_fm' and table_schema not like '%\_version')  or (table_name similar to 'equipment\_values\_configuration\_fm\_\d{14}' and table_schema like '%\_version')
    grant: select

  - name: equipment_values_configuration_fm_rw
    where_get_tables: (table_name = 'equipment_values_configuration_fm' and table_schema not like '%\_version')  or (table_name similar to 'equipment\_values\_configuration\_fm\_\d{14}' and table_schema like '%\_version')
    grant: select, insert, update, delete

  - name: auction_data_r
    where_get_tables: (table_name = 'auction_data' and table_schema not like '%_version')  or (table_name similar to 'auction_data_\d{14}' and table_schema like '%_version')
    grant: select

  - name: auction_data_rw
    where_get_tables: (table_name = 'auction_data' and table_schema not like '%_version')  or (table_name similar to 'auction_data_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: sales_erp_sync_r
    where_get_tables: ((table_schema like 'rfm%' and table_name like 'equipment%') or (table_schema = 'fleet_manager' and table_name = 'fleet_assets'))
    grant: select

  - name: sales_prevailing_exchange_rates_r
    where_get_tables: (table_name = 'sales_prevailing_exchange_rates' and table_schema not like '%_version')  or (table_name similar to 'sales_prevailing_exchange_rates_\d{14}' and table_schema like '%_version')
    grant: select

  - name: sales_prevailing_exchange_rates_rw
    where_get_tables: (table_name = 'sales_prevailing_exchange_rates' and table_schema not like '%_version')  or (table_name similar to 'sales_prevailing_exchange_rates_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: sales_values_lookup_reporting_r
    where_get_tables: (((table_name = 'values_lookup_transactions' or table_name = 'lookup_values_activity_fm') and table_schema not like '%_version' and table_schema like 'rfm%') or (table_name similar to 'lookup_values_activity_fm_\d{14}' and table_schema like '%_version'))
    grant: select

  - name: sales_values_lookup_reporting_rw
    where_get_tables: (((table_name = 'values_lookup_transactions' or table_name = 'lookup_values_activity_fm') and table_schema not like '%_version' and table_schema like 'rfm%') or (table_name similar to 'lookup_values_activity_fm_\d{14}' and table_schema like '%_version'))
    grant: select, insert, update, delete

  - name: sales_txn_fm_r
    where_get_tables: (table_name = 'sales_txn_fm' and table_schema not like '%_version' and table_schema like 'rfm%') or (table_name similar to 'sales_txn_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: sales_txn_fm_rw
    where_get_tables: (table_name = 'sales_txn_fm' and table_schema not like '%_version' and table_schema like 'rfm%') or (table_name similar to 'sales_txn_fm_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: sales_txn_historical_fm_r
    where_get_tables: (table_name = 'sales_txn_historical_fm' and table_schema not like '%_version' and table_schema like 'rfm%') or (table_name similar to 'sales_txn_historical_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: sales_txn_historical_fm_rw
    where_get_tables: (table_name = 'sales_txn_historical_fm' and table_schema not like '%_version' and table_schema like 'rfm%') or (table_name similar to 'sales_txn_historical_fm_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: auctions_r
    where_get_tables: (table_name = 'auctions' and table_schema not like '%_version')  or (table_name similar to 'auctions_\d{14}' and table_schema like '%_version')
    grant: select

  - name: auctions_rw
    where_get_tables: (table_name = 'auctions' and table_schema not like '%_version')  or (table_name similar to 'auctions_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_auction_c_r
    where_get_tables: (table_name in ('transaction_volume_auction_c', 'transaction_volume_auction_insi_c') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_c_\d{14}' or table_name similar to 'transaction_volume_auction_insi_c_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_c_rw
    where_get_tables: (table_name in ('transaction_volume_auction_c', 'transaction_volume_auction_insi_c') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_c_\d{14}' or table_name similar to 'transaction_volume_auction_insi_c_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_auction_cs_r
    where_get_tables: (table_name in ('transaction_volume_auction_cs', 'transaction_volume_auction_insi_cs') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_cs_\d{14}' or table_name similar to 'transaction_volume_auction_insi_cs_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_cs_rw
    where_get_tables: (table_name in ('transaction_volume_auction_cs', 'transaction_volume_auction_insi_cs') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_cs_\d{14}' or table_name similar to 'transaction_volume_auction_insi_cs_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_auction_csm_r
    where_get_tables: (table_name in ('transaction_volume_auction_csm', 'transaction_volume_auction_insi_csm') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_csm_\d{14}' or table_name similar to 'transaction_volume_auction_insi_csm_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_csm_rw
    where_get_tables: (table_name in ('transaction_volume_auction_csm', 'transaction_volume_auction_insi_csm') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_csm_\d{14}' or table_name similar to 'transaction_volume_auction_insi_csm_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_auction_csmm_r
    where_get_tables: (table_name in ('transaction_volume_auction_csmm', 'transaction_volume_auction_insi_csmm') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_csmm_\d{14}' or table_name similar to 'transaction_volume_auction_insi_csmm_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_csmm_rw
    where_get_tables: (table_name in ('transaction_volume_auction_csmm', 'transaction_volume_auction_insi_csmm') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_csmm_\d{14}' or table_name similar to 'transaction_volume_auction_insi_csmm_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_auction_csmmy_r
    where_get_tables: (table_name in ('transaction_volume_auction_csmmy', 'transaction_volume_auction_insi_csmmy') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_csmmy_\d{14}' or table_name similar to 'transaction_volume_auction_insi_csmmy_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_csmmy_rw
    where_get_tables: (table_name in ('transaction_volume_auction_csmmy', 'transaction_volume_auction_insi_csmmy') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_csmmy_\d{14}' or table_name similar to 'transaction_volume_auction_insi_csmmy_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_auction_eu_c_r
    where_get_tables: (table_name in ('transaction_volume_auction_eu_c', 'transaction_volume_auction_insi_eu_c') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_eu_c_\d{14}' or table_name similar to 'transaction_volume_auction_insi_eu_c_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_eu_c_rw
    where_get_tables: (table_name in ('transaction_volume_auction_eu_c', 'transaction_volume_auction_insi_eu_c') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_eu_c_\d{14}' or table_name similar to 'transaction_volume_auction_insi_eu_c_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_auction_eu_cs_r
    where_get_tables: (table_name in ('transaction_volume_auction_eu_cs', 'transaction_volume_auction_insi_eu_cs') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_eu_cs_\d{14}' or table_name similar to 'transaction_volume_auction_insi_eu_cs_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_eu_cs_rw
    where_get_tables: (table_name in ('transaction_volume_auction_eu_cs', 'transaction_volume_auction_insi_eu_cs') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_eu_cs_\d{14}' or table_name similar to 'transaction_volume_auction_insi_eu_cs_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_auction_eu_csm_r
    where_get_tables: (table_name in ('transaction_volume_auction_eu_csm', 'transaction_volume_auction_insi_eu_csm') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_eu_csm_\d{14}' or table_name similar to 'transaction_volume_auction_insi_eu_csm_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_eu_csm_rw
    where_get_tables: (table_name in ('transaction_volume_auction_eu_csm', 'transaction_volume_auction_insi_eu_csm') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_eu_csm_\d{14}' or table_name similar to 'transaction_volume_auction_insi_eu_csm_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_auction_eu_csmm_r
    where_get_tables: (table_name in ('transaction_volume_auction_eu_csmm', 'transaction_volume_auction_insi_eu_csmm') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_eu_csmm_\d{14}' or table_name similar to 'transaction_volume_auction_insi_eu_csmm_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_eu_csmm_rw
    where_get_tables: (table_name in ('transaction_volume_auction_eu_csmm', 'transaction_volume_auction_insi_eu_csmm') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_eu_csmm_\d{14}' or table_name similar to 'transaction_volume_auction_insi_eu_csmm_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_auction_eu_csmmy_r
    where_get_tables: (table_name in ('transaction_volume_auction_eu_csmmy', 'transaction_volume_auction_insi_eu_csmmy') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_eu_csmmy_\d{14}' or table_name similar to 'transaction_volume_auction_insi_eu_csmmy_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_eu_csmmy_rw
    where_get_tables: (table_name in ('transaction_volume_auction_eu_csmmy', 'transaction_volume_auction_insi_eu_csmmy') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_eu_csmmy_\d{14}' or table_name similar to 'transaction_volume_auction_insi_eu_csmmy_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_auction_quartile_csmm_r
    where_get_tables: (table_name = 'transaction_volume_auction_quartile_csmm' and table_schema not like '%_version')  or (table_name similar to 'transaction_volume_auction_quartile_csmm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_quartile_csmm_rw
    where_get_tables: (table_name = 'transaction_volume_auction_quartile_csmm' and table_schema not like '%_version')  or (table_name similar to 'transaction_volume_auction_quartile_csmm_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: transaction_volume_auction_eu_quartile_csmm_r
    where_get_tables: (table_name = 'transaction_volume_auction_eu_quartile_csmm' and table_schema not like '%_version')  or (table_name similar to 'transaction_volume_auction_eu_quartile_csmm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_eu_quartile_csmm_rw
    where_get_tables: (table_name = 'transaction_volume_auction_eu_quartile_csmm' and table_schema not like '%_version')  or (table_name similar to 'transaction_volume_auction_eu_quartile_csmm_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: tiger_r
    where_get_tables: (table_schema in ('tiger', 'tiger_data', 'tiger_staging'))
    grant: select

  - name: tiger_rw
    where_get_tables: (table_schema in ('tiger', 'tiger_data', 'tiger_staging'))
    grant: select, insert, update, delete

  - name: postal_codes_r
    where_get_tables: (table_name = 'geo_postal_codes' and table_schema = 'rfm_global')
    grant: select

  - name: rfm_global_r
    where_get_tables: (table_schema = 'rfm_global')
    grant: select

  - name: fleet_manager_backfill_rw
    where_get_tables: (table_schema = 'fleet_manager_backfill')
    grant: select, insert, update, delete

  - name: fleet_manager_all_tables_r
    where_get_tables: (table_schema = 'fleet_manager')
    grant: select

  - name: fleet_manager_all_tables_rw
    where_get_tables: (table_schema = 'fleet_manager')
    grant: select, insert, update, delete

  - name: fleet_tooling_rw
    where_get_tables: (table_schema = 'fleet_tooling')
    grant: select, insert, update, delete

  - name: fleet_manager_delta_rw
    where_get_tables: (table_schema = 'fleet_manager_delta')
    grant: select, insert, update, delete

  - name: rfm_multi_all_tables_r
    where_get_tables: (table_schema = 'rfm_multi')
    grant: select

  - name: fleet_customers_r
    where_get_tables: (table_name = 'fleet_customers' and table_schema = 'fleet_manager')
    grant: select

  - name: fleet_customers_rw
    where_get_tables: (table_name = 'fleet_customers' and table_schema = 'fleet_manager')
    grant: select, insert, update, delete

  - name: rfm_client_fleet_customers_rw
    where_get_tables: (table_name similar to 'fleet_customers_\d{14}' or table_name similar to 'fleet_customers_%_\d{14}') and table_schema like '%_version'
    grant: select, insert, update, delete

  - name: sold_assets_r
    where_get_tables: (table_name = 'fleet_assets' and table_schema = 'fleet_manager')
    grant: select

  - name: sold_assets_rw
    where_get_tables: (table_name = 'fleet_assets' and table_schema = 'fleet_manager')
    grant: select, insert, update, delete

  - name: rouse_rb_taxonomy_mapping_r
    where_get_tables: (table_name like 'rouse_rb_taxonomy_mapping%' and table_schema not like '%_version')  or ((table_name similar to 'rouse_rb_taxonomy_mapping_c_to_c_\d{14}' or table_name similar to 'rouse_rb_taxonomy_mapping_cs_to_ct_\d{14}' or table_name similar to 'rouse_rb_taxonomy_mapping_\d{14}') and table_schema like '%_version')
    grant: select

  - name: rouse_rb_taxonomy_mapping_rw
    where_get_tables: (table_name like 'rouse_rb_taxonomy_mapping%' and table_schema not like '%_version')  or ((table_name similar to 'rouse_rb_taxonomy_mapping_c_to_c_\d{14}' or table_name similar to 'rouse_rb_taxonomy_mapping_cs_to_ct_\d{14}' or table_name similar to 'rouse_rb_taxonomy_mapping_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: fleet_classification_r
    where_get_tables: (table_name = 'fleet_classification' and table_schema = 'fleet_manager')
    grant: select

  - name: fleet_classification_rw
    where_get_tables: (table_name = 'fleet_classification' and table_schema = 'fleet_manager')
    grant: select, insert, update, delete

  - name: fleet_manager_classification_r
    where_get_tables: (table_name = 'fleet_manager_classification' and table_schema = 'fleet_manager')
    grant: select

  - name: fleet_manager_classification_rw
    where_get_tables: (table_name = 'fleet_manager_classification' and table_schema = 'fleet_manager')
    grant: select, insert, update, delete

  - name: fleet_manager_cat_product_group_r
    where_get_tables: (table_name = 'fleet_manager_cat_product_group' and table_schema = 'fleet_manager')
    grant: select

  - name: fleet_manager_cat_product_group_rw
    where_get_tables: (table_name = 'fleet_manager_cat_product_group' and table_schema = 'fleet_manager')
    grant: select, insert, update, delete

  - name: fleet_manager_customers_metrics_reporting_r
    where_get_tables: ((table_name = 'listed_history' or table_name = 'fleet_assets' or table_name = 'fleet_customers' or table_name = 'fleet_entitlements') and table_schema = 'fleet_manager')
    grant: select

  - name: ims_fleet_ingest_etl_r
    where_get_tables: (table_name like 'fleet%' and table_schema = 'fleet_manager')
    grant: select

  - name: ims_fleet_refresh_etl_r
    where_get_tables: (table_name like 'fleet%' and table_schema = 'fleet_manager')
    grant: select

  - name: proposal_history_r
    where_get_tables: ((table_name = 'proposal_history' or table_name = 'proposal_history_details') and table_schema not like '%_version')  or ((table_name similar to 'proposal_history_\d{14}' or table_name similar to 'proposal_history_details_\d{14}') and table_schema like '%_version')
    grant: select

  - name: proposal_history_rw
    where_get_tables: ((table_name = 'proposal_history' or table_name = 'proposal_history_details') and table_schema not like '%_version')  or ((table_name similar to 'proposal_history_\d{14}' or table_name similar to 'proposal_history_details_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: meter_history_backfill_r
    where_get_tables: ((table_name = 'meter_history_hours_backfill' or table_name = 'meter_history_miles_backfill') and table_schema not like '%_version')  or ((table_name similar to 'meter_history_hours_backfill_\d{14}' or table_name similar to 'meter_history_miles_backfill_\d{14}')and table_schema like '%_version')
    grant: select

  - name: meter_history_backfill_rw
    where_get_tables: ((table_name = 'meter_history_hours_backfill' or table_name = 'meter_history_miles_backfill') and table_schema not like '%_version')  or ((table_name similar to 'meter_history_hours_backfill_\d{14}' or table_name similar to 'meter_history_miles_backfill_\d{14}')and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: asset_fields_set_by_user_sync_r
    where_get_tables: (table_name = 'asset_fields_set_by_user' and table_schema not like '%_version')  or (table_name similar to 'asset_fields_set_by_user_\d{14}' and table_schema like '%_version')
    grant: select

  - name: asset_fields_set_by_user_sync_rw
    where_get_tables: (table_name = 'asset_fields_set_by_user' and table_schema not like '%_version')  or (table_name similar to 'asset_fields_set_by_user_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: asset_change_history_backfill_r
    where_get_tables: (table_name = 'asset_change_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'asset_change_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select

  - name: asset_change_history_backfill_rw
    where_get_tables: (table_name = 'asset_change_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'asset_change_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: value_history_change_history_backfill_r
    where_get_tables: (table_name = 'value_history_change_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'value_history_change_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select

  - name: value_history_change_history_backfill_rw
    where_get_tables: (table_name = 'value_history_change_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'value_history_change_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: equipment_files_fm_r
    where_get_tables: (table_name = 'equipment_files_fm' and table_schema not like '%_version')  or (table_name similar to 'equipment_files_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_files_fm_rw
    where_get_tables: (table_name = 'equipment_files_fm' and table_schema not like '%_version')  or (table_name similar to 'equipment_files_fm_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: fleet_asset_files_backfill_r
    where_get_tables: (table_name = 'fleet_asset_files_backfill' and table_schema not like '%_version')  or (table_name similar to 'fleet_asset_files_backfill_\d{14}' and table_schema like '%_version')
    grant: select

  - name: fleet_asset_files_backfill_rw
    where_get_tables: (table_name = 'fleet_asset_files_backfill' and table_schema not like '%_version')  or (table_name similar to 'fleet_asset_files_backfill_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: sales_google_analytics_page_views_r
    where_get_tables: (table_name like 'webshop_%' and table_schema like 'sales_rfm_channels_api')
    grant: select

  - name: sales_google_analytics_page_views_rw
    where_get_tables: (table_name like 'webshop_%' and table_schema like 'sales_rfm_channels_api')
    grant: select, insert, update, delete

  - name: ss_export_r
    where_get_tables: (table_name like 'country' and table_schema like 'rfm_global')
    grant: select

  - name: ss_export_rw
    where_get_tables: (table_name like 'country' and table_schema like 'rfm_global')
    grant: select, insert, update, delete

  - name: fleet_manager_valuations_history_etl_r
    where_get_tables: (table_name like 'fleet%' and table_schema = 'fleet_manager')
    grant: select

  - name: list_price_history_backfill_r
    where_get_tables: (table_name = 'list_price_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'list_price_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select

  - name: list_price_history_backfill_rw
    where_get_tables: (table_name = 'list_price_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'list_price_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: legacy_list_price_history_backfill_r
    where_get_tables: (table_name = 'legacy_list_price_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'legacy_list_price_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select

  - name: legacy_list_price_history_backfill_rw
    where_get_tables: (table_name = 'legacy_list_price_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'legacy_list_price_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: list_price_customer_asking_history_backfill_r
    where_get_tables: (table_name = 'list_price_customer_asking_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'list_price_customer_asking_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select

  - name: list_price_customer_asking_history_backfill_rw
    where_get_tables: (table_name = 'list_price_customer_asking_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'list_price_customer_asking_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete


  - name: proposal_history_backfill_r
    where_get_tables: (table_name = 'proposal_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'proposal_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select

  - name: proposal_history_backfill_rw
    where_get_tables: (table_name = 'proposal_history_backfill' and table_schema not like '%_version')  or (table_name similar to 'proposal_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: fields_set_by_users_backfill_r
    where_get_tables: ((table_name = 'fields_set_by_users_is_listed_backfill' or table_name = 'fields_set_by_users_is_featured_backfill') and table_schema not like '%_version')  or ((table_name similar to 'fields_set_by_users_is_listed_backfill_\d{14}' or table_name similar to 'fields_set_by_users_is_featured_backfill_\d{14}')and table_schema like '%_version')
    grant: select

  - name: fields_set_by_users_backfill_rw
    where_get_tables: ((table_name = 'fields_set_by_users_is_listed_backfill' or table_name = 'fields_set_by_users_is_featured_backfill') and table_schema not like '%_version')  or ((table_name similar to 'fields_set_by_users_is_listed_backfill_\d{14}' or table_name similar to 'fields_set_by_users_is_featured_backfill_\d{14}')and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: first_seen_by_rouse_backfill_r
    where_get_tables: (table_name = 'first_seen_by_rouse_backfill' and table_schema not like '%_version')  or (table_name similar to 'first_seen_by_rouse_backfill_\d{14}' and table_schema like '%_version')
    grant: select

  - name: first_seen_by_rouse_backfill_rw
    where_get_tables: (table_name = 'first_seen_by_rouse_backfill' and table_schema not like '%_version')  or (table_name similar to 'first_seen_by_rouse_backfill_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: web_leads_r
    where_get_tables: (table_name like 'web_leads%' and table_schema not like '%_version')  or ((table_name similar to 'web_leads_aggregate_\d{14}' or table_name similar to 'web_leads_fm_\d{14}' or table_name similar to 'web_leads_\d{14}') and table_schema like '%_version')
    grant: select

  - name: web_leads_rw
    where_get_tables: (table_name like 'web_leads%' and table_schema not like '%_version')  or ((table_name similar to 'web_leads_aggregate_\d{14}' or table_name similar to 'web_leads_fm_\d{14}' or table_name similar to 'web_leads_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: legacy_meter_history_backfill_r
    where_get_tables: (table_name = 'legacy_meter_history_backfill'  and table_schema not like '%_version')  or (table_name similar to 'legacy_meter_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select

  - name: legacy_meter_history_backfill_rw
    where_get_tables: (table_name = 'legacy_meter_history_backfill'  and table_schema not like '%_version')  or (table_name similar to 'legacy_meter_history_backfill_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: mpe_kpi_metrics_r
    where_get_tables: (table_name like 'mpe_kpi_metrics%' and table_schema not like '%_version')  or ((table_name similar to 'mpe_kpi_metrics_fm_\d{14}' or table_name similar to 'mpe_kpi_metrics_\d{14}') and table_schema like '%_version')
    grant: select

  - name: mpe_kpi_metrics_rw
    where_get_tables: (table_name like 'mpe_kpi_metrics%' and table_schema not like '%_version')  or ((table_name similar to 'mpe_kpi_metrics_fm_\d{14}' or table_name similar to 'mpe_kpi_metrics_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: mpe_transactions_r
    where_get_tables: (table_name like 'mpe_transactions%' and table_schema not like '%_version')  or ((table_name similar to 'mpe_transactions_\d{14}') and table_schema like '%_version')
    grant: select

  - name: mpe_transactions_rw
    where_get_tables: (table_name like 'mpe_transactions%' and table_schema not like '%_version')  or ((table_name similar to 'mpe_transactions_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: rfm_archiving_r
    where_get_tables: (table_name like 'rfm_archiving%' and table_schema not like '%_version')  or ((table_name similar to 'rfm_archiving_\d{14}') and table_schema like '%_version')
    grant: select

  - name: rfm_archiving_rw
    where_get_tables: (table_name like 'rfm_archiving%' and table_schema not like '%_version')  or ((table_name similar to 'rfm_archiving_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: appraisal_valuation_canary_r
    where_get_tables: (table_name like 'canary%' and table_schema not like '%_version')  or ((table_name similar to 'canary_\d{14}') and table_schema like '%_version')
    grant: select

  - name: appraisal_valuation_canary_rw
    where_get_tables: (table_name like 'canary%' and table_schema not like '%_version')  or ((table_name similar to 'canary_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: rb_taxonomy_r
    where_get_tables: (table_name like 'rb_taxonomy%' and table_schema not like '%_version')  or ((table_name similar to 'rb_taxonomy_\d{14}') and table_schema like '%_version')
    grant: select

  - name: rb_taxonomy_rw
    where_get_tables: (table_name like 'rb_taxonomy%' and table_schema not like '%_version')  or ((table_name similar to 'rb_taxonomy_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: marketable_life_r
    where_get_tables: ((table_name like 'retail_marketable_life%' or table_name like 'auction_marketable_life%') and table_schema not like '%_version')  or ((table_name similar to 'retail_marketable_life_\d{14}' or table_name similar to 'auction_marketable_life_\d{14}') and table_schema like '%_version')
    grant: select

  - name: marketable_life_rw
    where_get_tables: ((table_name like 'retail_marketable_life%' or table_name like 'auction_marketable_life%') and table_schema not like '%_version')  or ((table_name similar to 'retail_marketable_life_\d{14}' or table_name similar to 'auction_marketable_life_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: values_lookup_schema_r
    where_get_tables: (table_name like 'rfm_equipment_classification%' and table_schema not like '%_version')  or ((table_name similar to 'rfm_equipment_classification_\d{14}') and table_schema like '%_version')
    grant: select

  - name: values_lookup_schema_rw
    where_get_tables: (table_name like 'rfm_equipment_classification%' and table_schema not like '%_version')  or ((table_name similar to 'rfm_equipment_classification_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: partman_rw
    where_get_routines: (routine_schema = 'partman' and routine_name not like '%_proc')
    where_get_tables: (table_schema = 'partman')
    grant: select, insert, update, delete
    grant_execute: yes

  - name: user_config_r
    where_get_schemas: table_schema = 'user_config'
    grant: select
    
  - name: root
    get_tables_like: "*"
    grant: select

  - name: fleet_manager_publish_insights_taxonomy_and_localization_etl_r
    where_get_tables: (table_name in ('insights_type', 'insights_category', 'insights_subcategory','taxonomy_locale', 'taxonomy_alias_type', 'taxonomy_alias_category','taxonomy_alias_subcategory', 'insights_taxonomy', 'insights_taxonomy_category', 'insights_taxonomy_type', 'insights_configuration_option_taxonomy', 'insights_configuration_value_taxonomy', 'insights_configuration_taxonomy') and table_schema not like '%_version')  or (table_name similar to 'insights_type_\d{14}|sales_category_\d{14}|insights_category_\d{14}|insights_subcategory_\d{14}|taxonomy_alias_type_\d{14}|taxonomy_alias_category_\d{14}|taxonomy_alias_subcategory_\d{14}|insights_taxonomy_\d{14}|insights_taxonomy_category_\d{14}|insights_taxonomy_type\d{14}|insights_configuration_option_taxonomy_\d{14}|insights_configuration_value_taxonomy_\d{14}|insights_configuration_taxonomy_\d{14}' and table_schema like '%_version')
    grant: select

  - name: rouse_rb_taxonomy_mapping_cs_to_ct_rw
    where_get_tables: (table_name = 'rouse_rb_taxonomy_mapping_cs_to_ct' and table_schema not like '%_version')  or (table_name similar to 'rouse_rb_taxonomy_mapping_cs_to_ct_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: rental_insights_etl_r
    where_get_tables: (table_schema like 'fm_ri' and table_name like 'rental_insights_fm%')
    grant: select
    
  - name: rental_insights_etl_rw
    where_get_tables: (table_schema like 'fm_ri' and table_name like 'rental_insights_fm%')
    grant: select, insert, update, delete

  - name: fleet_manager_expected_time_to_sell_etl_r
    where_get_tables: (table_name in ('asset_listed_history_fm', 'asset_retail_sales_fm') and table_schema not like '%_version')  or (table_name similar to 'asset_listed_history_fm_\d{14}|asset_retail_sales_fm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: fleet_manager_sales_subcategory_r
    where_get_tables: (table_schema like 'rfm_globals' and table_name like 'makes')
    grant: select

  - name: fleet_manager_sales_subcategory_rw
    where_get_tables: (table_schema like 'rfm_globals' and table_name like 'makes')
    grant: select, insert, update, delete
  
  - name: mixer_values_lookup_configuration_sync_rw
    where_get_tables: (table_schema like 'sales_classification' and table_name in ('configuration_options', 'configuration_options_values'))
    grant: select, insert, update, delete

permissions:
  - user: equipment_sales_api
    roles:
      - equipment_r
      - equipment_fm_r
      - equipment_marketing_rw
      - equipment_validation_fm_r
      - equipment_marketing_fm_r
      - equipment_channels_fm_r
      - equipment_photos_rw
      - equipment_photos_fm_r
      - equipment_catalog_configuration_rw
      - equipment_catalog_rw
      - classification_photos_rw
      - equipment_values_history_rw
      - rb_taxonomy_r
      - sales_prevailing_exchange_rates_r
      - tiger_r
      - web_leads_r


  - user: equipment_rfm_api
    roles:
      - auctions_r
      - auction_data_r
      - equipment_r
      - equipment_fm_r
      - fleet_metrics_fm_r
      - equipment_marketing_r
      - equipment_validation_fm_r
      - equipment_marketing_fm_r
      - equipment_channels_fm_r
      - equipment_files_fm_r
      - equipment_photos_r
      - equipment_photos_fm_r
      - equipment_notes_history_fm_r
      - change_history_fm_r
      - equipment_catalog_configuration_r
      - equipment_catalog_r
      - equipment_catalog_fm_r
      - classification_photos_r
      - equipment_values_history_fm_r
      - equipment_values_history_rw
      - mkt_metrics_login_r
      - mkt_metrics_photo_r
      - mkt_metrics_asset_r
      - mkt_metrics_weblead_r
      - mkt_metrics_weblead_fm_r
      - mkt_metrics_proposal_r
      - mkt_metrics_proposal_fm_r
      - mkt_metrics_photo_fm_r
      - mpe_kpi_metrics_r
      - sales_txn_r
      - sales_txn_fm_r
      - sales_historical_txn_r
      - sales_values_lookup_reporting_r
      - values_lookup_transactions_rw
      - values_lookup_configurations_r
      - transaction_volume_auction_c_r
      - transaction_volume_auction_cs_r
      - transaction_volume_auction_csm_r
      - transaction_volume_auction_csmm_r
      - transaction_volume_auction_csmmy_r
      - transaction_volume_auction_quartile_csmm_r
      - transaction_volume_auction_eu_c_r
      - transaction_volume_auction_eu_cs_r
      - transaction_volume_auction_eu_csm_r
      - transaction_volume_auction_eu_csmm_r
      - transaction_volume_auction_eu_csmmy_r
      - transaction_volume_auction_eu_quartile_csmm_r
      - transaction_volume_retail_c_fm_r
      - transaction_volume_retail_cs_fm_r
      - transaction_volume_retail_csm_fm_r
      - transaction_volume_retail_csmm_fm_r
      - transaction_volume_retail_csmmy_fm_r
      - transaction_volume_retail_quartile_csmm_fm_r
      - value_trends_r
      - value_trends_chart_r
      - value_trends_uk_r
      - value_trends_uk_chart_r
      - equipment_values_residuals_r
      - equipment_values_residuals_fm_r
      - equipment_values_r
      - equipment_values_fm_r
      - equipment_values_configuration_r
      - equipment_values_configuration_fm_r
      - sales_prevailing_exchange_rates_r
      - tiger_r
      - postal_codes_r
      - proposal_history_rw
      - equipment_classification_r
      - values_lookup_schema_r
      - rb_taxonomy_r
      - equipment_classification_relevancy_r
      - web_leads_r
      - fleet_listings_fm_r
      - fleet_manager_publish_insights_taxonomy_and_localization_etl_r
      - rental_insights_etl_r

  - user: equipment_catalog_api
    roles:
      - equipment_r
      - equipment_fm_r
      - equipment_marketing_r
      - equipment_marketing_fm_r
      - equipment_channels_fm_r
      - equipment_photos_r
      - equipment_photos_fm_r
      - equipment_catalog_configuration_r
      - equipment_catalog_r
      - classification_photos_rw
      - equipment_values_history_rw
      - tiger_r
      - equipment_catalog_fm_r
      - web_leads_r
      - fleet_manager_publish_insights_taxonomy_and_localization_etl_r
      - equipment_files_fm_r

    default_privileges:
      grant: select
      schemas_scope: in-user-roles

  - user: equipment_catalog_etl
    roles:
      - equipment_rw
      - equipment_fm_r
      - equipment_marketing_rw
      - equipment_marketing_fm_r
      - equipment_channels_fm_r
      - equipment_photos_rw
      - equipment_photos_fm_r
      - equipment_catalog_configuration_rw
      - equipment_catalog_rw
      - classification_photos_rw
      - equipment_values_history_r
      - values_lookup_transactions_rw
      - root

  - user: equipment_etl
    roles:
      - equipment_rw
      - equipment_fm_r
      - equipment_marketing_rw
      - equipment_validation_fm_r
      - equipment_marketing_fm_r
      - equipment_channels_fm_r
      - equipment_photos_r
      - equipment_photos_fm_r
      - equipment_catalog_configuration_r
      - equipment_catalog_r
      - classification_photos_r
      - equipment_values_history_r
      - root

  - user: equipment_sync_monitor
    roles:
      - equipment_fm_r
      - equipment_marketing_fm_r
      - fleet_manager_all_tables_r
      - root

  - user: equipment_classification_etl
    roles:
      - equipment_classification_rw
      - root

  - user: equipment_photos_etl
    roles:
      - equipment_r
      - equipment_fm_r
      - equipment_marketing_r
      - equipment_marketing_fm_r
      - equipment_channels_fm_r
      - equipment_photos_rw
      - equipment_photos_fm_r
      - equipment_catalog_configuration_r
      - equipment_catalog_r
      - classification_photos_r
      - equipment_values_history_r
      - root

  - user: fleet_manager_backfill
    roles:
      - sold_asset_backfill_rw
      - equipment_photos_backfill_rw
      - meter_history_backfill_rw
      - asset_change_history_backfill_rw
      - list_price_history_backfill_rw
      - legacy_list_price_history_backfill_rw
      - list_price_customer_asking_history_backfill_rw
      - first_seen_by_rouse_backfill_rw
      - fleet_asset_files_backfill_rw
      - legacy_valuation_eligibility_history_backfill_rw
      - legacy_in_portal_history_backfill_rw
      - legacy_rpo_history_backfill_rw
      - fleet_customer_financing_programs_backfill_rw
      - proposal_history_backfill_rw
      - fields_set_by_users_backfill_rw
      - legacy_meter_history_backfill_rw
      - fleet_manager_backfill_rw
      - value_history_change_history_backfill_rw
      - root

  - user: fleet_manager_pg_export
    roles:
      - fleet_manager_all_tables_r
      - proposal_history_r

  - user: fleet_manager_pg_export_multi_tenant
    roles:
      - fleet_manager_all_tables_r
      - rfm_multi_all_tables_r
      - proposal_history_r

  - user: equipment_values_history_etl
    roles:
      - equipment_r
      - equipment_fm_r
      - equipment_marketing_r
      - equipment_marketing_fm_r
      - equipment_channels_fm_r
      - equipment_photos_r
      - equipment_photos_fm_r
      - equipment_catalog_configuration_r
      - equipment_catalog_r
      - classification_photos_r
      - equipment_values_history_fm_r
      - equipment_values_history_rw
      - root

  - user: rouse_rb_taxonomy_mapping_etl
    roles:
      - rouse_rb_taxonomy_mapping_cs_to_ct_rw
      - root

  - user: sales_txns_etl
    roles:
      - sales_txn_rw
      - sales_historical_txn_rw
      - sold_assets_r
      - fleet_customers_r
      - fleet_classification_r
      - sales_txn_fm_rw
      - sales_txn_historical_fm_rw
      - root

  - user: ss_export
    roles:
      - ss_export_r
      - ss_export_rw
      - root

  - user: mkt_metrics_etl
    roles:
      - mkt_metrics_login_rw
      - mkt_metrics_photo_rw
      - mkt_metrics_asset_rw
      - mkt_metrics_weblead_rw
      - mkt_metrics_proposal_rw
      - mkt_metrics_proposal_fm_rw
      - mkt_metrics_photo_fm_rw
      - mkt_metrics_weblead_fm_rw
      - root

  - user: equipment_configuration_variants
    roles:
      - values_lookup_configurations_rw
      - root

  - user: transaction_volume_etl
    roles:
      - transaction_volume_retail_c_fm_rw
      - transaction_volume_retail_cs_fm_rw
      - transaction_volume_retail_csm_fm_rw
      - transaction_volume_retail_csmm_fm_rw
      - transaction_volume_retail_csmmy_fm_rw
      - transaction_volume_retail_quartile_csmm_fm_rw
      - root

  - user: value_trends_etl
    roles:
      - value_trends_rw
      - value_trends_chart_rw
      - value_trends_uk_rw
      - value_trends_uk_chart_rw
      - root

  - user: equipment_values_residuals_etl
    roles:
      - equipment_values_residuals_rw
      - equipment_values_residuals_fm_rw
      - root

  - user: equipment_valuations_etl
    roles:
      - equipment_values_rw
      - equipment_values_fm_rw
      - equipment_values_configuration_rw
      - equipment_values_configuration_fm_rw
      - root

  - user: create_empty_lookup_values_etl
    roles:
      - values_lookup_transactions_rw
      - root

  - user: copy_classification_photos_etl
    roles:
      - classification_photos_rw
      - equipment_classification_r
      - root

  - user: auction_data_etl
    roles:
      - auction_data_rw
      - root

  - user: sales_prevailing_exchange_rates_etl
    roles:
      - sales_prevailing_exchange_rates_rw
      - root

  - user: sales_values_lookup_reporting
    roles:
      - root
      - sales_values_lookup_reporting_r
      - sales_values_lookup_reporting_rw

  - user: auctions_etl
    roles:
      - auctions_rw
      - root

  - user: transaction_volume_auction_etl
    roles:
      - transaction_volume_auction_c_rw
      - transaction_volume_auction_cs_rw
      - transaction_volume_auction_csm_rw
      - transaction_volume_auction_csmm_rw
      - transaction_volume_auction_csmmy_rw
      - transaction_volume_auction_quartile_csmm_rw
      - transaction_volume_auction_eu_c_rw
      - transaction_volume_auction_eu_cs_rw
      - transaction_volume_auction_eu_csm_rw
      - transaction_volume_auction_eu_csmm_rw
      - transaction_volume_auction_eu_csmmy_rw
      - transaction_volume_auction_eu_quartile_csmm_rw
      - root

  - user: fleet_manager_supercategory_etl
    roles:
      - fleet_manager_classification_rw
      - root

  - user: fleet_manager_cat_product_group
    roles:
      - fleet_manager_cat_product_group_rw
      - root

  - user: fleet_manager_customers_metrics_reporting
    roles:
      - fleet_manager_customers_metrics_reporting_r
      - root

  - user: ims_fleet_ingest_etl
    roles:
      - appraisal_valuation_canary_r
      - ims_fleet_ingest_etl_r
      - root

  - user: equipment_notes_history_etl
    roles:
      - equipment_notes_history_fm_r
      - root

  - user: change_history_etl
    roles:
      - change_history_fm_r
      - root

  - user: ims_fleet_refresh_etl
    roles:
      - ims_fleet_refresh_etl_r
      - root

  - user: equipment_syndication_etl
    roles:
      - equipment_fm_r
      - equipment_photos_fm_r
      - ims_fleet_ingest_etl_r
      - rfm_client_fleet_customers_rw
      - sales_prevailing_exchange_rates_r
      - root

  - user: proposal_history_etl
    roles:
      - proposal_history_rw
      - root

  - user: fleet_manager_sync
    roles:
      - asset_fields_set_by_user_sync_r
      - asset_fields_set_by_user_sync_rw
      - root

  - user: fleet_manager_valuations_history_etl
    roles:
      - fleet_manager_valuations_history_etl_r

  - user: equipment_files_etl
    roles:
      - equipment_files_fm_rw
      - root

  - user: web_leads_etl
    roles:
      - web_leads_rw
      - root

  - user: client_asset_etl
    roles:
      - fleet_manager_all_tables_r
      - root

  - user: client_etl
    roles:
      - fleet_manager_all_tables_r
      - root

  - user: countries_etl
    roles:
      - rfm_global_r
      - root

  - user: csmm_relevancy_etl
    roles:
      - equipment_classification_relevancy_rw
      - root

  - user: mpe_kpi_metrics_etl
    roles:
      - mpe_kpi_metrics_rw
      - root

  - user: mpe_transactions_etl
    roles:
      - mpe_transactions_rw
      - root

  - user: rfm_archiving_etl
    roles:
      - rfm_archiving_rw
      - root

  - user: delta
    roles:
      - root
      - fleet_manager_delta_rw

  - user: sales_erp_sync
    roles:
      - sales_erp_sync_r
      - equipment_fm_r
      - equipment_values_fm_r
      - fleet_manager_all_tables_r
      - rfm_global_r
      - equipment_classification_r
      - sales_prevailing_exchange_rates_r
      - transaction_volume_auction_c_r
      - transaction_volume_auction_cs_r
      - transaction_volume_auction_csm_r
      - transaction_volume_auction_csmm_r
      - transaction_volume_auction_csmmy_r
      - transaction_volume_auction_quartile_csmm_r
      - transaction_volume_auction_eu_c_r
      - transaction_volume_auction_eu_cs_r
      - transaction_volume_auction_eu_csm_r
      - transaction_volume_auction_eu_csmm_r
      - transaction_volume_auction_eu_csmmy_r
      - transaction_volume_auction_eu_quartile_csmm_r
      - transaction_volume_retail_c_fm_r
      - transaction_volume_retail_cs_fm_r
      - transaction_volume_retail_csm_fm_r
      - transaction_volume_retail_csmm_fm_r
      - transaction_volume_retail_csmmy_fm_r
      - transaction_volume_retail_quartile_csmm_fm_r

  - user: sales_fleet_manager
    roles: &sales-fleet-manager-roles
      - fleet_manager_all_tables_rw
      - rfm_global_r
      - classification_photos_r
      - postal_codes_r
      - auction_data_r
      - auctions_r
      - equipment_classification_r
      - mpe_kpi_metrics_r
      - rfm_archiving_r
      - sales_prevailing_exchange_rates_r
      - transaction_volume_auction_c_r
      - transaction_volume_auction_cs_r
      - transaction_volume_auction_csm_r
      - transaction_volume_auction_csmm_r
      - transaction_volume_auction_csmmy_r
      - transaction_volume_auction_quartile_csmm_r
      - transaction_volume_auction_eu_c_r
      - transaction_volume_auction_eu_cs_r
      - transaction_volume_auction_eu_csm_r
      - transaction_volume_auction_eu_csmm_r
      - transaction_volume_auction_eu_csmmy_r
      - transaction_volume_auction_eu_quartile_csmm_r
      - transaction_volume_retail_c_fm_r
      - transaction_volume_retail_cs_fm_r
      - transaction_volume_retail_csm_fm_r
      - transaction_volume_retail_csmm_fm_r
      - transaction_volume_retail_csmmy_fm_r
      - transaction_volume_retail_quartile_csmm_fm_r
      - value_trends_r
      - value_trends_chart_r
      - value_trends_uk_r
      - value_trends_uk_chart_r
      - values_lookup_configurations_r
      - partman_rw
      - rb_taxonomy_r
      - root
      - fleet_manager_publish_insights_taxonomy_and_localization_etl_r
      - rental_insights_etl_r

  - user: fleet_manager_integrations
    roles: *sales-fleet-manager-roles

  - user: fleet_manager_tools
    roles:
      - fleet_manager_all_tables_r
      - fleet_tooling_rw
      - root

  - user: restore_etl
    roles:
      - root
      - value_trends_rw
      - sales_prevailing_exchange_rates_rw
      - auctions_rw
      - transaction_volume_auction_c_rw
      - transaction_volume_auction_cs_rw
      - transaction_volume_auction_csm_rw
      - transaction_volume_auction_csmm_rw
      - transaction_volume_auction_csmmy_rw
      - transaction_volume_auction_quartile_csmm_rw
      - transaction_volume_auction_eu_c_rw
      - transaction_volume_auction_eu_cs_rw
      - transaction_volume_auction_eu_csm_rw
      - transaction_volume_auction_eu_csmm_rw
      - transaction_volume_auction_eu_csmmy_rw
      - transaction_volume_auction_eu_quartile_csmm_rw
      - transaction_volume_retail_c_fm_rw
      - transaction_volume_retail_cs_fm_rw
      - transaction_volume_retail_csm_fm_rw
      - transaction_volume_retail_csmm_fm_rw
      - transaction_volume_retail_csmmy_fm_rw
      - transaction_volume_retail_quartile_csmm_fm_rw

  - user: fleet_manager_sales_subcategory
    roles:
      - root
      - fleet_manager_all_tables_r
      - fleet_manager_sales_subcategory_r
      - fleet_manager_sales_subcategory_rw

  - user: appraisal_valuation_canary_etl
    roles:
      - root
      - appraisal_valuation_canary_rw

  - user: legacy_provisioning_etl
    roles:
      - fleet_manager_all_tables_r

  - user: sales_rfm_channels_api
    roles:
      - fleet_manager_all_tables_rw
      - rfm_global_r
      - classification_photos_r
      - postal_codes_r
      - auction_data_r
      - auctions_r
      - equipment_classification_r
      - mpe_kpi_metrics_r
      - rfm_archiving_r
      - sales_prevailing_exchange_rates_r
      - transaction_volume_auction_c_r
      - transaction_volume_auction_cs_r
      - transaction_volume_auction_csm_r
      - transaction_volume_auction_csmm_r
      - transaction_volume_auction_csmmy_r
      - transaction_volume_auction_quartile_csmm_r
      - transaction_volume_auction_eu_c_r
      - transaction_volume_auction_eu_cs_r
      - transaction_volume_auction_eu_csm_r
      - transaction_volume_auction_eu_csmm_r
      - transaction_volume_auction_eu_csmmy_r
      - transaction_volume_auction_eu_quartile_csmm_r
      - transaction_volume_retail_c_fm_r
      - transaction_volume_retail_cs_fm_r
      - transaction_volume_retail_csm_fm_r
      - transaction_volume_retail_csmm_fm_r
      - transaction_volume_retail_csmmy_fm_r
      - transaction_volume_retail_quartile_csmm_fm_r
      - value_trends_r
      - value_trends_chart_r
      - value_trends_uk_r
      - value_trends_uk_chart_r
      - values_lookup_configurations_r
      - partman_rw
      - root
      - equipment_r
      - equipment_fm_r
      - equipment_marketing_r
      - equipment_marketing_fm_r
      - equipment_validation_fm_r
      - equipment_channels_fm_r
      - equipment_files_fm_r
      - equipment_photos_r
      - equipment_photos_fm_r
      - equipment_notes_history_fm_r
      - change_history_fm_r
      - equipment_catalog_configuration_r
      - equipment_catalog_r
      - equipment_catalog_fm_r
      - equipment_values_history_fm_r
      - equipment_values_history_rw
      - mkt_metrics_login_r
      - mkt_metrics_photo_r
      - mkt_metrics_asset_r
      - mkt_metrics_weblead_r
      - mkt_metrics_weblead_fm_r
      - mkt_metrics_proposal_r
      - mkt_metrics_proposal_fm_r
      - mkt_metrics_photo_fm_r
      - sales_txn_r
      - sales_txn_fm_r
      - sales_historical_txn_r
      - values_lookup_transactions_rw
      - equipment_values_residuals_r
      - equipment_values_residuals_fm_r
      - equipment_values_r
      - equipment_values_fm_r
      - equipment_values_configuration_r
      - equipment_values_configuration_fm_r
      - tiger_r
      - proposal_history_r
      - values_lookup_schema_r
      - rb_taxonomy_r

  - user: sales_google_analytics_page_views
    roles:
      - root
      - sales_google_analytics_page_views_r
      - sales_google_analytics_page_views_rw

  - user: fleet_manager_multi_checks
    roles:
      - root
      - equipment_fm_r
      - rfm_multi_all_tables_r
      - change_history_fm_r
      - classification_photos_r
      - equipment_fm_r
      - equipment_files_fm_r
      - equipment_channels_fm_r
      - equipment_catalog_fm_r
      - equipment_notes_history_fm_r
      - equipment_photos_fm_r
      - equipment_validation_fm_r
      - equipment_values_configuration_fm_r
      - equipment_values_fm_r
      - equipment_values_history_fm_r
      - equipment_values_residuals_fm_r
      - fleet_metrics_fm_r
      - mkt_metrics_proposal_fm_r
      - mkt_metrics_photo_fm_r
      - sales_txn_fm_r
      - sales_txn_historical_fm_r
      - web_leads_r

  - user: fleet_manager_fleet_listings
    roles:
      - root
      - fleet_listings_fm_r
      - fleet_manager_all_tables_r

  - user: fleet_manager_fleet_metrics
    roles:
      - root
      - fleet_manager_all_tables_r

  - user: fleet_manager_users_etl
    roles:
      - root

  - user: rb_taxonomy_etl
    roles:
      - rb_taxonomy_rw
      - root

  - user: values_lookup_schema_etl
    roles:
      - values_lookup_schema_rw
      - root

  - user: fleet_manager_partition_management_pipeline
    roles:
      - partman_rw
      - root

  - user: sales_rfm_user_config_reporting
    roles:
      - user_config_r

  - user: smartequip_parts_book_etl
    roles:
      - fleet_manager_all_tables_r

  - user: migrations_etl
    roles:
      - fleet_manager_all_tables_rw
      - root

  - user: marketable_life_etl
    roles:
      - marketable_life_rw
      - root

  - user: united_xvalues_export_etl
    roles:
      - equipment_values_fm_rw
      - root
      
  - user: equipment_view_publisher
    roles:
      - equipment_rw
      - equipment_marketing_rw
      - equipment_values_rw
      - equipment_values_fm_rw
      - equipment_values_configuration_rw
      - equipment_values_configuration_fm_rw
      - fleet_manager_all_tables_r
      - root

  - user: sales_fmx_api
    roles: pg_read_all_data

  - user: fleet_manager_publish_insights_taxonomy_and_localization_etl
    roles:
      - root
      - rb_taxonomy_rw
      - fleet_manager_classification_rw

  - user: rental_insights_etl
    roles:
      - root
      - rental_insights_etl_r
      - rental_insights_etl_rw
      
  - user: mixer_values_lookup_configuration_sync
    roles:
      - root
      - mixer_values_lookup_configuration_sync_rw
