alias:
  anna_biddle:
    roles: []
    user: <EMAIL>
    type: iam_user
  billy_herren:
    roles: []
    user: <EMAIL>
    type: iam_user
  brett_malone:
    roles: []
    user: <EMAIL>
    type: iam_user
  chris_<PERSON><PERSON><PERSON>:
    roles: []
    user: chris.<PERSON><PERSON><PERSON>@rouseservices.com
    type: iam_user
  dante_andrade:
    roles: []
    user: <EMAIL>
    type: iam_user
  david_ellend:
    roles: []
    user: <EMAIL>
    type: iam_user
  eduardo_serrano:
    roles: []
    user: <EMAIL>
    type: iam_user
  erick_ramirez:
    roles: []
    user: <EMAIL>
    type: iam_user
  hugo_castro:
    roles: []
    user: <EMAIL>
    type: iam_user
  john_sparks:
    roles: []
    user: <EMAIL>
    type: iam_user
  juan_vazquez:
    roles: []
    user: <EMAIL>
    type: iam_user
  ken_barton:
    roles: []
    user: <EMAIL>
    type: iam_user
  marcelo_shild:
    roles: []
    user: <EMAIL>
    type: iam_user
  matias_sarmiento:
    roles: []
    user: <EMAIL>
    type: iam_user
  nick_milburn:
    roles: []
    user: <EMAIL>
    type: iam_user
  ray_yoshida:
    roles: []
    user: <EMAIL>
    type: iam_user
  teddy_limousin:
    roles: []
    user: <EMAIL>
    type: iam_user


instance_class:
  rfm:
    users:
      - david_ellend
      - brett_malone
      - billy_herren
      - erick_ramirez
      - hugo_castro
      - eduardo_serrano

  rfm_dev:
    users:
      - david_ellend
      - brett_malone
      - billy_herren
      - erick_ramirez
      - hugo_castro
      - eduardo_serrano
      - ray_yoshida
    groups:
      - description: readonly
        roles:
          - pg_read_all_data
        users:
          - ray_yoshida

  platform:
    users:
      - david_ellend
      - brett_malone
      - billy_herren
      - erick_ramirez
      - hugo_castro

  appraisals:
    users:
      - ken_barton
      - john_sparks
      - juan_vazquez

  configs01:
    users:
      - david_ellend

  salesglobal:
    users:
      - david_ellend

  alloy:
    users: []

instance:
  rfm-services01-dev: rfm_dev
  rfm03-dev: rfm_dev
  rfm04r0-dev: rfm_dev
  rfm07-dev: rfm_dev
  rfm-services01-prod: rfm
  rfm03-prod: rfm
  rfm04-prod: rfm
  rfm05-prod: rfm
  rfm06r2-prod: rfm
  rfm07-prod: rfm
  rfm08r1-prod: rfm
  rfm09-prod: rfm
  rfm01-ppe-prod: rfm
  rfm-services01-ppe: rfm_dev
  
  rfm08-dev: alloy
  alloydb-rfm08-dev: alloy
  rfm101-prod: alloy
  rfm1001-dev: alloy
  rfm1001-prod: alloy

  configs01-dev: configs01
  configs01-prod: configs01

  platform01-dev: platform
  platform01-prod: platform

  record360-03-dev: appraisals
  appraisals-01-dev: appraisals
  appraisals-01-prod: appraisals

  salesglobal02-dev: salesglobal
  salesglobal02-prod: salesglobal

  
