db_users:
  - user: "pgbouncermonitor"
    password: "{{ lookup('rouse.gcp.secret_manager', 'alloydb_pgbouncer_monitor', project_id=cloud_sql_project) }}"

  - user: health_check
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_health_check', project_id=cloud_sql_project) }}"

  - user: equipment_catalog_api
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_equipment_catalog_api', project_id=cloud_sql_project) }}"

  - user: equipment_rfm_api
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_equipment_rfm_api', project_id=cloud_sql_project) }}"

  - user: equipment_sales_api
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_equipment_sales_api', project_id=cloud_sql_project) }}"

  - user: equipment_catalog_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_equipment_catalog_etl', project_id=cloud_sql_project) }}"

  - user: equipment_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_equipment_etl', project_id=cloud_sql_project) }}"

  - user: equipment_photos_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_equipment_photos_etl', project_id=cloud_sql_project) }}"

  - user: equipment_sync_monitor
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_equipment_sync_monitor', project_id=cloud_sql_project) }}"

  - user: fleet_manager_backfill
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_fleet_manager_backfill', project_id=cloud_sql_project) }}"

  - user: rouse_rb_taxonomy_mapping_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_rouse_rb_taxonomy_mapping_etl', project_id=cloud_sql_project) }}"

  - user: fleet_manager_pg_export
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_fleet_manager_pg_export', project_id=cloud_sql_project) }}"

  - user: fleet_manager_pg_export_multi_tenant
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_fleet_manager_pg_export_multi_tenant', project_id=cloud_sql_project) }}"

  - user: equipment_values_history_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_equipment_values_history_etl', project_id=cloud_sql_project) }}"

  - user: mkt_metrics_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_mkt_metrics_etl', project_id=cloud_sql_project) }}"

  - user: sales_txns_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_sales_txns_etl', project_id=cloud_sql_project) }}"

  - user: equipment_configuration_variants
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_equipment_configuration_variants', project_id=cloud_sql_project) }}"

  - user: transaction_volume_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_transaction_volume_etl', project_id=cloud_sql_project) }}"

  - user: value_trends_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_value_trends_etl', project_id=cloud_sql_project) }}"

  - user: equipment_values_residuals_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_equipment_values_residuals_etl', project_id=cloud_sql_project) }}"

  - user: equipment_valuations_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_equipment_valuations_etl', project_id=cloud_sql_project) }}"

  - user: equipment_notes_history_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_equipment_notes_history_etl', project_id=cloud_sql_project) }}"

  - user: change_history_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_change_history_etl', project_id=cloud_sql_project) }}"

  - user: copy_classification_photos_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_copy_classification_photos_etl', project_id=cloud_sql_project) }}"

  - user: create_empty_lookup_values_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_create_empty_lookup_values_etl', project_id=cloud_sql_project) }}"

  - user: auction_data_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_auction_data_etl', project_id=cloud_sql_project) }}"
  
  - user: sales_prevailing_exchange_rates_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_sales_prevailing_exchange_rates_etl', project_id=cloud_sql_project) }}"

  - user: sales_values_lookup_reporting
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_sales_values_lookup_reporting', project_id=cloud_sql_project) }}"

  - user: auctions_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_auctions_etl', project_id=cloud_sql_project) }}"
  
  - user: transaction_volume_auction_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_transaction_volume_auction_etl', project_id=cloud_sql_project) }}"

  - user: equipment_classification_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_equipment_classification_etl', project_id=cloud_sql_project) }}"

  - user: fleet_manager_supercategory_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_fleet_manager_supercategory_etl', project_id=cloud_sql_project) }}"

  - user: fleet_manager_cat_product_group
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_fleet_manager_cat_product_group', project_id=cloud_sql_project) }}"

  - user: fleet_manager_customers_metrics_reporting
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_fleet_manager_customers_metrics_reporting', project_id=cloud_sql_project) }}"

  - user: fleet_manager_sales_subcategory
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_platform_fleet_manager_sales_subcategory', project_id=cloud_sql_project) }}"

  - user: ims_fleet_ingest_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_ims_fleet_ingest_etl', project_id=cloud_sql_project) }}"

  - user: ims_fleet_refresh_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_ims_fleet_refresh_etl', project_id=cloud_sql_project) }}"

  - user: equipment_syndication_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_equipment_syndication_etl', project_id=cloud_sql_project) }}"

  - user: proposal_history_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_proposal_history_etl', project_id=cloud_sql_project) }}"

  - user: fleet_manager_sync
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_fleet_manager_sync', project_id=cloud_sql_project) }}"

  - user: fleet_manager_valuations_history_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_fleet_manager_valuations_history_etl', project_id=cloud_sql_project) }}"

  - user: equipment_files_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_equipment_files_etl', project_id=cloud_sql_project) }}"
  
  - user: web_leads_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_web_leads_etl', project_id=cloud_sql_project) }}"

  - user: client_asset_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_client_asset_etl', project_id=cloud_sql_project) }}"

  - user: client_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_client_etl', project_id=cloud_sql_project) }}"

  - user: countries_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_countries_etl', project_id=cloud_sql_project) }}"

  - user: mpe_kpi_metrics_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_mpe_kpi_metrics_etl', project_id=cloud_sql_project) }}"

  - user: mpe_transactions_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_mpe_transactions_etl', project_id=cloud_sql_project) }}"

  - user: rfm_archiving_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_rfm_archiving_etl', project_id=cloud_sql_project) }}"

  - user: restore_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_restore_etl', project_id=cloud_sql_project) }}"

  - user: appraisal_valuation_canary_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_appraisal_valuation_canary_etl', project_id=cloud_sql_project) }}"

  - user: sales_rfm_channels_api
    password: "{{ (lookup('rouse.gcp.secret_manager', 'dbuser_sales_rfm_channels_api', project_id=cloud_sql_project) | from_json).password }}"

  - user: "{{ (lookup('rouse.gcp.secret_manager', 'dbuser_fleet_manager_tools', project_id=cloud_sql_project) | from_json).username }}"
    password: "{{ (lookup('rouse.gcp.secret_manager', 'dbuser_fleet_manager_tools', project_id=cloud_sql_project) | from_json).password }}"

  - user: "{{ (lookup('rouse.gcp.secret_manager', 'dbuser_sales_fleet_manager', project_id=cloud_sql_project) | from_json).username }}"
    password: "{{ (lookup('rouse.gcp.secret_manager', 'dbuser_sales_fleet_manager', project_id=cloud_sql_project) | from_json).password }}"
    search_path: fleet_manager

  # dbuser: fleet_manger_integrations, namespace: sales_fleet_manager_integrations
  - user: "{{ (lookup('rouse.gcp.secret_manager', 'dbuser_sales_fleet_manager_integrations', project_id=cloud_sql_project) | from_json).username }}"
    password: "{{ (lookup('rouse.gcp.secret_manager', 'dbuser_sales_fleet_manager_integrations', project_id=cloud_sql_project) | from_json).password }}"
    search_path: fleet_manager

  - user: "{{ (lookup('rouse.gcp.secret_manager', 'dbuser_delta', project_id=cloud_sql_project) | from_json).username }}"
    password: "{{ (lookup('rouse.gcp.secret_manager', 'dbuser_delta', project_id=cloud_sql_project) | from_json).password }}"
    search_path: fleet_manager_delta, rfm_global

  - user: "{{ (lookup('rouse.gcp.secret_manager', 'dbuser_user_profiles', project_id=cloud_sql_project) | from_json).username }}"
    password: "{{ (lookup('rouse.gcp.secret_manager', 'dbuser_user_profiles', project_id=cloud_sql_project) | from_json).password }}"

  - user: legacy_provisioning_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_legacy_provisioning_etl', project_id=cloud_sql_project) }}"

  - user: fleet_manager_multi_checks
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_fleet_manager_multi_checks', project_id=cloud_sql_project) }}"

  - user: fleet_manager_fleet_listings
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_fleet_manager_fleet_listings', project_id=cloud_sql_project) }}"

  - user: fleet_manager_fleet_metrics
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_fleet_manager_fleet_metrics', project_id=cloud_sql_project) }}"

  - user: fleet_manager_users_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_fleet_manager_users_etl', project_id=cloud_sql_project) }}"

  - user: migrations_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_migrations_etl', project_id=cloud_sql_project) }}"

  - user: rb_taxonomy_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_rb_taxonomy_etl', project_id=cloud_sql_project) }}"

  - user: values_lookup_schema_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_salesglobal_values_lookup_schema_etl', project_id=cloud_sql_project) }}"

  - user: fleet_manager_partition_management_pipeline
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_fleet_manager_partition_management_pipeline', project_id=cloud_sql_project) }}"

  - user: sales_erp_sync
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_sales_erp_sync', project_id=cloud_sql_project) }}"

  - user: sales_rfm_user_config_reporting
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_sales_rfm_user_config_reporting', project_id=cloud_sql_project) }}"
  
  - user: sales_google_analytics_page_views
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_sales_google_analytics_page_views', project_id=cloud_sql_project) }}"

  - user: ss_export
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_ss_export', project_id=cloud_sql_project) }}"
  
  - user: smartequip_parts_book_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_smartequip_parts_book_etl', project_id=cloud_sql_project) }}"
  
  - user: replication_lag_user
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_replication_lag_user', project_id=cloud_sql_project) }}"

  - user: marketable_life_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_marketable_life_etl', project_id=cloud_sql_project) }}"

  - user: csmm_relevancy_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_csmm_relevancy_etl', project_id=cloud_sql_project) }}"
  
  - user: united_xvalues_export_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_united_xvalues_export_etl', project_id=cloud_sql_project) }}"
  
  - user: equipment_view_publisher
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_equipment_view_publisher', project_id=cloud_sql_project) }}"

  - user: sales_fmx_api
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_sales_fmx_api', project_id=cloud_sql_project) }}"

  - user: fleet_manager_publish_insights_taxonomy_and_localization_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_fleet_manager_publish_insights_taxonomy_and_localization_etl', project_id=cloud_sql_project) }}"

  - user: rental_insights_etl
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_rental_insights_etl', project_id=cloud_sql_project) }}" 
    
  - user: mixer_values_lookup_configuration_sync
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_rfm_mixer_values_lookup_configuration_sync', project_id=cloud_sql_project) }}"
    type: user
