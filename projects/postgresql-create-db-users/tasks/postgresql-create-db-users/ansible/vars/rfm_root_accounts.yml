root_users:
  - user: "{{ (lookup('rouse.gcp.secret_manager', ''.join(('dbadmin_rfm01_', env_type)), project_id=management_project) | from_json).username }}"
    password: "{{ (lookup('rouse.gcp.secret_manager', ''.join(('dbadmin_rfm01_', env_type)), project_id=management_project) | from_json).password }}"
  - user: "postgres"
    password: "{{ (lookup('rouse.gcp.secret_manager', ''.join(('dbadmin_rfm01_', env_type)), project_id=management_project) | from_json).password }}"

extra_users:
  - user: "{{ (lookup('rouse.gcp.secret_manager', 'dbuser_rfm_fleet_manager_delta', project_id=management_project) | from_json).username }}"
    password: "{{ (lookup('rouse.gcp.secret_manager', 'dbuser_rfm_fleet_manager_delta', project_id=management_project) | from_json).password }}"
  - user: "{{ (lookup('rouse.gcp.secret_manager', 'dbuser_pgbouncer_monitoring', project_id=management_project) | from_json).username }}"
    password: "{{ (lookup('rouse.gcp.secret_manager', 'dbuser_pgbouncer_monitoring', project_id=management_project) | from_json).password }}"
