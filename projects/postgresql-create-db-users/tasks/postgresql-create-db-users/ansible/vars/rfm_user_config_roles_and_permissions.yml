database_objects:
  schemas:
    user_config:
      authorization: root

database_roles:
  - name: user_profiles_r
    where_get_tables: table_name = 'user_profiles' and table_schema = 'user_config'
    grant: select

  - name: user_profiles_rw
    where_get_tables: table_name = 'user_profiles' and table_schema = 'user_config'
    grant: select, insert, update, delete

  - name: fleet_users_rw
    where_get_tables: table_name = 'fleet_users' and table_schema = 'user_config'
    grant: select, insert, update, delete

  - name: user_logins_r
    where_get_tables: (table_name = 'user_logins' and table_schema = 'user_config')
    grant: select

  - name: fleet_customers_r
    where_get_tables: (table_name = 'fleet_customers' and table_schema = 'user_config')
    grant: select

  - name: user_config_r
    where_get_schemas: table_schema = 'user_config'
    grant: select

  - name: user_config_rw
    where_get_schemas: table_schema = 'user_config'
    grant: select, update

  - name: sales_prevailing_exchange_rates_r
    where_get_tables: (table_name = 'sales_prevailing_exchange_rates' and table_schema not like '%_version')  or (table_name similar to 'sales_prevailing_exchange_rates_\d{14}' and table_schema like '%_version')
    grant: select

  - name: auction_data_r
    where_get_tables: (table_name = 'auction_data' and table_schema not like '%_version')  or (table_name similar to 'auction_data_\d{14}' and table_schema like '%_version')
    grant: select

  - name: auctions_r
    where_get_tables: (table_name = 'auctions' and table_schema not like '%_version')  or (table_name similar to 'auctions_\d{14}' and table_schema like '%_version')
    grant: select

  - name: equipment_classification_r
    where_get_tables: (table_name like 'equipment_classification%' and table_schema not like '%_version')  or ((table_name similar to 'equipment_classification_category_\d{14}' or table_name similar to 'equipment_classification_subcategory_\d{14}' or table_name similar to 'equipment_classification_\d{14}') and table_schema like '%_version')
    grant: select

  - name: mpe_kpi_metrics_r
    where_get_tables: (table_name like 'mpe_kpi_metrics%' and table_schema not like '%_version')  or ((table_name similar to 'mpe_kpi_metrics_fm_\d{14}' or table_name similar to 'mpe_kpi_metrics_\d{14}') and table_schema like '%_version')
    grant: select
    
  - name: rb_taxonomy_r
    where_get_tables: (table_name like 'rb_taxonomy%' and table_schema not like '%_version')  or ((table_name similar to 'rb_taxonomy_\d{14}') and table_schema like '%_version')
    grant: select

  - name: values_lookup_schema_r
    where_get_tables: (table_name like 'rfm_equipment_classification%' and table_schema not like '%_version')  or ((table_name similar to 'rfm_equipment_classification_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_retail_c_fm_r
    where_get_tables: ((table_name in ('transaction_volume_retail_c_fm','transaction_volume_retail_insi_c_fm', 'transaction_volume_retail_eu_c_fm', 'transaction_volume_retail_insi_eu_c_fm')) and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_c_\d{14}' or table_name similar to 'transaction_volume_retail_insi_c_\d{14}' or table_name similar to 'transaction_volume_retail_eu_c_\d{14}' or table_name similar to 'transaction_volume_retail_insi_eu_c_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_retail_cs_fm_r
    where_get_tables: ((table_name in ('transaction_volume_retail_cs_fm','transaction_volume_retail_insi_c_fm', 'transaction_volume_retail_eu_cs_fm', 'transaction_volume_retail_insi_eu_cs_fm')) and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_cs_\d{14}' or table_name similar to 'transaction_volume_retail_insi_cs_\d{14}' or table_name similar to 'transaction_volume_retail_eu_cs_\d{14}' or table_name similar to 'transaction_volume_retail_insi_eu_cs_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_retail_csm_fm_r
    where_get_tables: ((table_name in ('transaction_volume_retail_csm_fm','transaction_volume_retail_insi_csm_fm', 'transaction_volume_retail_eu_csm_fm', 'transaction_volume_retail_insi_eu_csm_fm')) and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_csm_\d{14}' or table_name similar to 'transaction_volume_retail_insi_csm_\d{14}' or table_name similar to 'transaction_volume_retail_eu_csm_\d{14}' or table_name similar to 'transaction_volume_retail_insi_eu_csm_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_retail_csmm_fm_r
    where_get_tables: ((table_name in ('transaction_volume_retail_csmm_fm','transaction_volume_retail_insi_csmm_fm', 'transaction_volume_retail_eu_csmm_fm', 'transaction_volume_retail_insi_eu_csmm_fm')) and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_csmm_\d{14}' or table_name similar to 'transaction_volume_retail_insi_csmm_\d{14}' or table_name similar to 'transaction_volume_retail_eu_csmm_\d{14}' or table_name similar to 'transaction_volume_retail_insi_eu_csmm_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_retail_csmmy_fm_r
    where_get_tables: ((table_name in ('transaction_volume_retail_csmmy_fm','transaction_volume_retail_insi_csmmy_fm', 'transaction_volume_retail_eu_csmmy_fm', 'transaction_volume_retail_insi_eu_csmmy_fm')) and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_csmmy_\d{14}' or table_name similar to 'transaction_volume_retail_insi_csmmy_\d{14}' or table_name similar to 'transaction_volume_retail_eu_csmmy_\d{14}' or table_name similar to 'transaction_volume_retail_insi_eu_csmmy_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_retail_quartile_csmm_fm_r
    where_get_tables: ((table_name = 'transaction_volume_retail_quartile_csmm_fm' or table_name = 'transaction_volume_retail_eu_quartile_csmm_fm') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_retail_quartile_csmm_\d{14}' or table_name similar to 'transaction_volume_retail_eu_quartile_csmm_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_c_r
    where_get_tables: (table_name in ('transaction_volume_auction_c', 'transaction_volume_auction_insi_c') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_c_\d{14}' or table_name similar to 'transaction_volume_auction_insi_c_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_cs_r
    where_get_tables: (table_name in ('transaction_volume_auction_cs', 'transaction_volume_auction_insi_cs') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_cs_\d{14}' or table_name similar to 'transaction_volume_auction_insi_cs_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_csm_r
    where_get_tables: (table_name in ('transaction_volume_auction_csm', 'transaction_volume_auction_insi_csm') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_csm_\d{14}' or table_name similar to 'transaction_volume_auction_insi_csm_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_csmm_r
    where_get_tables: (table_name in ('transaction_volume_auction_csmm', 'transaction_volume_auction_insi_csmm') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_csmm_\d{14}' or table_name similar to 'transaction_volume_auction_insi_csmm_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_csmmy_r
    where_get_tables: (table_name in ('transaction_volume_auction_csmmy', 'transaction_volume_auction_insi_csmmy') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_csmmy_\d{14}' or table_name similar to 'transaction_volume_auction_insi_csmmy_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_quartile_csmm_r
    where_get_tables: (table_name = 'transaction_volume_auction_quartile_csmm' and table_schema not like '%_version')  or (table_name similar to 'transaction_volume_auction_quartile_csmm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_eu_c_r
    where_get_tables: (table_name in ('transaction_volume_auction_eu_c', 'transaction_volume_auction_insi_eu_c') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_eu_c_\d{14}' or table_name similar to 'transaction_volume_auction_insi_eu_c_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_eu_cs_r
    where_get_tables: (table_name in ('transaction_volume_auction_eu_cs', 'transaction_volume_auction_insi_eu_cs') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_eu_cs_\d{14}' or table_name similar to 'transaction_volume_auction_insi_eu_cs_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_eu_csm_r
    where_get_tables: (table_name in ('transaction_volume_auction_eu_csm', 'transaction_volume_auction_insi_eu_csm') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_eu_csm_\d{14}' or table_name similar to 'transaction_volume_auction_insi_eu_csm_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_eu_csmm_r
    where_get_tables: (table_name in ('transaction_volume_auction_eu_csmm', 'transaction_volume_auction_insi_eu_csmm') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_eu_csmm_\d{14}' or table_name similar to 'transaction_volume_auction_insi_eu_csmm_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_eu_csmmy_r
    where_get_tables: (table_name in ('transaction_volume_auction_eu_csmmy', 'transaction_volume_auction_insi_eu_csmmy') and table_schema not like '%_version')  or ((table_name similar to 'transaction_volume_auction_eu_csmmy_\d{14}' or table_name similar to 'transaction_volume_auction_insi_eu_csmmy_\d{14}') and table_schema like '%_version')
    grant: select

  - name: transaction_volume_auction_eu_quartile_csmm_r
    where_get_tables: (table_name = 'transaction_volume_auction_eu_quartile_csmm' and table_schema not like '%_version')  or (table_name similar to 'transaction_volume_auction_eu_quartile_csmm_\d{14}' and table_schema like '%_version')
    grant: select

  - name: value_trends_r
    where_get_tables: ((table_name = 'value_trends' or table_name = 'value_trends_all' or table_name = 'value_trends_all_chart') and table_schema not like '%_version')  or ((table_name similar to 'value_trends_\d{14}' or table_name similar to 'value_trends_all_\d{14}' or table_name similar to 'value_trends_all_chart_\d{14}' ) and table_schema like '%_version')
    grant: select

  - name: value_trends_chart_r
    where_get_tables: (table_name = 'value_trends_chart' and table_schema not like '%_version')  or (table_name similar to 'value_trends_chart_\d{14}' and table_schema like '%_version')
    grant: select

  - name: value_trends_uk_r
    where_get_tables: (table_name = 'value_trends_uk' and table_schema not like '%_version')  or (table_name similar to 'value_trends_uk_\d{14}' and table_schema like '%_version')
    grant: select

  - name: value_trends_uk_chart_r
    where_get_tables: (table_name = 'value_trends_uk_chart' and table_schema not like '%_version')  or (table_name similar to 'value_trends_uk_chart_\d{14}' and table_schema like '%_version')
    grant: select

  - name: values_lookup_configurations_r
    where_get_tables: (table_name = 'values_lookup_configurations' and table_schema not like '%_version')  or (table_name similar to 'values_lookup_configurations_\d{14}' and table_schema like '%_version')
    grant: select

  - name: rouse_rb_taxonomy_mapping_cs_to_ct_r
    where_get_tables: (table_name = 'rouse_rb_taxonomy_mapping_cs_to_ct' and table_schema not like '%_version')  or (table_name similar to 'rouse_rb_taxonomy_mapping_cs_to_ct_\d{14}' and table_schema like '%_version')
    grant: select

  - name: postal_codes_r
    where_get_tables: (table_name = 'geo_postal_codes' and table_schema = 'rfm_global')
    grant: select

  - name: fleet_manager_customers_metrics_reporting_r
    where_get_tables: ((table_name = 'fleet_customers_metrics') and table_schema = 'user_config')
    grant: select

  - name: fleet_manager_customers_metrics_reporting_rw
    where_get_tables: ((table_name = 'fleet_customers_metrics') and table_schema = 'user_config')
    grant: select, insert, update, delete
  
  - name: classification_photos_rw
    where_get_tables: (table_name = 'classification_photos' and table_schema not like '%_version')  or (table_name similar to 'classification_photos_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: marketable_life_r
    where_get_tables: (table_name similar to '%_marketable_life_%' and table_schema not like '%_version')  or (table_name similar to '%_marketable_life_%_\d{14}' and table_schema like '%_version')
    grant: select

  - name: marketable_life_rw
    where_get_tables: (table_name similar to '%_marketable_life_%' and table_schema not like '%_version')  or (table_name similar to '%_marketable_life_%_\d{14}' and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: user_config_rule_change_log_reporting_r
    where_get_schemas: table_schema = 'user_config'
    grant: select

  - name: equipment_classification_relevancy_rw
    where_get_tables: (table_name like 'equipment_classification_relevancy%' and table_schema not like '%_version')  or ((table_name similar to 'equipment_classification_relevancy_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: equipment_classification_relevancy_r
    where_get_tables: (table_name like 'equipment_classification_relevancy%' and table_schema not like '%_version')  or ((table_name similar to 'equipment_classification_relevancy_\d{14}') and table_schema like '%_version')
    grant: select

  - name: rouse_rb_taxonomy_mapping_r
    where_get_tables: (table_name like 'rouse_rb_taxonomy_mapping%' and table_schema not like '%_version')  or ((table_name similar to 'rouse_rb_taxonomy_mapping_cs_to_ct_\d{14}' or table_name similar to 'rouse_rb_taxonomy_mapping_\d{14}') and table_schema like '%_version')
    grant: select

  - name: rouse_rb_taxonomy_mapping_rw
    where_get_tables: (table_name like 'rouse_rb_taxonomy_mapping%' and table_schema not like '%_version')  or ((table_name similar to 'rouse_rb_taxonomy_mapping_cs_to_ct_\d{14}' or table_name similar to 'rouse_rb_taxonomy_mapping_\d{14}') and table_schema like '%_version')
    grant: select, insert, update, delete

  - name: sales_google_analytics_page_views_r
    where_get_tables: (table_name like 'webshop_%' and table_schema like 'sales_rfm_channels_api')
    grant: select

  - name: sales_google_analytics_page_views_rw
    where_get_tables: (table_name like 'webshop_%' and table_schema like 'sales_rfm_channels_api')
    grant: select, insert, update, delete

  - name: fleet_manager_publish_insights_taxonomy_and_localization_etl_r
    where_get_tables: (table_name in ('insights_type', 'insights_category', 'insights_subcategory','taxonomy_locale', 'taxonomy_alias_type', 'taxonomy_alias_category','taxonomy_alias_subcategory', 'insights_taxonomy', 'insights_taxonomy_category', 'insights_taxonomy_type', 'insights_configuration_option_taxonomy', 'insights_configuration_value_taxonomy', 'insights_configuration_taxonomy') and table_schema not like '%_version')  or (table_name similar to 'insights_type_\d{14}|sales_category_\d{14}|insights_category_\d{14}|insights_subcategory_\d{14}|taxonomy_alias_type_\d{14}|taxonomy_alias_category_\d{14}|taxonomy_alias_subcategory_\d{14}|insights_taxonomy_\d{14}|insights_taxonomy_category_\d{14}|insights_taxonomy_type\d{14}|insights_configuration_option_taxonomy_\d{14}|insights_configuration_value_taxonomy_\d{14}|insights_configuration_taxonomy_\d{14}' and table_schema like '%_version')
    grant: select

  - name: rfm_global_r
    where_get_tables: (table_schema = 'rfm_global')
    grant: select

  - name: rental_insights_etl_r
    where_get_tables: (table_schema like 'fm_ri' and table_name like 'rental_insights_fm%')
    grant: select
    
  - name: rental_insights_etl_rw
    where_get_tables: (table_schema like 'fm_ri' and table_name like 'rental_insights_fm%')
    grant: select, insert, update, delete

  - name: fleet_manager_sales_subcategory_r
    where_get_tables: (table_schema like 'rfm_globals' and table_name like 'makes')
    grant: select

  - name: fleet_manager_sales_subcategory_rw
    where_get_tables: (table_schema like 'rfm_globals' and table_name like 'makes')
    grant: select, insert, update, delete

  - name: mixer_values_lookup_configuration_sync_rw
    where_get_tables: (table_schema like 'sales_classification' and table_name in ('configuration_options', 'configuration_options_values'))
    grant: select, insert, update, delete

  - name: ss_export_r
    where_get_tables: (table_name like 'country' and table_schema like 'rfm_global')
    grant: select

  - name: ss_export_rw
    where_get_tables: (table_name like 'country' and table_schema like 'rfm_global')
    grant: select, insert, update, delete

permissions:
  - user: user_profiles
    roles:
      - user_profiles_rw
      - root

  - user: equipment_syndication_etl
    roles:
      - fleet_customers_r
  
  - user: rfm_user_config_api
    roles:
      - user_profiles_rw
      - marketable_life_r
      - root

  - user: fleet_manager_users_etl
    roles:
      - user_logins_r
      - fleet_users_rw
      - root

  - user: rb_list_web_leads_etl
    roles:
      - fleet_customers_r

  - user: countries_etl
    roles:
      - rfm_global_r
      - root

  - user: ims_fleet_refresh_etl
    roles:
      - fleet_customers_r

  - user: fleet_manager_partition_management_pipeline
    roles:
      - fleet_customers_r

  - user: sales_rfm_user_config_reporting
    roles:
      - user_config_r

  - user: sales_google_analytics_page_views
    roles:
      - root
      - sales_google_analytics_page_views_r
      - sales_google_analytics_page_views_rw

  - user: rb_list_views_etl
    roles:
      - root
      - user_config_r
      - fleet_customers_r

  - user: equipment_catalog_api
    roles:
      - sales_prevailing_exchange_rates_r
      - classification_photos_rw
      - user_config_r
      - fleet_manager_publish_insights_taxonomy_and_localization_etl_r
    default_privileges:
      grant: select
      schemas_scope: in-user-roles

  - user: equipment_rfm_api
    roles:
      - auctions_r
      - auction_data_r
      - equipment_classification_r
      - mpe_kpi_metrics_r
      - rb_taxonomy_r
      - values_lookup_schema_r
      - sales_prevailing_exchange_rates_r
      - transaction_volume_auction_c_r
      - transaction_volume_auction_cs_r
      - transaction_volume_auction_csm_r
      - transaction_volume_auction_csmm_r
      - transaction_volume_auction_csmmy_r
      - transaction_volume_auction_quartile_csmm_r
      - transaction_volume_auction_eu_c_r
      - transaction_volume_auction_eu_cs_r
      - transaction_volume_auction_eu_csm_r
      - transaction_volume_auction_eu_csmm_r
      - transaction_volume_auction_eu_csmmy_r
      - transaction_volume_auction_eu_quartile_csmm_r
      - transaction_volume_retail_c_fm_r
      - transaction_volume_retail_cs_fm_r
      - transaction_volume_retail_csm_fm_r
      - transaction_volume_retail_csmm_fm_r
      - transaction_volume_retail_csmmy_fm_r
      - transaction_volume_retail_quartile_csmm_fm_r
      - value_trends_r
      - value_trends_chart_r
      - value_trends_uk_r
      - value_trends_uk_chart_r
      - values_lookup_configurations_r
      - rouse_rb_taxonomy_mapping_cs_to_ct_r
      - postal_codes_r
      - marketable_life_r
      - equipment_classification_relevancy_r
      - fleet_manager_publish_insights_taxonomy_and_localization_etl_r
      - rfm_global_r
      - rental_insights_etl_r

  - user: fleet_manager_customers_metrics_reporting
    roles:
      - fleet_manager_customers_metrics_reporting_r
      - fleet_manager_customers_metrics_reporting_rw
      - root

  - user: marketable_life_etl
    roles:
      - marketable_life_rw
      - root

  - user: user_config_rule_change_log_reporting
    roles:
      - user_config_rule_change_log_reporting_r

  - user: csmm_relevancy_etl
    roles:
      - equipment_classification_relevancy_rw
      - root

  - user: fleet_manager_publish_insights_taxonomy_and_localization_etl
    roles:
      - fleet_manager_publish_insights_taxonomy_and_localization_etl_r
      - root

  - user: pvac_user
    roles:
      - equipment_classification_r

  - user: valuation_taxonomy_user
    roles:
      - rouse_rb_taxonomy_mapping_r

  - user: equipment_etl
    roles:
      - sales_google_analytics_page_views_r
      - root

  - user: rental_insights_etl
    roles:
      - root
      - rental_insights_etl_r
      - rental_insights_etl_rw
      - user_config_rw

  - user: fleet_manager_sales_subcategory
    roles:
      - root
      - fleet_manager_sales_subcategory_r
      - fleet_manager_sales_subcategory_rw

  - user: mixer_values_lookup_configuration_sync
    roles:
      - root
      - mixer_values_lookup_configuration_sync_rw

  - user: ss_export
    roles:
      - ss_export_r
      - ss_export_rw
      - root
