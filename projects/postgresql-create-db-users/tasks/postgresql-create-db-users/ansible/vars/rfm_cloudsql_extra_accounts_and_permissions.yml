db_users:
  - user: replication_user
    password: "{{ lookup('rouse.gcp.secret_manager', 'dbuser_replication_user', project_id=cloud_sql_project) }}"

database_objects:
  schemas:
    pglogical_replication_lag:
      authorization: root

database_roles:
  - name: replication_lag
    where_get_tables: (table_name = 'pglogical_heartbeat_monitor' and table_schema = 'pglogical_replication_lag') or (table_schema = 'pglogical')
    grant: select


permissions:
  - user: replication_lag_user
    roles:
      - replication_lag
