import os
import re
import psycopg2
import psycopg2.extras
import psycopg2.extensions
from enum import Enum
from dataclasses import dataclass, field


class PostgreSQLDatabase(object):
    def __init__(self, host, database, login, password=None, port=5432, connect_params=None):
        if not password:
            password = os.getenv('PGPASSWORD')
        conn_args = {"host":host, "user":login, "password":password, "dbname":database, "port":port}
        if connect_params:
            conn_args.update(connect_params)
        self.conn = psycopg2.connect(**conn_args)
        self.conn.autocommit = True
        psycopg2.extensions.register_type(
            psycopg2.extensions.new_array_type((1034,), 'ACLITEM[]', psycopg2.STRING)
        )

    def get_cursor(self):
        return self.conn.cursor(cursor_factory=psycopg2.extras.DictCursor)


class AclType(Enum):
    SELECT = 'r'
    INSERT = 'a'
    UPDATE = 'w'
    DELETE = 'd'
    TRUNCATE = 'D'
    REFERENCES = 'x'
    TRIGGER = 't'
    CREATE = 'C'
    CONNECT = 'c'
    TEMPORARY = 'T'
    EXECUTE = 'X'
    USAGE = 'U'
    SET = 's'
    ALTER_SYSTEM = 'A'


@dataclass()
class AclItem(object):
    grantee: str
    permissions: list[AclType] = field(default_factory=list)
    grantor: str = ''
    with_grant: list[AclType] = field(default_factory=list)

    @staticmethod
    def from_acl_string(acl_string: str):
        re_permission = re.compile(r'(?P<grantee>\w+)?=(?P<perms>[\w*]+)/(?P<grantor>\w+)')
        perm_match = re_permission.match(acl_string)
        if not perm_match:
            raise ValueError(f'acl_string {acl_string} is not a valid ACL')
        grantee = perm_match.group('grantee') if perm_match.group('grantee') else 'PUBLIC'
        perms_keys = perm_match.group('perms')
        perms = []
        perms_with_grant = []
        for idx, perm in enumerate(perms_keys):
            if perm == '*':
                perms_with_grant.append(AclType(perms_keys[idx-1]))
                continue
            perms.append(AclType(perm))
        grantor = perm_match.group('grantor')
        acl = AclItem(grantee=grantee, permissions=perms, grantor=grantor, with_grant=perms_with_grant)
        return acl

    @staticmethod
    def get_permissions_from_acl_list(acls_list):
        permissions = {}
        for acl_def in acls_list:
            acl = AclItem.from_acl_string(acl_def)
            for permission in acl.permissions:
                if permission not in permissions:
                    users = {}
                    permissions.update({permission: users})
                else:
                    users = permissions[permission]
                users.update({acl.grantee: 1})
        return permissions


class HelperPermissions(object):
    def __init__(self, db):
        self.schemas = {}
        self.db = db
        self._read_permissions()

    def _read_permissions(self):
        sql = self._get_permissions_query()
        cursor = self.db.get_cursor()
        cursor.execute(sql)
        for permission_row in cursor:
            schema = permission_row['schema']
            object_name = permission_row['name']
            acls = permission_row['acls']
            if acls is None:
                continue
            perms = AclItem.get_permissions_from_acl_list(acls)
            if schema not in self.schemas:
                objects = {}
                self.schemas.update({schema: objects})
            else:
                objects = self.schemas[schema]
            objects.update({object_name: perms})

    @staticmethod
    def _get_permissions_query():
        pass

    def get_permissions(self, schema, object_name):
        return self.schemas.get(schema, {}).get(object_name, None)

    def get_missing_permissions(self, role, grants, objects_data):
        all_missing = {}
        counter_missing = 0
        counter_ok = 0
        for objects_data in objects_data:
            schema = objects_data[0]
            object_name = objects_data[1]
            perms = self.get_permissions(schema, object_name)
            missing = []
            for grant in grants:
                if perms:
                    users = perms[AclType[grant]]
                    if role not in users and 'PUBLIC' not in users:
                        missing.append(grant)
                else:
                    missing.append(grant)
            if missing:
                key = ", ".join(missing)
                if key not in all_missing:
                    objects = []
                    all_missing[key] = objects
                else:
                    objects = all_missing[key]
                objects.append((schema, object_name))
                counter_missing += 1
            else:
                counter_ok += 1
        # print(f'    missing GRANTs: {counter_missing}. existing GRANTs: {counter_ok}')
        commands = self._get_commands_for_missing(all_missing, role)
        return all_missing, commands

    def _get_commands_for_missing(self, missing, role):
        return []


class HelperTablePermissions(HelperPermissions):
    @staticmethod
    def _get_permissions_query():
        return """select n.nspname as schema, t.relname as name, t.relacl as acls
        from pg_class t
            left join pg_namespace n on t.relnamespace = n.oid
        where t.relkind in ('r', 't', 'v', 'm', 'f', 'p') 
          and n.nspname not in ('information_schema', 'pg_catalog')
        order by n.nspname, t.relname"""

    def _get_commands_for_missing(self, missing, role):
        commands = []
        for key, objects in missing.items():
            fq_names = [f'{item[0]}."{item[1]}"' for item in objects]
            s_tables = ', '.join(fq_names)
            commands.append(f'set role root; GRANT {key} ON TABLE {s_tables} TO {role}; ')
        return commands


class HelperFunctionPermissions(HelperPermissions):
    def __init__(self, db):
        self.schemas = {}
        self.parameters_info = {}
        super().__init__(db)

    def _read_permissions(self):
        sql = self._get_permissions_query()
        cursor = self.db.get_cursor()
        cursor.execute(sql)
        re_invalid_param = re.compile(r'IN\s+.+')
        for permission_row in cursor:
            schema = permission_row['schema']
            object_name = permission_row['name']
            params = permission_row['params']
            if params:
                if schema not in self.parameters_info:
                    function_namespace = {}
                    self.parameters_info.update({schema: function_namespace})
                else:
                    function_namespace = self.parameters_info[schema]
                if object_name not in function_namespace:
                    function_params = []
                    function_namespace.update({object_name: function_params})
                else:
                    function_params = function_namespace[object_name]
                if not any([re_invalid_param.match(param) for param in [param.strip() for param in params.split(',')]]):
                    function_params.append(params)
            acls = permission_row['acls']
            if acls is None:
                continue
            perms = AclItem.get_permissions_from_acl_list(acls)
            if schema not in self.schemas:
                objects = {}
                self.schemas.update({schema: objects})
            else:
                objects = self.schemas[schema]
            objects.update({object_name: perms})

    @staticmethod
    def _get_permissions_query():
        return """select n.nspname as schema, p.proname as name,
       pg_get_function_identity_arguments(p.oid) as params, p.proacl as acls
from pg_proc p
      left join pg_namespace n on p.pronamespace = n.oid
      left join pg_language l on p.prolang = l.oid
      left join pg_type t on t.oid = p.prorettype
where l.lanname != 'internal' and n.nspname not in ('pg_catalog', 'information_schema')
order by n.nspname, p.proname"""

    def _get_commands_for_missing(self, missing, role):
        commands = []
        for key, objects in missing.items():
            for function_def in objects:
                schema = function_def[0]
                function_name = function_def[1]
                params = self.parameters_info.get(schema, {}).get(function_name, [''])
                for param in params:
                    commands.append(f'GRANT {key} ON FUNCTION {schema}.{function_name}({param}) TO {role}; ')
        return commands
