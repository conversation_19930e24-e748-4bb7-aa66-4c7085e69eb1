---
- name: Configure users in pgbouncer
  hosts: database
  connection: local
  pre_tasks:
    - name: set users fact
      set_fact:
        users: []
    - name: get vars 
      include_vars:
        file: "{{ item }}"
      loop:
        - "vars/rfm_accounts.yml"
        - "vars/rfm_root_accounts.yml"

    - name: add db_users -> users 
      set_fact: 
        users: "{{ db_users + root_users  + extra_users }}"
      no_log: true

    - name: get user_config vars
      include_vars:
        file: "vars/rfm_user_config_accounts.yml"
        name: user_config_users

    - name: get vars 
      include_vars:
        file: vars/rfmservices_configuration_accounts.yml
        name: rfm_configuration

    - name: get rfm_sales_rfm_channels_api_accounts vars
      include_vars:
        file: "vars/rfm_sales_rfm_channels_api_accounts.yml"
        name: rfm_sales_rfm_channels_api_accounts

    - name: add user_config_users -> users 
      set_fact: 
        users: "{{ users + user_config_users.db_users + user_config_users.extra_users + rfm_sales_rfm_channels_api_accounts.db_users + rfm_configuration.db_users }}"
      no_log: true

  roles:
    - role: postgresql-pgbouncer
