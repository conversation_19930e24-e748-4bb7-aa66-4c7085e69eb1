---
- name: Configure users in pgbouncer
  hosts: database
  connection: local

  pre_tasks:
    - name: get vars 
      include_vars:
        file: "{{ item }}"
      loop:
        - "vars/platform_accounts.yml"
        - "vars/platform_root_accounts.yml"

    - name: set users fact
      set_fact:
        users: []
    - name: add db_users -> users 
      set_fact: 
        users: "{{ db_users + root_users  + extra_users }}"
      no_log: true
      
  roles:
    - role: postgresql-pgbouncer