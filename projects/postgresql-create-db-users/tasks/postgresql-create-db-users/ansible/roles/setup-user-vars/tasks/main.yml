    - name: get <PERSON><PERSON> auth vars 
      include_vars:
        file: vars/iam_authentication.yml
        name: iam_authentication_vars_all

    - name: get the data for the instance
      set_fact:
        iam_authentication_aliases: "{{ iam_authentication_vars_all.instance_class[iam_authentication_vars_all.instance[cloud_sql_instance]].users }}"

    - name: get the iam users for the instance
      set_fact:
        iam_users: "{{ iam_users + [iam_authentication_vars_all.alias[alias_name]] }} "
      when: iam_authentication_vars_all.alias[alias_name].type == "iam_user"
      loop: "{{ iam_authentication_aliases }}"
      loop_control:
        loop_var: alias_name

    - name: set users fact
      set_fact:
        users: "{{ db_users | json_query('[*].{user: user, password: password, type: `service_account`}') }}"

    - name: add iam_users -> users 
      set_fact: 
        users: "{{ users + iam_users }}"
      no_log: true

    - name: split database into a list
      set_fact:
        all_databases_list: "{{ credentials.databases | split(',') }}"
      when: credentials.databases is defined and credentials.databases != None and credentials.databases != ''

    - name: get first database as a special element 
      # we required a database for creating users just for the connection, but any db will work
      set_fact:
         first_database: "{{ all_databases_list[0] }}"
      when: credentials.databases is defined and credentials.databases != None and credentials.databases != ''

    - name: single database
      set_fact:
        all_databases_list: [ "{{ credentials.database }}" ]
        first_database: "{{ credentials.database }}"
      when: credentials.databases is not defined or credentials.databases == None or credentials.databases == ''
