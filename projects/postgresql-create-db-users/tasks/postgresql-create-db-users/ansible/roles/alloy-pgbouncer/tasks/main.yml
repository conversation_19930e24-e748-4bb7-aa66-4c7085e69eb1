- name: create user list
  set_fact:
    user_list: '{{ lookup("template", "templates/userlist.j2")  | b64encode }}'
  no_log: true

- name: authenticate in k8 cluster
  shell: |
    gcloud config set project {{ cloud_sql_project }}
    gcloud container clusters get-credentials {{ k8_cluster_name}} --zone {{ k8_cluster_zone }} --project {{ cloud_sql_project }}

- name: list k8 secrets
  shell: |
    kubectl get secrets -n {{ k8_pgbouncer_namespace }}
  no_log: true
  register: secrets

- name: list k8 deployments
  shell: |
    kubectl get deployments -n {{ k8_pgbouncer_namespace }}
  no_log: true
  register: deployments

- name:  determine if secret exist
  set_fact:
    secret_exist: "{{ True if  k8_pgbouncer_secret in secrets.stdout  else False }}"

- name:  determine if deployment exist
  set_fact:
    deployment_exist: "{{ True if  deployment_name in deployments.stdout  else False }}"

- name: print if secret does not exist
  debug:
    msg: "The secret was not updated because it doesn't exist."
  when: not secret_exist

- name: get current pgbouncer secret
  shell: |
    k<PERSON><PERSON>l get secrets {{ k8_pgbouncer_secret }}   -n {{ k8_pgbouncer_namespace }}  -o json | jq -r '.data["{{ secret_key }}"]'
  register: pgbouncer_secret
  no_log: true
  when: k8_pgbouncer_namespace is defined and k8_pgbouncer_secret is defined and secret_exist

- name: calculate hash for existing secret and desired secret
  set_fact:
    existing_secret_sha1: "{{ pgbouncer_secret.stdout | sha1 }}"
    desired_secret_sha1: "{{ user_list | sha1 }}"
  when: secret_exist

- name: update userlist secret
  shell: |
    kubectl patch secret {{ k8_pgbouncer_secret }}  -n {{ k8_pgbouncer_namespace }} -p='{"data": { "{{ secret_key }}": "{{user_list}}" }}'
  no_log: true
  when: secret_exist and desired_secret_sha1 != existing_secret_sha1

- name: create userlist secret
  shell: |
    kubectl create secret generic {{ k8_pgbouncer_secret }}  -n {{ k8_pgbouncer_namespace }} --from-literal='{"data": { "{{ secret_key }}": "{{user_list}}" }}'
  no_log: true
  when: not secret_exist

- name: restart pgbouncer deployment
  shell: |
    NAMESPACE="{{ k8_pgbouncer_namespace }}"
    SECRET_NAME="{{ k8_pgbouncer_secret }}"
    DEPLOYMENTS=$(kubectl get deployments -n $NAMESPACE --output=name | cut -d '/' -f 2)
    for DEPLOYMENT in $DEPLOYMENTS; do
      IS_USING_USERS_SECRET=$(kubectl get deployment $DEPLOYMENT -n $NAMESPACE -o yaml | grep $SECRET_NAME)
      if [ ! -z "$IS_USING_USERS_SECRET" ]; then
        echo "Secret found in deployment $DEPLOYMENT in namespace $NAMESPACE"
        echo "kubectl rollout restart deployment $DEPLOYMENT -n $NAMESPACE"
        kubectl rollout restart deployment $DEPLOYMENT -n $NAMESPACE
      fi
    done
  when: not secret_exist or (desired_secret_sha1 != existing_secret_sha1)
