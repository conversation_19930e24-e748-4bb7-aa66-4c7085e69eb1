
- name: show connection params
  debug:
    msg: |
      database: {{ database }}
      host: {{ host }}
      user: {{ user }}
      instance_type: {{ instance_type }}

- name: create database objects
  create_database_objects:
    database_objects: "{{ database_objects }}"
    db: "{{ database }}"
    login_host: "{{ host }}"
    login_password: "{{ password }}"
    login_user: "{{ user }}"
    connect_params: "{{ connection_parameters | default({}) }}"
  when: database_objects is defined

- name: grant permissions on schemas to role
  grant_permissions_on_schemas_to_roles:
    db: "{{ database }}"
    login_host: "{{ host }}"
    login_password: "{{ database_password }}"
    login_user: "{{ user }}"
    roles: "{{ database_roles }}"
    connect_params: "{{ connection_parameters | default({}) }}"

- name: grant permissions on table to role
  grant_permissions_on_tables_to_roles:
    db: "{{ database }}"
    login_host: "{{ host }}"
    login_password: "{{ database_password }}"
    login_user: "{{ user }}"
    roles: "{{ database_roles }}"
    connect_params: "{{ connection_parameters | default({}) }}"

#- name: Grant permissions to roles
#  include_tasks: "grant_permission_to_role.yml"
#  loop: "{{ database_roles }}"
#  loop_control:
#    loop_var: role

- name: Grant permissions on functions to role
  grant_permissions_on_functions_to_roles:
    db: "{{ database }}"
    login_host: "{{ host }}"
    login_password: "{{ database_password }}"
    login_user: "{{ user }}"
    roles: "{{ database_roles }}"
    connect_params: "{{ connection_parameters | default({}) }}"

- name: Grant Default Privileges to users
  grant_default_privileges_to_users:
    db: "{{ database }}"
    login_host: "{{ host }}"
    login_password: "{{ database_password }}"
    login_user: "{{ user }}"
    permissions: "{{ permissions }}"
    roles: "{{ database_roles }}"
    connect_params: "{{ connection_parameters | default({}) }}"

- name: Remove create role/database from users
  postgresql_query:
    db: "{{ database }}"
    login_host: "{{ host }}"
    login_password: "{{ password }}"
    login_user: "{{ user }}"
    query: |
      ALTER ROLE "{{ item.user }}" NOCREATEROLE NOCREATEDB;
      GRANT connect,temp,temporary ON DATABASE {{ database }} TO "{{ item.user }}";
    connect_params: "{{ connection_parameters | default({}) }}"
  no_log: true
  loop: "{{ users }}"
  ignore_errors: true
  when: item.user not in ["root", "admin"] and item.type == "service_account"

- name: Grant specified roles to IAM users
  postgresql_membership:
    db: "{{ database }}"
    login_host: "{{ host }}"
    login_password: "{{ password }}"
    login_user: "{{ user }}"
    groups: "{{ item.roles }}"
    target_role: "{{ item.user }}"
    state: present
    connect_params: "{{ connection_parameters | default({}) }}"
  no_log: true
  loop: "{{ iam_users }}"
  when: item.type == "iam_user" and item.roles is defined and (item.roles | length > 0)
