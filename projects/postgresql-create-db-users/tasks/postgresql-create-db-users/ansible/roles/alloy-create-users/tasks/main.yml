


#- name: Print db_type
#  debug:
#    var: db_type
#
#- name: custom db type
#  set_fact:
#    custom_db_type: "{{ True if db_type in custom_dbs else False }}"
#
#- name: Setup custom_db_type config
#  include_tasks: "setup_custom_db_config.yml"
#  when: custom_db_type

- name: get existing users
  shell: gcloud alloydb users list --cluster={{ alloydb_cluster_name }} --region={{ cloud_sql_region }} --format json --project {{ cloud_sql_project }}
  register: alloy_existing_users_raw

- name: set empty alloy_existing_users list
  set_fact:
    alloy_existing_users: "{{ alloy_existing_users_raw.stdout | from_json }}"
    alloy_existing_users_list: []

- name: populate alloy_existing_users
  set_fact:
    alloy_existing_users_list: "{{ alloy_existing_users_list + [ item.name | basename ] }}"
  loop: "{{ alloy_existing_users }}"

- name: create database "normal" users
  shell: |
    gcloud config set project {{ cloud_sql_project }}
    gcloud alloydb users create {{ item.user }} --region=us-central1 --cluster={{ alloydb_cluster_name }} --password '{{ item.password }}' 
  no_log: true
  when: item.type is defined and item.type == 'service_account' and item.user not in alloy_existing_users_list
  ignore_errors: true
  loop: "{{ users }}"

- name: create database IAM users
  shell: |
    gcloud config set project {{ cloud_sql_project }}
    gcloud alloydb users create {{ item.user }} --region=us-central1 --cluster={{ alloydb_cluster_name }} --type=IAM_BASED 
  no_log: true
  ignore_errors: true
  when: item.type is defined and item.type == 'iam_user' and item.user not in alloy_existing_users_list
  loop: "{{ users }}"

- name: Create / alter role to set password
  postgresql_query:
    db: "{{ database }}"
    login_host: "{{ credentials.host }}"
    login_password: "{{ database_password }}"
    login_user: "{{ user }}"
    query: |
      {% if item.user in alloy_existing_users_list %}
      ALTER ROLE {{ item.user }} LOGIN ENCRYPTED PASSWORD '{{ item.password }}';
      {% else %}
      CREATE ROLE {{ item.user }} LOGIN ENCRYPTED PASSWORD '{{ item.password }}';
      {% endif %}
    connect_params: "{{ connection_parameters | default({}) }}"
  when: "item.password is defined"
  ignore_errors: true
  no_log: true
  loop: "{{ users }}"

- name: Create / alter role to set search_path
  postgresql_query:
    db: "{{ database }}"
    login_host: "{{ credentials.host }}"
    login_password: "{{ database_password }}"
    login_user: "{{ user }}"
    query: |
      ALTER USER {{ item.user }} SET search_path= {{ item.search_path }};
    connect_params: "{{ connection_parameters | default({}) }}"
  when: item.search_path is defined
  ignore_errors: true
  no_log: true
  loop: "{{ users }}"

- name: Create roles
  postgresql_user:
    db: "{{ database }}"
    login_host: "{{ credentials.host }}"
    login_password: "{{ database_password }}"
    login_user: "{{ user }}"
    name: "{{ role.name }}"
    priv: CONNECT/TEMPORARY/TEMP
    role_attr_flags: NOLOGIN
    no_password_changes: yes
    state: present
    connect_params: "{{ connection_parameters | default({}) }}"
  loop: "{{ database_roles }}"
  ignore_errors: true
  loop_control:
    loop_var: role
  when: role.name not in ["root", "admin"]

- name: Assign user list to roles
  postgresql_membership:
    db: "{{ database }}"
    login_host: "{{ credentials.host }}"
    login_password: "{{ database_password }}"
    login_user: "{{ user }}"
    groups: "{{ permission.roles }}"
    target_role: "{{ permission.user }}"
    state: present
    connect_params: "{{ connection_parameters | default({}) }}"
  loop: "{{ permissions }}"
  loop_control:
    loop_var: permission

- name: Grant specified roles to IAM users
  postgresql_membership:
    db: "{{ database }}"
    login_host: "{{ credentials.host }}"
    login_password: "{{ database_password }}"
    login_user: "{{ user }}"
    groups: "{{ item.roles }}"
    target_role: "{{ item.user }}"
    state: present
    connect_params: "{{ connection_parameters | default({}) }}"
  no_log: true
  loop: "{{ iam_users }}"
  when: item.type == "iam_user" and item.roles is defined and (item.roles|length>0)
