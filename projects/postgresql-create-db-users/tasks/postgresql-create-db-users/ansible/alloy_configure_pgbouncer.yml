---
- name: Configure users in pgbouncer
  hosts: database
  connection: local
  pre_tasks:
    - name: set users fact
      set_fact:
        users: []

    - name: get vars 
      include_vars:
        file: "{{ item }}"
      loop:
        - "vars/rfm_accounts.yml"
        - "vars/alloy_root_accounts.yml"

    - name: add db_users -> users 
      set_fact: 
        users: "{{ db_users + root_users }}"
      no_log: true

  roles:
    - role: alloy-pgbouncer