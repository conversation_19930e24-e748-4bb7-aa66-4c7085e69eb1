---
- name: Create roles & users for RFM database
  hosts: database
  connection: local
  vars:
    database_password: "{{ lookup('env', 'PGPASSWORD') }}"
    root_user: "{{ lookup('env', 'ROOT_USER') }}"
    admin_secret_name: alloydb-admin
    db_type: "{{ lookup('env', 'DB_TYPE')  | trim | lower }}"
    iam_users: []

  tasks:
    - name: get vars
      include_vars:
        file: "{{ item }}"
      loop:
        - "vars/rfm_roles_and_permissions.yml"
        - "vars/rfm_accounts.yml"

    - name: get extra vars
      include_role:
        name: include_extra_users_and_permissions
        public: true
      vars:  
        vars_file: "{{ item }}"
      loop: "{{ lookup('env', 'EXTRA_PERMISSION') | split(',') }}"
      when: lookup('env', 'EXTRA_PERMISSION')!=''

    - name: setup user vars
      include_role:
        name: setup-user-vars

    - name: create Users & Roles
      include_role:
        name: alloy-create-users
      vars:
        database: "{{ first_database }}"
        host:  "{{ credentials.host }}"
        password: "{{ database_password }}"
        user: "admin"

    - name: grant permissions to roles
      include_role:
        name: grant-permissions
      vars:
        database: "{{ db }}"
        instance_type: alloydb
        host:  "{{ credentials.host }}"
        password: "{{ database_password }}"
        user: "admin"
      loop: "{{ all_databases_list }}"
      loop_control:
        loop_var: db

    - name: Check if the associated alias has groups
      include_role:
        name: iam-group-permissions
      vars:
        database: "{{ first_database }}"
        host: "{{ credentials.host }}"
        password: "{{ database_password }}"
        user: "{{ credentials.user }}"
        group_def: "{{ group }}"
      loop: "{{ iam_authentication_vars_all.instance_class[iam_authentication_vars_all.instance[cloud_sql_instance]].groups }}"
      loop_control:
        loop_var: group
      when: iam_authentication_vars_all.instance_class[iam_authentication_vars_all.instance[cloud_sql_instance]].groups is defined and (iam_authentication_vars_all.instance_class[iam_authentication_vars_all.instance[cloud_sql_instance]].groups | length > 0)