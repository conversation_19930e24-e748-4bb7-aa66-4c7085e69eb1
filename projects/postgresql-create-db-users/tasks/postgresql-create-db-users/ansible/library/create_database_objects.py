# !/usr/local/bin/python3
# -*- coding: utf-8 -*-
import psycopg2
from ansible.module_utils.basic import *
import os
import yaml
import json
from ansible.module_utils.database import PostgreSQLDatabase
from ansible.module_utils.cloudSQL import CloudSQLService
# from module_utils.database import PostgreSQLDatabase
# from module_utils.cloudSQL import CloudSQLService


def get_all_schemas(db):
    schemas = []
    sql = "select nspname as schema_name from pg_namespace order by schema_name"
    cursor = db.get_cursor()
    cursor.execute(sql)
    for row in cursor:
        schemas.append(row['schema_name'])
    return schemas


def create_schema(db, schema_name, schema_def, all_schemas):
    new_schema = None
    if schema_name in all_schemas:
        return new_schema
    cursor = db.get_cursor()
    ddl_create_schema = f'set role root; CREATE SCHEMA {schema_name}'
    if 'authorization' in schema_def:
        ddl_create_schema += f' AUTHORIZATION {schema_def["authorization"]}'
    cursor.execute(ddl_create_schema)
    new_schema = schema_name
    return new_schema


def create_sequence(db, sequence_def):
    new_sequence = None
    base_table = sequence_def["table"]
    base_column = sequence_def['column']
    sql_get_sequence_name = f"select pg_get_serial_sequence('{base_table}', '{base_column}')"
    try:
        cursor = db.get_cursor()
        cursor.execute(sql_get_sequence_name)
        data_seq_name = cursor.fetchone()
        sequence_full_name = data_seq_name['pg_get_serial_sequence'].split('.')
        sql = "select * from pg_sequences where schemaname = %s and sequencename = %s"
        cursor.execute(sql, (sequence_full_name[0], sequence_full_name[1]))
        data = cursor.fetchone()
        last_value = data['last_value']
        start_value = data['start_value']
        if last_value is None and start_value == 1:
            sequence_seed = int(sequence_def['initial_value'])
            new_sequence = f'{sequence_full_name[0]}.{sequence_full_name[1]}'
            sql_set_sequence = f"set role root; select setval('{new_sequence}', %s);"
            cursor.execute(sql_set_sequence, (sequence_seed, ))
    except psycopg2.DatabaseError:
        return new_sequence
    return new_sequence


def ansible_entrypoint():
    module = AnsibleModule(
        bypass_checks=False,
        argument_spec=dict(
            db=dict(required=True, type="str"),
            login_host=dict(required=True, type="str"),
            login_password=dict(required=True, type="str"),
            login_user=dict(required=True, type="str"),
            database_objects=dict(required=True, type="dict"),
            connect_params=dict(required=False, type="dict"),
        ),
        supports_check_mode=False,
    )
    stdout = []
    db_name = module.params['db']
    user = module.params['login_user']
    host = module.params['login_host']
    passwd = module.params['login_password']
    database_objects = module.params['database_objects']
    connect_params = module.params.get('connect_params', {})
    db = PostgreSQLDatabase(host, db_name, user, password=passwd, connect_params=connect_params)
    new_schemas = []
    new_sequences = []
    for db_object_type, objects in database_objects.items():
        if db_object_type == 'schemas':
            all_schemas = get_all_schemas(db)
            new_schemas = [new_schema for schema_name, schema_def in objects.items()
                           if (new_schema := create_schema(db, schema_name, schema_def, all_schemas))]
            if len(new_schemas) == 0:
                sequences_desc = ','.join(objects.keys())
                stdout.append(f'All these schemas already exists {sequences_desc}')
        elif db_object_type == 'sequences':
            new_sequences = [new_sequence for _, sequence_def in objects.items()
                             if (new_sequence := create_sequence(db, sequence_def))]
            if len(new_sequences) == 0:
                sequences_desc = ','.join(objects.keys())
                stdout.append(f'All these sequences already exists {sequences_desc}')
        else:
            raise f'Creation of {db_object_type} has not been implemented.'
    if new_schemas:
        stdout.append('New schemas: {}'.format(', '.join(new_schemas)))
    if new_sequences:
        stdout.append('New sequences: {}'.format(', '.join(new_sequences)))
    changed = len(new_schemas) or len(new_sequences)
    module.exit_json(changed=changed, stdout='\n'.join(stdout),)


def standalone_test():
    db_name = 'rfm'
    host = 'localhost'
    user = os.environ['PGUSER']
    passwd = os.environ['PGPASSWORD']
    roles_filename = '../vars/rfm_roles_and_permissions.yml'
    with open(roles_filename, 'rt') as roles_file:
        data = yaml.load(roles_file, yaml.Loader)
        database_objects = data['database_objects']

    db = PostgreSQLDatabase(host, db_name, user, password=passwd, port=5432)
    new_schemas = None
    new_sequences = None
    for db_object_type, objects in database_objects.items():
        if db_object_type == 'schemas':
            all_schemas = get_all_schemas(db)
            new_schemas = [new_schema for schema_name, schema_def in objects.items()
                           if (new_schema := create_schema(db, schema_name, schema_def, all_schemas))]
        elif db_object_type == 'sequences':
            new_sequences = [new_sequence for _, sequence_def in objects.items()
                             if (new_sequence := create_sequence(db, sequence_def))]
        else:
            raise f'Creation of {db_object_type} has not been implemented.'
    if new_schemas:
        print(f'New schemas: {new_schemas}')
    if new_sequences:
        print(f'Sequences being set: {new_sequences}')


# if __name__ == '__main__':
#     standalone_test()
ansible_entrypoint()
