import os
from datetime import <PERSON><PERSON><PERSON>

import pendulum
from airflow import DAG
from airflow.kubernetes.secret import Secret
from airflow.providers.google.cloud.operators.kubernetes_engine import (
    GKEStartPodOperator,
)
from shared_libs.default_args import get_default_args
from shared_libs.image_versions import get_full_image_name, get_gcr_registry 
from shared_libs.kubernetes import (
    standard_tolerations,
    standard_affinity,
    get_image_pull_policy,
)
from shared_libs.slack_callback import task_fail_slack_alert

environment = os.environ.get("COMPOSER_ENVIRONMENT", "dev")
gcr_registry = get_gcr_registry(environment)
image_pull_policy = get_image_pull_policy(environment)
cluster_zone = "us-central1-b"
local_tz = pendulum.timezone("America/Los_Angeles")
namespace = "postgresql-create-db-users"
service_account_name = namespace
k8_cluster_zone = "us-central1-b"


start_date = pendulum.datetime(2024, 4, 16, 0, 0, 0, tz=local_tz)
if environment != "prod":
    project_name = "management-dev-d6ba4d"
    schedule_interval = None
    cluster_name = "composer-jobs-v3-dev"
    prod_or_no_prod = "dev"
    database_name = "rfm_etcxl"
    databases = "rfm_etcxl,rfm,rfm_all,rfm_alloy"
    database_project = "services-dev-525bf6"
    database_region = "us-central1"
    instance = "alloydb-rfm08-dev"
    pgbouncer_ip = "************"
    k8_pgbouncer_namespace = "enterprise-pgbouncer-alloydb"
    k8_pgbouncer_secret = "pgbouncer-alloydb-userlist"
    k8_cluster_name = "services-dev"
    db_secret_name = "alloydb-dev-admin"
    alloy_db_cluster_name = "alloydb-cluster"

else:
    project_name = "management-prod-837a97"
    schedule_interval = timedelta(hours=1)
    cluster_name = "composer-jobs-v3-prod"
    prod_or_no_prod = "prod"
    database_name = "rfm"
    databases = "rfm"
    database_project = "services-prod-e6fffc"
    database_region = "us-central1"
    instance = "rfm101-prod"
    pgbouncer_ip = "*************"
    k8_pgbouncer_namespace = "enterprise-pgbouncer-rfm101"
    k8_pgbouncer_secret = "pgbouncer-rfm101-userlist"
    k8_cluster_name = "services-prod"
    db_secret_name = "rfm101-prod-admin"
    alloy_db_cluster_name = "rfm101-prod"

custom_args = {
    "start_date": start_date,
    "on_failure_callback": task_fail_slack_alert,
    "namespace": namespace,
    "service_account_name": service_account_name,
}
default_args = get_default_args(custom_args)


def create_dag(dag_id):
    dag = DAG(
        dag_id,
        default_args=default_args,
        description="Create Roles and Users on alloydb database",
        schedule_interval=schedule_interval,
        catchup=False,
        concurrency=8,
        dagrun_timeout=timedelta(minutes=60),
    )
    db_user = Secret(
        deploy_type="env",
        deploy_target="PGUSER",
        secret=db_secret_name,
        key="username"
    )
    db_password = Secret(
        deploy_type="env",
        deploy_target="PGPASSWORD",
        secret=db_secret_name,
        key="password"
    )

    with dag:
        task_id = "alloydb-create-db-users-task"
        t_create_alloy = GKEStartPodOperator(
            task_id=task_id,
            name=task_id,
            location=cluster_zone,
            project_id=project_name,
            cluster_name=cluster_name,
            affinity=standard_affinity,
            tolerations=standard_tolerations,
            arguments=["/bin/bash", "create_users_and_roles.sh"],
            image=get_full_image_name("postgresql-create-db-users", gcr_registry),
            secrets=[db_password,db_user],
            dag=dag,
            retries=0,
            env_vars={
                "TASK_ID": task_id,
                "DATABASE": database_name,
                "DATABASES": databases,
                "PROJECT": database_project,
                "MANAGEMENT_PROJECT": project_name,
                "REGION": database_region,
                "INSTANCE": instance,
                "PLAYBOOK": "alloydb_grant_permissions.yml",
                "ENVIRONMENT": prod_or_no_prod,
                "DB_TYPE": "alloydb",
                "ALLOYDB_PGBOUNCER_HOST": pgbouncer_ip,
                "ALLOY_CLUSTER_NAME": alloy_db_cluster_name,
                'EXTRA_PERMISSION': 'vars/rfm_alloydb_extra_accounts_and_permissions.yml',
            },
            depends_on_past=False,
        )

        task_id = "alloydb-pgbouncer-config-rfm-users-task"
        t_create_users = GKEStartPodOperator(
            task_id=task_id,
            name=task_id,
            location=cluster_zone,
            project_id=project_name,
            cluster_name=cluster_name,
            affinity=standard_affinity,
            tolerations=standard_tolerations,
            arguments=["/bin/bash", "config_pgbouncer.sh"],
            image=get_full_image_name("postgresql-create-db-users", gcr_registry),
            secrets=[db_password,db_user],
            dag=dag,
            retries=0,
            env_vars={
                "TASK_ID": task_id,
                "PROJECT": database_project,
                "MANAGEMENT_PROJECT": project_name,
                "PLAYBOOK": "alloy_configure_pgbouncer.yml",
                "K8_CLUSTER_ZONE": k8_cluster_zone,
                "K8_CLUSTER_NAME": k8_cluster_name,
                "K8_PGBOUNCER_NAMESPACE": k8_pgbouncer_namespace,
                "K8_PGBOUNCER_SECRET": k8_pgbouncer_secret,
                "ENVIRONMENT": prod_or_no_prod,
            },
            depends_on_past=False,
        )
    return dag

dag_id = "alloydb-create-db-users"
create_dag(dag_id)


