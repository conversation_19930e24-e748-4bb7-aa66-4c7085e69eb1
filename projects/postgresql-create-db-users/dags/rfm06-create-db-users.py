import os
from datetime import timed<PERSON><PERSON>

import pendulum
from airflow import DAG
from airflow.kubernetes.secret import Secret
from airflow.providers.google.cloud.operators.kubernetes_engine import (
    GKEStartPodOperator,
)
from shared_libs.default_args import get_default_args
from shared_libs.image_versions import get_full_image_name, get_gcr_registry
from shared_libs.kubernetes import (
    standard_tolerations,
    standard_affinity,
    get_image_pull_policy,
)
from shared_libs.slack_callback import task_fail_slack_alert

environment = os.environ.get("COMPOSER_ENVIRONMENT", "dev")
gcr_registry = get_gcr_registry(environment)
image_pull_policy = get_image_pull_policy(environment)
cluster_zone = "us-central1-b"
local_tz = pendulum.timezone("America/Los_Angeles")
db_secret_name = "rfm01-root"
namespace = "postgresql-create-db-users"
service_account_name = namespace
k8_pgbouncer_namespace = "enterprise-pgbouncer-rfm06"
k8_pgbouncer_secret = "pgbouncer-pgbouncer-secret-userlist-txt"
k8_cluster_zone = "us-central1-b"
expected_owner = "root"

start_date = pendulum.datetime(2022, 2, 23, 7, 0, 0, tz=local_tz)
if environment != "prod":
    project_name = "management-dev-d6ba4d"
    schedule_interval = None
    cluster_name = "composer-jobs-v3-dev"
    prod_or_no_prod = "dev"
    database_name = "rfm"
    database_project = "services-dev-525bf6"
    database_region = "us-central1"
    database_instance = "rfm06-dev"
    k8_cluster_name = "services-dev"
    exit
else:
    project_name = "management-prod-837a97"
    schedule_interval = timedelta(hours=1)
    cluster_name = "composer-jobs-v3-prod"
    prod_or_no_prod = "prod"
    database_name = "rfm"
    database_project = "services-prod-e6fffc"
    database_region = "us-central1"
    database_instance = "rfm06r2-prod"
    k8_cluster_name = "services-prod"

custom_args = {
    "start_date": start_date,
    "on_failure_callback": task_fail_slack_alert,
    "startup_timeout_seconds": 300,
    "namespace": namespace,
    "service_account_name": service_account_name,
}
default_args = get_default_args(custom_args)


def create_dag(dag_id):
    dag = DAG(
        dag_id,
        default_args=default_args,
        description="Create Roles and Users on RFM database",
        schedule_interval=schedule_interval,
        catchup=False,
        concurrency=8,
        dagrun_timeout=timedelta(minutes=60),
    )

    # secrets/passwords
    db_user = Secret(
        deploy_type="env", deploy_target="PGUSER", secret=db_secret_name, key="username"
    )

    db_password = Secret(
        deploy_type="env",
        deploy_target="PGPASSWORD",
        secret=db_secret_name,
        key="password",
    )

    with dag:
        task_id = "rfm06-create-db-users-task"
        t_create_users = GKEStartPodOperator(
            task_id=task_id,
            name=task_id,
            location=cluster_zone,
            project_id=project_name,
            cluster_name=cluster_name,
            affinity=standard_affinity,
            tolerations=standard_tolerations,
            arguments=["/bin/bash", "create_users_and_roles.sh"],
            image=get_full_image_name("postgresql-create-db-users", gcr_registry),
            secrets=[db_user, db_password],
            dag=dag,
            retries=3,
            env_vars={
                "TASK_ID": task_id,
                "DATABASE": database_name,
                "PROJECT": database_project,
                "MANAGEMENT_PROJECT": project_name,
                "REGION": database_region,
                "INSTANCE": database_instance,
                "PLAYBOOK": "rfm_grant_permissions.yml",
                "ENVIRONMENT": prod_or_no_prod,
                'EXTRA_PERMISSION': 'vars/rfm_cloudsql_extra_accounts_and_permissions.yml',
            },
            depends_on_past=False,
        )
        # task_id = "rfm06-create-db-indexes-task"
        # t_create_indexes = GKEStartPodOperator(
        #     task_id=task_id,
        #     name=task_id,
        #     location=cluster_zone,
        #     project_id=project_name,
        #     cluster_name=cluster_name,
        #     affinity=standard_affinity,
        #     tolerations=standard_tolerations,
        #     arguments=["/bin/bash", "create_indexes.sh"],
        #     image=get_full_image_name("postgresql-create-db-users", gcr_registry),
        #     secrets=[db_user, db_password],
        #     dag=dag,
        #     retries=3,
        #     env_vars={
        #         "TASK_ID": task_id,
        #         "DATABASE": database_name,
        #         "PROJECT": database_project,
        #         "REGION": database_region,
        #         "INSTANCE": database_instance,
        #         "DEFINITION": "rfm_create_indexes.yml",
        #         "ENVIRONMENT": prod_or_no_prod,
        #     },
        #     depends_on_past=False,
        # )

        task_id = "rfm-pgbouncer-config-rfm-users-task"
        t_create_users = GKEStartPodOperator(
            task_id=task_id,
            name=task_id,
            location=cluster_zone,
            project_id=project_name,
            cluster_name=cluster_name,
            affinity=standard_affinity,
            tolerations=standard_tolerations,
            arguments=["/bin/bash", "config_pgbouncer.sh"],
            image=get_full_image_name("postgresql-create-db-users", gcr_registry),
            secrets=[db_user, db_password],
            dag=dag,
            retries=3,
            env_vars={
                "TASK_ID": task_id,
                "PROJECT": database_project,
                "MANAGEMENT_PROJECT": project_name,
                "PLAYBOOK": "rfm_configure_pgbouncer.yml",
                "K8_CLUSTER_ZONE": k8_cluster_zone,
                "K8_CLUSTER_NAME": k8_cluster_name,
                "K8_PGBOUNCER_NAMESPACE": k8_pgbouncer_namespace,
                "K8_PGBOUNCER_SECRET": k8_pgbouncer_secret,
                "ENVIRONMENT": prod_or_no_prod,
            },
            depends_on_past=False,
        )
    return dag


dag_id = "rfm06-create-db-users"
a_dag = create_dag(dag_id)
globals()[dag_id] = a_dag
