import datetime as dt
import os

from airflow import DAG
from airflow.providers.google.cloud.operators.kubernetes_engine import (
    GKEStartPodOperator,
)
from airflow.operators.bash_operator import BashOperator
from airflow.operators.python_operator import ShortCircuitOperator
import pytz

from datetime import timedelta, datetime
from airflow.kubernetes.secret import Secret

from shared_libs.slack_callback import task_fail_slack_alert, task_notification_factory
from shared_libs.image_versions import get_gcr_registry, get_full_image_name
from shared_libs.kubernetes import (
    standard_tolerations,
    standard_affinity,
    get_image_pull_policy,
)
from airflow.operators.dummy_operator import DummyOperator
from shared_libs.default_args import get_default_args
from shared_libs.pagerduty_callback import dag_fail_pagerduty_alert
from airflow.utils.task_group import TaskGroup
from airflow.models import Variable
from airflow.operators.python_operator import BranchPythonOperator
from kubernetes.client import models as k8s
from airflow.utils.state import State
from airflow.exceptions import AirflowFailException


# CONSTANTS
ENVIRONMENT = os.environ.get("COMPOSER_ENVIRONMENT", "dev")
CLUSTER_ZONE = "us-central1-b"
IMAGE_PULL_POLICY = get_image_pull_policy(ENVIRONMENT)
GCR_REGISTRY = get_gcr_registry(ENVIRONMENT)
RESOURCES = k8s.V1ResourceRequirements(
    requests={"cpu": "500m", "memory": "1.0Gi"},
)
MAJOR_VERSION = "1"
MINOR_VERSION = "1"
TABLE_SUFFIX = "{{dag_run.conf['table_suffix']}}"
RESOURCES_SMALL_TASK = k8s.V1ResourceRequirements(
    requests={"cpu": "500m", "memory": "1.0Gi"},
)

if ENVIRONMENT not in ["prod", "dr"]:
    DB_SECRET_NAME = "de-rambo-export-dev"
    BUCKET_NAME = "analytics-data-dev-62c5a2-rental-metrics-benchmark-aggs"
    PROJECT_NAME = "management-dev-d6ba4d"
    DATA_PROJECT_NAME = "analytics-data-dev-62c5a2"
    CLUSTER_NAME = "composer-jobs-v3-dev"
    AGGS_DESTINATION_DATASET = "rental_metrics_aggs_dev"
    AGGS_STAGE_DATASET_NAME = "rental_metrics_aggs_stage_dev"
    LOGGING_GCP_PROJECT = "management-dev-d6ba4d"
    PAGERDUTY_NOTIFY = False

    # import into SS pool
    ss_import_pool_name = "gdevetl01_bcp_export"

    # RATES COMPUTE PIPELINE CONSTANTS
    RATES_STAGE_DATASET_NAME = "rental_benchmarks_stage_dev"
    RATES_DATASET_NAME = "rental_benchmarks_dev"
    RATES_GOOGLE_GCS_BUCKET = "analytics-data-dev-62c5a2-analytics-benchmark-dev-v2"
    REPRESENTATION_JS_PATH: str = (
        "/usr/local/airflow/dags/constants/representation.json"
    )

    # MATERIALIZED INPUT CONSTANTS
    # We are reusing the version database for aggs so that we reduce the amount of datasets
    MATERIALIZED_INPUTS_VERSION_DATASET_NAME = "rental_metrics_aggs_version_dev"
    GETL01_SQL_SERVER = "gdevetl01.rasgcp.net"
    AVRO_SCHEMA_BASE_PATH = (
        "gs://analytics-data-dev-62c5a2-rental-metrics-aggs/avro_schemas/"
    )
    GCS_DESTINATION_PATH = (
        "gs://analytics-data-dev-62c5a2-rental-metrics-aggs/avro_exports/"
    )

    # Customer Segmentation Constants
    CUSTOMER_SEGMENTATION_SQL_FOLDER = (
        "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies"
        "/customer_segmentation_queries/"
    )
    CUSSEG_DESTINATION_DATASET = "rental_metrics_aggs_dev"
    CUSSEG_VERSION_DATASET = "rental_metrics_aggs_version_dev"

    # DimFMRR constants
    DIM_FM_RR_DESTINATION_DATASET = "dim_fm_rr_dev"
    DIM_FM_RR_STAGE_DATASET = "dim_fm_rr_stage_dev"
    DIM_FM_RR_SOURCE_DATASET = "dim_fm_rr_version_dev"
    DIM_FMRR_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/dim_fm_rr_queries/"
    FMRR_BENCH_SEASONALITY_TABLES_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/fmrr_bench_seasonality_tables/"
    FMRR_BENCH_GROWTH_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/fmrr_bench_growth/"
    LOAD_BENCH_PRELOADS_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/bench_preloads/"
    DAG_BASE_SQL_FOLDER = (
        "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/"
    )

    # DEMO CLIENTS constants
    DEMO_CLIENTS_QUERY_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/demo_clients/"

    # RDO PUBLISH
    RDO_PUBLISH_VERSION_DATASET_NAME = "rdo_publish_version_dev"
    RDO_PUBLISH_STAGE_DATASET_NAME = "rdo_publish_stage_dev"
    RDO_PUBLISH_DATASET_NAME = "rdo_publish_dev"

    # Month To Date
    REFRESH_DATASET_NAME = "data_refresh_dev"
    REFRESH_VERSION_DATASET_NAME = "data_refresh_version_dev"
    REFRESH_STAGE_DATASET_NAME = "data_refresh_stage_dev"

    PUBLISH_SEQUENCE_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/publish_sequence/"
    RDO_TABLES_SQL_FOLDER = (
        "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/rdo_tables/"
    )
    MONTH_TO_DATE_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/month_to_date/"

    # PUSH TO PROD
    RDO_PROD_VERSION_DATASET_NAME = "rdo_prod_version_dev"
    RDO_PROD_STAGE_DATASET_NAME = "rdo_prod_stage_dev"
    RDO_PROD_DATASET_NAME = "rdo_prod_dev"

    PUSH_TO_PROD_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/push_to_prod/"

    if ENVIRONMENT == "local":
        REPRESENTATION_JS_PATH: str = (
            "/usr/local/airflow/projects/rental-metrics-aggs/dags/representation.json"
        )
        CUSTOMER_SEGMENTATION_SQL_FOLDER = (
            "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies"
            "/customer_segmentation_queries/"
        )
        DIM_FMRR_SQL_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/dim_fm_rr_queries/"

        FMRR_BENCH_SEASONALITY_TABLES_SQL_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/fmrr_bench_seasonality_tables/"
        FMRR_BENCH_GROWTH_SQL_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/fmrr_bench_growth/"
        LOAD_BENCH_PRELOADS_SQL_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/bench_preloads/"
        DAG_BASE_SQL_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/"
        DEMO_CLIENTS_QUERY_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/demo_clients/"
        PUBLISH_SEQUENCE_SQL_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/publish_sequence/"
        RDO_TABLES_SQL_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/rdo_tables/"
        MONTH_TO_DATE_SQL_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/month_to_date/"

        PUSH_TO_PROD_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/push_to_prod/"

elif ENVIRONMENT == "dr":
    DB_SECRET_NAME = "de-rambo-export-prod"
    BUCKET_NAME = "dr-prod-ca5e7f-rental-metrics-benchmark-aggs"
    PROJECT_NAME = "dr-prod-ca5e7f"
    LOGGING_GCP_PROJECT = "dr-prod-ca5e7f"
    DATA_PROJECT_NAME = "dr-prod-ca5e7f"
    CLUSTER_NAME = "composer-jobs-v3-dev"
    AGGS_DESTINATION_DATASET = "rental_metrics_aggs"
    AGGS_STAGE_DATASET_NAME = "rental_metrics_aggs_stage"
    SERVER_DOMAIN = "RASGCP.NET"
    PAGERDUTY_NOTIFY = True

    # import into SS pool
    ss_import_pool_name = "getl01_bcp_export"

    # RATES COMPUTE PIPELINE CONSTANTS
    RATES_STAGE_DATASET_NAME = "rental_benchmarks_stage"
    RATES_DATASET_NAME = "rental_benchmarks"
    RATES_GOOGLE_GCS_BUCKET = "dr-prod-ca5e7f-analytics-benchmark-rates-v2"

    # MATERIALIZED INPUT CONSTANTS
    MATERIALIZED_INPUTS_VERSION_DATASET_NAME = "rental_metrics_aggs_version"
    GETL01_SQL_SERVER = "getl01.rasgcp.net"
    AVRO_SCHEMA_BASE_PATH = (
        "gs://dr-prod-ca5e7f-rental-metrics-benchmark-aggs/avro_schemas/"
    )
    GCS_DESTINATION_PATH = (
        "gs://dr-prod-ca5e7f-rental-metrics-benchmark-aggs/avro_exports/"
    )
    REPRESENTATION_JS_PATH: str = (
        "/usr/local/airflow/dags/constants/representation.json"
    )

    # Customer Segmentation Constants
    CUSTOMER_SEGMENTATION_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/customer_segmentation_queries/"
    CUSSEG_DESTINATION_DATASET = "rental_metrics_aggs"
    CUSSEG_VERSION_DATASET = "rental_metrics_aggs_version"

    # DimFMRR constants
    DIM_FM_RR_DESTINATION_DATASET = "dim_fm_rr"
    DIM_FM_RR_STAGE_DATASET = "dim_fm_rr_stage"
    DIM_FM_RR_SOURCE_DATASET = "dim_fm_rr_version"
    DIM_FMRR_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/dim_fm_rr_queries/"
    FMRR_BENCH_SEASONALITY_TABLES_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/fmrr_bench_seasonality_tables/"
    FMRR_BENCH_GROWTH_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/fmrr_bench_growth/"
    LOAD_BENCH_PRELOADS_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/bench_preloads/"
    DAG_BASE_SQL_FOLDER = (
        "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/"
    )

    # demo clients constants
    DEMO_CLIENTS_QUERY_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/demo_clients/"

    # RDO Publish
    RDO_PUBLISH_VERSION_DATASET_NAME = "rdo_publish_version"
    RDO_PUBLISH_STAGE_DATASET_NAME = "rdo_publish_stage"
    RDO_PUBLISH_DATASET_NAME = "rdo_publish"

    # Month To Date
    REFRESH_DATASET_NAME = "data_refresh"
    REFRESH_VERSION_DATASET_NAME = "data_refresh_version"
    REFRESH_STAGE_DATASET_NAME = "data_refresh_stage"

    PUBLISH_SEQUENCE_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/publish_sequence/"
    RDO_TABLES_SQL_FOLDER = (
        "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/rdo_tables/"
    )

    MONTH_TO_DATE_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/month_to_date/"

    # PUSH TO PROD
    RDO_PROD_VERSION_DATASET_NAME = "rdo_prod_version"
    RDO_PROD_STAGE_DATASET_NAME = "rdo_prod_stage"
    RDO_PROD_DATASET_NAME = "rdo_prod"
    PUSH_TO_PROD_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/push_to_prod/"

else:
    DB_SECRET_NAME = "de-rambo-export-prod"
    BUCKET_NAME = "analytics-data-prod-1e04f6-rental-metrics-benchmark-aggs"
    PROJECT_NAME = "management-prod-837a97"
    LOGGING_GCP_PROJECT = "management-prod-837a97"
    DATA_PROJECT_NAME = "analytics-data-prod-1e04f6"
    CLUSTER_NAME = "composer-jobs-v3-prod"
    AGGS_DESTINATION_DATASET = "rental_metrics_aggs"
    AGGS_STAGE_DATASET_NAME = "rental_metrics_aggs_stage"
    SERVER_DOMAIN = "RASGCP.NET"
    PAGERDUTY_NOTIFY = True

    # import into SS pool
    ss_import_pool_name = "getl01_bcp_export"

    # RATES COMPUTE PIPELINE CONSTANTS
    RATES_STAGE_DATASET_NAME = "rental_benchmarks_stage"
    RATES_DATASET_NAME = "rental_benchmarks"
    RATES_GOOGLE_GCS_BUCKET = "analytics-data-prod-1e04f6-analytics-benchmark-rates-v2"

    # MATERIALIZED INPUT CONSTANTS
    MATERIALIZED_INPUTS_VERSION_DATASET_NAME = "rental_metrics_aggs_version"
    GETL01_SQL_SERVER = "getl01.rasgcp.net"
    AVRO_SCHEMA_BASE_PATH = (
        "gs://analytics-data-prod-1e04f6-rental-metrics-benchmark-aggs/avro_schemas/"
    )
    GCS_DESTINATION_PATH = (
        "gs://analytics-data-prod-1e04f6-rental-metrics-benchmark-aggs/avro_exports/"
    )
    REPRESENTATION_JS_PATH: str = (
        "/usr/local/airflow/dags/constants/representation.json"
    )

    # Customer Segmentation Constants
    CUSTOMER_SEGMENTATION_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/customer_segmentation_queries/"
    CUSSEG_DESTINATION_DATASET = "rental_metrics_aggs"
    CUSSEG_VERSION_DATASET = "rental_metrics_aggs_version"

    # DimFMRR constants
    DIM_FM_RR_DESTINATION_DATASET = "dim_fm_rr"
    DIM_FM_RR_STAGE_DATASET = "dim_fm_rr_stage"
    DIM_FM_RR_SOURCE_DATASET = "dim_fm_rr_version"
    DIM_FMRR_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/dim_fm_rr_queries/"
    FMRR_BENCH_SEASONALITY_TABLES_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/fmrr_bench_seasonality_tables/"
    FMRR_BENCH_GROWTH_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/fmrr_bench_growth/"
    LOAD_BENCH_PRELOADS_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/bench_preloads/"
    DAG_BASE_SQL_FOLDER = (
        "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/"
    )

    # demo clients constants
    DEMO_CLIENTS_QUERY_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/demo_clients/"

    # RDO Publish
    RDO_PUBLISH_VERSION_DATASET_NAME = "rdo_publish_version"
    RDO_PUBLISH_STAGE_DATASET_NAME = "rdo_publish_stage"
    RDO_PUBLISH_DATASET_NAME = "rdo_publish"

    # Month To Date
    REFRESH_DATASET_NAME = "data_refresh"
    REFRESH_VERSION_DATASET_NAME = "data_refresh_version"
    REFRESH_STAGE_DATASET_NAME = "data_refresh_stage"

    PUBLISH_SEQUENCE_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/publish_sequence/"
    RDO_TABLES_SQL_FOLDER = (
        "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/rdo_tables/"
    )

    MONTH_TO_DATE_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/month_to_date/"

    # PUSH TO PROD
    RDO_PROD_VERSION_DATASET_NAME = "rdo_prod_version"
    RDO_PROD_STAGE_DATASET_NAME = "rdo_prod_stage"
    RDO_PROD_DATASET_NAME = "rdo_prod"
    PUSH_TO_PROD_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/push_to_prod/"

namespace = "rental-metrics-aggs"
service_account_name = namespace
custom_args = {
    "owner": "<EMAIL>",
    "start_date": datetime(2021, 9, 20),
    "retries": 4,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": task_fail_slack_alert,
    "project_id": PROJECT_NAME,
    "cluster_name": CLUSTER_NAME,
    "tolerations": standard_tolerations,
    "affinity": standard_affinity,
    "image_pull_policy": IMAGE_PULL_POLICY,
    "startup_timeout_seconds": 300,
    "is_delete_operator_pod": True,
    "execution_timeout": timedelta(hours=2),
    "namespace": "rental-metrics-aggs",
    "service_account_name": service_account_name,
    "depends_on_past": False,
    # our goal here is that we know exactly how we want to prioritize the tasks
    "weight_rule": "absolute",
}
default_args = get_default_args(custom_args)

dag = DAG(
    f"rambo-enhancement-v{MAJOR_VERSION}.{MINOR_VERSION}",
    schedule_interval=None,
    default_args=default_args,
    max_active_runs=1,
    catchup=False,
    concurrency=50,
    tags=["rambo"],
    description="""This dag computes everything after benchmark_rates table until it finishes the publish sequence and RDO tables. 
    This includes the Seasonality tables, FMRR Bench geo model, Outlier Feedback model, Ctrl Tables, and the Publish Sequence and populating the RDO Tables.
    It monitors the records on benchmark_etl_run table, and it only finishes and mark RAMBO as DONE when the transfer of earlier parts finish.""",
    on_failure_callback=dag_fail_pagerduty_alert(
        "compute_rental_metrics_aggs_pagerduty_api_key"
    ),
)

# secrets/passwords for the compute benchmark rates tasks
secret_db_username = Secret(
    deploy_type="env", deploy_target="WIN_USER", secret=DB_SECRET_NAME, key="username"
)

secret_db_password = Secret(
    deploy_type="env",
    deploy_target="WIN_PASSWORD",
    secret=DB_SECRET_NAME,
    key="password",
)


start_rambo_enhancement = DummyOperator(task_id="start-rambo-enhancement", dag=dag)


BENCHMARK_SUCCESS = task_notification_factory(
    ":white_check_mark: Analytics Benchmark pipeline in {} "
    "succeeded.".format(ENVIRONMENT),
    channel="#cloud-infra-alerts{}".format("-dev" if ENVIRONMENT == "dev" else ""),
)

ss_done = GKEStartPodOperator(
    task_id="ss-done",
    name="ss-done",
    dag=dag,
    execution_timeout=dt.timedelta(minutes=5),
    image=get_full_image_name("rental-metric-benchmark-rambo", GCR_REGISTRY),
    arguments=["./script/sqlsvr_done.sh"],
    secrets=[secret_db_username, secret_db_password],
    env_vars={
        "PROJECT": DATA_PROJECT_NAME,
        "SQLSVR_HOSTNAME": GETL01_SQL_SERVER,
        "PK": "{{ dag_run.conf['rambo_pk'] }}",
        "BATCH": TABLE_SUFFIX,
        "LOGGING_PROJECT_ID": LOGGING_GCP_PROJECT,
        "DAG_EXECUTION_DATE": "{{ ts }}",
    },
    on_success_callback=BENCHMARK_SUCCESS,
    on_failure_callback=task_fail_slack_alert,
)


update_rambo_record_when_clear = GKEStartPodOperator(
    task_id="update-rambo-record-when-clear",
    name="update-rambo-record-when-clear",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    trigger_rule="one_success",
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "rambo",
        "STATUS_VALUE": "RUNNING",
        "PK": "{{ dag_run.conf['rambo_pk'] }}",
        "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
        "PARENT": "{{ dag_run.conf['parent_batch'] }}",
    },
    dag=dag,
)


def branch_transfer_to_cloud_db_fn(**kwargs):
    if kwargs.get("transfer_to_cloud_db") == "False":
        task_to_run = "skip-transfers-to-cloud-db"
    else:
        task_to_run = "run-transfers-to-cloud-db"
    return task_to_run


branch_transfer_to_cloud_db = BranchPythonOperator(
    task_id="branch-transfer-to-cloud-db",
    python_callable=branch_transfer_to_cloud_db_fn,
    dag=dag,
    op_kwargs={"transfer_to_cloud_db": '{{dag_run.conf["transfer_to_cloud_db"]}}'},
    on_failure_callback=task_fail_slack_alert,
)
skip_transfers_to_cloud_db = DummyOperator(
    task_id="skip-transfers-to-cloud-db", dag=dag
)
run_transfers_to_cloud_db = DummyOperator(task_id="run-transfers-to-cloud-db", dag=dag)
(
    start_rambo_enhancement
    >> branch_transfer_to_cloud_db
    >> [skip_transfers_to_cloud_db, run_transfers_to_cloud_db]
)

###############################################################
############    FMRR BENCH AND AUX TABLES    ##################
###############################################################

fmrr_bench_apply_orders_complete = DummyOperator(
    task_id="fmrr-bench-apply-orders-complete", dag=dag
)

transfer_fmrr_bench_complete = DummyOperator(
    task_id="transfer-fmrr-bench-geo-complete", dag=dag
)

update_fmrr_bench_record = GKEStartPodOperator(
    task_id="update-fmrr-bench-record",
    name="update-fmrr-bench-record",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "fmrr_bench",
        "STATUS_VALUE": "DONE",
        "PK": "{{ dag_run.conf['fmrr_bench_pk'] }}",
        "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
        "PARENT": "{{ dag_run.conf['parent_batch'] }}",
    },
    dag=dag,
)
update_fmrr_bench_record_error = GKEStartPodOperator(
    task_id="update-fmrr-bench-record-error",
    name="update-fmrr-bench-record-error",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "fmrr_bench",
        "STATUS_VALUE": "FAILED",
        "PK": "{{ dag_run.conf['fmrr_bench_pk'] }}",
        "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
        "PARENT": "{{ dag_run.conf['parent_batch'] }}",
    },
    trigger_rule="one_failed",
    dag=dag,
)

update_fmrr_bench_record_when_clear = GKEStartPodOperator(
    task_id="update-fmrr-bench-record-when-clear",
    name="update-fmrr-bench-record-when-clear",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    trigger_rule="one_success",
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "fmrr_bench",
        "STATUS_VALUE": "RUNNING",
        "PK": "{{ dag_run.conf['fmrr_bench_pk'] }}",
        "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
        "PARENT": "{{ dag_run.conf['parent_batch'] }}",
    },
    dag=dag,
)


###############################################################
################ Data Model - Demo Clients ####################
###############################################################

with TaskGroup(group_id="demo-clients", dag=dag) as demo_clients_group:
    # update view at the end of the pipeline
    update_xyz_rental_rates_view = GKEStartPodOperator(
        task_id="update-xyz-rental-rates-view",
        name="update-xyz-rental-rates-view",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": f"""
            CREATE OR REPLACE VIEW
            `{DATA_PROJECT_NAME}.{DIM_FM_RR_DESTINATION_DATASET}.ras_datamart_analytics_reporting_dbo_xyz_rentalrates` AS (
            SELECT
                *
            FROM
                `{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.ras_datamart_analytics_reporting_dbo_xyz_rentalrates__{TABLE_SUFFIX}` );
            """,
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    update_xyz_fleet_metrics_view = GKEStartPodOperator(
        task_id="update-xyz-fleet-metrics-view",
        name="update-xyz-fleet-metrics-view",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": f"""
            CREATE OR REPLACE VIEW
            `{DATA_PROJECT_NAME}.{DIM_FM_RR_DESTINATION_DATASET}.ras_datamart_analytics_reporting_dbo_xyz_fleetmetrics` AS (
            SELECT
                *
            FROM
                `{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.ras_datamart_analytics_reporting_dbo_xyz_fleetmetrics__{TABLE_SUFFIX}` );
            """,
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    populate_xyz_transform_query = open(
        DEMO_CLIENTS_QUERY_FOLDER + "generate_xyz_transform.sql"
    ).read()
    populate_xyz_transform = GKEStartPodOperator(
        task_id="populate-xyz-transform",
        name="populate-xyz-transform",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        do_xcom_push=False,
        env_vars={
            "QUERY": populate_xyz_transform_query.replace(
                "{DATA_PROJECT_NAME}", DATA_PROJECT_NAME
            )
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET),
            "DESTINATION_TABLE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.ras_magic_dbo_xyz_transform__{TABLE_SUFFIX}",
            "VIEW_TO_REPLACE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_DESTINATION_DATASET}.ras_magic_dbo_xyz_transform",
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "XCOM_PUSH": "False",
            "WRITE_DISPOSITION": "WRITE_TRUNCATE",
        },
        dag=dag,
    )

    populate_xyz_fleet_metrics = GKEStartPodOperator(
        task_id="populate-xyz-fleet-metrics",
        name="populate-xyz-fleet-metrics",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "populate_xyz_fleetmetrics.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET),
            "DESTINATION_TABLE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.ras_datamart_analytics_reporting_dbo_xyz_fleetmetrics__{TABLE_SUFFIX}",
            "VIEW_TO_REPLACE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_DESTINATION_DATASET}.ras_datamart_analytics_reporting_dbo_xyz_fleetmetrics",
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "WRITE_DISPOSITION": "WRITE_TRUNCATE",
        },
        dag=dag,
    )

    populate_xyz_rental_rates = GKEStartPodOperator(
        task_id="populate-xyz-rental-rates",
        name="populate-xyz-rental-rates",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "populate_xyz_rentalrates.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET),
            "DESTINATION_TABLE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.ras_datamart_analytics_reporting_dbo_xyz_rentalrates__{TABLE_SUFFIX}",
            "VIEW_TO_REPLACE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_DESTINATION_DATASET}.ras_datamart_analytics_reporting_dbo_xyz_rentalrates",
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "WRITE_DISPOSITION": "WRITE_TRUNCATE",
        },
        dag=dag,
    )

    inject_abc_small_tools_in_texas = GKEStartPodOperator(
        task_id="inject-abc-small-tools-in-texas",
        name="inject-abc-small-tools-in-texas",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(
                DEMO_CLIENTS_QUERY_FOLDER + "inject_abc_small_tools_texas.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    inject_hvy_xyz_records = GKEStartPodOperator(
        task_id="inject-hvy-xyz-records",
        name="inject-hvy-xyz-records",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "inject_hvy.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    inject_flr_xyz_records = GKEStartPodOperator(
        task_id="inject-flr-xyz-records",
        name="inject-flr-xyz-records",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "inject_flr.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    inject_spr_xyz_records = GKEStartPodOperator(
        task_id="inject-spr-xyz-records",
        name="inject-spr-xyz-records",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "inject_spr.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    inject_crr_xyz_records = GKEStartPodOperator(
        task_id="inject-crr-xyz-records",
        name="inject-crr-xyz-records",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "inject_crr.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    inject_xuk_xyz_records = GKEStartPodOperator(
        task_id="inject-xuk-xyz-records",
        name="inject-xuk-xyz-records",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "inject_xuk.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    inject_xau_xyz_records = GKEStartPodOperator(
        task_id="inject-xau-xyz-records",
        name="inject-xau-xyz-records",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "inject_xau.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    inject_xeu_xyz_records = GKEStartPodOperator(
        task_id="inject-xeu-xyz-records",
        name="inject-xeu-xyz-records",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "inject_xeu.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    inject_xjp_xyz_records = GKEStartPodOperator(
        task_id="inject-xjp-xyz-records",
        name="inject-xjp-xyz-records",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "inject_xjp.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    inject_xcn_xyz_records = GKEStartPodOperator(
        task_id="inject-xcn-xyz-records",
        name="inject-xcn-xyz-records",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "inject_xcn.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    inject_xhd_xyz_records = GKEStartPodOperator(
        task_id="inject-xhd-xyz-records",
        name="inject-xhd-xyz-records",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "inject_xhd.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    reload_etc_geomapping = GKEStartPodOperator(
        task_id="reload-etc-geomapping",
        name="reload-etc-geomapping",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "etc/etc_geomapping.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )
    reload_etc_product_types = GKEStartPodOperator(
        task_id="reload-etc-product-types",
        name="reload-etc-product-types",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "etc/etc_product_types.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )
    reload_etc_equipment_multipliers = GKEStartPodOperator(
        task_id="reload-etc-equipment-multipliers",
        name="reload-etc-equipment-multipliers",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "etc/etc_equipment_multipliers.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )
    reload_etc_product_types_swapped = GKEStartPodOperator(
        task_id="reload-etc-product-types-swapped",
        name="reload-etc-product-types-swapped",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "etc/etc_product_types_swapped.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )
    reload_etc_rented_as_mapping = GKEStartPodOperator(
        task_id="reload-etc-rented-as-mapping",
        name="reload-etc-rented-as-mapping",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "etc/etc_rented_as_mapping.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )
    reload_etc_transactions = GKEStartPodOperator(
        task_id="reload-etc-transactions",
        name="reload-etc-transactions",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "etc/etc_transactions.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )
    
    inject_etcri_records = GKEStartPodOperator(
        task_id="inject-etcri-records",
        name="inject-etcri-records",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "inject_etcri.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )
    
    inject_etc_analytics_01 = GKEStartPodOperator(
        task_id="inject-etc-analytics-01",
        name="inject-etc-analytics-01",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "inject_etc_analytics_01.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )


#####################################################################
############# FMRR Bench - Apply Orders and Transfers ###############
#####################################################################
with TaskGroup(
    group_id="fmrr-bench-apply-orders-and-transfers", dag=dag
) as fmrr_bench_apply_orders:
    # table column mapping templates
    fmrr_bench_rouse_and_company_mapping_template = """
    {"fmrr_id": "FMRR_ID",
                "physical_ut_bench": "Physical Ut Bench", 
                "dollar_ut_bench": "Dollar Ut Bench",
                "fleet_age_bench": "Fleet Age Bench",
                "rrd_bench": "RRD Bench",
                "rad_bench": "RAD Bench", 
                "new_monthly_bench_rate": "New Monthly Bench Rate", 
                "exist_monthly_bench_rate": "Exist Monthly Bench Rate", 
                "monthly_bench_rate": "Monthly Bench Rate", 
                "weekly_bench_rate": "Weekly Bench Rate", 
                "daily_bench_rate": "Daily Bench Rate", 
                "monthly_top_q_client_rate": "Monthly TopQ Client Rate",
                "monthly_top_q_bench_rate": "Monthly TopQ Bench Rate", 
                "monthly_bot_q_client_rate": "Monthly BotQ Client Rate", 
                "monthly_bot_q_bench_rate": "Monthly BotQ Bench Rate",
                "weekly_top_q_client_rate": "Weekly TopQ Client Rate",  
                "weekly_top_q_bench_rate": "Weekly TopQ Bench Rate", 
                "weekly_bot_q_client_rate": "Weekly BotQ Client Rate", 
                "weekly_bot_q_bench_rate": "Weekly BotQ Bench Rate",
                "daily_top_q_client_rate": "Daily TopQ Client Rate",  
                "daily_top_q_bench_rate": "Daily TopQ Bench Rate",
                "daily_bot_q_client_rate": "Daily BotQ Client Rate",  
                "daily_bot_q_bench_rate": "Daily BotQ Bench Rate", 
                "new_monthly_spot_bench_rate": "New Monthly Spot Bench Rate", 
                "monthly_spot_bench_rate": "Monthly Spot Bench Rate", 
                "monthly_spot_top_q_bench_rate": "Monthly Spot TopQ Bench Rate", 
                "monthly_spot_bot_q_bench_rate": "Monthly Spot BotQ Bench Rate", 
                "weekly_spot_bench_rate": "Weekly Spot Bench Rate", 
                "weekly_spot_top_q_bench_rate": "Weekly Spot TopQ Bench Rate", 
                "weekly_spot_bot_q_bench_rate": "Weekly Spot BotQ Bench Rate", 
                "daily_spot_bench_rate": "Daily Spot Bench Rate", 
                "daily_spot_top_q_bench_rate": "Daily Spot TopQ Bench Rate", 
                "daily_spot_bot_q_bench_rate": "Daily Spot BotQ Bench Rate", 
                "new_monthly_core_bench_rate": "New Monthly Core Bench Rate", 
                "monthly_core_bench_rate": "Monthly Core Bench Rate", 
                "monthly_core_top_q_bench_rate": "Monthly Core TopQ Bench Rate", 
                "monthly_core_bot_q_bench_rate": "Monthly Core BotQ Bench Rate", 
                "weekly_core_bench_rate": "Weekly Core Bench Rate", 
                "weekly_core_top_q_bench_rate": "Weekly Core TopQ Bench Rate", 
                "weekly_core_bot_q_bench_rate": "Weekly Core BotQ Bench Rate", 
                "daily_core_bench_rate": "Daily Core Bench Rate", 
                "daily_core_top_q_bench_rate": "Daily Core TopQ Bench Rate", 
                "daily_core_bot_q_bench_rate": "Daily Core BotQ Bench Rate", 
                "monthly_min_bench_rate": "Monthly Min Bench Rate", 
                "monthly_max_bench_rate": "Monthly Max Bench Rate", 
                "weekly_min_bench_rate": "Weekly Min Bench Rate", 
                "weekly_max_bench_rate": "Weekly Max Bench Rate", 
                "daily_min_bench_rate": "Daily Min Bench Rate", 
                "daily_max_bench_rate": "Daily Max Bench Rate", 
                "monthly_spot_min_bench_rate": "Monthly Spot Min Bench Rate", 
                "monthly_spot_max_bench_rate": "Monthly Spot Max Bench Rate", 
                "weekly_spot_min_bench_rate": "Weekly Spot Min Bench Rate", 
                "weekly_spot_max_bench_rate": "Weekly Spot Max Bench Rate", 
                "daily_spot_min_bench_rate": "Daily Spot Min Bench Rate", 
                "daily_spot_max_bench_rate": "Daily Spot Max Bench Rate", 
                "monthly_core_min_bench_rate": "Monthly Core Min Bench Rate", 
                "monthly_core_max_bench_rate": "Monthly Core Max Bench Rate", 
                "weekly_core_min_bench_rate": "Weekly Core Min Bench Rate", 
                "weekly_core_max_bench_rate": "Weekly Core Max Bench Rate", 
                "daily_core_min_bench_rate": "Daily Core Min Bench Rate", 
                "daily_core_max_bench_rate": "Daily Core Max Bench Rate",
                "hourly_bench_rate": "Hourly Bench Rate",
                "hourly_top_q_bench_rate": "Hourly TopQ Bench Rate",
                "hourly_bot_q_bench_rate": "Hourly BotQ Bench Rate",
                "hourly_min_bench_rate": "Hourly Min Bench Rate",
                "hourly_max_bench_rate": "Hourly Max Bench Rate",
                "new_monthly_top_q_bench_rate": "New Monthly TopQ Bench Rate",
                "new_monthly_bot_q_bench_rate": "New Monthly BotQ Bench Rate",
                "new_monthly_min_bench_rate": "New Monthly Min Bench Rate",
                "new_monthly_max_bench_rate": "New Monthly Max Bench Rate",
                "new_monthly_spot_top_q_bench_rate": "New Monthly Spot TopQ Bench Rate",
                "new_monthly_spot_bot_q_bench_rate": "New Monthly Spot BotQ Bench Rate",
                "new_monthly_spot_min_bench_rate": "New Monthly Spot Min Bench Rate",
                "new_monthly_spot_max_bench_rate": "New Monthly Spot Max Bench Rate",
                "new_monthly_core_top_q_bench_rate": "New Monthly Core TopQ Bench Rate",
                "new_monthly_core_bot_q_bench_rate": "New Monthly Core BotQ Bench Rate",
                "new_monthly_core_min_bench_rate": "New Monthly Core Min Bench Rate",
                "new_monthly_core_max_bench_rate": "New Monthly Core Max Bench Rate", 
                "client_order": "ClientOrder" }
    """

    fmrr_bench_cat_and_komatsu_mapping_template = """
    {"fmrr_id": "FMRR_ID",
                "dollar_ut_bench": "Dollar Ut Bench",
                "new_monthly_bench_rate": "New Monthly Bench Rate", 
                "exist_monthly_bench_rate": "Exist Monthly Bench Rate", 
                "monthly_bench_rate": "Monthly Bench Rate", 
                "weekly_bench_rate": "Weekly Bench Rate", 
                "daily_bench_rate": "Daily Bench Rate",
                "monthly_top_q_bench_rate": "Monthly TopQ Bench Rate",
                "monthly_bot_q_bench_rate": "Monthly BotQ Bench Rate", 
                "weekly_top_q_bench_rate": "Weekly TopQ Bench Rate",  
                "weekly_bot_q_bench_rate": "Weekly BotQ Bench Rate",  
                "daily_top_q_bench_rate": "Daily TopQ Bench Rate", 
                "daily_bot_q_bench_rate": "Daily BotQ Bench Rate", 
                "new_monthly_spot_bench_rate": "New Monthly Spot Bench Rate", 
                "monthly_spot_bench_rate": "Monthly Spot Bench Rate", 
                "monthly_spot_top_q_bench_rate": "Monthly Spot TopQ Bench Rate", 
                "monthly_spot_bot_q_bench_rate": "Monthly Spot BotQ Bench Rate", 
                "weekly_spot_bench_rate": "Weekly Spot Bench Rate", 
                "weekly_spot_top_q_bench_rate": "Weekly Spot TopQ Bench Rate", 
                "weekly_spot_bot_q_bench_rate": "Weekly Spot BotQ Bench Rate", 
                "daily_spot_bench_rate": "Daily Spot Bench Rate", 
                "daily_spot_top_q_bench_rate": "Daily Spot TopQ Bench Rate", 
                "daily_spot_bot_q_bench_rate": "Daily Spot BotQ Bench Rate", 
                "new_monthly_core_bench_rate": "New Monthly Core Bench Rate", 
                "monthly_core_bench_rate": "Monthly Core Bench Rate", 
                "monthly_core_top_q_bench_rate": "Monthly Core TopQ Bench Rate", 
                "monthly_core_bot_q_bench_rate": "Monthly Core BotQ Bench Rate", 
                "weekly_core_bench_rate": "Weekly Core Bench Rate", 
                "weekly_core_top_q_bench_rate": "Weekly Core TopQ Bench Rate", 
                "weekly_core_bot_q_bench_rate": "Weekly Core BotQ Bench Rate", 
                "daily_core_bench_rate": "Daily Core Bench Rate", 
                "daily_core_top_q_bench_rate": "Daily Core TopQ Bench Rate", 
                "daily_core_bot_q_bench_rate": "Daily Core BotQ Bench Rate", 
                "monthly_min_bench_rate": "Monthly Min Bench Rate", 
                "monthly_max_bench_rate": "Monthly Max Bench Rate", 
                "weekly_min_bench_rate": "Weekly Min Bench Rate", 
                "weekly_max_bench_rate": "Weekly Max Bench Rate", 
                "daily_min_bench_rate": "Daily Min Bench Rate", 
                "daily_max_bench_rate": "Daily Max Bench Rate", 
                "monthly_spot_min_bench_rate": "Monthly Spot Min Bench Rate", 
                "monthly_spot_max_bench_rate": "Monthly Spot Max Bench Rate", 
                "weekly_spot_min_bench_rate": "Weekly Spot Min Bench Rate", 
                "weekly_spot_max_bench_rate": "Weekly Spot Max Bench Rate", 
                "daily_spot_min_bench_rate": "Daily Spot Min Bench Rate", 
                "daily_spot_max_bench_rate": "Daily Spot Max Bench Rate", 
                "monthly_core_min_bench_rate": "Monthly Core Min Bench Rate", 
                "monthly_core_max_bench_rate": "Monthly Core Max Bench Rate", 
                "weekly_core_min_bench_rate": "Weekly Core Min Bench Rate", 
                "weekly_core_max_bench_rate": "Weekly Core Max Bench Rate", 
                "daily_core_min_bench_rate": "Daily Core Min Bench Rate", 
                "daily_core_max_bench_rate": "Daily Core Max Bench Rate",
                "client_order": "ClientOrder" }
    """

    fmrr_bench_parameters = [
        {
            "field_string": "Market",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_market_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_market_apply_orders.sql",
            "ss_bench_table_name": "dbo.FMRR_Bench_Market",
            "ss_bench_table_template": fmrr_bench_rouse_and_company_mapping_template,
            "number_of_partitions": 5,
            "partition_column": "fmrr_id",
            "partition_column_ss": "FMRR_ID",
        },
        {
            "field_string": "District",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_district_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_district_apply_orders.sql",
            "ss_bench_table_name": "dbo.FMRR_Bench_District",
            "ss_bench_table_template": fmrr_bench_rouse_and_company_mapping_template,
            "number_of_partitions": 5,
            "partition_column": "fmrr_id",
            "partition_column_ss": "FMRR_ID",
        },
        {
            "field_string": "Region",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_region_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_region_apply_orders.sql",
            "ss_bench_table_name": "dbo.FMRR_Bench_Region",
            "ss_bench_table_template": fmrr_bench_rouse_and_company_mapping_template,
            "number_of_partitions": 5,
            "partition_column": "fmrr_id",
            "partition_column_ss": "FMRR_ID",
        },
        {
            "field_string": "CATsubterr",
            "benchmark_rates_filter_column": "rouse_market",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_catsubterr_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_catsubterr_apply_orders.sql",
            "ss_bench_table_name": "dbo.FMRR_Bench_CATsubterr",
            "ss_bench_table_template": fmrr_bench_cat_and_komatsu_mapping_template,
            "number_of_partitions": 2,
            "partition_column": "fmrr_id",
            "partition_column_ss": "FMRR_ID",
        },
        {
            "field_string": "CATterr",
            "benchmark_rates_filter_column": "rouse_district",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_catterr_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_catterr_apply_orders.sql",
            "ss_bench_table_name": "dbo.FMRR_Bench_CATterr",
            "ss_bench_table_template": fmrr_bench_cat_and_komatsu_mapping_template,
            "number_of_partitions": 2,
            "partition_column": "fmrr_id",
            "partition_column_ss": "FMRR_ID",
        },
        {
            "field_string": "CATdist",
            "benchmark_rates_filter_column": "rouse_region",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_catdist_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_catdist_apply_orders.sql",
            "ss_bench_table_name": "dbo.FMRR_Bench_CATdist",
            "ss_bench_table_template": fmrr_bench_cat_and_komatsu_mapping_template,
            "number_of_partitions": 2,
            "partition_column": "fmrr_id",
            "partition_column_ss": "FMRR_ID",
        },
        {
            "field_string": "KOMterr",
            "benchmark_rates_filter_column": "rouse_district",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_komterr_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_komterr_apply_orders.sql",
            "ss_bench_table_name": "dbo.FMRR_Bench_KOMterr",
            "ss_bench_table_template": fmrr_bench_cat_and_komatsu_mapping_template,
            "number_of_partitions": 2,
            "partition_column": "fmrr_id",
            "partition_column_ss": "FMRR_ID",
        },
        {
            "field_string": "KOMsubterr",
            "benchmark_rates_filter_column": "rouse_market",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_komsubterr_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_komsubterr_apply_orders.sql",
            "ss_bench_table_name": "dbo.FMRR_Bench_KOMsubterr",
            "ss_bench_table_template": fmrr_bench_cat_and_komatsu_mapping_template,
            "number_of_partitions": 2,
            "partition_column": "fmrr_id",
            "partition_column_ss": "FMRR_ID",
        },
        {
            "field_string": "KOMreg",
            "benchmark_rates_filter_column": "rouse_region",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_komreg_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_komreg_apply_orders.sql",
            "ss_bench_table_name": "dbo.FMRR_Bench_KOMreg",
            "ss_bench_table_template": fmrr_bench_cat_and_komatsu_mapping_template,
            "number_of_partitions": 2,
            "partition_column": "fmrr_id",
            "partition_column_ss": "FMRR_ID",
        },
        {
            "field_string": "Company",
            "benchmark_rates_filter_column": "",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_company_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_company_apply_orders.sql",
            "ss_bench_table_name": "dbo.FMRR_Bench_Company",
            "ss_bench_table_template": fmrr_bench_rouse_and_company_mapping_template,
            "number_of_partitions": 5,
            "partition_column": "fmrr_id",
            "partition_column_ss": "FMRR_ID",
        },
    ]

    for fmrr_bench_object in fmrr_bench_parameters:
        apply_orders_parameters = {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_STAGE_DATASET": DIM_FM_RR_STAGE_DATASET,
            "TABLE_SUFFIX": TABLE_SUFFIX,
        }
        apply_orders_query = (
            open(fmrr_bench_object["apply_orders_file"])
            .read()
            .format(**apply_orders_parameters)
        )
        apply_orders_cf_query = (
            open(fmrr_bench_object["apply_orders_cf_file"])
            .read()
            .format(**apply_orders_parameters)
        )
        apply_orders = GKEStartPodOperator(
            task_id=f"apply-orders-{fmrr_bench_object['field_string'].lower()}",
            name=f"apply-orders-{fmrr_bench_object['field_string'].lower()}",
            image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
            arguments=["python3", "main.py", "execute-bigquery"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "QUERY": apply_orders_query,
            },
            dag=dag,
        )
        apply_orders_cf = GKEStartPodOperator(
            task_id=f"apply-orders-cf-{fmrr_bench_object['field_string'].lower()}",
            name=f"apply-orders-cf-{fmrr_bench_object['field_string'].lower()}",
            image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
            arguments=["python3", "main.py", "execute-bigquery"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "QUERY": apply_orders_cf_query,
            },
            dag=dag,
        )

        bq_table_name = f"fmrr_bench_{fmrr_bench_object['field_string'].lower()}"
        partition_task_id = f"partition-bq-table-{bq_table_name}"
        partition_bq_table = GKEStartPodOperator(
            task_id=partition_task_id,
            name=partition_task_id,
            image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
            arguments=["python3", "main.py", "execute-bigquery"],
            do_xcom_push=False,
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "QUERY": open(
                    PUSH_TO_PROD_FOLDER
                    + "push_to_prod_preload/partition_bq_tables_template_with_ceiling.sql"
                )
                .read()
                .replace(
                    "{NUMBER_OF_PARTITIONS}",
                    str(fmrr_bench_object["number_of_partitions"]),
                )
                .replace(
                    "{PARTITION_COLUMN}",
                    fmrr_bench_object["partition_column"],
                )
                .replace(
                    "{TABLE_NAME}",
                    DIM_FM_RR_STAGE_DATASET + "." + bq_table_name + f"__{TABLE_SUFFIX}",
                ),
                "XCOM_PUSH": "False",
                "WRITE_LOGS": "True",
                "LOGS_GCP_PROJECT": PROJECT_NAME,
                "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/PUSH_TO_PROD/{bq_table_name}",
                "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
                "WRITE_DISPOSITION": None,
            },
            dag=dag,
        )

        ss_table_name = fmrr_bench_object["ss_bench_table_name"].split(".")[1]
        create_partition_bounds_table = GKEStartPodOperator(
            task_id=f"create-partitions-bounds-table-{bq_table_name}",
            name=f"create-partitions-bounds-table-{bq_table_name}",
            secrets=[secret_db_username, secret_db_password],
            image=get_full_image_name("execute-sql-server", GCR_REGISTRY),
            execution_timeout=timedelta(hours=2),
            arguments=["python3", "main.py", "execute-sql-server"],
            resources=RESOURCES_SMALL_TASK,
            priority_weight=9,
            env_vars={
                "DB_SERVER": GETL01_SQL_SERVER,
                "DB_NAME": "ras_DataMart_Analytics_Cloud",
                "TABLE_NAME": fmrr_bench_object["ss_bench_table_name"],
                "QUERY": f"IF OBJECT_ID(N'[dbo].[{ss_table_name}_PartitionBounds]', N'U') IS NULL "
                + "BEGIN "
                + f"CREATE TABLE {fmrr_bench_object['ss_bench_table_name']}_PartitionBounds ( "
                + "pid int NOT NULL, "
                + "table_id int NOT NULL, "
                + "min_fmrr_id bigint NOT NULL, "
                + "max_fmrr_id bigint NOT NULL, "
                + f"CONSTRAINT PK_{ss_table_name}_PartitionBounds PRIMARY KEY (table_id) "
                + "); "
                + "END; ",
            },
            dag=dag,
        )

        partition_bounds_bq = GKEStartPodOperator(
            task_id=f"partition-bounds-{bq_table_name}",
            name=f"partition-bounds-{bq_table_name}",
            image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
            arguments=["python3", "main.py", "execute-bigquery"],
            do_xcom_push=False,
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "QUERY": open(
                    DAG_BASE_SQL_FOLDER
                    + "push_to_prod/push_to_prod_preload/partition_bounds_table.sql"
                )
                .read()
                .replace("{PARTITION_COLUMN}", "FMRR_ID")
                .replace(
                    "{TABLE_NAME_BOUNDS}",
                    f"{DIM_FM_RR_STAGE_DATASET}.{bq_table_name}_bounds_table_{TABLE_SUFFIX}",
                )
                .replace("{TABLE_NAME}", f"{DIM_FM_RR_STAGE_DATASET}.{bq_table_name}"+f"__{TABLE_SUFFIX}"),
                "XCOM_PUSH": "False",
                "WRITE_LOGS": "True",
                "LOGS_GCP_PROJECT": PROJECT_NAME,
                "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/BENCHMARK/BENCHMARK_RATES",
                "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
                "WRITE_DISPOSITION": None,
            },
            dag=dag,
        )

        transfer_partition_bounds_benchmark_rates = GKEStartPodOperator(
            task_id=f"transfer-partition-bounds-{bq_table_name}",
            name=f"transfer-partition-bounds-{bq_table_name}",
            priority_weight=12,
            execution_timeout=timedelta(hours=3),
            image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
            secrets=[secret_db_username, secret_db_password],
            arguments=["python3", "main.py", "load-from-bq-to-ss"],
            resources=k8s.V1ResourceRequirements(
                requests={"cpu": "3000m", "memory": "4Gi"}
            ),
            pool=ss_import_pool_name,
            env_vars={
                "GCP_PROJECT": DATA_PROJECT_NAME,
                "BQ_DATASET": DIM_FM_RR_STAGE_DATASET,
                "BQ_TABLE": f"{bq_table_name}_bounds_table_{TABLE_SUFFIX}",
                "DB_SERVER": GETL01_SQL_SERVER,
                "DB_NAME": "ras_DataMart_Analytics_Cloud",
                "TABLE_NAME": f"{fmrr_bench_object['ss_bench_table_name']}_PartitionBounds",
                "WRITE_MODE": "overwrite",
                "DO_QA_ROW_COUNT": "False",
                "GCS_BUCKET_NAME": BUCKET_NAME,
                "PARALLEL_PROCESSES": "3",
                "CHUNKSIZE": "100000",
                "COLUMN_MAPPING": """{
                    "table_id": "table_id"
                    , "pid": "pid"
                    , "min_fmrr_id": "min_fmrr_id"
                    , "max_fmrr_id": "max_fmrr_id"
                }""",
                "PARSE_DTYPES": "False",
            },
            dag=dag,
        )

        partition_scheme_suffix = (
            f"_partition_obj_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        )
        create_stg_table = GKEStartPodOperator(
            task_id=f"create-stg-table-{bq_table_name}",
            name=f"create-stg-table-{bq_table_name}",
            secrets=[secret_db_username, secret_db_password],
            image=get_full_image_name("execute-sql-server", GCR_REGISTRY),
            execution_timeout=timedelta(hours=2),
            arguments=["python3", "main.py", "execute-sql-server"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "DB_SERVER": GETL01_SQL_SERVER,
                "DB_NAME": "ras_DataMart_Analytics_Cloud",
                "QUERY": "EXECUTE [dbo].[Create_FMRR_StagingTable] "
                + f"@table_name='{ss_table_name}', "
                + "@staging_suffix='_STG', "
                + f"@partition_suffix='{partition_scheme_suffix}', "
                + f"@partition_bounds='{fmrr_bench_object['ss_bench_table_name']}_PartitionBounds', "
                + "@show_sql_only= 0,"
                + "@partition_term='FMRR_ID',"
                + "@partition_dt='BIGINT'; ",
            },
            dag=dag,
        )

        staging_table_name = fmrr_bench_object["ss_bench_table_name"] + "_STG"

        stop_table_migration = DummyOperator(
            task_id=f"stop-table-migration-{bq_table_name}", dag=dag
        )

        (
            partition_bq_table
            >> create_partition_bounds_table
            >> partition_bounds_bq
            >> transfer_partition_bounds_benchmark_rates
            >> create_stg_table
            >> stop_table_migration
        )

        replace_main_with_stg_table = GKEStartPodOperator(
            task_id=f"replace-main-with-stg-table-{bq_table_name}",
            name=f"replace-main-with-stg-table-{bq_table_name}",
            image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
            secrets=[secret_db_username, secret_db_password],
            arguments=["python3", "main.py", "recreate-stg-table-indexes"],
            resources=RESOURCES_SMALL_TASK,
            execution_timeout=timedelta(hours=3),
            priority_weight=9,
            env_vars={
                "DB_SERVER": GETL01_SQL_SERVER,
                "DB_NAME": "ras_DataMart_Analytics_Cloud",
                "TABLE_NAME": fmrr_bench_object["ss_bench_table_name"],
                "DO_QA_ROW_COUNT": "True",
                "QA_GCP_PROJECT": DATA_PROJECT_NAME,
                "BQ_QA_TABLE": DATA_PROJECT_NAME
                + "."
                + DIM_FM_RR_STAGE_DATASET
                + "."
                + bq_table_name
                + f"__{TABLE_SUFFIX}",
                "SS_QA_TABLE": f"ras_DataMart_Analytics_Cloud.{staging_table_name}",
                "REPLACE_TABLE": "True",
                "MANUAL_INDEX_COMMAND": "--",  # setting it as empty will not recreate the index but will replace the main table with the staging one
            },
            dag=dag,
        )
        replace_main_with_stg_table >> transfer_fmrr_bench_complete

        for partition_id in range(0, fmrr_bench_object["number_of_partitions"]):
            transfer_bench_partition = GKEStartPodOperator(
                task_id=f"transfer-{bq_table_name}-partition-p{partition_id}",
                name=f"transfer-{bq_table_name}-partition-p{partition_id}",
                priority_weight=13,
                execution_timeout=timedelta(hours=3),
                image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
                secrets=[secret_db_username, secret_db_password],
                arguments=["python3", "main.py", "migrate-bq-partition-and-index"],
                resources=k8s.V1ResourceRequirements(
                    requests={"cpu": "3000m", "memory": "4Gi"}
                ),
                pool=ss_import_pool_name,
                env_vars={
                    "GCP_PROJECT": DATA_PROJECT_NAME,
                    "BQ_DATASET": DIM_FM_RR_STAGE_DATASET,
                    "BQ_TABLE": f"{bq_table_name}__{TABLE_SUFFIX}_p{partition_id}",
                    "DB_SERVER": GETL01_SQL_SERVER,
                    "DB_NAME": "ras_DataMart_Analytics_Cloud",
                    "TABLE_NAME": fmrr_bench_object["ss_bench_table_name"],
                    "TABLE_SUFFIX": f"_part_{str(partition_id)}",
                    "WRITE_MODE": "overwrite",
                    "DO_QA_ROW_COUNT": "True",
                    # in the case of having a particular table for each partition we do not need this qa
                    # since the method already compares bq to ss.
                    # "SS_QA_QUERY_OVERRIDE": qa_row_count_query,
                    # we do not need to run a query before the migration in this scenario since it is moving the partition from bq to ss
                    "EXECUTE_QUERY_BEFORE": "",
                    "GCS_BUCKET_NAME": BUCKET_NAME,
                    "PARALLEL_PROCESSES": "4",
                    "CHUNKSIZE": "200000",
                    "COLUMN_MAPPING": fmrr_bench_object["ss_bench_table_template"],
                    "PARSE_DTYPES": "False",
                    "SS_INDEX_PROCEDURE": "IndexAndSwitch_FMRR_StagingTable",
                    "PARTITION_INDEX": f"{str(partition_id)}",
                    "PARTITION_COLUMN": "FMRR_ID",
                    "SS_DESTINATION_TABLE": f"{ss_table_name}_STG",
                    "SS_PARTITION_BOUNDS_TABLE": f"{fmrr_bench_object['ss_bench_table_name']}_PartitionBounds",
                },
                dag=dag,
            )

            (
                stop_table_migration
                >> transfer_bench_partition
                >> replace_main_with_stg_table
            )

        transfer_fmrr_cf_table_to_ss = GKEStartPodOperator(
            task_id=f"transfer-fmrr-cf-{fmrr_bench_object['field_string'].lower()}-to-ss",
            name=f"transfer-fmrr-cf-{fmrr_bench_object['field_string'].lower()}-to-ss",
            image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
            priority_weight=11,
            execution_timeout=timedelta(minutes=60),
            secrets=[secret_db_username, secret_db_password],
            arguments=["python3", "main.py", "load-from-bq-to-ss"],
            resources=k8s.V1ResourceRequirements(
                requests={"cpu": "3000m", "memory": "2Gi"}
            ),
            pool=ss_import_pool_name,
            env_vars={
                "GCP_PROJECT": DATA_PROJECT_NAME,
                "BQ_DATASET": DIM_FM_RR_STAGE_DATASET,
                "BQ_TABLE": f"fmrr_cf_{fmrr_bench_object['field_string'].lower()}__{TABLE_SUFFIX}",
                "DB_SERVER": GETL01_SQL_SERVER,
                "DB_NAME": "ras_DataMart_Analytics_Cloud",
                "TABLE_NAME": f"dbo.FMRR_CF_{fmrr_bench_object['field_string']}",
                "WRITE_MODE": "overwrite",
                "GCS_BUCKET_NAME": BUCKET_NAME,
                "CHUNKSIZE": "100000",
                "COLUMN_MAPPING": fmrr_bench_object["ss_bench_table_template"],
                "PARSE_DTYPES": "False",
            },
            dag=dag,
        )
        # FMRR Bench and FMRR CF creation dependency
        (
            start_rambo_enhancement
            >> apply_orders_cf
            >> transfer_fmrr_cf_table_to_ss
            >> transfer_fmrr_bench_complete
        )
        (start_rambo_enhancement >> apply_orders >> partition_bq_table)
        run_transfers_to_cloud_db >> [transfer_fmrr_cf_table_to_ss, partition_bq_table]

        # After the Apply orders task I'll run the dummy fmrr_bench_apply_orders_complete
        # so the dependencies can run while the FMRR Bench tables are sent back to SQL Server
        [apply_orders, apply_orders_cf] >> fmrr_bench_apply_orders_complete

    (
        [
            start_rambo_enhancement,
            fmrr_bench_apply_orders_complete,
        ]
        >> update_fmrr_bench_record_when_clear
        >> update_fmrr_bench_record
    )
    update_fmrr_bench_record_when_clear >> update_rambo_record_when_clear


###########################################################
################ FMRR BENCH CF VIEWS ######################
###########################################################

# This dict will have all the tasks for FMRR Bench and will be used for the dependencies.

rambo_tasks_dict = {}

with TaskGroup(group_id="fmrr-benchcf-views", dag=dag) as fmrr_benchcf_views_group:
    rambo_tasks_dict["fmrr-benchcf-views"] = fmrr_benchcf_views_group

    # fmrr-bench-apply-orders-complete will be used as a dependency for the views
    rambo_tasks_dict[
        "fmrr-bench-apply-orders-complete"
    ] = fmrr_bench_apply_orders_complete

    fmrr_benchcf_views = [
        {
            "task_id": "view-fmrr-benchcf-catterr",
            "table_name": "fmrr_benchcf_catterr",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["fmrr-bench-apply-orders-complete"],
            "query_file": "view_fmrr_benchcf_catterr.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "view-fmrr-benchcf-catsubterr",
            "table_name": "fmrr_benchcf_catsubterr",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["fmrr-bench-apply-orders-complete"],
            "query_file": "view_fmrr_benchcf_catsubterr.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "view-fmrr-benchcf-catdist",
            "table_name": "fmrr_benchcf_catdist",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["fmrr-bench-apply-orders-complete"],
            "query_file": "view_fmrr_benchcf_catdist.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "view-fmrr-benchcf-company",
            "table_name": "fmrr_benchcf_company",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["fmrr-bench-apply-orders-complete"],
            "query_file": "view_fmrr_benchcf_company.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "view-fmrr-benchcf-district",
            "table_name": "fmrr_benchcf_district",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["fmrr-bench-apply-orders-complete"],
            "query_file": "view_fmrr_benchcf_district.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "view-fmrr-benchcf-komterr",
            "table_name": "fmrr_benchcf_komterr",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["fmrr-bench-apply-orders-complete"],
            "query_file": "view_fmrr_benchcf_komterr.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "view-fmrr-benchcf-komsubterr",
            "table_name": "fmrr_benchcf_komsubterr",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["fmrr-bench-apply-orders-complete"],
            "query_file": "view_fmrr_benchcf_komsubterr.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "view-fmrr-benchcf-komreg",
            "table_name": "fmrr_benchcf_komreg",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["fmrr-bench-apply-orders-complete"],
            "query_file": "view_fmrr_benchcf_komreg.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "view-fmrr-benchcf-market",
            "table_name": "fmrr_benchcf_market",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["fmrr-bench-apply-orders-complete"],
            "query_file": "view_fmrr_benchcf_market.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "view-fmrr-benchcf-region",
            "table_name": "fmrr_benchcf_region",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["fmrr-bench-apply-orders-complete"],
            "query_file": "view_fmrr_benchcf_region.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "view-fmrr-bench-bestavmd-noco",
            "table_name": "fmrr_bench_bestavmd_noco",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["fmrr-bench-apply-orders-complete"],
            "query_file": "view_fmrr_bench_bestavmd_noco.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "view-fmrr-benchcf-bestavmd-noco",
            "table_name": "fmrr_benchcf_bestavmd_noco",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": [
                "view-fmrr-benchcf-market",
                "view-fmrr-benchcf-district",
            ],
            "query_file": "view_fmrr_benchcf_bestavmd_noco.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "view-fmrr-benchcf-bestavmdrc-noco",
            "table_name": "fmrr_benchcf_bestavmdrc_noco",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": [
                "view-fmrr-benchcf-market",
                "view-fmrr-benchcf-district",
                "view-fmrr-benchcf-region",
                "view-fmrr-benchcf-company",
            ],
            "query_file": "view_fmrr_benchcf_bestavmdrc_noco.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
    ]

    fmrr_benchcf_views_complete = DummyOperator(
        task_id="fmrr-benchcf-views-complete", dag=dag
    )
    rambo_tasks_dict["fmrr-benchcf-views-complete"] = fmrr_benchcf_views_complete

    # Initializing parameters for fmrr_benchcf_views
    for task_info in fmrr_benchcf_views:
        log_task_name = task_info["task_id"].replace("-", "_").upper()
        task_info[
            "logs_job_identifier"
        ] = f"RAMBO_PIPELINE/FMRR_BENCHCF_VIEWS/{log_task_name}"

        task_info["sql_folder"] = (
            DAG_BASE_SQL_FOLDER
            + "dim_fm_rr_queries/fmrr_bench_geo_model/fmrr_benchcf_views/"
        )

        task_info["end_task"] = "fmrr-benchcf-views-complete"
        task_info["task_group"] = "fmrr-benchcf-views"

###########################################################
################# RELOAD CRTL TABLES ######################
###########################################################

with TaskGroup(group_id="ctrl-tables", dag=dag) as ctrl_tables_group:
    rambo_tasks_dict["ctrl-tables"] = ctrl_tables_group

    ctrl_tables_dependencies_finished = DummyOperator(
        task_id="ctrl-tables-dependencies-finished", dag=dag
    )
    rambo_tasks_dict[
        "ctrl-tables-dependencies-finished"
    ] = ctrl_tables_dependencies_finished

    [
        fmrr_bench_apply_orders_complete,
        fmrr_benchcf_views_complete,
    ] >> ctrl_tables_dependencies_finished

    # RELOAD CRTL TABLES
    reload_ctrl_tables_inputs_tasks = [
        {
            "task_id": "populate-ctrl-branchinfo",
            "table_name": "ctrl_branchinfo",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["ctrl-tables-dependencies-finished"],
            "query_file": "populate_ctrl_branchinfo.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-ctrl-branchinfo",
            "sql_server_table_name": "dbo.CTRL_BranchInfo",
            "column_mapping_file": "mapping_ctrl_branchinfo.json",
            "resources": k8s.V1ResourceRequirements(
                requests={"cpu": "1000m", "memory": "0.5Gi"}
            ),
        },
        {
            "task_id": "populate-ctrl-equipmentschema",
            "table_name": "ctrl_equipmentschema",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["ctrl-tables-dependencies-finished"],
            "query_file": "populate_ctrl_equipmentschema.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-ctrl-equipmentschema",
            "sql_server_table_name": "dbo.CTRL_EquipmentSchema",
            "column_mapping_file": "mapping_ctrl_equipmentschema.json",
            "resources": k8s.V1ResourceRequirements(
                requests={"cpu": "1000m", "memory": "0.5Gi"}
            ),
        },
        {
            "task_id": "populate-ctrl-fleetmetrics",
            "table_name": "ctrl_fleetmetrics",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["ctrl-tables-dependencies-finished"],
            "query_file": "populate_ctrl_fleetmetrics.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-ctrl-fleetmetrics",
            "sql_server_table_name": "dbo.CTRL_FleetMetrics",
            "column_mapping_file": "mapping_ctrl_fleetmetrics.json",
            "resources": k8s.V1ResourceRequirements(
                requests={"cpu": "1000m", "memory": "0.5Gi"}
            ),
        },
        {
            "task_id": "populate-ctrl-rentalrates",
            "table_name": "ctrl_rentalrates",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["ctrl-tables-dependencies-finished"],
            "query_file": "populate_ctrl_rentalrates.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-ctrl-rentalrates",
            "sql_server_table_name": "dbo.CTRL_RentalRates",
            "column_mapping_file": "mapping_ctrl_rentalrates.json",
            "resources": k8s.V1ResourceRequirements(
                requests={"cpu": "1000m", "memory": "0.5Gi"}
            ),
        },
        {
            "task_id": "populate-ctrl-transactionattributes",
            "table_name": "ctrl_transactionattributes",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["ctrl-tables-dependencies-finished"],
            "query_file": "populate_ctrl_transactionattributes.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-ctrl-transactionattributes",
            "sql_server_table_name": "dbo.CTRL_TransactionAttributes",
            "column_mapping_file": "mapping_ctrl_transactionattributes.json",
            "resources": k8s.V1ResourceRequirements(
                requests={"cpu": "1000m", "memory": "0.5Gi"}
            ),
        },
        {
            "task_id": "reload-ctrl-coverage",
            "table_name": "ctrl_coverage",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["ctrl-tables-dependencies-finished"],
            "query_file": "reload_ctrl_coverage.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-ctrl-coverage",
            "sql_server_table_name": "dbo.CTRL_Coverage",
            "column_mapping_file": "mapping_ctrl_coverage.json",
            "resources": k8s.V1ResourceRequirements(
                requests={"cpu": "1000m", "memory": "0.5Gi"}
            ),
        },
    ]

    reload_ctrl_tables_inputs_complete = DummyOperator(
        task_id="reload-ctrl-tables-inputs-complete", dag=dag
    )
    rambo_tasks_dict[
        "reload-ctrl-tables-inputs-complete"
    ] = reload_ctrl_tables_inputs_complete

    insert_ctrl_tables_record = GKEStartPodOperator(
        task_id="insert-ctrl-tables-record",
        name="insert-ctrl-tables-record",
        secrets=[secret_db_username, secret_db_password],
        do_xcom_push=True,
        image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
        arguments=["python3", "main.py", "update-control-table"],
        execution_timeout=timedelta(minutes=5),
        retry_delay=timedelta(seconds=10),
        env_vars={
            "TABLE_NAME": "benchmark_etl_run",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Computation",
            "PIPELINE": "ctrl_tables",
            "STATUS_VALUE": "RUNNING",
            "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
            "PARENT": "{{ dag_run.conf['parent_batch'] }}",
        },
        dag=dag,
    )
    update_ctrl_tables_record = GKEStartPodOperator(
        task_id="update-ctrl-tables-record",
        name="update-ctrl-tables-record",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
        do_xcom_push=True,
        arguments=["python3", "main.py", "update-control-table"],
        execution_timeout=timedelta(minutes=5),
        retry_delay=timedelta(seconds=10),
        env_vars={
            "TABLE_NAME": "benchmark_etl_run",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Computation",
            "PIPELINE": "ctrl_tables",
            "STATUS_VALUE": "DONE",
            "PK": '{{ task_instance.xcom_pull(task_ids="ctrl-tables.insert-ctrl-tables-record", key="return_value")["pk"] }}',
            "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
            "PARENT": "{{ dag_run.conf['parent_batch'] }}",
        },
        dag=dag,
    )
    update_ctrl_tables_record_error = GKEStartPodOperator(
        task_id="update-ctrl-tables-record-error",
        name="update-ctrl-tables-record-error",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
        do_xcom_push=True,
        arguments=["python3", "main.py", "update-control-table"],
        execution_timeout=timedelta(minutes=5),
        retry_delay=timedelta(seconds=10),
        env_vars={
            "TABLE_NAME": "benchmark_etl_run",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Computation",
            "PIPELINE": "ctrl_tables",
            "STATUS_VALUE": "FAILED",
            "PK": '{{ task_instance.xcom_pull(task_ids="ctrl-tables.insert-ctrl-tables-record", key="return_value")["pk"] }}',
            "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
            "PARENT": "{{ dag_run.conf['parent_batch'] }}",
        },
        trigger_rule="one_failed",
        dag=dag,
    )
    update_ctrl_tables_record_when_clear = GKEStartPodOperator(
        task_id="update-ctrl-tables-record-when-clear",
        name="update-ctrl-tables-record-when-clear",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
        do_xcom_push=True,
        arguments=["python3", "main.py", "update-control-table"],
        execution_timeout=timedelta(minutes=5),
        retry_delay=timedelta(seconds=10),
        trigger_rule="one_success",
        env_vars={
            "TABLE_NAME": "benchmark_etl_run",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Computation",
            "PIPELINE": "ctrl_tables",
            "STATUS_VALUE": "RUNNING",
            "PK": '{{ task_instance.xcom_pull(task_ids="ctrl-tables.insert-ctrl-tables-record", key="return_value")["pk"] }}',
            "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
            "PARENT": "{{ dag_run.conf['parent_batch'] }}",
        },
        dag=dag,
    )

    ctrl_tables_dependencies_finished >> insert_ctrl_tables_record
    update_ctrl_tables_record >> ss_done
    [
        insert_ctrl_tables_record,
        update_ctrl_tables_record,
    ] >> update_ctrl_tables_record_when_clear
    update_ctrl_tables_record_when_clear >> update_rambo_record_when_clear

    # Initializing parameters common to ctrl_tables_inputs tasks
    for task_info in reload_ctrl_tables_inputs_tasks:
        log_task_name = task_info["task_id"].replace("-", "_").upper()
        task_info[
            "logs_job_identifier"
        ] = f"RAMBO_PIPELINE/RELOAD_CTRL_TABLES_INPUTS/{log_task_name}"

        task_info["sql_folder"] = DAG_BASE_SQL_FOLDER + "reload_ctrl_tables_inputs/"

        task_info["end_task"] = "reload-ctrl-tables-inputs-complete"

        task_info["priority_weight_transfer"] = 15
        task_info["end_transfer_task"] = [
            update_ctrl_tables_record,
            update_ctrl_tables_record_error,
        ]
        task_info["task_group"] = "ctrl-tables"

    # ---------------------------------------

    # ---------------------------------------

    # RELOAD CRTL FMRR BASE
    reload_ctrl_fmrr_base_tasks = [
        {
            "task_id": "create-ctrl-fmrr-base",
            "table_name": "ctrl_fmrr_base",
            "write_disposition": None,
            "task_dependencies": ["ctrl-tables-dependencies-finished"],
            "query_file": "create_ctrl_fmrr_base.sql",
            "query_params": {
                "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "CTRL_FMRR_BASE_TABLE": f"ctrl_fmrr_base__{TABLE_SUFFIX}",
            },
        },
        {
            "task_id": "fmrr-base-district",
            "table_name": "ctrl_fmrr_base",
            # "destination_table": """{{ task_instance.xcom_pull(task_ids="ctrl-tables.create-ctrl-fmrr-base",
            #                                     key="return_value")["metadata"]["bigquery_table_name"]}}""",
            "task_dependencies": ["create-ctrl-fmrr-base"],
            "query_file": "fmrr_base_district.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "TABLE_SUFFIX": TABLE_SUFFIX,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "fmrr-base-market",
            "table_name": "ctrl_fmrr_base",
            # "destination_table": """{{ task_instance.xcom_pull(task_ids="ctrl-tables.create-ctrl-fmrr-base",
            #                                     key="return_value")["metadata"]["bigquery_table_name"]}}""",
            "task_dependencies": ["create-ctrl-fmrr-base"],
            "query_file": "fmrr_base_market.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "TABLE_SUFFIX": TABLE_SUFFIX,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "fmrr-base-national",
            "table_name": "ctrl_fmrr_base",
            # "destination_table": """{{ task_instance.xcom_pull(task_ids="ctrl-tables.create-ctrl-fmrr-base",
            #                                     key="return_value")["metadata"]["bigquery_table_name"]}}""",
            "task_dependencies": ["create-ctrl-fmrr-base"],
            "query_file": "fmrr_base_national.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "TABLE_SUFFIX": TABLE_SUFFIX,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "fmrr-base-region",
            "table_name": "ctrl_fmrr_base",
            # "destination_table": """{{ task_instance.xcom_pull(task_ids="ctrl-tables.create-ctrl-fmrr-base",
            #                                     key="return_value")["metadata"]["bigquery_table_name"]}}""",
            "task_dependencies": ["create-ctrl-fmrr-base"],
            "query_file": "fmrr_base_region.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "TABLE_SUFFIX": TABLE_SUFFIX,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "fmrr-komterr",
            "table_name": "ctrl_fmrr_base",
            # "destination_table": """{{ task_instance.xcom_pull(task_ids="ctrl-tables.create-ctrl-fmrr-base",
            #                                     key="return_value")["metadata"]["bigquery_table_name"]}}""",
            "task_dependencies": ["create-ctrl-fmrr-base"],
            "query_file": "fmrr_komterr.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "TABLE_SUFFIX": TABLE_SUFFIX,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "fmrr-catterr",
            "table_name": "ctrl_fmrr_base",
            # "destination_table": """{{ task_instance.xcom_pull(task_ids="ctrl-tables.create-ctrl-fmrr-base",
            #                                     key="return_value")["metadata"]["bigquery_table_name"]}}""",
            "task_dependencies": ["create-ctrl-fmrr-base"],
            "query_file": "fmrr_catterr.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "TABLE_SUFFIX": TABLE_SUFFIX,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "fmrr-md-bestav-actual",
            "table_name": "ctrl_fmrr_base",
            # "destination_table": """{{ task_instance.xcom_pull(task_ids="ctrl-tables.create-ctrl-fmrr-base",
            #                                     key="return_value")["metadata"]["bigquery_table_name"]}}""",
            "task_dependencies": [
                "create-ctrl-fmrr-base",
                "view-fmrr-bench-bestavmd-noco",
            ],
            "query_file": "fmrr_base_md_bestav_actual.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "TABLE_SUFFIX": TABLE_SUFFIX,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "fmrr-md-bestav-cf",
            "table_name": "ctrl_fmrr_base",
            # "destination_table": """{{ task_instance.xcom_pull(task_ids="ctrl-tables.create-ctrl-fmrr-base",
            #                                     key="return_value")["metadata"]["bigquery_table_name"]}}""",
            "task_dependencies": ["create-ctrl-fmrr-base"],
            "query_file": "fmrr_base_md_bestav_cf.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "TABLE_SUFFIX": TABLE_SUFFIX,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "fmrr-mdrv-bestav",
            "table_name": "ctrl_fmrr_base",
            # "destination_table": """{{ task_instance.xcom_pull(task_ids="ctrl-tables.create-ctrl-fmrr-base",
            #                                     key="return_value")["metadata"]["bigquery_table_name"]}}""",
            "task_dependencies": ["create-ctrl-fmrr-base"],
            "query_file": "fmrr_base_mdrv_bestav.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "TABLE_SUFFIX": TABLE_SUFFIX,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
    ]

    ctrl_fmrr_base_complete = DummyOperator(task_id="ctrl-fmrr-base-complete", dag=dag)
    rambo_tasks_dict["ctrl-fmrr-base-complete"] = ctrl_fmrr_base_complete

    # I'll have to create this task and dependency 'manually' here as it don't fit the model of tasks in dicts.
    mapping_ctrl_fmrr_base = open(
        DAG_BASE_SQL_FOLDER + "reload_ctrl_tables_bench/mapping_ctrl_fmrr_base.json"
    ).read()
    transfer_ctrl_fmrr_base_back_to_ss = GKEStartPodOperator(
        task_id="transfer-ctrl-fmrr-base-to-ss",
        name="transfer-ctrl-fmrr-base-to-ss",
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        priority_weight=15,
        execution_timeout=timedelta(minutes=30),
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(
            requests={"cpu": "2000m", "memory": "1.5Gi"}
        ),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BQ_TABLE": f"{reload_ctrl_fmrr_base_tasks[0]['table_name']}__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.CTRL_FMRR_Base",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": mapping_ctrl_fmrr_base,
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )
    (
        ctrl_fmrr_base_complete
        >> transfer_ctrl_fmrr_base_back_to_ss
        >> [update_ctrl_tables_record, update_ctrl_tables_record_error]
    )
    run_transfers_to_cloud_db >> transfer_ctrl_fmrr_base_back_to_ss
    rambo_tasks_dict[
        "transfer-ctrl-fmrr-base-to-ss"
    ] = transfer_ctrl_fmrr_base_back_to_ss

    # Initializing parameters for reload_ctrl_fmrr_base_tasks
    for task_info in reload_ctrl_fmrr_base_tasks:
        log_task_name = task_info["task_id"].replace("-", "_").upper()
        task_info[
            "logs_job_identifier"
        ] = f"RAMBO_PIPELINE/RELOAD_CTRL_TABLES_BENCH/{log_task_name}"

        task_info["sql_folder"] = DAG_BASE_SQL_FOLDER + "reload_ctrl_tables_bench/"

        task_info["end_task"] = "ctrl-fmrr-base-complete"

        task_info["priority_weight_transfer"] = 15
        task_info["end_transfer_task"] = [
            update_ctrl_tables_record,
            update_ctrl_tables_record_error,
        ]
        task_info["task_group"] = "ctrl-tables"

    reload_ctrl_benchrevenue_task = [
        {
            "task_id": "reload-ctrl-benchrevenue",
            "table_name": "ctrl_benchrevenue",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": [
                "ctrl-fmrr-base-complete",
                "reload-ctrl-tables-inputs-complete",
            ],
            "query_file": "reload_ctrl_benchrevenue.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-ctrl-benchrevenue",
            "sql_server_table_name": "dbo.CTRL_BenchRevenue",
            "column_mapping_file": "mapping_ctrl_benchrevenue.json",
            "resources": k8s.V1ResourceRequirements(
                requests={"cpu": "2000m", "memory": "0.5Gi"}
            ),
        }
    ]
    ctrl_tables_bench_complete = DummyOperator(
        task_id="ctrl-tables-bench-complete", dag=dag
    )
    rambo_tasks_dict["ctrl-tables-bench-complete"] = ctrl_tables_bench_complete

    # Initializing parameters for ctrl_benchrevenue_task
    for task_info in reload_ctrl_benchrevenue_task:
        log_task_name = task_info["task_id"].replace("-", "_").upper()
        task_info[
            "logs_job_identifier"
        ] = f"RAMBO_PIPELINE/RELOAD_CTRL_TABLES_BENCH/{log_task_name}"

        task_info["sql_folder"] = DAG_BASE_SQL_FOLDER + "reload_ctrl_tables_bench/"

        task_info["end_task"] = "ctrl-tables-bench-complete"

        task_info["priority_weight_transfer"] = 15
        task_info["end_transfer_task"] = [
            update_ctrl_tables_record,
            update_ctrl_tables_record_error,
        ]
        task_info["task_group"] = "ctrl-tables"

############################################################
############## OUTLIER MODEL FEEDBACK ######################
############################################################

with TaskGroup(group_id="outlier-model-feedback", dag=dag) as outlier_model_group:
    rambo_tasks_dict["outlier-model-feedback"] = outlier_model_group
    outlier_model_feedback_tasks = [
        {
            "task_id": "benchmark-mwd-actuals",
            "table_name": "benchmark_mwd_actuals",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["fmrr-benchcf-views-complete"],
            "query_file": "benchmark_mwd_actuals.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-benchmark-mwd-actuals",
            "sql_server_table_name": "dbo.Benchmark_MWD_Actuals",
            "column_mapping_file": "mapping_benchmark_mwd_actuals.json",
            "resources": k8s.V1ResourceRequirements(
                requests={"cpu": "2000m", "memory": "1.5Gi"}
            ),
        },
        {
            "task_id": "benchmark-interpolated",
            "table_name": "benchmark_interpolated",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["fmrr-benchcf-views-complete"],
            "query_file": "benchmark_interpolated.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-benchmark-interpolated",
            "sql_server_table_name": "dbo.Benchmark_Interpolated",
            "column_mapping_file": "mapping_benchmark_interpolated.json",
            "resources": k8s.V1ResourceRequirements(
                requests={"cpu": "2000m", "memory": "1.5Gi"}
            ),
        },
        {
            "task_id": "benchmark-linearfit",
            "table_name": "benchmark_linearfit",
            "write_disposition": None,
            "task_dependencies": ["benchmark-interpolated"],
            "query_file": "benchmark_linearfit.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
                "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "TABLE_SUFFIX": TABLE_SUFFIX,
            },
        },
        # The following 3 tasks will update the daily, weekly and monthly
        # fields on benchmark_linearfit table, and will execute in sequence
        # because they all rewrite the table with the updated fields
        {
            "task_id": "reload-monthly-spot-core-offsets",
            "table_name": "benchmark_linearfit",
            "write_disposition": None,
            "task_dependencies": ["benchmark-linearfit"],
            "query_file": "reload_monthly_spot_core_offsets.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
                "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "TABLE_SUFFIX": TABLE_SUFFIX,
            },
        },
        {
            "task_id": "reload-weekly-spot-core-offsets",
            "table_name": "benchmark_linearfit",
            "write_disposition": None,
            "task_dependencies": ["reload-monthly-spot-core-offsets"],
            "query_file": "reload_weekly_spot_core_offsets.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
                "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "TABLE_SUFFIX": TABLE_SUFFIX,
            },
        },
        {
            "task_id": "reload-daily-spot-core-offsets",
            "table_name": "benchmark_linearfit",
            "write_disposition": None,
            "task_dependencies": ["reload-weekly-spot-core-offsets"],
            "query_file": "reload_daily_spot_core_offsets.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
                "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "TABLE_SUFFIX": TABLE_SUFFIX,
            },
        },
        {
            "task_id": "reload-hourly-spot-core-offsets",
            "table_name": "benchmark_linearfit",
            "write_disposition": None,
            "task_dependencies": ["reload-daily-spot-core-offsets"],
            "query_file": "reload_hourly_spot_core_offsets.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
                "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "TABLE_SUFFIX": TABLE_SUFFIX,
            },
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-benchmark-linearfit",
            "sql_server_table_name": "dbo.Benchmark_LinearFit",
            "column_mapping_file": "mapping_benchmark_linearfit.json",
            "resources": k8s.V1ResourceRequirements(
                requests={"cpu": "1000m", "memory": "0.8Gi"}
            ),
        },
    ]
    outlier_model_feedback_complete = DummyOperator(
        task_id="outlier-model-feedback-complete", dag=dag
    )
    rambo_tasks_dict[
        "outlier-model-feedback-complete"
    ] = outlier_model_feedback_complete

    # Initializing parameters for outlier_model_feedback_tasks
    for task_info in outlier_model_feedback_tasks:
        log_task_name = task_info["task_id"].replace("-", "_").upper()
        task_info[
            "logs_job_identifier"
        ] = f"RAMBO_PIPELINE/OUTLIER_MODEL_FEEDBACK/{log_task_name}"

        task_info["sql_folder"] = DAG_BASE_SQL_FOLDER + "bench_preloads/"

        task_info["end_task"] = "outlier-model-feedback-complete"

        task_info["task_group"] = "outlier-model-feedback"


# ---------------------------------------

# CREATE THE TASKS AND DEPENDENCIES

fmrr_bench_tasks = (
    reload_ctrl_tables_inputs_tasks
    + reload_ctrl_fmrr_base_tasks
    + reload_ctrl_benchrevenue_task
    + outlier_model_feedback_tasks
    + fmrr_benchcf_views
)

# Loop first to create the tasks
for task_info in fmrr_bench_tasks:
    # Default view and default destination tables
    default_view_to_replace = (
        f"{DATA_PROJECT_NAME}.{DIM_FM_RR_DESTINATION_DATASET}.{task_info['table_name']}"
    )
    default_destination_table = f"{DATA_PROJECT_NAME}.{DIM_FM_RR_SOURCE_DATASET}.{task_info['table_name']}__{TABLE_SUFFIX}"

    # Open the query file, and replace the query parameters if needed
    sql_file = open(task_info["sql_folder"] + task_info["query_file"])
    sql_as_string = sql_file.read()
    if "query_params" in task_info.keys():
        sql_as_string = sql_as_string.format(**task_info["query_params"])

    if "column_mapping_file" in task_info.keys():
        mapping_as_string = open(
            task_info["sql_folder"] + task_info["column_mapping_file"]
        )
        task_info["column_mapping"] = mapping_as_string.read()

    # Base Operator 'Execute Bigquery' will do all the work of executing the queries and creating the FMRR Bench Carry Forward tables/views
    query_task = GKEStartPodOperator(
        task_id=task_info["task_id"],
        name=task_info["task_id"],
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        task_group=rambo_tasks_dict[task_info["task_group"]]
        if "task_group" in task_info.keys()
        else None,
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": task_info["bigquery_project"]
            if "bigquery_project" in task_info.keys()
            else DATA_PROJECT_NAME,
            "QUERY": sql_as_string,
            "DESTINATION_TABLE": task_info["destination_table"]
            if "destination_table" in task_info.keys()
            else default_destination_table,
            "VIEW_TO_REPLACE": task_info["view_to_replace"]
            if "view_to_replace" in task_info.keys()
            else default_view_to_replace,
            "XCOM_PUSH": "False",
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": task_info["logs_job_identifier"],
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
            "WRITE_DISPOSITION": task_info["write_disposition"]
            if "write_disposition" in task_info.keys()
            else None,
            "RECREATE_TABLE": task_info["recreate_table"]
            if "recreate_table" in task_info.keys()
            else "True",
            "TYPE_OF_PARTITIONING": task_info["type_of_partitioning"]
            if "type_of_partitioning" in task_info.keys()
            else None,
            "CLUSTERING_FIELDS": task_info["clustering_fields"]
            if "clustering_fields" in task_info.keys()
            else None,
            "PARTITION_COLUMN": task_info["partition_column"]
            if "partition_column" in task_info.keys()
            else None,
            "RANGE_START": task_info["range_start"]
            if "range_start" in task_info.keys()
            else None,
            "RANGE_END": task_info["range_end"]
            if "range_end" in task_info.keys()
            else None,
            "RANGE_INTERVAL": task_info["range_interval"]
            if "range_interval" in task_info.keys()
            else None,
        },
        dag=dag,
    )

    # Salving the task object to create the dependencies at the next step
    rambo_tasks_dict[task_info["task_id"]] = query_task

    # With task groups the full_task_id is task_group_name.task_id (It changes the xcom_Pull)
    # If the task isn't in a task group, its just the regular task_id
    full_task_id = (
        f'{task_info["task_group"]}.{task_info["task_id"]}'
        if "task_group" in task_info.keys()
        else task_info["task_id"]
    )

    # Logic for sending the tables back to SQL Server using 'bq-to-ss' operator
    if task_info["send_back_to_ss"] if "send_back_to_ss" in task_info.keys() else False:
        transfer_table_back_to_ss = GKEStartPodOperator(
            task_id=task_info["back_to_ss_task_id"],
            name=task_info["back_to_ss_task_id"],
            image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
            execution_timeout=timedelta(hours=3),
            task_group=rambo_tasks_dict[task_info["task_group"]]
            if "task_group" in task_info.keys()
            else None,
            # The priority for all FMRR Bench is 11, but for CTRL Tables it is higher
            priority_weight=task_info["priority_weight_transfer"]
            if "priority_weight_transfer" in task_info.keys()
            else 11,
            secrets=[secret_db_username, secret_db_password],
            arguments=["python3", "main.py", "load-from-bq-to-ss"],
            resources=task_info["resources"]
            if "resources" in task_info.keys()
            else k8s.V1ResourceRequirements(requests={"cpu": "2000m", "memory": "2Gi"}),
            pool=ss_import_pool_name,
            env_vars={
                "GCP_PROJECT": task_info["bigquery_project"]
                if "bigquery_project" in task_info.keys()
                else DATA_PROJECT_NAME,
                "BQ_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "BQ_TABLE": task_info["destination_table"].split(".")[-1]
                + f"__{TABLE_SUFFIX}"
                if "destination_table" in task_info.keys()
                else f"{task_info['table_name']}__{TABLE_SUFFIX}",
                "DB_SERVER": GETL01_SQL_SERVER,
                "DB_NAME": "ras_DataMart_Analytics_Cloud",
                "TABLE_NAME": task_info["sql_server_table_name"],
                "WRITE_MODE": "overwrite",
                "GCS_BUCKET_NAME": BUCKET_NAME,
                "CHUNKSIZE": task_info["chunksize"]
                if "chunksize" in task_info.keys()
                else "100000",
                "PARALLEL_PROCESSES": task_info["parallel_processes"]
                if "parallel_processes" in task_info.keys()
                else "2",
                "COLUMN_MAPPING": task_info["column_mapping"],
                "PARSE_DTYPES": task_info["parse_dtypes"]
                if "parse_dtypes" in task_info.keys()
                else "False",
            },
            dag=dag,
        )
        # The finished_fmrr_bench_growth dummy task won't depend on the Back to SS tasks (as the BQ processes can go on)
        query_task >> transfer_table_back_to_ss

        # I'll only transfer when receiving the transfer_to_cloud_db = True
        run_transfers_to_cloud_db >> transfer_table_back_to_ss

        if "end_transfer_task" in task_info.keys():
            transfer_table_back_to_ss >> task_info["end_transfer_task"]
        else:
            transfer_table_back_to_ss >> transfer_fmrr_bench_complete

# Looping again to set the dependencies
for task_info in fmrr_bench_tasks:
    # Using the task object to set the dependencies
    if "task_dependencies" in task_info.keys():
        for dependency in task_info["task_dependencies"]:
            rambo_tasks_dict[dependency] >> rambo_tasks_dict[task_info["task_id"]]

    # All FMRR Bench Carry Forward tasks without dependencies (end leafs of Customer Segmentation)
    # must run before the flag 'finished_fmrr_bench_growth' run
    is_end_leaf = True
    for another_task in fmrr_bench_tasks:
        if "task_dependencies" in another_task.keys():
            for dependency in another_task["task_dependencies"]:
                if dependency == task_info["task_id"]:
                    is_end_leaf = False
    if is_end_leaf:
        (
            rambo_tasks_dict[task_info["task_id"]]
            >> rambo_tasks_dict[task_info["end_task"]]
        )

    # ---------------------------------------


###########################################
########### PUBLISH SEQUENCE ##############
###########################################

with TaskGroup(group_id="publish-sequence", dag=dag) as publish_sequence_group:
    insert_publish_record = GKEStartPodOperator(
        task_id="insert-publish-record",
        name="insert-publish-record",
        secrets=[secret_db_username, secret_db_password],
        do_xcom_push=True,
        image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
        arguments=["python3", "main.py", "update-control-table"],
        execution_timeout=timedelta(minutes=5),
        retry_delay=timedelta(seconds=10),
        env_vars={
            "TABLE_NAME": "benchmark_etl_run",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Computation",
            "PIPELINE": "publish",
            "STATUS_VALUE": "RUNNING",
            "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
            "PARENT": "{{ dag_run.conf['parent_batch'] }}",
        },
        dag=dag,
    )
    update_publish_record = GKEStartPodOperator(
        task_id="update-publish-record",
        name="update-publish-record",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
        do_xcom_push=True,
        arguments=["python3", "main.py", "update-control-table"],
        execution_timeout=timedelta(minutes=5),
        retry_delay=timedelta(seconds=10),
        env_vars={
            "TABLE_NAME": "benchmark_etl_run",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Computation",
            "PIPELINE": "publish",
            "STATUS_VALUE": "DONE",
            "PK": '{{ task_instance.xcom_pull(task_ids="publish-sequence.insert-publish-record", key="return_value")["pk"] }}',
            "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
            "PARENT": "{{ dag_run.conf['parent_batch'] }}",
        },
        dag=dag,
    )
    update_publish_record_error = GKEStartPodOperator(
        task_id="update-publish-record-error",
        name="update-publish-record-error",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
        do_xcom_push=True,
        arguments=["python3", "main.py", "update-control-table"],
        execution_timeout=timedelta(minutes=5),
        retry_delay=timedelta(seconds=10),
        env_vars={
            "TABLE_NAME": "benchmark_etl_run",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Computation",
            "PIPELINE": "publish",
            "STATUS_VALUE": "FAILED",
            "PK": '{{ task_instance.xcom_pull(task_ids="publish-sequence.insert-publish-record", key="return_value")["pk"] }}',
            "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
            "PARENT": "{{ dag_run.conf['parent_batch'] }}",
        },
        trigger_rule="one_failed",
        dag=dag,
    )

    update_publish_record_when_clear = GKEStartPodOperator(
        task_id="update-publish-record-when-clear",
        name="update-publish-record-when-clear",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
        do_xcom_push=True,
        arguments=["python3", "main.py", "update-control-table"],
        execution_timeout=timedelta(minutes=5),
        retry_delay=timedelta(seconds=10),
        trigger_rule="one_success",
        env_vars={
            "TABLE_NAME": "benchmark_etl_run",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Computation",
            "PIPELINE": "publish",
            "STATUS_VALUE": "RUNNING",
            "PK": '{{ task_instance.xcom_pull(task_ids="publish-sequence.insert-publish-record", key="return_value")["pk"] }}',
            "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
            "PARENT": "{{ dag_run.conf['parent_batch'] }}",
        },
        dag=dag,
    )

    create_rdo_publish_dataset = GKEStartPodOperator(
        task_id="create-rdo-publish-dataset",
        name="create-rdo-publish-dataset",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": f"""
            CREATE SCHEMA IF NOT EXISTS {RDO_PUBLISH_DATASET_NAME}
            """,
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    create_rdo_publish_stage_dataset = GKEStartPodOperator(
        task_id="create-rdo-publish-stage-dataset",
        name="create-rdo-publish-stage-dataset",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": f"""
            CREATE SCHEMA IF NOT EXISTS {RDO_PUBLISH_STAGE_DATASET_NAME}
            OPTIONS(
            default_table_expiration_days=45
            )
            """,
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    create_rdo_publish_version_dataset = GKEStartPodOperator(
        task_id="create-rdo-publish-version-dataset",
        name="create-rdo-publish-version-dataset",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": f"""
            CREATE SCHEMA IF NOT EXISTS {RDO_PUBLISH_VERSION_DATASET_NAME}
            OPTIONS(
            default_table_expiration_days=45
            )
            """,
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    start_rambo_enhancement >> [
        create_rdo_publish_dataset,
        create_rdo_publish_version_dataset,
        create_rdo_publish_stage_dataset,
    ]

    start_rdo_publish = DummyOperator(task_id="start_rdo_publish", dag=dag)

    finish_copying_tables_to_rdo = DummyOperator(
        task_id="finished-copying-tables-to-rdo", dag=dag
    )
    # Publish Sequence

    copy_dim_fmrr_table_to_rdo_publish = GKEStartPodOperator(
        task_id="copy-dim-fmrr-to-rdo-publish",
        name="copy-dim-fmrr-to-rdo-publish",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.dim_fm_rr__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.dim_fm_rr__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_rouse_category_to_rdo_publish = GKEStartPodOperator(
        task_id="copy-dim-rouse-category-to-rdo-publish",
        name="copy-dim-rouse-category-to-rdo-publish",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_rouse_category__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_rouse_category__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_country_to_rdo_publish = GKEStartPodOperator(
        task_id="copy-dim-country-to-rdo-publish",
        name="copy-dim-country-to-rdo-publish",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_country__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_country__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_rouse_market_to_rdo_publish = GKEStartPodOperator(
        task_id="copy-dim-rouse-market-to-rdo-publish",
        name="copy-dim-rouse-market-to-rdo-publish",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_rouse_market__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_rouse_market__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_rouse_region_to_rdo_publish = GKEStartPodOperator(
        task_id="copy-dim-rouse-region-to-rdo-publish",
        name="copy-dim-rouse-region-to-rdo-publish",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_rouse_region__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_rouse_region__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_rouse_district_to_rdo_publish = GKEStartPodOperator(
        task_id="copy-dim-rouse-district-to-rdo-publish",
        name="copy-dim-rouse-district-to-rdo-publish",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_rouse_district__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_rouse_district__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    update_dim_fmrr_table_rouse_category = GKEStartPodOperator(
        task_id="update-dim-fmrr-rouse-category",
        name="update-dim-fmrr-rouse-category",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(PUBLISH_SEQUENCE_SQL_FOLDER + "update_dim_fmrr.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_TABLES/PUBLISH_SEQUENCE",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    update_dim_rouse_category_in_rdo = GKEStartPodOperator(
        task_id="update-dim-rouse-category-in-rdo",
        name="update-dim-rouse-category-in-rdo",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(PUBLISH_SEQUENCE_SQL_FOLDER + "update_dim_rouse_category.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_TABLES/PUBLISH_SEQUENCE",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    copy_dim_client_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-client-into-rdo-dataset",
        name="copy-dim-client-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_client_configurations_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-client-configurations-into-rdo-dataset",
        name="copy-client-configurations-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_client_configurations__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_client_configurations__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_client_region_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-client-region-into-rdo-dataset",
        name="copy-dim-client-region-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_region__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_region__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_client_district_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-client-district-into-rdo-dataset",
        name="copy-dim-client-district-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_district__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_district__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_client_branch_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-client-branch-into-rdo-dataset",
        name="copy-dim-client-branch-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_branch__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_branch__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_client_product_type_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-client-product-type-into-rdo-dataset",
        name="copy-dim-client-product-type-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{AGGS_DESTINATION_DATASET}.ras_datamart_analytics_reporting_dbo_dim_client_product_type",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_product_type__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_rouse_product_type_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-rouse-product-type-into-rdo-dataset",
        name="copy-dim-rouse-product-type-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_rouse_product_type__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_rouse_product_type__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_cat_district_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-cat-district-into-rdo-dataset",
        name="copy-dim-cat-district-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_cat_district__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_cat_district__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_cat_territory_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-cat-territory-into-rdo-dataset",
        name="copy-dim-cat-territory-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_cat_territory__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_cat_territory__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_cat_subterritory_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-cat-subterritory-into-rdo-dataset",
        name="copy-dim-cat-subterritory-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_cat_subterritory__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_cat_subterritory__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_benchmark_rev_dist_pt_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-benchmark-rev-dist-pt-into-rdo-dataset",
        name="copy-benchmark-rev-dist-pt-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.benchmark_revdistpt_{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.rdo_publish_dbo_benchmark_rev_dist_pt__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_komatsu_region_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-komatsu-region-into-rdo-dataset",
        name="copy-dim-komatsu-region-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_komatsu_region__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_komatsu_region__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_komatsu_territory_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-komatsu-territory-into-rdo-dataset",
        name="copy-dim-komatsu-territory-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_komatsu_territory__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_komatsu_territory__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_komatsu_subterritory_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-komatsu-subterritory-into-rdo-dataset",
        name="copy-dim-komatsu-subterritory-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_komatsu_subterritory__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_komatsu_subterritory__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_client_category_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-client-category-into-rdo-dataset",
        name="copy-dim-client-category-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_category__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_category__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_vw_cat_corp_report_subset_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-vw-cat-corp-report-subset-into-rdo-dataset",
        name="copy-vw-cat-corp-report-subset-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_vw_cat_corp_report_subset__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_vw_cat_corp_report_subset__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_client_vertical_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-client-vertical-into-rdo-dataset",
        name="copy-dim-client-vertical-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_vertical__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_vertical__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_fmrr_rentalrates_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-fmrr-rentalrates-into-rdo-dataset",
        name="copy-fmrr-rentalrates-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.fmrr_rentalrates__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.fmrr_rentalrates__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_client_equipment_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-client-equipment-into-rdo-dataset",
        name="copy-dim-client-equipment-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_equipment__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_equipment__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_client_customer_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-client-customer-into-rdo-dataset",
        name="copy-dim-client-customer-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_customer__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_customer__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_client_customer_mtd_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-client-customer-mtd-into-rdo-dataset",
        name="copy-dim-client-customer-mtd-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_customer_mtd__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_customer_mtd__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_month_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-month-into-rdo-dataset",
        name="copy-dim-month-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_month__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_month__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_outlier_transactions_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-outlier-transactions-into-rdo-dataset",
        name="copy-outlier-transactions-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{AGGS_DESTINATION_DATASET}.ras_datamart_analytics_reporting_dbo_outlier_transactions",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_outlier_transactions__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_benchmark_client_cat_class_blacklist_rdo_dataset = GKEStartPodOperator(
        task_id="copy-benchmark-client-cat-class-blacklist-into-rdo-dataset",
        name="copy-benchmark-client-cat-class-blacklist-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_benchmark_client_cat_class_blacklist__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_benchmark_client_cat_class_blacklist__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_benchmark_client_division_whitelist_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-benchmark-client-division-whitelist-into-rdo-dataset",
        name="copy-benchmark-client-division-whitelist-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_benchmark_client_division_whitelist__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_benchmark_client_division_whitelist__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_week_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-week-into-rdo-dataset",
        name="copy-dim-week-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{AGGS_DESTINATION_DATASET}.ras_datamart_analytics_reporting_dbo_dim_week",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_week__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_rdo_client_attribute_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-rdo-client-attribute-into-rdo-dataset",
        name="copy-rdo-client-attribute-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_rdo_client_attribute__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_rdo_client_attribute__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_reporting_months_filter_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-reporting-months-filter-into-rdo-dataset",
        name="copy-reporting-months-filter-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_reporting_months_filter__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_reporting_months_filter__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_data_feed_rental_rates_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-data-feed-rental-rates-into-rdo-dataset",
        name="copy-data-feed-rental-rates-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_data_feed_rental_rates",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_data_feed_rental_rates__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_bench_summary_order_schema_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-bench-summary-order-schema-into-rdo-dataset",
        name="copy-bench-summary-order-schema-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_bench_summary_order_schema__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_bench_summary_order_schema__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_cat_pt_family_mapping_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-cat-pt-family-mapping-into-rdo-dataset",
        name="copy-cat-pt-family-mapping-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_supplemental_dbo_cat_pt_family_mapping__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_supplemental_dbo_cat_pt_family_mapping__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_xyz_rental_rates_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-xyz-rental-rates-into-rdo-dataset",
        name="copy-xyz-rental-rates-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            # I must use the view here, because the view is pointing to the current version of the XYZ tables (generated on the last run),
            # and it will only point to the current version of the tables at the end of Rambo, after the XYZ tables are recalculted.
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_DESTINATION_DATASET}.ras_datamart_analytics_reporting_dbo_xyz_rentalrates",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_xyz_rentalrates__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_xyz_fleet_metrics_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-xyz-fleet-metrics-into-rdo-dataset",
        name="copy-xyz-fleet-metrics-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            # I must use the view here, because the view is pointing to the current version of the XYZ tables (generated on the last run),
            # and it will only point to the current version of the tables at the end of Rambo, after the XYZ tables are recalculted.
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_DESTINATION_DATASET}.ras_datamart_analytics_reporting_dbo_xyz_fleetmetrics",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_xyz_fleetmetrics__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_data_feed_branch_info_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-data-feed-branch-info-into-rdo-dataset",
        name="copy-data-feed-branch-info-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{AGGS_DESTINATION_DATASET}.ras_datamart_analytics_reporting_dbo_data_feed_branch_info",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_data_feed_branch_info__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_fmrr_fmaggs_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-fmrr-fmaggs-into-rdo-dataset",
        name="copy-fmrr-fmaggs-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.fmrr_fmaggs__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.fmrr_fmaggs__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_fmrr_rraggs_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-fmrr-rraggs-into-rdo-dataset",
        name="copy-fmrr-rraggs-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.fmrr_rraggs__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.fmrr_rraggs__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_fmrr_fleetmetrics_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-fmrr-fleetmetrics-into-rdo-dataset",
        name="copy-fmrr-fleetmetrics-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.fmrr_fleetmetrics__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.fmrr_fleetmetrics__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_time_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-time-into-rdo-dataset",
        name="copy-dim-time-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_time__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_time__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_dim_rouse_equipment_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-dim-rouse-equipment-into-rdo-dataset",
        name="copy-dim-rouse-equipment-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{AGGS_DESTINATION_DATASET}.ras_datamart_analytics_reporting_dbo_dim_rouse_equipment",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_rouse_equipment__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_rdo_location_name_swap_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-rdo-location-name-swap-into-rdo-dataset",
        name="copy-rdo-location-name-swap-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.rdo_publish_dbo_rdo_location_name_swap__{TABLE_SUFFIX}",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.rdo_publish_dbo_rdo_location_name_swap__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    copy_drpt_groups_into_rdo_dataset = GKEStartPodOperator(
        task_id="copy-drpt-groups-into-rdo-dataset",
        name="copy-drpt-groups-into-rdo-dataset",
        image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
        arguments=["python3", "main.py", "copy-bq-tables"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_DESTINATION_DATASET}.drpt_groups",
            "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.drpt_groups__{TABLE_SUFFIX}",
        },
        dag=dag,
    )

    tables_to_copy = [
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_client",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_current_list_rates",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_dim_outlier_reason",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_weekly_fleet_detail",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_client_nvar_char_string_dictionary",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_dim_zip_code",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_dim_rouse_sub_category",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_dim_rouse_kingdom",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_dim_rouse_equipment_mtd",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_dim_county",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_data_feed_fleet_metrics_wk",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_dim_customer_size",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_dim_rouse_sub_product_type",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "fmrr_benchcf_region",
            "bigquery_dataset": DIM_FM_RR_SOURCE_DATASET,
        },
        {
            "table_name": "fmrr_benchcf_market",
            "bigquery_dataset": DIM_FM_RR_SOURCE_DATASET,
        },
        {
            "table_name": "fmrr_benchcf_komterr",
            "bigquery_dataset": DIM_FM_RR_SOURCE_DATASET,
        },
        {
            "table_name": "fmrr_benchcf_komsubterr",
            "bigquery_dataset": DIM_FM_RR_SOURCE_DATASET,
        },
        {
            "table_name": "fmrr_benchcf_komreg",
            "bigquery_dataset": DIM_FM_RR_SOURCE_DATASET,
        },
        {
            "table_name": "fmrr_benchcf_district",
            "bigquery_dataset": DIM_FM_RR_SOURCE_DATASET,
        },
        {
            "table_name": "fmrr_benchcf_company",
            "bigquery_dataset": DIM_FM_RR_SOURCE_DATASET,
        },
        {
            "table_name": "fmrr_benchcf_catterr",
            "bigquery_dataset": DIM_FM_RR_SOURCE_DATASET,
        },
        {
            "table_name": "fmrr_benchcf_catsubterr",
            "bigquery_dataset": DIM_FM_RR_SOURCE_DATASET,
        },
        {
            "table_name": "fmrr_benchcf_catdist",
            "bigquery_dataset": DIM_FM_RR_SOURCE_DATASET,
        },
        {
            "table_name": "benchmark_growth",
            "bigquery_dataset": DIM_FM_RR_SOURCE_DATASET,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_seasonal_rate_factor",
            "bigquery_dataset": DIM_FM_RR_SOURCE_DATASET,
        },
        {
            "table_name": "ras_datamart_supplemental_dbo_alert_client_product_type_subset",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_alert_configuration",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_alert_threshold_diff_by_category",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_alert_threshold_same_for_all_category",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_dim_cycle_bill_range",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_rdo_geography_level",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_rdo_client_geography_level",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_rdo_benchmark",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_alert_type",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_dim_rate_source",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_benchmark_acquisitions",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
        {
            "table_name": "ras_datamart_analytics_reporting_dbo_client_largest_market",
            "bigquery_dataset": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
        },
    ]
    for table_to_copy in tables_to_copy:
        copy_task_id = "copy-" + table_to_copy["table_name"].replace("_", "-")
        copy_task = GKEStartPodOperator(
            task_id=copy_task_id,
            name=copy_task_id,
            image=get_full_image_name("rambo-copy-bq-tables-and-views", GCR_REGISTRY),
            arguments=["python3", "main.py", "copy-bq-tables"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "SOURCE_TABLE_NAME": f"{DATA_PROJECT_NAME}.{table_to_copy['bigquery_dataset']}.{table_to_copy['table_name']}__{TABLE_SUFFIX}",
                "DESTINATION_TABLE_NAME": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.{table_to_copy['table_name']}__{TABLE_SUFFIX}",
            },
            dag=dag,
        )
        start_rdo_publish >> copy_task >> finish_copying_tables_to_rdo

    finish_publish_sequence_package = DummyOperator(
        task_id="finished-publish-sequence-package", dag=dag
    )

    insert_dims_for_demo_clients = GKEStartPodOperator(
        task_id="insert-dims-for-demo-clients",
        name="insert-dims-for-demo-clients",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                PUBLISH_SEQUENCE_SQL_FOLDER + "insert_dims_for_demo_clients.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_TABLES/PUBLISH_SEQUENCE",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    delete_dim_fmrr_without_revenue = GKEStartPodOperator(
        task_id="delete-dim-fmrr-without-revenue",
        name="delete-dim-fmrr-without-revenue",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                PUBLISH_SEQUENCE_SQL_FOLDER + "delete_dim_fmrr_without_revenue.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_TABLES/PUBLISH_SEQUENCE",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    stuff_rouse_geo_with_cat_and_komatsu = GKEStartPodOperator(
        task_id="stuff-rouse-geo-with-cat-and-komatsu",
        name="stuff-rouse-geo-with-cat-and-komatsu",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                PUBLISH_SEQUENCE_SQL_FOLDER + "stuff_rouse_geo_with_cat_and_komatsu.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_TABLES/PUBLISH_SEQUENCE",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    # Publish tables dependencies
    [fmrr_benchcf_views_complete] >> start_rdo_publish

    start_rdo_publish >> insert_publish_record
    (
        start_rdo_publish
        >> [
            copy_dim_client_into_rdo_dataset,
            copy_client_configurations_into_rdo_dataset,
            copy_dim_client_category_into_rdo_dataset,
            copy_dim_rouse_category_to_rdo_publish,
            copy_dim_fmrr_table_to_rdo_publish,
            copy_dim_client_region_into_rdo_dataset,
            copy_dim_client_district_into_rdo_dataset,
            copy_dim_client_branch_into_rdo_dataset,
            copy_dim_client_product_type_into_rdo_dataset,
            copy_dim_rouse_product_type_into_rdo_dataset,
            copy_dim_cat_territory_into_rdo_dataset,
            copy_dim_rouse_market_to_rdo_publish,
            copy_dim_cat_subterritory_into_rdo_dataset,
            copy_dim_rouse_region_to_rdo_publish,
            copy_dim_komatsu_territory_into_rdo_dataset,
            copy_dim_komatsu_region_into_rdo_dataset,
            copy_dim_komatsu_subterritory_into_rdo_dataset,
            copy_dim_rouse_district_to_rdo_publish,
            copy_dim_cat_district_into_rdo_dataset,
            copy_fmrr_rentalrates_into_rdo_dataset,
            copy_dim_client_equipment_into_rdo_dataset,
            copy_dim_client_customer_into_rdo_dataset,
            copy_dim_client_customer_mtd_into_rdo_dataset,
            copy_dim_month_into_rdo_dataset,
            copy_rdo_client_attribute_into_rdo_dataset,
            copy_outlier_transactions_into_rdo_dataset,
            copy_vw_cat_corp_report_subset_into_rdo_dataset,
            copy_dim_client_vertical_into_rdo_dataset,
            copy_benchmark_client_cat_class_blacklist_rdo_dataset,
            copy_xyz_rental_rates_into_rdo_dataset,
            copy_xyz_fleet_metrics_into_rdo_dataset,
            copy_cat_pt_family_mapping_into_rdo_dataset,
            copy_bench_summary_order_schema_into_rdo_dataset,
            copy_reporting_months_filter_into_rdo_dataset,
            copy_data_feed_rental_rates_into_rdo_dataset,
            copy_data_feed_branch_info_into_rdo_dataset,
            copy_fmrr_fmaggs_into_rdo_dataset,
            copy_dim_week_into_rdo_dataset,
            copy_fmrr_fleetmetrics_into_rdo_dataset,
            copy_dim_time_into_rdo_dataset,
            copy_dim_country_to_rdo_publish,
            copy_benchmark_rev_dist_pt_into_rdo_dataset,
            copy_fmrr_rraggs_into_rdo_dataset,
            copy_dim_rouse_equipment_into_rdo_dataset,
            copy_rdo_location_name_swap_into_rdo_dataset,
            copy_drpt_groups_into_rdo_dataset,
            copy_benchmark_client_division_whitelist_into_rdo_dataset,
        ]
        >> finish_copying_tables_to_rdo
    )

    (
        copy_dim_fmrr_table_to_rdo_publish
        >> update_dim_fmrr_table_rouse_category
        >> insert_dims_for_demo_clients
    )
    (
        copy_dim_rouse_category_to_rdo_publish
        >> update_dim_rouse_category_in_rdo
        >> insert_dims_for_demo_clients
    )
    (
        finish_copying_tables_to_rdo
        >> insert_dims_for_demo_clients
        >> delete_dim_fmrr_without_revenue
        >> stuff_rouse_geo_with_cat_and_komatsu
    )
    stuff_rouse_geo_with_cat_and_komatsu >> finish_publish_sequence_package

    finish_publish_sequence_package >> [
        update_publish_record,
        update_publish_record_error,
    ]
    update_publish_record >> ss_done
    [insert_publish_record, update_publish_record] >> update_publish_record_when_clear
    update_publish_record_when_clear >> update_rambo_record_when_clear

###############################################
########### POPULATE RDO TABLES  ##############
###############################################


def short_circuit_noco_fn(**kwargs):
    noco_run = kwargs["noco_run"]
    if noco_run == "True":
        return True
    else:
        return False


with TaskGroup(group_id="rdo-tables", dag=dag) as rdo_tables_group:
    rdo_tables_transfers_complete = DummyOperator(
        task_id="rdo-tables-transfers-complete", dag=dag
    )

    reload_rdo_support_tables = GKEStartPodOperator(
        task_id="reload-rdo-support-tables",
        name="reload-rdo-support-tables",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(RDO_TABLES_SQL_FOLDER + "reload_rdo_support_tables.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{REFRESH_DATASET_NAME}", REFRESH_DATASET_NAME),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    short_circuit_noco = ShortCircuitOperator(
        task_id="short-circuit-noco",
        python_callable=short_circuit_noco_fn,
        dag=dag,
        op_kwargs={
            "noco_run": "{{True if 'noco_run' in dag_run.conf.keys() else False}}"
        },
    )

    update_is_trial_client_tx_attribute = GKEStartPodOperator(
        task_id="update-is-trial-client-tx-attribute",
        name="update-is-trial-client-tx-attribute",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": f"""
                update {RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_rdo_client_tx_attribute__{TABLE_SUFFIX}
                set is_trial = 0
                where true
            """,
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    (
        reload_rdo_support_tables
        >> short_circuit_noco
        >> update_is_trial_client_tx_attribute
    )

    populate_rate_client_schema = GKEStartPodOperator(
        task_id="populate-rate-client-schema",
        name="populate-rate-client-schema",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(RDO_TABLES_SQL_FOLDER + "populate_rate_client_schema.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{RDO_PUBLISH_DATASET_NAME}", RDO_PUBLISH_DATASET_NAME)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )
    rambo_tasks_dict["populate-rate-client-schema"] = populate_rate_client_schema

    populate_rdo_sales_rep_branch = GKEStartPodOperator(
        task_id="populate-rdo-sales-rep-branch",
        name="populate-rdo-sales-rep-branch",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(RDO_TABLES_SQL_FOLDER + "populate_rdo_sales_rep_branch.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    populate_rev_dist_pt_client_schema = GKEStartPodOperator(
        task_id="populate-rev-dist-client-schema",
        name="populate-rev-dist-client-schema",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                RDO_TABLES_SQL_FOLDER + "populate_rev_dist_pt_client_schema.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{RDO_PUBLISH_DATASET_NAME}", RDO_PUBLISH_DATASET_NAME)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    populate_fleet_client_schema = GKEStartPodOperator(
        task_id="populate-fleet-client-schema",
        name="populate-fleet-client-schema",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(RDO_TABLES_SQL_FOLDER + "populate_fleet_client_schema.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )
    rambo_tasks_dict["populate-fleet-client-schema"] = populate_fleet_client_schema

    populate_fleet_client_schema_wk_xyz = GKEStartPodOperator(
        task_id="populate-fleet-client-schema-wk-xyz",
        name="populate-fleet-client-schema-wk-xyz",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                RDO_TABLES_SQL_FOLDER + "populate_fleet_client_schema_wk_xyz.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    populate_fleet_client_schema_wk = GKEStartPodOperator(
        task_id="populate-fleet-client-schema-wk",
        name="populate-fleet-client-schema-wk",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(RDO_TABLES_SQL_FOLDER + "populate_fleet_client_schema_wk.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace(
                "{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}",
                MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
            )
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    populate_fleet_client_schema_eqid = GKEStartPodOperator(
        task_id="populate-fleet-client-schema-eqid",
        name="populate-fleet-client-schema-eqid",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                RDO_TABLES_SQL_FOLDER + "populate_fleet_client_schema_eqid.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    create_rdo_bench_rev_dist_pt = GKEStartPodOperator(
        task_id="create-rdo-bench-rev-dist-pt",
        name="create-rdo-bench-rev-dist-pt",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(RDO_TABLES_SQL_FOLDER + "create_rdo_bench_rev_dist_pt.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    populate_rev_dist_pt_client_bench_data = GKEStartPodOperator(
        task_id="populate-rev-dist-pt-client-bench-data",
        name="populate-rev-dist-pt-client-bench-data",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                RDO_TABLES_SQL_FOLDER + "populate_rev_dist_pt_client_bench_data.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{RDO_PUBLISH_DATASET_NAME}", RDO_PUBLISH_DATASET_NAME)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    populate_rate_change_client_schema = GKEStartPodOperator(
        task_id="populate-rate-change-client-schema",
        name="populate-rate-change-client-schema",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                RDO_TABLES_SQL_FOLDER + "populate_rate_change_client_schema.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{RDO_PUBLISH_DATASET_NAME}", RDO_PUBLISH_DATASET_NAME)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    populate_preload_rate_change_bench_data = GKEStartPodOperator(
        task_id="populate-preload-rate-change-bench-data",
        name="populate-preload-rate-change-bench-data",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                RDO_TABLES_SQL_FOLDER + "populate_preload_rate_change_bench_data.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{RDO_PUBLISH_DATASET_NAME}", RDO_PUBLISH_DATASET_NAME)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    populate_rate_change_bench_data_xuri = GKEStartPodOperator(
        task_id="populate-rate-change-bench-data-xuri",
        name="populate-rate-change-bench-data-xuri",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                RDO_TABLES_SQL_FOLDER + "populate_rate_change_bench_data_xuri.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{RDO_PUBLISH_DATASET_NAME}", RDO_PUBLISH_DATASET_NAME)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    add_rate_change_bench_data_spot = GKEStartPodOperator(
        task_id="add-rate-change-bench-data-spot",
        name="add-rate-change-bench-data-spot",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                RDO_TABLES_SQL_FOLDER + "populate_rate_change_bench_data_spot.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    add_rate_change_bench_data_core = GKEStartPodOperator(
        task_id="add-rate-change-bench-data-core",
        name="add-rate-change-bench-data-core",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                RDO_TABLES_SQL_FOLDER + "populate_rate_change_bench_data_core.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    carry_forward_rate_change_bench_data = GKEStartPodOperator(
        task_id="carry-forward-rate-change-bench-data",
        name="carry-forward-rate-change-bench-data",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                RDO_TABLES_SQL_FOLDER + "carry_forward_rate_change_bench_data.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    populate_rate_change_client_bench_data = GKEStartPodOperator(
        task_id="populate-rate-change-client-bench-data",
        name="populate-rate-change-client-bench-data",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                RDO_TABLES_SQL_FOLDER + "populate_rate_change_client_bench_data.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{RDO_PUBLISH_DATASET_NAME}", RDO_PUBLISH_DATASET_NAME)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    populate_ctrl_rate_change_category = GKEStartPodOperator(
        task_id="populate-ctrl-rate-change-category",
        name="populate-ctrl-rate-change-category",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                RDO_TABLES_SQL_FOLDER + "populate_ctrl_rate_change_category.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{RDO_PUBLISH_DATASET_NAME}", RDO_PUBLISH_DATASET_NAME)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    create_fn_rate_change_company = GKEStartPodOperator(
        task_id="create-fn-rate-change-company",
        name="create-fn-rate-change-company",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(RDO_TABLES_SQL_FOLDER + "fn_rate_change_company.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    populate_ctrl_rate_change = GKEStartPodOperator(
        task_id="populate-ctrl-rate-change",
        name="populate-ctrl-rate-change",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(RDO_TABLES_SQL_FOLDER + "populate_ctrl_rate_change.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME),
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_PUBLISH/RDO_TABLES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    # Transfer back to SQL Server tasks
    transfer_rdo_client_geography = GKEStartPodOperator(
        task_id="transfer-rdo-client-geography",
        name="transfer-rdo-client-geography",
        priority_weight=9,
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(
            requests={"cpu": "2000m", "memory": "1Gi"}
        ),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
            "BQ_TABLE": f"rdo_publish_dbo_client_geography__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.RDO_ClientGeography",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": """{
        "id": "Id",
        "client_id": "ClientId",
        "client_region": "ClientRegion",
        "client_district": "ClientDistrict",
        "location_code": "LocationCode"
    }""",
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )

    transfer_rdo_client_month = GKEStartPodOperator(
        task_id="transfer-rdo-client-month",
        name="transfer-rdo-client-month",
        priority_weight=9,
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(
            requests={"cpu": "2000m", "memory": "0.5Gi"}
        ),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
            "BQ_TABLE": f"rdo_publish_dbo_rdo_client_month__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.RDO_ClientMonth",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "30000",
            "COLUMN_MAPPING": """{
        "id": "Id",
        "client_id": "ClientId",
        "month": "Month"
    }""",
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )

    transfer_rdo_client_tx_attribute = GKEStartPodOperator(
        task_id="transfer-rdo-client-tx-attribute",
        name="transfer-rdo-client-tx-attribute",
        priority_weight=9,
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(
            requests={"cpu": "300m", "memory": "0.5Gi"}
        ),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
            "BQ_TABLE": f"ras_datamart_analytics_reporting_dbo_rdo_client_tx_attribute__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.RDO_ClientTxAttribute",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "100",
            "COLUMN_MAPPING": """{
        "id": "Id",
        "client_id": "ClientId",
        "exists_national_acct": "ExistsNationalAcct",
        "exists_substitution": "ExistsSubstitution",
        "exists_rpo": "ExistsRpo",
        "exists_contract": "ExistsContracts",
        "exists_special_pricing": "ExistsSpecialPricing",
        "exists_operated": "ExistsIsOperated",
        "exists_prime_unit": "ExistsPrimeUnit",
        "is_trial": "IsTrial",
        "has_change_log": "HasChangeLog",
        "exists_re_rent": "ExistsReRent",
        "dashed_line_month": "DashedLineMonth",
        "has_asset_grid": "HasAssetGrid",
        "max_prod_type_len": "MaxProdTypeLen",
        "exists_outlier": "ExistsOutlier",
        "max_equipment_len": "MaxEquipmentLen",
        "is_mqa_trial": "IsMqaTrial"
    }""",
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )

    transfer_rdo_dim_customer = GKEStartPodOperator(
        task_id="transfer-rdo-dim-customer",
        name="transfer-rdo-dim-customer",
        priority_weight=9,
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(
            requests={"cpu": "2000m", "memory": "1.5Gi"}
        ),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
            "BQ_TABLE": f"rdo_publish_dbo_rdo_dim_customer__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.RDO_DimCustomer",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": """{
        "id": "Id",
        "client_id": "ClientId",
        "customer_num": "CustomerNum",
        "customer_name": "CustomerName"
    }""",
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )
    transfer_rdo_dim_sales_rep = GKEStartPodOperator(
        task_id="transfer-rdo-dim-sales-rep",
        name="transfer-rdo-dim-sales-rep",
        priority_weight=9,
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(
            requests={"cpu": "2000m", "memory": "1.5Gi"}
        ),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
            "BQ_TABLE": f"rdo_publish_dim_sales_rep__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.RDO_DimSalesRep",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": """{
        "id": "Id",
        "client_id": "ClientId",
        "sales_rep_num": "SalesRepNum",
        "sales_rep_name": "SalesRepName"
    }""",
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )

    transfer_rate_change_client_bench_data = GKEStartPodOperator(
        task_id="transfer-rate-change-client-bench-data",
        name="transfer-rate-change-client-bench-data",
        priority_weight=9,
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        execution_timeout=timedelta(hours=3),
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(
            requests={"cpu": "2000m", "memory": "2Gi"}
        ),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
            "BQ_TABLE": f"rdo_publish_dbo_rate_change_client_bench_data__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.RateChange_ClientBenchData",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": """{     
            "client_id" : "ClientID",      
            "bgid" : "BGID",     
            "month_id" : "MonthID",     
            "rouse_geography_id" : "RouseGeographyID",     
            "rouse_product_type_id" : "RouseProductTypeID",      
            "rate_change_mom_bench" : "RateChangeMoMBench",      
            "rate_change_yoy_bench" : "RateChangeYoYBench"  }""",
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )

    transfer_rate_change_bench_data_xuri = GKEStartPodOperator(
        task_id="transfer-rate-change-bench-data-xuri",
        name="transfer-rate-change-bench-data-xuri",
        priority_weight=9,
        execution_timeout=timedelta(hours=3),
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(
            requests={"cpu": "2000m", "memory": "1.5Gi"}
        ),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
            "BQ_TABLE": f"rdo_publish_dbo_rate_change_bench_data_xuri__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.RateChange_BenchData_xURI",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": """{
        "client_id": "ClientID",
        "gid": "GID",
        "month_id": "MonthID",
        "rouse_geography_id": "RouseGeographyID",
        "rouse_product_type_id": "RouseProductTypeID",
        "rate_change_mom_bench": "RateChangeMoMBench",
        "rate_change_yoy_bench": "RateChangeYoYBench"
    }""",
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )

    transfer_rev_dist_pt_client_bench_data = GKEStartPodOperator(
        task_id="transfer-rev-dist-pt-client-bench-data",
        name="transfer-rev-dist-pt-client-bench-data",
        priority_weight=9,
        execution_timeout=timedelta(hours=3),
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(
            requests={"cpu": "3000m", "memory": "2Gi"}
        ),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
            "BQ_TABLE": f"rdo_publish_dbo_populate_rev_dist_pt_client_bench_data__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.RevDistPT_ClientBenchData",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "150000",
            "PARALLEL_PROCESSES": "3",
            "COLUMN_MAPPING": open(RDO_TABLES_SQL_FOLDER + "mapping_json/rev_dist_pt_client_bench_data.json").read(),
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )

    transfer_rev_dist_pt_client_schema = GKEStartPodOperator(
        task_id="transfer-rev-dist-pt-client-schema",
        name="transfer-rev-dist-pt-client-schema",
        priority_weight=9,
        execution_timeout=timedelta(hours=3),
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(
            requests={"cpu": "2000m", "memory": "2Gi"}
        ),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
            "BQ_TABLE": f"rdo_publish_rev_dist_pt_client_schema__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.RevDistPT_ClientSchema",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": open(RDO_TABLES_SQL_FOLDER + "mapping_json/rev_dist_pt_client_schema.json").read(),
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )

    transfer_ctrl_rate_change = GKEStartPodOperator(
        task_id="transfer-ctrl-rate-change",
        name="transfer-ctrl-rate-change",
        priority_weight=9,
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(
            requests={"cpu": "1000m", "memory": "1Gi"}
        ),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
            "BQ_TABLE": f"rdo_publish_dbo_ctrl_rate_change__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.CTRL_RateChange",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": """{
        "client_id": "ClientID",
        "geo_level": "GeoLevel",
        "month_id": "MonthID",
        "total_rc_mom": "TotalRC_MoM",
        "compared_rc_mom": "ComparedRC_MoM",
        "bench_rc_mom": "BenchRC_MoM",
        "total_rc_yoy": "TotalRC_YoY",
        "compared_rc_yoy": "ComparedRC_YoY",
        "bench_rc_yoy": "BenchRC_YoY",
        "total_rc_mom_wt": "TotalRC_MoM_Wt",
        "bench_rc_mom_wt": "BenchRC_MoM_Wt",
        "total_rc_yoy_wt": "TotalRC_YoY_Wt",
        "bench_rc_yoy_wt": "BenchRC_YoY_Wt"
    }""",
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )

    transfer_rate_change_client_schema = GKEStartPodOperator(
        task_id="transfer-rate-change-client-schema",
        name="transfer-rate-change-client-schema",
        priority_weight=9,
        execution_timeout=timedelta(hours=3),
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(
            requests={"cpu": "3000m", "memory": "2Gi"}
        ),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
            "BQ_TABLE": f"rdo_publish_dbo_rate_change_client_schema__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.RateChange_ClientSchema",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "PARALLEL_PROCESSES": "3",
            "CHUNKSIZE": "150000",
            "COLUMN_MAPPING": """{
        "client_id": "ClientID",
        "month_id": "MonthID",
        "rouse_market_id": "RouseMarketID",
        "rouse_district_id": "RouseDistrictID",
        "rouse_region_id": "RouseRegionID",
        "country_id": "CountryID",
        "rouse_product_type_id": "RouseProductTypeID",
        "client_product_type_id": "ClientProductTypeID",
        "client_branch_id": "ClientBranchID",
        "curr_monthly_rate": "CurrMonthlyRate",
        "mom_monthly_rate": "MoMMonthlyRate",
        "yoy_monthly_rate": "YoYMonthlyRate",
        "curr_weekly_rate": "CurrWeeklyRate",
        "mom_weekly_rate": "MoMWeeklyRate",
        "yoy_weekly_rate": "YoYWeeklyRate",
        "curr_daily_rate": "CurrDailyRate",
        "mom_daily_rate": "MoMDailyRate",
        "yoy_daily_rate": "YoYDailyRate",
        "curr_hourly_rate": "CurrHourlyRate",
        "mom_hourly_rate": "MoMHourlyRate",
        "yoy_hourly_rate": "YoYHourlyRate",
        "curr_months": "CurrMonths",
        "mom_months": "MoMMonths",
        "yoy_months": "YoYMonths",
        "curr_weeks": "CurrWeeks",
        "mom_weeks": "MoMWeeks",
        "yoy_weeks": "YoYWeeks",
        "curr_days": "CurrDays",
        "mom_days": "MoMDays",
        "yoy_days": "YoYDays",
        "curr_hours": "CurrHours",
        "mom_hours": "MoMHours",
        "yoy_hours": "YoYHours",
        "show_bench": "ShowBench"
    }""",
            "PARSE_DTYPES": "False",
            "PARALLEL_PROCESSES": "3",
        },
        dag=dag,
    )

    transfer_ctrl_rate_change_category = GKEStartPodOperator(
        task_id="transfer-ctrl-rate-change-category",
        name="transfer-ctrl-rate-change-category",
        priority_weight=9,
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(
            requests={"cpu": "2000m", "memory": "1.5Gi"}
        ),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
            "BQ_TABLE": f"rdo_publish_dbo_ctrl_rate_change_category__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.CTRL_RateChangeCategory",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": """{
        "client_id": "ClientID",
        "client_code": "ClientCode",
        "month_id": "MonthID",
        "month": "Month",
        "group": "Group",
        "sub_group": "SubGroup",
        "rouse_category": "RouseCategory",
        "rc_mom_num": "RC_MoM_Num",
        "rc_mom_den": "RC_MoM_Den",
        "rc_mom": "RC_MoM",
        "rc_yoy_num": "RC_YoY_Num",
        "rc_yoy_den": "RC_YoY_Den",
        "rc_yoy": "RC_YoY"
    }""",
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )

    rate_client_schema_partitions = 8
    staging_table_name = "dbo.Rate_ClientSchema" + "_STG"
    partition_bq_rate_client_schema = GKEStartPodOperator(
        task_id="partition-rate-client-schema",
        name="partition-rate-client-schema",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                PUSH_TO_PROD_FOLDER
                + "push_to_prod_preload/partition_bq_tables_template.sql"
            )
            .read()
            .replace("{NUMBER_OF_PARTITIONS}", str(rate_client_schema_partitions))
            .replace("{PARTITION_COLUMN}", "month_id")
            .replace(
                "{TABLE_NAME}",
                f"{RDO_PUBLISH_VERSION_DATASET_NAME}.rdo_publish_dbo_rate_client_schema__{TABLE_SUFFIX}",
            ),
            "XCOM_PUSH": "False",
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/RDO_TABLES/RATE_CLIENT_SCHEMA",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
            "WRITE_DISPOSITION": None,
        },
        dag=dag,
    )
    create_stg_table = GKEStartPodOperator(
        task_id=f"create-stg-table-rate-client-schema",
        name=f"create-stg-table-rate-client-schema",
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "create-stg-table"],
        resources=RESOURCES_SMALL_TASK,
        priority_weight=9,
        env_vars={
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.Rate_ClientSchema",
        },
        dag=dag,
    )
    partition_bq_rate_client_schema >> create_stg_table

    recreate_stg_table_indexes = GKEStartPodOperator(
        task_id=f"recreate-stg-table-indexes-rate-client-schema",
        name=f"recreate-stg-table-indexes-rate-client-schema",
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "recreate-stg-table-indexes"],
        resources=RESOURCES_SMALL_TASK,
        priority_weight=9,
        env_vars={
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.Rate_ClientSchema",
            "DO_QA_ROW_COUNT": "True",
            "QA_GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_QA_TABLE": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.rdo_publish_dbo_rate_client_schema__{TABLE_SUFFIX}",
            "SS_QA_TABLE": f"ras_DataMart_Analytics_Cloud.{staging_table_name}",
            "REPLACE_TABLE": "True",
        },
        dag=dag,
    )

    recreate_stg_table_indexes >> rdo_tables_transfers_complete

    for partition_id in range(0, rate_client_schema_partitions):
        delete_before_insert_query = (
            open(
                PUSH_TO_PROD_FOLDER
                + "push_to_prod_preload/delete_partition_before_inserting_template.sql"
            )
            .read()
            .replace("{DB_NAME}", "ras_DataMart_Analytics_Cloud")
            .replace("{TABLE_NAME}", staging_table_name)
            .replace("{PARTITION_ID}", str(partition_id))
            .replace("{NUMBER_OF_PARTITIONS}", str(rate_client_schema_partitions))
            .replace("{PARTITION_COLUMN}", "MonthID")
        )

        qa_row_count_query = (
            open(
                PUSH_TO_PROD_FOLDER
                + "push_to_prod_preload/ss_qa_partition_override_template.sql"
            )
            .read()
            .replace("{DB_NAME}", "ras_DataMart_Analytics_Cloud")
            .replace("{TABLE_NAME}", staging_table_name)
            .replace("{PARTITION_ID}", str(partition_id))
            .replace("{NUMBER_OF_PARTITIONS}", str(rate_client_schema_partitions))
            .replace("{PARTITION_COLUMN}", "MonthID")
        )

        transfer_rate_client_schema = GKEStartPodOperator(
            task_id=f"transfer-rate-client-schema-p{partition_id}",
            name=f"transfer-rate-client-schema-p{partition_id}",
            priority_weight=9,
            execution_timeout=timedelta(hours=3),
            image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
            secrets=[secret_db_username, secret_db_password],
            arguments=["python3", "main.py", "load-from-bq-to-ss"],
            resources=k8s.V1ResourceRequirements(
                requests={"cpu": "3000m", "memory": "4Gi"}
            ),
            pool=ss_import_pool_name,
            env_vars={
                "GCP_PROJECT": DATA_PROJECT_NAME,
                "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
                "BQ_TABLE": f"rdo_publish_dbo_rate_client_schema__{TABLE_SUFFIX}_p{partition_id}",
                "DB_SERVER": GETL01_SQL_SERVER,
                "DB_NAME": "ras_DataMart_Analytics_Cloud",
                "TABLE_NAME": staging_table_name,
                "WRITE_MODE": "append",
                "DO_QA_ROW_COUNT": "True",
                "SS_QA_QUERY_OVERRIDE": qa_row_count_query,
                "EXECUTE_QUERY_BEFORE": delete_before_insert_query,
                "GCS_BUCKET_NAME": BUCKET_NAME,
                "PARALLEL_PROCESSES": "4",
                "CHUNKSIZE": "100000",
                "COLUMN_MAPPING": open(
                    DAG_BASE_SQL_FOLDER
                    + "rdo_tables/mapping_json/rate_client_schema.json"
                ).read(),
                "PARSE_DTYPES": "False",
            },
            dag=dag,
        )
        create_stg_table >> transfer_rate_client_schema >> recreate_stg_table_indexes

    transfer_rdo_sales_rep_branch_xref = GKEStartPodOperator(
        task_id="transfer-rdo-sales-rep-branch-xref",
        name="transfer-rdo-sales-rep-branch-xref",
        priority_weight=9,
        execution_timeout=timedelta(hours=3),
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(
            requests={"cpu": "1000m", "memory": "1Gi"}
        ),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
            "BQ_TABLE": f"rdo_publish_dbo_rdo_sales_rep_branch_xref__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.RDO_SalesRepBranch_xref",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": """{
        "sales_rep_id": "SalesRepId",
        "client_branch_id": "ClientBranchID",
        "client_id": "ClientID"
    }""",
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )

    transfer_fleet_client_schema_wk_xyz = GKEStartPodOperator(
        task_id="transfer-fleet-client-schema-wk-xyz",
        name="transfer-fleet-client-schema-wk-xyz",
        priority_weight=9,
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(
            requests={"cpu": "2000m", "memory": "1.2Gi"}
        ),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
            "BQ_TABLE": f"rdo_publish_dbo_fleet_client_schema_wk_xyz__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.Fleet_ClientSchema_WK_XYZ",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": """{
        "client_id": "ClientID",
        "week_id": "WeekID",
        "rouse_market_id": "RouseMarketID",
        "rouse_district_id": "RouseDistrictID",
        "rouse_region_id": "RouseRegionID",
        "country_id": "CountryID",
        "rouse_product_type_id": "RouseProductTypeID",
        "rouse_category_id": "RouseCategoryID",
        "client_branch_id": "ClientBranchID",
        "client_product_type_id": "ClientProductTypeID",
        "is_rpo": "IsRPO",
        "cost_in_fleet": "CostInFleet",
        "cost_on_rent": "CostOnRent",
        "cost_available": "CostAvailable",
        "unit_count": "UnitCount",
        "fleet_age_num": "FleetAgeNum",
        "fleet_age_den": "FleetAgeDen",
        "annualized_revenue": "AnnualizedRevenue",
        "show_bench": "ShowBench"
    }""",
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )

    fleet_client_schema_wk_partitions = 6
    staging_table_name = "dbo.Fleet_ClientSchema_WK_STG"
    partition_bq_fleet_client_schema_wk = GKEStartPodOperator(
        task_id="partition-fleet-client-schema-wk",
        name="partition-fleet-client-schema-wk",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                PUSH_TO_PROD_FOLDER
                + "push_to_prod_preload/partition_bq_tables_template.sql"
            )
            .read()
            .replace("{NUMBER_OF_PARTITIONS}", str(fleet_client_schema_wk_partitions))
            .replace("{PARTITION_COLUMN}", "week_id")
            .replace(
                "{TABLE_NAME}",
                f"{RDO_PUBLISH_VERSION_DATASET_NAME}.rdo_publish_dbo_fleet_client_schema_wk__{TABLE_SUFFIX}",
            ),
            "XCOM_PUSH": "False",
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/RDO_TABLES/FLEET_CLIENT_SCHEMA_WK",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
            "WRITE_DISPOSITION": None,
        },
        dag=dag,
    )
    create_stg_table = GKEStartPodOperator(
        task_id=f"create-stg-table-fleet-client-schema-wk",
        name=f"create-stg-table-fleet-client-schema-wk",
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "create-stg-table"],
        resources=RESOURCES_SMALL_TASK,
        priority_weight=9,
        env_vars={
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.Fleet_ClientSchema_WK",
        },
        dag=dag,
    )
    partition_bq_fleet_client_schema_wk >> create_stg_table

    recreate_stg_table_indexes = GKEStartPodOperator(
        task_id=f"recreate-stg-table-indexes-fleet-client-schema-wk",
        name=f"recreate-stg-table-indexes-fleet-client-schema-wk",
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "recreate-stg-table-indexes"],
        resources=RESOURCES_SMALL_TASK,
        priority_weight=9,
        env_vars={
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.Fleet_ClientSchema_WK",
            "DO_QA_ROW_COUNT": "True",
            "QA_GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_QA_TABLE": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.rdo_publish_dbo_fleet_client_schema_wk__{TABLE_SUFFIX}",
            "SS_QA_TABLE": f"ras_DataMart_Analytics_Cloud.{staging_table_name}",
            "REPLACE_TABLE": "True",
        },
        dag=dag,
    )
    recreate_stg_table_indexes >> rdo_tables_transfers_complete

    for partition_id in range(0, fleet_client_schema_wk_partitions):
        delete_before_insert_query = (
            open(
                PUSH_TO_PROD_FOLDER
                + "push_to_prod_preload/delete_partition_before_inserting_template.sql"
            )
            .read()
            .replace("{DB_NAME}", "ras_DataMart_Analytics_Cloud")
            .replace("{TABLE_NAME}", staging_table_name)
            .replace("{PARTITION_ID}", str(partition_id))
            .replace("{NUMBER_OF_PARTITIONS}", str(fleet_client_schema_wk_partitions))
            .replace("{PARTITION_COLUMN}", "WeekID")
        )

        qa_row_count_query = (
            open(
                PUSH_TO_PROD_FOLDER
                + "push_to_prod_preload/ss_qa_partition_override_template.sql"
            )
            .read()
            .replace("{DB_NAME}", "ras_DataMart_Analytics_Cloud")
            .replace("{TABLE_NAME}", staging_table_name)
            .replace("{PARTITION_ID}", str(partition_id))
            .replace("{NUMBER_OF_PARTITIONS}", str(fleet_client_schema_wk_partitions))
            .replace("{PARTITION_COLUMN}", "WeekID")
        )

        transfer_fleet_client_schema_wk = GKEStartPodOperator(
            task_id=f"transfer-fleet-client-schema-wk-p{partition_id}",
            name=f"transfer-fleet-client-schema-wk-p{partition_id}",
            priority_weight=9,
            execution_timeout=timedelta(hours=3),
            image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
            secrets=[secret_db_username, secret_db_password],
            arguments=["python3", "main.py", "load-from-bq-to-ss"],
            resources=k8s.V1ResourceRequirements(
                requests={"cpu": "3000m", "memory": "2Gi"}
            ),
            pool=ss_import_pool_name,
            env_vars={
                "GCP_PROJECT": DATA_PROJECT_NAME,
                "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
                "BQ_TABLE": f"rdo_publish_dbo_fleet_client_schema_wk__{TABLE_SUFFIX}_p{partition_id}",
                "DB_SERVER": GETL01_SQL_SERVER,
                "DB_NAME": "ras_DataMart_Analytics_Cloud",
                "TABLE_NAME": staging_table_name,
                "WRITE_MODE": "append",
                "DO_QA_ROW_COUNT": "True",
                "SS_QA_QUERY_OVERRIDE": qa_row_count_query,
                "EXECUTE_QUERY_BEFORE": delete_before_insert_query,
                "GCS_BUCKET_NAME": BUCKET_NAME,
                "PARALLEL_PROCESSES": "3",
                "CHUNKSIZE": "120000",
                "COLUMN_MAPPING": open(
                    DAG_BASE_SQL_FOLDER
                    + "rdo_tables/mapping_json/fleet_client_schema_wk.json"
                ).read(),
                "PARSE_DTYPES": "False",
            },
            dag=dag,
        )
        (
            create_stg_table
            >> transfer_fleet_client_schema_wk
            >> recreate_stg_table_indexes
        )

    transfer_fleet_client_schema = GKEStartPodOperator(
        task_id="transfer-fleet-client-schema",
        name="transfer-fleet-client-schema",
        priority_weight=9,
        execution_timeout=timedelta(hours=3),
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(
            requests={"cpu": "3000m", "memory": "3Gi"}
        ),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
            "BQ_TABLE": f"rdo_publish_dbo_fleet_client_schema__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.Fleet_ClientSchema",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "PARALLEL_PROCESSES": "3",
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": open(
                DAG_BASE_SQL_FOLDER
                + f"rdo_tables/mapping_json/fleet_client_schema.json"
            ).read(),
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )

    # Populate RDO Tables Package
    insert_rdo_tables_record = GKEStartPodOperator(
        task_id="insert-rdo-tables-record",
        name="insert-rdo-tables-record",
        secrets=[secret_db_username, secret_db_password],
        do_xcom_push=True,
        image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
        arguments=["python3", "main.py", "update-control-table"],
        execution_timeout=timedelta(minutes=5),
        retry_delay=timedelta(seconds=10),
        env_vars={
            "TABLE_NAME": "benchmark_etl_run",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Computation",
            "PIPELINE": "rdo_tables",
            "STATUS_VALUE": "RUNNING",
            "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
            "PARENT": "{{ dag_run.conf['parent_batch'] }}",
        },
        dag=dag,
    )
    update_rdo_tables_record = GKEStartPodOperator(
        task_id="update-rdo-tables-record",
        name="update-rdo-tables-record",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
        do_xcom_push=True,
        arguments=["python3", "main.py", "update-control-table"],
        execution_timeout=timedelta(minutes=5),
        retry_delay=timedelta(seconds=10),
        env_vars={
            "TABLE_NAME": "benchmark_etl_run",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Computation",
            "PIPELINE": "rdo_tables",
            "STATUS_VALUE": "DONE",
            "PK": '{{ task_instance.xcom_pull(task_ids="rdo-tables.insert-rdo-tables-record", key="return_value")["pk"] }}',
            "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
            "PARENT": "{{ dag_run.conf['parent_batch'] }}",
        },
        dag=dag,
    )
    update_rdo_tables_record_error = GKEStartPodOperator(
        task_id="update-rdo-tables-record-error",
        name="update-rdo-tables-record-error",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
        do_xcom_push=True,
        arguments=["python3", "main.py", "update-control-table"],
        execution_timeout=timedelta(minutes=5),
        retry_delay=timedelta(seconds=10),
        env_vars={
            "TABLE_NAME": "benchmark_etl_run",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Computation",
            "PIPELINE": "rdo_tables",
            "STATUS_VALUE": "FAILED",
            "PK": '{{ task_instance.xcom_pull(task_ids="rdo-tables.insert-rdo-tables-record", key="return_value")["pk"] }}',
            "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
            "PARENT": "{{ dag_run.conf['parent_batch'] }}",
        },
        trigger_rule="one_failed",
        dag=dag,
    )

    update_rdo_tables_record_when_clear = GKEStartPodOperator(
        task_id="update-rdo-tables-record-when-clear",
        name="update-rdo-tables-record-when-clear",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
        do_xcom_push=True,
        arguments=["python3", "main.py", "update-control-table"],
        execution_timeout=timedelta(minutes=5),
        retry_delay=timedelta(seconds=10),
        trigger_rule="one_success",
        env_vars={
            "TABLE_NAME": "benchmark_etl_run",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Computation",
            "PIPELINE": "rdo_tables",
            "STATUS_VALUE": "RUNNING",
            "PK": '{{ task_instance.xcom_pull(task_ids="rdo-tables.insert-rdo-tables-record", key="return_value")["pk"] }}',
            "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
            "PARENT": "{{ dag_run.conf['parent_batch'] }}",
        },
        dag=dag,
    )

    finish_publish_sequence_package >> insert_rdo_tables_record

    # I'll add to this list some tasks used on RDO Publish
    parametrized_rdo_publish_tasks = []

    # Creating the tasks dict for rdo-tables
    rdo_tables_tasks = []
    rdo_tables_complete = DummyOperator(task_id="rdo-tables-complete", dag=dag)
    rambo_tasks_dict["rdo-tables-complete"] = rdo_tables_complete
    rambo_tasks_dict["rdo-tables-transfers-complete"] = rdo_tables_transfers_complete
    rdo_tables_complete >> [update_rdo_tables_record, update_rdo_tables_record_error]
    rdo_tables_transfers_complete >> [
        update_rdo_tables_record,
        update_rdo_tables_record_error,
    ]
    update_rdo_tables_record >> ss_done
    [
        insert_rdo_tables_record,
        update_rdo_tables_record,
    ] >> update_rdo_tables_record_when_clear
    update_rdo_tables_record_when_clear >> update_rambo_record_when_clear
    # Adding the dummy task to the tasks dict as its a dependency of rate bench data tasks
    rambo_tasks_dict[
        "finish-publish-sequence-package"
    ] = finish_publish_sequence_package

    #########################################
    ### Transfer Derived Dimension Tables ###
    #########################################
    derived_dimensions = {
        # The name of the record should be the same as the .json file with a mapping at the rdo_tables/mapping_json/ folder.
        # It will also be the name of the table in RDO_PUBLISH_DATASET_NAME dataset
        "dim_client_region_derived": {
            "table_sql_server": "dbo.DimClientRegion_Derived",
            "query": f"""SELECT * FROM {RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_region__{TABLE_SUFFIX}
                    WHERE client_id < 0 or client_id = 80""",
        },
        "dim_client_district_derived": {
            "table_sql_server": "dbo.DimClientDistrict_Derived",
            "query": f"""SELECT * FROM {RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_district__{TABLE_SUFFIX}
                    WHERE client_id < 0 or client_id = 80""",
        },
        "dim_client_branch_derived": {
            "table_sql_server": "dbo.DimClientBranch_Derived",
            "query": f"""SELECT * FROM {RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_branch__{TABLE_SUFFIX}
                    WHERE clientid < 0 or clientid = 80""",
        },
        "dim_client_product_type_derived": {
            "table_sql_server": "dbo.DimClientProductType_Derived",
            "query": f"""SELECT * FROM {RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_product_type__{TABLE_SUFFIX}
                    WHERE clientid < 0 or clientid = 80""",
        },
        "dim_client_category_derived": {
            "table_sql_server": "dbo.DimClientCategory_Derived",
            "query": f"""SELECT * FROM {RDO_PUBLISH_VERSION_DATASET_NAME}.ras_datamart_analytics_reporting_dbo_dim_client_category__{TABLE_SUFFIX}
                    WHERE client_id < 0 or client_id = 80""",
        },
    }

    for derived_dimension_table in derived_dimensions.keys():
        derived_dimension_info = derived_dimensions[derived_dimension_table]

        derived_dimension_table_versioned = f"{derived_dimension_table}__{TABLE_SUFFIX}"

        populate_derived_dimension_task = GKEStartPodOperator(
            task_id="populate-" + derived_dimension_table.replace("_", "-"),
            name="populate-" + derived_dimension_table.replace("_", "-"),
            image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
            arguments=["python3", "main.py", "execute-bigquery"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "QUERY": derived_dimension_info["query"],
                "DESTINATION_TABLE": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_VERSION_DATASET_NAME}.{derived_dimension_table_versioned}",
                "VIEW_TO_REPLACE": f"{DATA_PROJECT_NAME}.{RDO_PUBLISH_DATASET_NAME}.{derived_dimension_table}",
                "WRITE_LOGS": "True",
                "LOGS_GCP_PROJECT": PROJECT_NAME,
                "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/RDO_TABLES/{derived_dimension_table.upper()}",
                "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
                "WRITE_DISPOSITION": "WRITE_TRUNCATE",
            },
            dag=dag,
        )

        transfer_task = GKEStartPodOperator(
            task_id="transfer-" + derived_dimension_table.replace("_", "-"),
            name="transfer-" + derived_dimension_table.replace("_", "-"),
            priority_weight=9,
            execution_timeout=timedelta(minutes=20),
            image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
            secrets=[secret_db_username, secret_db_password],
            arguments=["python3", "main.py", "load-from-bq-to-ss"],
            resources=k8s.V1ResourceRequirements(
                requests={"cpu": "2000m", "memory": "1Gi"}
            ),
            pool=ss_import_pool_name,
            env_vars={
                "GCP_PROJECT": DATA_PROJECT_NAME,
                "BQ_DATASET": RDO_PUBLISH_VERSION_DATASET_NAME,
                "BQ_TABLE": derived_dimension_table_versioned,
                "DB_SERVER": GETL01_SQL_SERVER,
                "DB_NAME": "ras_DataMart_Analytics_Cloud",
                "TABLE_NAME": derived_dimension_info["table_sql_server"],
                "WRITE_MODE": "overwrite",
                "GCS_BUCKET_NAME": BUCKET_NAME,
                "CHUNKSIZE": "100000",
                "COLUMN_MAPPING": open(
                    DAG_BASE_SQL_FOLDER
                    + f"rdo_tables/mapping_json/{derived_dimension_table}.json"
                ).read(),
                "PARSE_DTYPES": "False",
                "PARALLEL_PROCESSES": "2",
            },
            dag=dag,
        )
        (
            finish_publish_sequence_package
            >> populate_derived_dimension_task
            >> transfer_task
            >> rdo_tables_transfers_complete
        )
        # I'll only transfer when receiving the transfer_to_cloud_db = True
        run_transfers_to_cloud_db >> transfer_task

    # --------------------------------

    # Rate Bench Data
    rate_bench_data_tasks = [
        {
            "task_id": "pre-load-rate-bench-data",
            "table_name": "pre_load_rate_bench_data",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["finish-publish-sequence-package"],
            "query_file": "pre_load_rate_bench_data.sql",
        },
        {
            "task_id": "pre-load-client-market-pts",
            "table_name": "pre_load_client_market_pts",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["finish-publish-sequence-package"],
            "query_file": "pre_load_client_market_pts.sql",
        },
        # This task is created right below, but I'm including it here for the depenencies.
        {
            "task_id": "rate-bench-data",
            "type": "task created manually",
            "task_dependencies": [
                "pre-load-client-market-pts",
                "pre-load-rate-bench-data",
            ],
            "send_back_to_ss": True,
            "table_name": "rate_bench_data",
            "transfer_execution_timeout": timedelta(hours=7),
            "back_to_ss_task_id": "transfer-rate-bench-data-to-ss",
            "sql_server_table_name": "dbo.Rate_BenchData",
            "column_mapping_file": "mapping_rate_bench_data.json",
            "number_of_partitions": 12,
            "partition_column": "month_id",
            "partition_column_ss": "MonthID",
        },
        {
            "task_id": "rate-order-coverage",
            "table_name": "rate_order_coverage",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["rate-bench-data"],
            "query_file": "rate_order_coverage.sql",
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-rate-order-coverage-to-ss",
            "sql_server_table_name": "dbo.Rate_OrderCoverage",
            "column_mapping_file": "mapping_rate_order_coverage.json",
        },
        {
            "task_id": "view-xyz-client-equipment",
            "table_name": "rdo_publish_dbo_xyz_client_equipment",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["finish-publish-sequence-package"],
            "query_file": "view_xyz_client_equipment.sql",
        },
    ]
    rdo_tables_tasks += rate_bench_data_tasks

    # Task that executes the Dynamic SQL inside the stored procedure.
    rate_bench_data = GKEStartPodOperator(
        task_id="rate-bench-data",
        name="rate-bench-data",
        image=get_full_image_name("rambo-rate-bench-dynamic-task", GCR_REGISTRY),
        arguments=["python3", "main.py", "read-bench-data"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "RDO_PUBLISH_VERSION_DATASET_NAME": RDO_PUBLISH_VERSION_DATASET_NAME,
            "AGGS_DESTINATION_DATASET": AGGS_DESTINATION_DATASET,
            "RDO_PUBLISH_DATASET_NAME": RDO_PUBLISH_DATASET_NAME,
            "TABLE_SUFFIX": TABLE_SUFFIX,
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_TABLES/RATE_BENCH_DATA",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )
    rambo_tasks_dict["rate-bench-data"] = rate_bench_data

    # --------------------------------

    # Fleet Bench Data
    fleet_bench_data_tasks = [
        {
            "task_id": "pre-load-fleet-bench-data",
            "table_name": "pre_load_fleet_bench_data",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["rate-order-coverage"],
            "query_file": "pre_load_fleet_bench_data.sql",
        },
        # This task is created right below, but I'm including it here for the depenencies.
        {
            "task_id": "fleet-bench-data",
            "type": "task created manually",
            "task_dependencies": [
                "pre-load-client-market-pts",
                "pre-load-fleet-bench-data",
            ],
            "send_back_to_ss": True,
            "table_name": "fleet_bench_data",
            "back_to_ss_task_id": "transfer-fleet-bench-data-to-ss",
            "sql_server_table_name": "dbo.Fleet_BenchData",
            "column_mapping_file": "mapping_fleet_bench_data.json",
        },
        {
            "task_id": "fleet-bench-data-wk-xyz",
            "table_name": "fleet_bench_data_wk_xyz",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["fleet-bench-data"],
            "query_file": "fleet_bench_data_wk_xyz.sql",
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-fleet-bench-data-wk-xyz-to-ss",
            "sql_server_table_name": "dbo.Fleet_BenchData_WK_XYZ",
            "column_mapping_file": "mapping_fleet_bench_data_wk_xyz.json",
        },
        {
            "task_id": "calculate-bench-util-wk",
            "table_name": "benchmark_util_wk",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["finish-publish-sequence-package"],
            "query_file": "calculate_bench_util_wk.sql",
        },
        {
            "task_id": "bench-util-wk-cleanup",
            "table_name": "benchmark_util_wk",
            "write_disposition": None,
            "task_dependencies": ["calculate-bench-util-wk"],
            "query_file": "bench_util_wk_cleanup.sql",
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-benchmark-util-wk-to-ss",
            "sql_server_table_name": "dbo.Benchmark_Util_WK",
            "column_mapping_file": "mapping_benchmark_util_wk.json",
        },
        {
            "task_id": "pre-load-client-market-pts-wk",
            "table_name": "pre_load_client_market_pts_wk",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["finish-publish-sequence-package"],
            "query_file": "pre_load_client_market_pts_wk.sql",
        },
        {
            "task_id": "rate-order-coverage-wk",
            "table_name": "rate_order_coverage_wk",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["rate-order-coverage"],
            "query_file": "rate_order_coverage_wk.sql",
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-rate-order-coverage-wk-to-ss",
            "sql_server_table_name": "dbo.Rate_OrderCoverage_WK",
            "column_mapping_file": "mapping_rate_order_coverage_wk.json",
        },
        {
            "task_id": "pre-load-fleet-bench-data-wk",
            "table_name": "pre_load_fleet_bench_data_wk",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["rate-order-coverage-wk", "bench-util-wk-cleanup"],
            "query_file": "pre_load_fleet_bench_data_wk.sql",
        },
        # This task is created right below, but I'm including it here for the depenencies.
        {
            "task_id": "fleet-bench-data-wk",
            "type": "task created manually",
            "task_dependencies": [
                "pre-load-client-market-pts-wk",
                "pre-load-fleet-bench-data-wk",
            ],
            "send_back_to_ss": True,
            "table_name": "fleet_bench_data_wk",
            "back_to_ss_task_id": "transfer-fleet-bench-data-wk-to-ss",
            "sql_server_table_name": "dbo.Fleet_Benchdata_WK",
            "column_mapping_file": "mapping_fleet_bench_data_wk.json",
        },
    ]
    rdo_tables_tasks += fleet_bench_data_tasks

    # Task that executes the Dynamic SQL inside the stored procedure.
    fleet_bench_data = GKEStartPodOperator(
        task_id="fleet-bench-data",
        name="fleet-bench-data",
        image=get_full_image_name("rambo-rate-bench-dynamic-task", GCR_REGISTRY),
        arguments=["python3", "main.py", "read-fleet-bench-data"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "RDO_PUBLISH_VERSION_DATASET_NAME": RDO_PUBLISH_VERSION_DATASET_NAME,
            "AGGS_DESTINATION_DATASET": AGGS_DESTINATION_DATASET,
            "RDO_PUBLISH_DATASET_NAME": RDO_PUBLISH_DATASET_NAME,
            "TABLE_SUFFIX": TABLE_SUFFIX,
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_TABLES/FLEET_BENCH_DATA",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )
    rambo_tasks_dict["fleet-bench-data"] = fleet_bench_data

    # Task that executes the Dynamic SQL inside the stored procedure.
    fleet_bench_data_wk = GKEStartPodOperator(
        task_id="fleet-bench-data-wk",
        name="fleet-bench-data-wk",
        image=get_full_image_name("rambo-rate-bench-dynamic-task", GCR_REGISTRY),
        arguments=["python3", "main.py", "read-fleet-bench-data-wk"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "RDO_PUBLISH_VERSION_DATASET_NAME": RDO_PUBLISH_VERSION_DATASET_NAME,
            "AGGS_DESTINATION_DATASET": AGGS_DESTINATION_DATASET,
            "RDO_PUBLISH_DATASET_NAME": RDO_PUBLISH_DATASET_NAME,
            "TABLE_SUFFIX": TABLE_SUFFIX,
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_TABLES/FLEET_BENCH_DATA_WK",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )
    rambo_tasks_dict["fleet-bench-data-wk"] = fleet_bench_data_wk

    # --------------------------------

    # Cus Bin Bench Data
    rate_cus_bin_bench_data_tasks = [
        {
            "task_id": "pre-load-rate-cus-bin-bench-data",
            "table_name": "pre_load_rate_cus_bin_bench_data",
            "write_disposition": None,
            "task_dependencies": ["pre-load-rate-bench-data", "rate-order-coverage"],
            "query_file": "pre_load_rate_cus_bin_bench_data.sql",
        },
        {
            "task_id": "pre-load-rate-cus-bin-bench-data-carry-forward",
            "table_name": "pre_load_rate_cus_bin_bench_data",
            "write_disposition": None,
            "task_dependencies": ["pre-load-rate-cus-bin-bench-data"],
            "query_file": "pre_load_rate_cus_bin_bench_data_carry_forward.sql",
        },
        # This task is created right below, but I'm including it here for the depenencies.
        {
            "task_id": "rate-cus-bin-bench-data",
            "type": "task created manually",
            "task_dependencies": [
                "pre-load-rate-cus-bin-bench-data-carry-forward",
                "pre-load-client-market-pts",
            ],
            "send_back_to_ss": True,
            "transfer_execution_timeout": timedelta(hours=4),
            "table_name": "rate_cus_bin_bench_data",
            "back_to_ss_task_id": "transfer-rate-cus-bin-bench-data-to-ss",
            "sql_server_table_name": "dbo.Rate_CusBinBenchData",
            "column_mapping_file": "mapping_rate_cus_bin_bench_data.json",
            "number_of_partitions": 6,
            "partition_column": "month_id",
            "partition_column_ss": "MonthID",
        },
    ]
    rdo_tables_tasks += rate_cus_bin_bench_data_tasks

    # Task that executes the Dynamic SQL inside the stored procedure.
    rate_cus_bin_bench_data = GKEStartPodOperator(
        task_id="rate-cus-bin-bench-data",
        name="rate-cus-bin-bench-data",
        image=get_full_image_name("rambo-rate-bench-dynamic-task", GCR_REGISTRY),
        arguments=["python3", "main.py", "read-cus-bin-bench-data"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "RDO_PUBLISH_VERSION_DATASET_NAME": RDO_PUBLISH_VERSION_DATASET_NAME,
            "AGGS_DESTINATION_DATASET": AGGS_DESTINATION_DATASET,
            "RDO_PUBLISH_DATASET_NAME": RDO_PUBLISH_DATASET_NAME,
            "TABLE_SUFFIX": TABLE_SUFFIX,
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_TABLES/RATE_CUS_BIN_BENCH_DATA",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )
    rambo_tasks_dict["rate-cus-bin-bench-data"] = rate_cus_bin_bench_data

    # --------------------------------

    # CTRL Growth tasks
    ctrl_growth_tasks = [
        {
            "task_id": "update-international-clients-benchmark-growth",
            "table_name": "benchmark_growth",
            "write_disposition": None,
            "task_dependencies": ["finish-publish-sequence-package"],
            "query_file": "update_international_clients_benchmark_growth.sql",
        },
        {
            "task_id": "pre-load-growth-bench-data",
            "table_name": "pre_load_growth_bench_data",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": [
                "update-international-clients-benchmark-growth",
                "rate-order-coverage",
            ],
            "query_file": "pre_load_growth_bench_data.sql",
        },
        # This task is created right below, but I'm including it here for the depenencies.
        {
            "task_id": "growth-client-bench-data",
            "type": "task created manually",
            "task_dependencies": [
                "pre-load-growth-bench-data",
                "pre-load-client-market-pts",
            ],
            "send_back_to_ss": True,
            "transfer_execution_timeout": timedelta(hours=4),
            "table_name": "growth_client_bench_data",
            "back_to_ss_task_id": "transfer-growth-client-bench-data-to-ss",
            "sql_server_table_name": "dbo.Growth_ClientBenchData",
            "column_mapping_file": "mapping_growth_client_bench_data.json",
            "number_of_partitions": 6,
            "partition_column": "month_id",
            "partition_column_ss": "MonthID",
        },
        {
            "task_id": "growth-client-schema",
            "table_name": "growth_client_schema",
            "write_disposition": None,
            "task_dependencies": ["finish-publish-sequence-package"],
            "query_file": "growth_client_schema.sql",
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-growth-client-schema-to-ss",
            "sql_server_table_name": "dbo.Growth_ClientSchema",
            "column_mapping_file": "mapping_growth_client_schema.json",
        },
        # This task is created right below, but I'm including it here for the depenencies.
        {
            "task_id": "ctrl-growth",
            "type": "task created manually",
            "task_dependencies": [
                "growth-client-schema",
                "growth-client-bench-data",
            ],
            "send_back_to_ss": True,
            "table_name": "ctrl_growth",
            "back_to_ss_task_id": "transfer-ctrl-growth-to-ss",
            "sql_server_table_name": "dbo.CTRL_Growth",
            "column_mapping_file": "mapping_ctrl_growth.json",
        },
    ]
    rdo_tables_tasks += ctrl_growth_tasks

    # Task that executes the Dynamic SQL inside the stored procedure.
    growth_client_bench_data = GKEStartPodOperator(
        task_id="growth-client-bench-data",
        name="growth-client-bench-data",
        image=get_full_image_name("rambo-rate-bench-dynamic-task", GCR_REGISTRY),
        arguments=["python3", "main.py", "read-growth-client-bench-data"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "RDO_PUBLISH_VERSION_DATASET_NAME": RDO_PUBLISH_VERSION_DATASET_NAME,
            "AGGS_DESTINATION_DATASET": AGGS_DESTINATION_DATASET,
            "RDO_PUBLISH_DATASET_NAME": RDO_PUBLISH_DATASET_NAME,
            "TABLE_SUFFIX": TABLE_SUFFIX,
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_TABLES/GROWTH_CLIENT_BENCH_DATA",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )
    rambo_tasks_dict["growth-client-bench-data"] = growth_client_bench_data

    # Task that executes the Dynamic SQL inside the stored procedure.
    ctrl_growth = GKEStartPodOperator(
        task_id="ctrl-growth",
        name="ctrl-growth",
        image=get_full_image_name("rambo-rate-bench-dynamic-task", GCR_REGISTRY),
        arguments=["python3", "main.py", "read-ctrl-growth"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "RDO_PUBLISH_VERSION_DATASET_NAME": RDO_PUBLISH_VERSION_DATASET_NAME,
            "AGGS_DESTINATION_DATASET": AGGS_DESTINATION_DATASET,
            "RDO_PUBLISH_DATASET_NAME": RDO_PUBLISH_DATASET_NAME,
            "TABLE_SUFFIX": TABLE_SUFFIX,
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_TABLES/CTRL_GROWTH",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )
    rambo_tasks_dict["ctrl-growth"] = ctrl_growth

    # --------------------------------

    # Alerts tasks
    alerts_tasks = [
        {
            "task_id": "create-alerts-table",
            "write_disposition": None,
            "task_dependencies": [
                "rate-bench-data",
                "fleet-bench-data",
                "populate-rate-client-schema",
                "populate-fleet-client-schema",
            ],
            "query_file": "alerts/create_alerts_table.sql",
        },
        {
            "task_id": "create-alerts-procedure",
            "write_disposition": None,
            "task_dependencies": ["create-alerts-table"],
            "query_file": "alerts/create_alerts_procedure.sql",
        },
        {
            "task_id": "populate-alerts",
            # Task created below, adding here for the dependencies and transfer task
            "type": "task created manually",
            "task_dependencies": ["create-alerts-procedure"],
        },
        {
            "task_id": "update-alert-id",
            "table_name": "alert",
            "write_disposition": None,
            "task_dependencies": ["populate-alerts"],
            "query_file": "alerts/update_alert_id.sql",
        },
        {
            "task_id": "qa-duplicates-alerts",
            "type": "task created manually",
            "task_dependencies": ["update-alert-id"],
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-alerts-to-ss",
            "sql_server_table_name": "dbo.Alert",
            "column_mapping_file": "mapping_json/alert.json",
        },
    ]
    rdo_tables_tasks += alerts_tasks

    # Number of paralel tasks to run cat_prro
    max_tasks = 20
    alerts_task_ids = [
        "rdo-tables.create-alerts-table",
        "rdo-tables.create-alerts-procedure",
        "rdo-tables.update-alert-id",
        "rdo-tables.qa-duplicates-alerts",
    ]
    rambo_tasks_dict["populate-alerts"] = []
    for task_number in range(0, max_tasks):
        populate_alerts = GKEStartPodOperator(
            task_id=f"populate-alerts-{str(task_number)}",
            name=f"populate-alerts-{str(task_number)}",
            image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
            arguments=["python3", "main.py", "execute-bigquery"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "QUERY": open(
                    DAG_BASE_SQL_FOLDER + "rdo_tables/alerts/populate_alerts.sql"
                )
                .read()
                .format(
                    **{
                        "AGGS_DESTINATION_DATASET": AGGS_DESTINATION_DATASET,
                        "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
                        "RDO_PUBLISH_DATASET_NAME": RDO_PUBLISH_DATASET_NAME,
                        "RDO_PUBLISH_VERSION_DATASET_NAME": RDO_PUBLISH_VERSION_DATASET_NAME,
                        "DATA_PROJECT_NAME": DATA_PROJECT_NAME,
                        "TABLE_SUFFIX": TABLE_SUFFIX,
                        "RATES_DATASET_NAME": RATES_DATASET_NAME,
                        "MAX_TASKS": max_tasks,
                        "TASK_NUMBER": task_number,
                    }
                ),
                "WRITE_LOGS": "True",
                "LOGS_GCP_PROJECT": PROJECT_NAME,
                "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/RDO_TABLES/POPULATE_ALERTS",
                "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
            },
            dag=dag,
        )
        rambo_tasks_dict["populate-alerts"].append(populate_alerts)
        alerts_task_ids.append(f"rdo-tables.populate-alerts-{str(task_number)}")

    # QA task to ensure there are no repeted Alerts records
    qa_duplicate_alerts = GKEStartPodOperator(
        task_id="qa-duplicates-alerts",
        name="qa-duplicates-alerts",
        image=get_full_image_name("qa-check-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "run-qa-check"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "CHECK_TYPE": "expect_empty_result",
            "QA_QUERY": open(
                DAG_BASE_SQL_FOLDER + "rdo_tables/alerts/qa_alerts_unique_columns.sql"
            )
            .read()
            .replace(
                "{RDO_PUBLISH_VERSION_DATASET_NAME}", RDO_PUBLISH_VERSION_DATASET_NAME
            )
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
        },
        dag=dag,
    )
    rambo_tasks_dict["qa-duplicates-alerts"] = qa_duplicate_alerts

    # Custom grids supporting tasks
    custom_grids_supporting_tasks = [
        {
            "task_id": "rev-track-base",
            "write_disposition": None,
            "task_dependencies": ["finish-publish-sequence-package"],
            "query_file": "rev_track_base.sql",
        },
    ]
    rdo_tables_tasks += custom_grids_supporting_tasks

    # Initializing parameters common to rdo_tables tasks and adding it to parametrized_rdo_publish_tasks
    for task_info in rdo_tables_tasks:
        log_task_name = task_info["task_id"].replace("-", "_").upper()
        task_info["logs_job_identifier"] = f"RAMBO_PIPELINE/RDO_TABLES/{log_task_name}"
        task_info["sql_folder"] = DAG_BASE_SQL_FOLDER + "rdo_tables/"
        task_info["end_task"] = "rdo-tables-complete"
        task_info["end_transfers_task"] = "rdo-tables-transfers-complete"
        task_info["bq_version_dataset"] = RDO_PUBLISH_VERSION_DATASET_NAME
        task_info["bq_dataset"] = RDO_PUBLISH_DATASET_NAME
        task_info["priority_weight_transfer"] = 9
        # Default timeout for RDO tables transfers is 3 hours, as most tables are big
        task_info["transfer_execution_timeout"] = timedelta(hours=3)
        task_info["query_params"] = {
            "AGGS_DESTINATION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "RDO_PUBLISH_DATASET_NAME": RDO_PUBLISH_DATASET_NAME,
            "RDO_PUBLISH_VERSION_DATASET_NAME": RDO_PUBLISH_VERSION_DATASET_NAME,
            "DATA_PROJECT_NAME": DATA_PROJECT_NAME,
            "TABLE_SUFFIX": TABLE_SUFFIX,
            "RATES_DATASET_NAME": RATES_DATASET_NAME,
        }

    parametrized_rdo_publish_tasks += rdo_tables_tasks
    # ---------------------------------------

    # Loop first to create the tasks
    for task_info in parametrized_rdo_publish_tasks:
        # If the task was created manually, I'll not create the task here.
        # The dependencies will be created in the next FOR
        # Note: The task must be inside rambo_tasks_dict
        if "type" in task_info.keys():
            if task_info["type"] == "task created manually":
                pass
        else:
            if "table_name" in task_info.keys():
                # Default view and default destination tables
                default_view_to_replace = f"{DATA_PROJECT_NAME}.{task_info['bq_dataset']}.{task_info['table_name']}"
                default_destination_table = f"{DATA_PROJECT_NAME}.{task_info['bq_version_dataset']}.{task_info['table_name']}__{TABLE_SUFFIX}"
            else:
                default_view_to_replace = None
                default_destination_table = None

            # Open the query file, and replace the query parameters if needed
            sql_file = open(task_info["sql_folder"] + task_info["query_file"])
            sql_as_string = sql_file.read()
            if "query_params" in task_info.keys():
                sql_as_string = sql_as_string.format(**task_info["query_params"])

            # Base Operator 'Execute Bigquery' will do all the work of executing the queries and creating tables/views
            query_task = GKEStartPodOperator(
                task_id=task_info["task_id"],
                name=task_info["task_id"],
                image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
                arguments=["python3", "main.py", "execute-bigquery"],
                do_xcom_push=False,
                execution_timeout=task_info["task_execution_timeout"]
                if "task_execution_timeout" in task_info.keys()
                else timedelta(hours=2),
                resources=RESOURCES_SMALL_TASK,
                env_vars={
                    "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                    "QUERY": sql_as_string,
                    "DESTINATION_TABLE": task_info["destination_table"]
                    if "destination_table" in task_info.keys()
                    else default_destination_table,
                    "VIEW_TO_REPLACE": task_info["view_to_replace"]
                    if "view_to_replace" in task_info.keys()
                    else default_view_to_replace,
                    "XCOM_PUSH": "False",
                    "WRITE_LOGS": "True",
                    "LOGS_GCP_PROJECT": PROJECT_NAME,
                    "LOGS_JOB_IDENTIFIER": task_info["logs_job_identifier"],
                    "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
                    "WRITE_DISPOSITION": task_info["write_disposition"]
                    if "write_disposition" in task_info.keys()
                    else None,
                    "TYPE_OF_PARTITIONING": task_info["type_of_partitioning"]
                    if "type_of_partitioning" in task_info.keys()
                    else None,
                    "CLUSTERING_FIELDS": task_info["clustering_fields"]
                    if "clustering_fields" in task_info.keys()
                    else None,
                    "PARTITION_COLUMN": task_info["partition_column"]
                    if "partition_column" in task_info.keys()
                    else None,
                    "RANGE_START": task_info["range_start"]
                    if "range_start" in task_info.keys()
                    else None,
                    "RANGE_END": task_info["range_end"]
                    if "range_end" in task_info.keys()
                    else None,
                    "RANGE_INTERVAL": task_info["range_interval"]
                    if "range_interval" in task_info.keys()
                    else None,
                },
                dag=dag,
            )

            # Salving the task object to create the dependencies at the next step
            rambo_tasks_dict[task_info["task_id"]] = query_task

        # Logic for sending the tables back to SQL Server using 'load-from-bq-to-ss' operator
        if (
            task_info["send_back_to_ss"]
            if "send_back_to_ss" in task_info.keys()
            else False
        ):
            # For tasks created manually being sent back to SQL Server
            if "table_name" in task_info.keys():
                default_destination_table = f"{DATA_PROJECT_NAME}.{task_info['bq_version_dataset']}.{task_info['table_name']}__{TABLE_SUFFIX}"

            destination_table = (
                task_info["destination_table"]
                if "destination_table" in task_info.keys()
                else default_destination_table
            )

            # We need to get everything after the second '.' so we don't split the '.' on
            # the jinja_template we use to get the table_suffix ( dag_run.conf['table_suffix'] )
            bq_table_name = ".".join(destination_table.split(".")[2:])

            if "column_mapping_file" in task_info.keys():
                mapping_as_string = open(
                    task_info["sql_folder"] + task_info["column_mapping_file"]
                )
                task_info["column_mapping"] = mapping_as_string.read()

            if "number_of_partitions" not in task_info.keys():
                transfer_table_back_to_ss = GKEStartPodOperator(
                    task_id=task_info["back_to_ss_task_id"],
                    name=task_info["back_to_ss_task_id"],
                    image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
                    secrets=[secret_db_username, secret_db_password],
                    execution_timeout=task_info["transfer_execution_timeout"]
                    if "transfer_execution_timeout" in task_info.keys()
                    else timedelta(hours=2),
                    arguments=["python3", "main.py", "load-from-bq-to-ss"],
                    resources=k8s.V1ResourceRequirements(
                        requests={"cpu": "2000m", "memory": "2Gi"}
                    ),
                    # The priority for RDO tables is 9, and for MTD tables is 10. If the parameter is not set, 9 is the defalt
                    priority_weight=task_info["priority_weight_transfer"]
                    if "priority_weight_transfer" in task_info.keys()
                    else 9,
                    pool=ss_import_pool_name,
                    env_vars={
                        "GCP_PROJECT": DATA_PROJECT_NAME,
                        "BQ_DATASET": task_info["bq_version_dataset"],
                        "BQ_TABLE": bq_table_name,
                        "DB_SERVER": GETL01_SQL_SERVER,
                        "DB_NAME": "ras_DataMart_Analytics_Cloud",
                        "TABLE_NAME": task_info["sql_server_table_name"],
                        "WRITE_MODE": "overwrite",
                        "GCS_BUCKET_NAME": BUCKET_NAME,
                        "CHUNKSIZE": task_info["chunksize"]
                        if "chunksize" in task_info.keys()
                        else "100000",
                        "COLUMN_MAPPING": task_info["column_mapping"],
                        "PARSE_DTYPES": task_info["parse_dtypes"]
                        if "parse_dtypes" in task_info.keys()
                        else "False",
                    },
                    dag=dag,
                )
                # The dummy task end_task won't depend on the Back to SS tasks (as the BQ processes can go on)
                # But it will be a dependency of the end_transfers_task
                (
                    rambo_tasks_dict[task_info["task_id"]]
                    >> transfer_table_back_to_ss
                    >> rambo_tasks_dict[task_info["end_transfers_task"]]
                )
                # I'll only transfer when receiving the transfer_to_cloud_db = True
                run_transfers_to_cloud_db >> transfer_table_back_to_ss

            else:
                # there is partition_ammount on the keys, so I'll first partition the table
                partition_task_id = f"partition-bq-table-{task_info['table_name']}"
                partition_bq_table = GKEStartPodOperator(
                    task_id=partition_task_id,
                    name=partition_task_id,
                    image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
                    arguments=["python3", "main.py", "execute-bigquery"],
                    do_xcom_push=False,
                    resources=RESOURCES_SMALL_TASK,
                    env_vars={
                        "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                        "QUERY": open(
                            PUSH_TO_PROD_FOLDER
                            + "push_to_prod_preload/partition_bq_tables_template.sql"
                        )
                        .read()
                        .replace(
                            "{NUMBER_OF_PARTITIONS}",
                            str(task_info["number_of_partitions"]),
                        )
                        .replace(
                            "{PARTITION_COLUMN}",
                            task_info["partition_column"],
                        )
                        .replace(
                            "{TABLE_NAME}",
                            task_info["bq_version_dataset"] + "." + bq_table_name,
                        ),
                        "XCOM_PUSH": "False",
                        "WRITE_LOGS": "True",
                        "LOGS_GCP_PROJECT": PROJECT_NAME,
                        "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/PUSH_TO_PROD/{task_info['table_name']}",
                        "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
                        "WRITE_DISPOSITION": None,
                    },
                    dag=dag,
                )

                create_stg_table = GKEStartPodOperator(
                    task_id=f"create-stg-table-{task_info['table_name']}",
                    name=f"create-stg-table-{task_info['table_name']}",
                    image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
                    secrets=[secret_db_username, secret_db_password],
                    arguments=["python3", "main.py", "create-stg-table"],
                    resources=RESOURCES_SMALL_TASK,
                    # Same priority as other RAMBO Push to prod tasks
                    priority_weight=task_info["priority_weight_transfer"]
                    if "priority_weight_transfer" in task_info.keys()
                    else 14,
                    env_vars={
                        "DB_SERVER": GETL01_SQL_SERVER,
                        "DB_NAME": "ras_DataMart_Analytics_Cloud",
                        "TABLE_NAME": task_info["sql_server_table_name"],
                    },
                    dag=dag,
                )
                # Used on some dependencies
                rambo_tasks_dict[f"create-stg-table-{bq_table_name}"] = create_stg_table

                staging_table_name = task_info["sql_server_table_name"] + "_STG"

                partition_bq_table >> create_stg_table
                recreate_stg_table_indexes = GKEStartPodOperator(
                    task_id=f"recreate-stg-table-indexes-{task_info['table_name']}",
                    name=f"recreate-stg-table-indexes-{task_info['table_name']}",
                    image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
                    secrets=[secret_db_username, secret_db_password],
                    arguments=["python3", "main.py", "recreate-stg-table-indexes"],
                    resources=RESOURCES_SMALL_TASK,
                    # Same priority as other RAMBO Push to prod tasks
                    priority_weight=task_info["priority_weight_transfer"]
                    if "priority_weight_transfer" in task_info.keys()
                    else 14,
                    env_vars={
                        "DB_SERVER": GETL01_SQL_SERVER,
                        "DB_NAME": "ras_DataMart_Analytics_Cloud",
                        "TABLE_NAME": task_info["sql_server_table_name"],
                        "DO_QA_ROW_COUNT": "True",
                        "QA_GCP_PROJECT": DATA_PROJECT_NAME,
                        "BQ_QA_TABLE": f'{DATA_PROJECT_NAME}.{task_info["bq_version_dataset"]}.{bq_table_name}',
                        "SS_QA_TABLE": f"ras_DataMart_Analytics_Cloud.{staging_table_name}",
                        "REPLACE_TABLE": "True",
                    },
                    dag=dag,
                )

                for partition_id in range(0, task_info["number_of_partitions"]):
                    delete_before_insert_query = (
                        open(
                            PUSH_TO_PROD_FOLDER
                            + "push_to_prod_preload/delete_partition_before_inserting_template.sql"
                        )
                        .read()
                        .replace("{DB_NAME}", "ras_DataMart_Analytics_Cloud")
                        .replace("{TABLE_NAME}", staging_table_name)
                        .replace("{PARTITION_ID}", str(partition_id))
                        .replace(
                            "{NUMBER_OF_PARTITIONS}",
                            str(task_info["number_of_partitions"]),
                        )
                        .replace(
                            "{PARTITION_COLUMN}",
                            task_info["partition_column_ss"],
                        )
                    )

                    qa_row_count_query = (
                        open(
                            PUSH_TO_PROD_FOLDER
                            + "push_to_prod_preload/ss_qa_partition_override_template.sql"
                        )
                        .read()
                        .replace("{DB_NAME}", "ras_DataMart_Analytics_Cloud")
                        .replace("{TABLE_NAME}", staging_table_name)
                        .replace("{PARTITION_ID}", str(partition_id))
                        .replace(
                            "{NUMBER_OF_PARTITIONS}",
                            str(task_info["number_of_partitions"]),
                        )
                        .replace(
                            "{PARTITION_COLUMN}",
                            task_info["partition_column_ss"],
                        )
                    )

                    # I'll execute the transfers in 'append' mode for each partition
                    transfer_task_id = (
                        f"transfer-{task_info['table_name']}-p{partition_id}-to-prod-ss"
                    )
                    transfer_table_to_prod = GKEStartPodOperator(
                        task_id=transfer_task_id,
                        name=transfer_task_id,
                        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
                        secrets=[secret_db_username, secret_db_password],
                        arguments=["python3", "main.py", "load-from-bq-to-ss"],
                        resources=k8s.V1ResourceRequirements(
                            requests={"cpu": "2000m", "memory": "2Gi"}
                        ),
                        pool=ss_import_pool_name,
                        execution_timeout=task_info["transfer_execution_timeout"]
                        if "transfer_execution_timeout" in task_info.keys()
                        else timedelta(hours=1),
                        # Same priority as other RAMBO Push to prod tasks
                        priority_weight=task_info["priority_weight_transfer"]
                        if "priority_weight_transfer" in task_info.keys()
                        else 14,
                        env_vars={
                            "GCP_PROJECT": DATA_PROJECT_NAME,
                            "BQ_DATASET": task_info["bq_version_dataset"],
                            "BQ_TABLE": bq_table_name + f"_p{partition_id}",
                            "DB_SERVER": GETL01_SQL_SERVER,
                            "DB_NAME": "ras_DataMart_Analytics_Cloud",
                            "TABLE_NAME": staging_table_name,
                            "WRITE_MODE": "append",
                            "DO_QA_ROW_COUNT": "True",
                            "SS_QA_QUERY_OVERRIDE": qa_row_count_query,
                            "EXECUTE_QUERY_BEFORE": delete_before_insert_query,
                            "GCS_BUCKET_NAME": BUCKET_NAME,
                            "CHUNKSIZE": task_info["chunksize"]
                            if "chunksize" in task_info.keys()
                            else "100000",
                            "COLUMN_MAPPING": task_info["column_mapping"],
                            "PARSE_DTYPES": task_info["parse_dtypes"]
                            if "parse_dtypes" in task_info.keys()
                            else "False",
                        },
                        dag=dag,
                    )
                    (
                        create_stg_table
                        >> transfer_table_to_prod
                        >> recreate_stg_table_indexes
                    )
                    rambo_tasks_dict[transfer_task_id] = transfer_table_to_prod

                rambo_tasks_dict[task_info["task_id"]] >> partition_bq_table
                (
                    recreate_stg_table_indexes
                    >> rambo_tasks_dict[task_info["end_transfers_task"]]
                )

                # I'll only transfer when receiving the transfer_to_cloud_db = True
                run_transfers_to_cloud_db >> partition_bq_table

    # Looping again to set the dependencies
    for task_info in parametrized_rdo_publish_tasks:
        # Using the task object to set the dependencies
        if "task_dependencies" in task_info.keys():
            for dependency in task_info["task_dependencies"]:
                rambo_tasks_dict[dependency] >> rambo_tasks_dict[task_info["task_id"]]

        # All tasks without dependencies downstream (end leafs)
        # must run before the 'end_task' of that block
        is_end_leaf = True
        for another_task in parametrized_rdo_publish_tasks:
            if "task_dependencies" in another_task.keys():
                for dependency in another_task["task_dependencies"]:
                    if dependency == task_info["task_id"]:
                        is_end_leaf = False
        if is_end_leaf:
            (
                rambo_tasks_dict[task_info["task_id"]]
                >> rambo_tasks_dict[task_info["end_task"]]
            )

    # ---------------------------------------

    (
        finish_publish_sequence_package
        >> reload_rdo_support_tables
        >> populate_rate_client_schema
        >> populate_rdo_sales_rep_branch
        >> rdo_tables_complete
    )
    (
        finish_publish_sequence_package
        >> [
            populate_rev_dist_pt_client_schema,
            populate_fleet_client_schema,
            populate_fleet_client_schema_wk_xyz,
            populate_fleet_client_schema_wk,
            populate_fleet_client_schema_eqid,
            populate_rate_change_client_schema,
            populate_preload_rate_change_bench_data,
            populate_rate_change_bench_data_xuri,
        ]
        >> rdo_tables_complete
    )

    (
        populate_preload_rate_change_bench_data
        >> add_rate_change_bench_data_spot
        >> add_rate_change_bench_data_core
    )
    (
        [
            add_rate_change_bench_data_core,
            populate_rate_change_client_schema,
            rambo_tasks_dict["pre-load-rate-bench-data"],
        ]
        >> carry_forward_rate_change_bench_data
        >> rdo_tables_complete
    )
    (
        [
            finish_publish_sequence_package,
            rambo_tasks_dict["rate-order-coverage"],
            rambo_tasks_dict["pre-load-client-market-pts"],
            carry_forward_rate_change_bench_data,
        ]
        >> populate_rate_change_client_bench_data
        >> rdo_tables_complete
    )
    [
        rambo_tasks_dict["rate-order-coverage"],
        rambo_tasks_dict["pre-load-client-market-pts"],
    ] >> populate_rate_change_bench_data_xuri
    populate_rate_change_client_schema >> [
        populate_ctrl_rate_change_category,
        transfer_rate_change_client_schema,
    ]
    (
        [
            finish_publish_sequence_package,
            rambo_tasks_dict["rate-order-coverage"],
            rambo_tasks_dict["pre-load-client-market-pts"],
        ]
        >> create_rdo_bench_rev_dist_pt
        >> populate_rev_dist_pt_client_bench_data
    )
    (
        finish_publish_sequence_package
        >> create_fn_rate_change_company
        >> populate_ctrl_rate_change
        >> transfer_ctrl_rate_change
    )
    (
        populate_rate_change_client_bench_data
        >> populate_ctrl_rate_change
        >> rdo_tables_complete
    )
    # set transfer to Cloud Database
    reload_rdo_support_tables >> [
        transfer_rdo_client_geography,
        transfer_rdo_client_tx_attribute,
        transfer_rdo_dim_customer,
        transfer_rdo_client_month,
        transfer_rdo_dim_sales_rep,
    ]

    populate_rate_change_client_bench_data >> transfer_rate_change_client_bench_data
    populate_rev_dist_pt_client_bench_data >> transfer_rev_dist_pt_client_bench_data
    populate_rev_dist_pt_client_schema >> transfer_rev_dist_pt_client_schema
    populate_rate_change_bench_data_xuri >> transfer_rate_change_bench_data_xuri
    populate_ctrl_rate_change_category >> transfer_ctrl_rate_change_category
    populate_rate_client_schema >> partition_bq_rate_client_schema
    populate_rdo_sales_rep_branch >> transfer_rdo_sales_rep_branch_xref
    populate_fleet_client_schema >> transfer_fleet_client_schema
    (
        populate_fleet_client_schema
        >> populate_fleet_client_schema_wk_xyz
        >> transfer_fleet_client_schema_wk_xyz
    )
    populate_fleet_client_schema_wk >> partition_bq_fleet_client_schema_wk

    # I'll only transfer when receiving the transfer_to_cloud_db = True
    (
        run_transfers_to_cloud_db
        >> [
            transfer_rdo_dim_sales_rep,
            transfer_rate_change_client_schema,
            transfer_ctrl_rate_change,
            transfer_rdo_client_geography,
            transfer_rdo_client_tx_attribute,
            transfer_rdo_dim_customer,
            transfer_rdo_client_month,
            transfer_rate_change_client_bench_data,
            transfer_rev_dist_pt_client_bench_data,
            transfer_rev_dist_pt_client_schema,
            transfer_rate_change_bench_data_xuri,
            transfer_ctrl_rate_change_category,
            transfer_rdo_sales_rep_branch_xref,
            transfer_fleet_client_schema_wk_xyz,
            partition_bq_fleet_client_schema_wk,
            partition_bq_rate_client_schema,
            transfer_fleet_client_schema,
        ]
        >> rdo_tables_transfers_complete
    )

    # Retry tasks block
    def check_tasks_status(tasks_id_check, task_success, task_failed, dag_run):
        """
        Method to verify the status of the ingestion qa and then proceed to the restart
        step but only in 1 ocassion. If the second there are failed tasks then this
        method will fail as well.
        """
        for task_id in tasks_id_check:
            upstream_task_state = dag_run.get_task_instance(task_id).state

            print(f"Task {task_id} status: {upstream_task_state}")
            if upstream_task_state != State.SUCCESS:
                #  we ensure that the failed (restart tasks) gets executed once and then we fail the dag.
                failed_task_try = dag_run.get_task_instance(task_failed).try_number
                if failed_task_try > 3:
                    print(f"Failed task: {task_failed} can only run once.")
                    raise AirflowFailException(
                        f"Failing this task since not all required tasks were completed."
                    )
                else:
                    return task_failed

        return task_success

    # operator to verify if the alerts steps where success otherwise do the retry once.
    check_alert_tasks_status = BranchPythonOperator(
        dag=dag,
        task_id="check-alert-tasks-status",
        python_callable=check_tasks_status,
        op_args=[
            alerts_task_ids,
            "rdo-tables.rdo-tables-complete",
            "rdo-tables.clear-alerts-from-start",
        ],
        trigger_rule="all_done",
    )

    # operator that uses Airflow CLI from the Airflow worker to connect to the db and restart the tasks.
    clear_alerts_tasks = BashOperator(
        dag=dag,
        task_id="clear-alerts-from-start",
        retries=0,
        bash_command="airflow tasks clear -y -d --start-date {{ data_interval_start }} --end-date {{ data_interval_end }} -t rdo-tables.create-alerts-table "
        + f"rambo-enhancement-v{MAJOR_VERSION}.{MINOR_VERSION}",
    )
    # rambo_tasks_dict["update-alert-id"] >> check_alert_tasks_status >> [rdo_tables_complete, clear_alerts_tasks]
    (
        qa_duplicate_alerts
        >> check_alert_tasks_status
        >> [rdo_tables_complete, clear_alerts_tasks]
    )

# end of pipeline logic
update_if_etl_failed = GKEStartPodOperator(
    task_id="failure-update",
    name="failure-update",
    dag=dag,
    execution_timeout=dt.timedelta(minutes=5),
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    secrets=[secret_db_username, secret_db_password],
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "rambo",
        "STATUS_VALUE": "FAILED",
        "PK": "{{ dag_run.conf['rambo_pk'] }}",
        "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
        "PARENT": "{{ dag_run.conf['parent_batch'] }}",
    },
    trigger_rule="one_failed",
)

command = """gcloud logging write rambo_pipeline '{ "type":"RAMBO_PIPELINE/RAMBO_ENHANCEMENT", "version":"1.0.0", """
command += """ "payload":{ "event_group":"NOTIFY_PAGER_DUTY", "event":"ERROR", "event_type": "END", "additional_info":"", "duration_sec": null, "message":"Error found in some tasks of the dag", "dag_execution_date":"""
command += '"' + "{{ ts }}" + '"' + "} }'"
command += (
    f""" --payload-type=json --project="{PROJECT_NAME}" --severity=ERROR || true"""
)

log_error = BashOperator(
    task_id="log-error", bash_command=command, trigger_rule="one_failed", dag=dag
)

rambo_enhancement_finished = DummyOperator(
    task_id="rambo-enhancement-finished", dag=dag
)

transfer_fmrr_bench_complete >> [
    update_fmrr_bench_record,
    update_fmrr_bench_record_error,
]

(
    [start_rambo_enhancement, rambo_enhancement_finished]
    >> update_rambo_record_when_clear
    >> ss_done
)


# As soon as the rdo-tables are complete on bigquery, I can start the preload job
insert_preload_record = GKEStartPodOperator(
    task_id="insert-preload-record",
    name="insert-preload-record",
    secrets=[secret_db_username, secret_db_password],
    do_xcom_push=True,
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "push_to_prod_preload",
        "STATUS_VALUE": "SCHEDULED",
        "BATCH": datetime.now(pytz.timezone("America/Los_Angeles")).strftime(
            "%Y%m%dT%H%M%S"
        ),
        "PARENT": TABLE_SUFFIX,
    },
    dag=dag,
)
rdo_tables_complete >> insert_preload_record


# I'll run the Demo Clients after all the bigquery part is done.
# This way, the changes on Demo Clients will only apply to the next run consistently,
# instead of some tables using XYZ on this run and some using the tables
# we were already preparing for the next run.
(
    rdo_tables_complete
    >> populate_xyz_transform
    >> [
        populate_xyz_fleet_metrics,
        populate_xyz_rental_rates,
    ]
    >> inject_abc_small_tools_in_texas
)
(
    [inject_abc_small_tools_in_texas]
    >> inject_hvy_xyz_records
    >> inject_xcn_xyz_records
    >> inject_xhd_xyz_records
    >> inject_flr_xyz_records
)
(
    inject_flr_xyz_records
    >> inject_spr_xyz_records
    >> inject_crr_xyz_records
    >> inject_xuk_xyz_records
)
inject_xuk_xyz_records >> inject_xjp_xyz_records >> inject_xeu_xyz_records >> inject_xau_xyz_records
inject_abc_small_tools_in_texas >> [reload_etc_geomapping,reload_etc_product_types,reload_etc_equipment_multipliers,reload_etc_product_types_swapped,reload_etc_rented_as_mapping,reload_etc_transactions]
[reload_etc_geomapping,reload_etc_product_types,reload_etc_equipment_multipliers,reload_etc_product_types_swapped,reload_etc_rented_as_mapping,reload_etc_transactions] >> inject_etcri_records

# update views at the end so that in the next run we can use the data generated by this run when inserting xyz records
inject_xau_xyz_records >> [update_xyz_fleet_metrics_view, update_xyz_rental_rates_view]
inject_etcri_records >> inject_etc_analytics_01 >> [update_xyz_fleet_metrics_view, update_xyz_rental_rates_view]


monitor_cus_seg_record = GKEStartPodOperator(
    task_id="monitor-cus-seg-record",
    name="monitor-cus-seg-record",
    resources=RESOURCES_SMALL_TASK,
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("sql-record-status", GCR_REGISTRY),
    arguments=["python3", "main.py", "check-for-record-status"],
    execution_timeout=timedelta(hours=4),
    env_vars={
        "QUERY": f"SELECT status FROM dbo.benchmark_etl_run WHERE batch = '{TABLE_SUFFIX}' AND pipeline = 'cus_seg'",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DATABASE_NAME": "ras_DataMart_Computation",
        "EXPECTED_VALUE": "DONE",
        "FAILURE_VALUE": "FAILED",
        "REFRESH_INTERVAL": "30",
    },
    dag=dag,
)
monitor_fmrr_base_record = GKEStartPodOperator(
    task_id="monitor-fmrr-base-record",
    name="monitor-fmrr-base-record",
    resources=RESOURCES_SMALL_TASK,
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("sql-record-status", GCR_REGISTRY),
    arguments=["python3", "main.py", "check-for-record-status"],
    execution_timeout=timedelta(hours=4),
    env_vars={
        "QUERY": f"SELECT status FROM dbo.benchmark_etl_run WHERE batch = '{TABLE_SUFFIX}' AND pipeline = 'fmrr_base'",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DATABASE_NAME": "ras_DataMart_Computation",
        "EXPECTED_VALUE": "DONE",
        "FAILURE_VALUE": "FAILED",
        "REFRESH_INTERVAL": "30",
    },
    dag=dag,
)
monitor_bench_rates_record = GKEStartPodOperator(
    task_id="monitor-bench-rates-record",
    name="monitor-bench-rates-record",
    resources=RESOURCES_SMALL_TASK,
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("sql-record-status", GCR_REGISTRY),
    arguments=["python3", "main.py", "check-for-record-status"],
    execution_timeout=timedelta(hours=4),
    env_vars={
        "QUERY": f"SELECT status FROM dbo.benchmark_etl_run WHERE batch = '{TABLE_SUFFIX}' AND pipeline = 'bench_rates'",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DATABASE_NAME": "ras_DataMart_Computation",
        "EXPECTED_VALUE": "DONE",
        "FAILURE_VALUE": "FAILED",
        "REFRESH_INTERVAL": "30",
    },
    dag=dag,
)
[
    update_fmrr_bench_record,
    update_ctrl_tables_record,
    update_rdo_tables_record,
    monitor_bench_rates_record,
    monitor_fmrr_base_record,
    monitor_cus_seg_record,
    update_xyz_fleet_metrics_view,
    update_xyz_rental_rates_view,
] >> rambo_enhancement_finished

# Check if indexes are the same between the staging tables and
# the stored list on dbo.RDO_Indexes_List table
check_if_indexes_exist = GKEStartPodOperator(
    task_id="check-if-indexes-exist",
    name="check-if-indexes-exist",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-check-indexes", GCR_REGISTRY),
    arguments=["python3", "main.py", "check-if-indexes-exist-cloud-db"],
    execution_timeout=dt.timedelta(minutes=10),
    retry_delay=dt.timedelta(seconds=10),
    env_vars={
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Analytics_Cloud",
    },
    dag=dag,
)
(
    rambo_enhancement_finished
    >> check_if_indexes_exist
    >> ss_done
    >> [update_if_etl_failed, log_error]
)

# This dummy will be an 'end leaf' of the dag, which will fail the dag if there is any error.
fail_dag_if_error = DummyOperator(task_id="fail-dag-if-error", dag=dag)
ss_done >> fail_dag_if_error


#################################################
##### Update status when skipping transfers #####
#################################################
update_fmrr_bench_record_skip_transfers = GKEStartPodOperator(
    task_id="update-fmrr-bench-record-after-skipping-transfers",
    name="update-fmrr-bench-record-after-skipping-transfers",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "fmrr_bench",
        "STATUS_VALUE": "DONE",
        "PK": "{{ dag_run.conf['fmrr_bench_pk'] }}",
        "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
        "PARENT": "{{ dag_run.conf['parent_batch'] }}",
    },
    dag=dag,
)
update_ctrl_tables_record_skip_transfers = GKEStartPodOperator(
    task_id="update-ctrl-tables-record-after-skipping-transfers",
    name="update-ctrl-tables-record-after-skipping-transfers",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "ctrl_tables",
        "STATUS_VALUE": "DONE",
        "PK": '{{ task_instance.xcom_pull(task_ids="ctrl-tables.insert-ctrl-tables-record", key="return_value")["pk"] }}',
        "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
        "PARENT": "{{ dag_run.conf['parent_batch'] }}",
    },
    dag=dag,
)
update_rdo_tables_record_skip_transfers = GKEStartPodOperator(
    task_id="update-rdo-tables-record-after-skipping-transfers",
    name="update-rdo-tables-record-after-skipping-transfers",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "rdo_tables",
        "STATUS_VALUE": "DONE",
        "PK": '{{ task_instance.xcom_pull(task_ids="rdo-tables.insert-rdo-tables-record", key="return_value")["pk"] }}',
        "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
        "PARENT": "{{ dag_run.conf['parent_batch'] }}",
    },
    dag=dag,
)


# If noco run, don't run update
def short_circuit_done_update_fn(**kwargs):
    noco_run = kwargs["noco_run"]
    if noco_run == "True":
        return False
    else:
        return True


short_circuit_done_update = ShortCircuitOperator(
    task_id="short-circuit-done-update",
    python_callable=short_circuit_done_update_fn,
    dag=dag,
    op_kwargs={"noco_run": "{{True if 'noco_run' in dag_run.conf.keys() else False}}"},
)

ss_done_skip_transfers = GKEStartPodOperator(
    task_id="ss-done-after-skipping-transfers",
    name="ss-done-after-skipping-transfers",
    dag=dag,
    execution_timeout=dt.timedelta(minutes=5),
    image=get_full_image_name("rental-metric-benchmark-rambo", GCR_REGISTRY),
    arguments=["./script/sqlsvr_done.sh"],
    secrets=[secret_db_username, secret_db_password],
    env_vars={
        "PROJECT": DATA_PROJECT_NAME,
        "SQLSVR_HOSTNAME": GETL01_SQL_SERVER,
        "PK": "{{ dag_run.conf['rambo_pk'] }}",
        "BATCH": "{{ dag_run.conf['pipeline_batch'] }}",
        "LOGGING_PROJECT_ID": LOGGING_GCP_PROJECT,
        "DAG_EXECUTION_DATE": "{{ ts }}",
    },
    on_success_callback=BENCHMARK_SUCCESS,
    on_failure_callback=task_fail_slack_alert,
)
short_circuit_done_update >> ss_done_skip_transfers

# Those 4 tasks will run after we insert the preload record (at the end of the bigquery process),
# if the task skip_transfers_to_cloud_db runs, and are skipped otherwise
skip_transfers_to_cloud_db >> [
    update_fmrr_bench_record_skip_transfers,
    update_ctrl_tables_record_skip_transfers,
    update_rdo_tables_record_skip_transfers,
    short_circuit_done_update,
]
insert_preload_record >> [
    update_fmrr_bench_record_skip_transfers,
    update_ctrl_tables_record_skip_transfers,
    update_rdo_tables_record_skip_transfers,
    short_circuit_done_update,
]
