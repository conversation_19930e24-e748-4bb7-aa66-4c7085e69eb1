import datetime as dt
import json
import logging
import os
import re

from airflow import DAG
from airflow.providers.google.cloud.operators.kubernetes_engine import (
    GKEStartPodOperator,
)
from airflow.operators.dagrun_operator import TriggerDagRunOperator
from airflow.operators.bash_operator import <PERSON><PERSON><PERSON><PERSON>ator

from datetime import timed<PERSON><PERSON>, datetime
from airflow.kubernetes.secret import Secret

from shared_libs.slack_callback import task_fail_slack_alert
from shared_libs.pagerduty_callback import dag_fail_pagerduty_alert
from shared_libs.image_versions import get_gcr_registry, get_full_image_name
from shared_libs.kubernetes import (
    standard_tolerations,
    standard_affinity,
    get_image_pull_policy,
)
from airflow.hooks.base_hook import BaseHook
from airflow.contrib.operators.slack_webhook_operator import SlackWebhookOperator
from airflow.operators.dummy_operator import DummyOperator
from shared_libs.default_args import get_default_args
from airflow.models import Variable
from airflow.utils.task_group import TaskGroup
from kubernetes.client import models as k8s


def create_partition_intervals(partition_amount=10):
    # it is 4000 because that is the maximum number of partitions in BigQuery
    upper_bound = 4000
    lower_bound = 0

    result = []
    partition_size = int(upper_bound / partition_amount)
    lower_bound_item = lower_bound
    upper_bound_item = lower_bound_item + partition_size
    while upper_bound_item < upper_bound:
        result.append((lower_bound_item, upper_bound_item))
        lower_bound_item = upper_bound_item
        lower_bound_item += 1
        upper_bound_item += partition_size

        # just to guarantee the last interval is going to be set.
        if upper_bound_item >= upper_bound:
            upper_bound_item = upper_bound
            result.append((lower_bound_item, upper_bound_item))

    return result

 
# CONSTANTS
ENVIRONMENT = os.environ.get("COMPOSER_ENVIRONMENT", "dev")
CLUSTER_ZONE = "us-central1-b"
IMAGE_PULL_POLICY = get_image_pull_policy(ENVIRONMENT)
GCR_REGISTRY = get_gcr_registry(ENVIRONMENT)
RESOURCES = k8s.V1ResourceRequirements(
    requests={"cpu": "500m", "memory": "1.0Gi"},
)
MAJOR_VERSION = "1"
MINOR_VERSION = "1"
TABLE_SUFFIX = "{{dag_run.conf['table_suffix']}}"
RESOURCES_SMALL_TASK = k8s.V1ResourceRequirements(
    requests={"cpu": "500m", "memory": "1.0Gi"},
)
QA_SLACK_CHANNEL = '#data-analytics-alerts'

if ENVIRONMENT not in ['prod','dr']:
    DB_SECRET_NAME = "de-rambo-export-dev"
    BUCKET_NAME = "analytics-data-dev-62c5a2-rental-metrics-benchmark-aggs"
    PROJECT_NAME = "management-dev-d6ba4d"
    DATA_PROJECT_NAME = "analytics-data-dev-62c5a2"
    CLUSTER_NAME = "composer-jobs-v3-dev"
    AGGS_DESTINATION_DATASET = "rental_metrics_aggs_dev"
    LOGGING_GCP_PROJECT = "management-dev-d6ba4d"
    PAGERDUTY_NOTIFY = False
    ss_import_pool_name = "gdevetl01_bcp_export"

    # RATES COMPUTE PIPELINE CONSTANTS
    RATES_STAGE_DATASET_NAME = "rental_benchmarks_stage_dev"
    RATES_DATASET_NAME = "rental_benchmarks_dev"
    RATES_GOOGLE_GCS_BUCKET = "analytics-data-dev-62c5a2-analytics-benchmark-dev-v2"
    partition_intervals = create_partition_intervals(80)
    REPRESENTATION_JS_PATH: str = (
        "/usr/local/airflow/dags/constants/representation.json"
    )
    GETL01_SQL_SERVER = "gdevetl01.rasgcp.net"
    DAG_BASE_SQL_FOLDER = (
        "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/"
    )

    AGGS_DESTINATION_DATASET = "rental_metrics_aggs_dev"
    AGGS_STAGE_DATASET_NAME = "rental_metrics_aggs_stage_dev"
    DIM_FM_RR_DESTINATION_DATASET = "dim_fm_rr_dev"
    DIM_FM_RR_STAGE_DATASET = "dim_fm_rr_stage_dev"
    DIM_FM_RR_SOURCE_DATASET = "dim_fm_rr_version_dev"
    DIM_FMRR_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/dim_fm_rr_queries/"
    FMRR_BENCH_SEASONALITY_TABLES_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/fmrr_bench_seasonality_tables/"
    DAG_BASE_SQL_FOLDER = (
        "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/"
    )

    if ENVIRONMENT == "local":
        partition_intervals = create_partition_intervals(80)
        DAG_BASE_SQL_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/"

        DIM_FMRR_SQL_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/dim_fm_rr_queries/"
        FMRR_BENCH_SEASONALITY_TABLES_SQL_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/fmrr_bench_seasonality_tables/"

elif ENVIRONMENT == "dr":
    DB_SECRET_NAME = "de-rambo-export-prod"
    BUCKET_NAME = "analytics-data-prod-1e04f6-rental-metrics-benchmark-aggs"
    PROJECT_NAME = "dr-prod-ca5e7f"
    LOGGING_GCP_PROJECT = "dr-prod-ca5e7f"
    DATA_PROJECT_NAME = "dr-prod-ca5e7f"
    CLUSTER_NAME = "composer-jobs-v3-dev"
    AGGS_DESTINATION_DATASET = "rental_metrics_aggs"
    SERVER_DOMAIN = "RASGCP.NET"
    PAGERDUTY_NOTIFY = True
    ss_import_pool_name = "getl01_bcp_export"

    # RATES COMPUTE PIPELINE CONSTANTS
    RATES_STAGE_DATASET_NAME = "rental_benchmarks_stage"
    RATES_DATASET_NAME = "rental_benchmarks"
    RATES_GOOGLE_GCS_BUCKET = "dr-prod-ca5e7f-analytics-benchmark-rates-v2"

    partition_intervals = create_partition_intervals(80)

    GETL01_SQL_SERVER = "getl01.rasgcp.net"

    AGGS_DESTINATION_DATASET = "rental_metrics_aggs"
    AGGS_STAGE_DATASET_NAME = "rental_metrics_aggs_stage"
    DIM_FM_RR_DESTINATION_DATASET = "dim_fm_rr"
    DIM_FM_RR_STAGE_DATASET = "dim_fm_rr_stage"
    DIM_FM_RR_SOURCE_DATASET = "dim_fm_rr_version"
    DIM_FMRR_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/dim_fm_rr_queries/"
    FMRR_BENCH_SEASONALITY_TABLES_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/fmrr_bench_seasonality_tables/"
    DAG_BASE_SQL_FOLDER = (
        "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/"
    )

else:
    DB_SECRET_NAME = "de-rambo-export-prod"
    BUCKET_NAME = "analytics-data-prod-1e04f6-rental-metrics-benchmark-aggs"
    PROJECT_NAME = "management-prod-837a97"
    LOGGING_GCP_PROJECT = "management-prod-837a97"
    DATA_PROJECT_NAME = "analytics-data-prod-1e04f6"
    CLUSTER_NAME = "composer-jobs-v3-prod"
    AGGS_DESTINATION_DATASET = "rental_metrics_aggs"
    SERVER_DOMAIN = "RASGCP.NET"
    PAGERDUTY_NOTIFY = True
    ss_import_pool_name = "getl01_bcp_export"

    # RATES COMPUTE PIPELINE CONSTANTS
    RATES_STAGE_DATASET_NAME = "rental_benchmarks_stage"
    RATES_DATASET_NAME = "rental_benchmarks"
    RATES_GOOGLE_GCS_BUCKET = "analytics-data-prod-1e04f6-analytics-benchmark-rates-v2"

    partition_intervals = create_partition_intervals(80)

    GETL01_SQL_SERVER = "getl01.rasgcp.net"

    AGGS_DESTINATION_DATASET = "rental_metrics_aggs"
    AGGS_STAGE_DATASET_NAME = "rental_metrics_aggs_stage"
    DIM_FM_RR_DESTINATION_DATASET = "dim_fm_rr"
    DIM_FM_RR_STAGE_DATASET = "dim_fm_rr_stage"
    DIM_FM_RR_SOURCE_DATASET = "dim_fm_rr_version"
    DIM_FMRR_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/dim_fm_rr_queries/"
    FMRR_BENCH_SEASONALITY_TABLES_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/fmrr_bench_seasonality_tables/"
    DAG_BASE_SQL_FOLDER = (
        "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/"
    )



namespace = "rental-metrics-aggs"
service_account_name = namespace
custom_args = {
    "owner": "<EMAIL>",
    "start_date": datetime(2021, 9, 20),
    "retries": 4,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": task_fail_slack_alert,
    "project_id": PROJECT_NAME,
    "cluster_name": CLUSTER_NAME,
    "tolerations": standard_tolerations,
    "affinity": standard_affinity,
    "image_pull_policy": IMAGE_PULL_POLICY,
    "startup_timeout_seconds": 300,
    "is_delete_operator_pod": True,
    "execution_timeout": timedelta(hours=2),
    "namespace": "rental-metrics-aggs",
    "service_account_name": service_account_name,
    "depends_on_past": False,
    # our goal here is that we know exactly how we want to prioritize the tasks
    "weight_rule": "absolute",
}
default_args = get_default_args(custom_args)

dag = DAG(
    f"rambo-benchmark-v{MAJOR_VERSION}.{MINOR_VERSION}",
    schedule_interval=None,
    default_args=default_args,
    max_active_runs=1,
    catchup=False,
    concurrency=80,
    description="""This dag computes benchmark_rates logic. 
    After benchmark_rates is created in bigquery, it triggers the dag rambo_enhancement.
    But the transfer to SQL Server continues running and this DAG finishes later.""",
    tags=["rambo"],
    on_failure_callback=dag_fail_pagerduty_alert(
        "compute_rental_metrics_aggs_pagerduty_api_key"
    ),
)

# secrets/passwords for the compute benchmark rates tasks
secret_db_username = Secret(
    deploy_type="env", deploy_target="WIN_USER", secret=DB_SECRET_NAME, key="username"
)

secret_db_password = Secret(
    deploy_type="env",
    deploy_target="WIN_PASSWORD",
    secret=DB_SECRET_NAME,
    key="password",
)

finished_rambo_benchmark_dag = DummyOperator(
    task_id="finished-rambo-benchmark-dag", dag=dag
)

get_rambo_previous_version = GKEStartPodOperator(
    task_id="get-rambo-previous-version",
    name="get-rambo-previous-version",
    resources=RESOURCES_SMALL_TASK,
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "get-record-from-query"],
    execution_timeout=timedelta(minutes=5),
    env_vars={
        "QUERY": """SELECT top 1 batch 
                    FROM ras_DataMart_Computation.dbo.benchmark_etl_run WITH (NOLOCK) 
                    WHERE status = 'DONE' and pipeline = 'rambo'
                    ORDER BY pk desc;
        """,
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
    },
    dag=dag,
)
PREVIOUS_TABLE_SUFFIX = '{{task_instance.xcom_pull(task_ids="get-rambo-previous-version")["record_value"]}}'

######################################
###### Compute Benchmark Rates #######
######################################

finished_benchmark_rates_in_bq = DummyOperator(
    task_id="finished-benchmark-rates-in-bq", dag=dag
)

finished_benchmark_rates_transfer_to_ss = DummyOperator(
    task_id="finished-benchmark-rates-transfer-to-ss", dag=dag
)

insert_bench_rates_record = GKEStartPodOperator(
    task_id="insert-bench-rates-record",
    name="insert-bench-rates-record",
    secrets=[secret_db_username, secret_db_password],
    do_xcom_push=True,
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "bench_rates",
        "STATUS_VALUE": "RUNNING",
        "BATCH": TABLE_SUFFIX,
    },
    dag=dag,
)

update_bench_rates_record = GKEStartPodOperator(
    task_id="update-bench-rates-record",
    name="update-bench-rates-record",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "bench_rates",
        "STATUS_VALUE": "DONE",
        "PK": '{{ task_instance.xcom_pull(task_ids="insert-bench-rates-record", key="return_value")["pk"] }}',
        "BATCH": TABLE_SUFFIX,
    },
    dag=dag,
)
update_bench_rates_record_error = GKEStartPodOperator(
    task_id="update-bench-rates-record-error",
    name="update-bench-rates-record-error",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "bench_rates",
        "STATUS_VALUE": "FAILED",
        "PK": '{{ task_instance.xcom_pull(task_ids="insert-bench-rates-record", key="return_value")["pk"] }}',
        "BATCH": TABLE_SUFFIX,
    },
    trigger_rule="one_failed",
    dag=dag,
)

update_bench_rates_record_when_clear = GKEStartPodOperator(
    task_id="update-bench-rates-record-when-clear",
    name="update-bench-rates-record-when-clear",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    trigger_rule="one_success",
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "bench_rates",
        "STATUS_VALUE": "RUNNING",
        "PK": '{{ task_instance.xcom_pull(task_ids="insert-bench-rates-record", key="return_value")["pk"] }}',
        "BATCH": TABLE_SUFFIX,
    },
    dag=dag,
)

with TaskGroup(group_id='compute-benchmark-rates', dag=dag) as compute_benchmark_rates_group:
    """
    For every interval of the partition column _namespace1 we compute the benchmark rates of said partition
    using the operator below.
    """
    for partition_interval in partition_intervals:
        bottom_interval, top_interval = partition_interval

        bq_compute = GKEStartPodOperator(
            task_id="bq-compute-{}-{}".format(bottom_interval, top_interval),
            name="bq-compute-{}-{}".format(bottom_interval, top_interval),
            dag=dag,
            execution_timeout=dt.timedelta(minutes=30),
            image=get_full_image_name("rental-metric-benchmark-rambo", GCR_REGISTRY),
            arguments=["./script/bigquery_compute.sh"],
            secrets=[secret_db_username, secret_db_password],
            pool="bigquery_compute",
            env_vars={
                "PROJECT": DATA_PROJECT_NAME,
                "OUTPUT_BUCKET": RATES_GOOGLE_GCS_BUCKET,
                "SQLSVR_HOSTNAME": GETL01_SQL_SERVER,
                "BATCH": TABLE_SUFFIX,
                "LOGGING_PROJECT_ID": LOGGING_GCP_PROJECT,
                "DAG_EXECUTION_DATE": "{{ ts }}",
                "DATASET": RATES_STAGE_DATASET_NAME,
                "RESULT_DATASET": RATES_DATASET_NAME,
                "AGGS_SOURCE_DATASET": AGGS_DESTINATION_DATASET,
                "START_DT": str(bottom_interval),
                "END_DT": str(top_interval),
            },
            on_failure_callback=task_fail_slack_alert,
        )

        benchmark_rates_qa = GKEStartPodOperator(
            task_id="benchmark-rates-qa-{}-{}".format(bottom_interval, top_interval),
            name="benchmark-rates-qa-{}-{}".format(bottom_interval, top_interval),
            image=get_full_image_name("rambo-data-quality", GCR_REGISTRY),
            priority_weight=3,
            do_xcom_push=False,
            arguments=["python3", "main.py", "run-qa-check"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "BQ_DATASET_NAME": RATES_DATASET_NAME,
                "BQ_TABLE_NAME": "benchmark_rates",
                "START_PARTITION": str(bottom_interval),
                "END_PARTITION": str(top_interval),
            },
            dag=dag,
        )

        insert_bench_rates_record >> bq_compute >> benchmark_rates_qa >> finished_benchmark_rates_in_bq

populate_benchmark_rates_unified = GKEStartPodOperator(
    task_id="populate-benchmark-rates-unified",
    name="populate-benchmark-rates-unified",
    image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
    arguments=["python3", "main.py", "execute-bigquery"],
    do_xcom_push=False,
    resources=RESOURCES_SMALL_TASK,
    env_vars={
        "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        "QUERY": f"SELECT * FROM `{RATES_DATASET_NAME}.benchmark_rates__*`",
        "DESTINATION_TABLE": f"{DATA_PROJECT_NAME}.{RATES_STAGE_DATASET_NAME}.benchmark_rates_unified__{TABLE_SUFFIX}",
        "VIEW_TO_REPLACE": f"{DATA_PROJECT_NAME}.{RATES_DATASET_NAME}.benchmark_rates_unified",
        "XCOM_PUSH": "False",
        "WRITE_LOGS": "True",
        "LOGS_GCP_PROJECT": PROJECT_NAME,
        "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/COMPUTE_RATES/TRANSFER_BENCHMARK_RATES",
        "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        "WRITE_DISPOSITION": "WRITE_TRUNCATE",
    },
    dag=dag,
)

benchmark_rates_partitions = 10
staging_table_name = "dbo.Benchmark_Rates_STG"
partition_bq_benchmark_rates = GKEStartPodOperator(
    task_id="partition-benchmark-rates",
    name="partition-benchmark-rates",
    image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
    arguments=["python3", "main.py", "execute-bigquery"],
    do_xcom_push=False,
    resources=RESOURCES_SMALL_TASK,
    env_vars={
        "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        "QUERY": open(
            DAG_BASE_SQL_FOLDER
            + "push_to_prod/push_to_prod_preload/partition_bq_tables_template.sql"
        )
        .read()
        .replace("{NUMBER_OF_PARTITIONS}",str(benchmark_rates_partitions))
        .replace("{PARTITION_COLUMN}","partition_id")
        .replace("{TABLE_NAME}",f"{RATES_STAGE_DATASET_NAME}.benchmark_rates_unified__{TABLE_SUFFIX}"),
        "XCOM_PUSH": "False",
        "WRITE_LOGS": "True",
        "LOGS_GCP_PROJECT": PROJECT_NAME,
        "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/BENCHMARK/BENCHMARK_RATES",
        "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        "WRITE_DISPOSITION": None,
    },
    dag=dag,
)
create_stg_table = GKEStartPodOperator(
    task_id=f"create-stg-table-benchmark-rates",
    name=f"create-stg-table-benchmark-rates",
    image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
    secrets=[secret_db_username, secret_db_password],
    arguments=["python3", "main.py", "create-stg-table"],
    resources=RESOURCES_SMALL_TASK,
    priority_weight=9,
    env_vars={
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Analytics_Cloud",
        "TABLE_NAME": "dbo.Benchmark_Rates",
    },
    dag=dag,
)
partition_bq_benchmark_rates >> create_stg_table

recreate_stg_table_indexes = GKEStartPodOperator(
    task_id=f"recreate-stg-table-indexes-benchmark-rates",
    name=f"recreate-stg-table-indexes-benchmark-rates",
    image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
    secrets=[secret_db_username, secret_db_password],
    arguments=["python3", "main.py", "recreate-stg-table-indexes"],
    resources=RESOURCES_SMALL_TASK,
    execution_timeout=timedelta(hours=5),
    priority_weight=9,
    env_vars={
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Analytics_Cloud",
        "TABLE_NAME": "dbo.Benchmark_Rates",
        "DO_QA_ROW_COUNT": "True",
        "QA_GCP_PROJECT": DATA_PROJECT_NAME,
        "BQ_QA_TABLE": f'{DATA_PROJECT_NAME}.{RATES_STAGE_DATASET_NAME}.benchmark_rates_unified__{TABLE_SUFFIX}',
        "SS_QA_TABLE": f"ras_DataMart_Analytics_Cloud.{staging_table_name}",
        "REPLACE_TABLE": "True",
    },
    dag=dag,
)
recreate_stg_table_indexes >> finished_benchmark_rates_transfer_to_ss

for partition_id in range(0, benchmark_rates_partitions):
    delete_before_insert_query = (
        open(
            DAG_BASE_SQL_FOLDER
            + "push_to_prod/push_to_prod_preload/delete_partition_before_inserting_template.sql"
        )
        .read()
        .replace("{DB_NAME}", "ras_DataMart_Analytics_Cloud")
        .replace("{TABLE_NAME}", staging_table_name)
        .replace("{PARTITION_ID}", str(partition_id))
        .replace("{NUMBER_OF_PARTITIONS}",str(benchmark_rates_partitions))
        .replace("{PARTITION_COLUMN}","PARTITION_ID")
    )

    qa_row_count_query = (
            open(
                DAG_BASE_SQL_FOLDER
                + "push_to_prod/push_to_prod_preload/ss_qa_partition_override_template.sql"
            )
            .read()
            .replace("{DB_NAME}", "ras_DataMart_Analytics_Cloud")
            .replace("{TABLE_NAME}", staging_table_name)
            .replace("{PARTITION_ID}", str(partition_id))
            .replace("{NUMBER_OF_PARTITIONS}",str(benchmark_rates_partitions))
            .replace("{PARTITION_COLUMN}","PARTITION_ID")
        )

    transfer_benchmark_rates = GKEStartPodOperator(
        task_id=f"transfer-benchmark-rates-p{partition_id}",
        name=f"transfer-benchmark-rates-p{partition_id}",
        priority_weight=13,
        execution_timeout=timedelta(hours=3),
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(requests={"cpu": "3000m", "memory": "2Gi"}),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": RATES_STAGE_DATASET_NAME,
            "BQ_TABLE": f"benchmark_rates_unified__{TABLE_SUFFIX}_p{partition_id}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": staging_table_name,
            "WRITE_MODE": "append",
            "DO_QA_ROW_COUNT": "True",
            "SS_QA_QUERY_OVERRIDE": qa_row_count_query,
            "EXECUTE_QUERY_BEFORE": delete_before_insert_query,
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "PARALLEL_PROCESSES": "3",
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": open(
                DAG_BASE_SQL_FOLDER + "bench_preloads/mapping_benchmark_rates.json"
            ).read(),
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )
    create_stg_table >> transfer_benchmark_rates >> recreate_stg_table_indexes

(
    finished_benchmark_rates_in_bq
    >> populate_benchmark_rates_unified
    >> partition_bq_benchmark_rates
)
   
finished_benchmark_rates_transfer_to_ss >> [
    update_bench_rates_record,
    update_bench_rates_record_error,
]

[
    insert_bench_rates_record,
    finished_benchmark_rates_transfer_to_ss,
] >> update_bench_rates_record_when_clear >> update_bench_rates_record


###############################################################
############    FMRR BENCH AND AUX TABLES    ##################
###############################################################

fmrr_bench_geo_complete = DummyOperator(
    task_id="fmrr-bench-geo-complete", dag=dag
)

insert_fmrr_bench_record = GKEStartPodOperator(
    task_id="insert-fmrr-bench-record",
    name="insert-fmrr-bench-record",
    secrets=[secret_db_username, secret_db_password],
    do_xcom_push=True,
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "fmrr_bench",
        "STATUS_VALUE": "RUNNING",
        "BATCH": TABLE_SUFFIX,
    },
    dag=dag,
)

update_fmrr_bench_record_when_clear = GKEStartPodOperator(
    task_id="update-fmrr-bench-record-when-clear",
    name="update-fmrr-bench-record-when-clear",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    trigger_rule="one_success",
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "fmrr_bench",
        "STATUS_VALUE": "RUNNING",
        "PK": '{{ task_instance.xcom_pull(task_ids="insert-fmrr-bench-record", key="return_value")["pk"] }}',
        "BATCH": TABLE_SUFFIX,
    },
    dag=dag,
)

###############################################################
############ FMRR BENCH - SEASONALITY TABLES ##################
###############################################################

with TaskGroup(group_id='fmrr-bench-seasonality-tables', dag=dag) as seasonality_tables_group:
        
    finished_seasonality_tables_reload = DummyOperator(
        task_id="finished-seasonality-tables-reload", dag=dag
    )
    finished_seasonality_tables_transfer = DummyOperator(
        task_id="finished-seasonality-tables-transfer", dag=dag
    )

    fmrr_bench_cf_tasks = [
        {
            "task_id": "seasonal-util-raw",
            "table_name": "ras_datamart_analytics_reporting_dbo_seasonal_util_raw",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["insert-fmrr-bench-record"],
            "query_file": "seasonal_util_raw.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "RATES_DATASET": RATES_DATASET_NAME,
            },
        },
        {
            "task_id": "seasonal-rate-raw",
            "table_name": "ras_datamart_analytics_reporting_dbo_seasonal_rate_raw",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["insert-fmrr-bench-record"],
            "query_file": "seasonal_rate_raw.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "RATES_DATASET": RATES_DATASET_NAME,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "seasonal-rate-trendline",
            "table_name": "ras_datamart_analytics_reporting_dbo_seasonal_rate_trendline",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["insert-fmrr-bench-record"],
            "query_file": "seasonal_rate_trendline.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "RATES_DATASET": RATES_DATASET_NAME,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "reload-seasonality-table-errors-procedure",
            "table_name": None,
            "task_dependencies": ["insert-fmrr-bench-record"],
            "query_file": "reload_seasonality_table_errors.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "RATES_DATASET": RATES_DATASET_NAME,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
        },
        {
            "task_id": "seasonal-rate-error",
            "table_name": "ras_datamart_analytics_reporting_dbo_seasonal_rate_errors",
            "task_dependencies": ["reload-seasonality-table-errors-procedure"],
            "query_file": "seasonal_rate_errors.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "RATES_DATASET": RATES_DATASET_NAME,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
                "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
                "TABLE_SUFFIX" : TABLE_SUFFIX,
            },
        },
        {
            "task_id": "seasonal-rate-factor",
            "table_name": "ras_datamart_analytics_reporting_dbo_seasonal_rate_factor",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["seasonal-rate-trendline", "seasonal-rate-raw","seasonal-rate-error"],
            "query_file": "seasonal_rate_factor.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "RATES_DATASET": RATES_DATASET_NAME,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-seasonal-rate-factor-to-ss",
            "sql_server_table_name": "dbo.Seasonal_Rate_Factor",
            "column_mapping": """{
                                "geo_level" : "Geo Level",
                                "month" : "Month",
                                "rouse_region" : "Rouse Region",
                                "rouse_district" : "Rouse District",
                                "rouse_market" : "Rouse Market",
                                "rouse_category" : "Rouse Category",
                                "rouse_product_type" : "Rouse Product Type",
                                "factor" : "Factor"  
                            }""",
            "parse_dtypes": "False",
        },
        {
            "task_id": "seasonal-util-factor",
            "table_name": "ras_datamart_analytics_reporting_dbo_seasonal_util_factor",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["seasonal-util-raw"],
            "query_file": "seasonal_util_factor.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "RATES_DATASET": RATES_DATASET_NAME,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-seasonal-util-factor-to-ss",
            "sql_server_table_name": "dbo.Seasonal_Util_Factor",
            "column_mapping": """{
                                "geo_level" : "Geo Level",
                                "fleet_level" : "Fleet Level",
                                "month" : "Month",
                                "rouse_region" : "Rouse Region",
                                "rouse_district" : "Rouse District",
                                "rouse_market" : "Rouse Market",
                                "rouse_category" : "Rouse Category",
                                "factor" : "Factor"  
                            }""",
            "parse_dtypes": "False",
        },
        {
            "task_id": "seasonal-util-factor-monthtodate",
            "table_name": "ras_datamart_analytics_reporting_dbo_seasonal_util_factor_monthtodate",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["seasonal-util-raw"],
            "query_file": "seasonal_util_factor_monthtodate.sql",
            "query_params": {
                "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
                "RATES_DATASET": RATES_DATASET_NAME,
                "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            },
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-seasonal-util-factor-monthtodate-to-ss",
            "sql_server_table_name": "dbo.Seasonal_Util_Factor_MonthToDate",
            "column_mapping": """{
                                "geo_level" : "Geo Level",
                                "fleet_level" : "Fleet Level",
                                "month" : "Month",
                                "rouse_region" : "Rouse Region",
                                "rouse_district" : "Rouse District",
                                "rouse_market" : "Rouse Market",
                                "rouse_category" : "Rouse Category",
                                "factor" : "Factor"  
                            }""",
            "parse_dtypes": "False",
        },
    ]

    fmrr_bench_cf_tasks_dict = {
        "insert-fmrr-bench-record": insert_fmrr_bench_record
        }

    # Loop first to create the tasks
    for task_info in fmrr_bench_cf_tasks:

        if task_info['table_name']: 
            # Default view and default destination tables
            default_view_to_replace = (
                f"{DATA_PROJECT_NAME}.{DIM_FM_RR_DESTINATION_DATASET}.{task_info['table_name']}"
            )
            default_destination_table = f"{DATA_PROJECT_NAME}.{DIM_FM_RR_SOURCE_DATASET}.{task_info['table_name']}__{TABLE_SUFFIX}"
        else: 
            # For procedures, I'll just run the BQ command to create the procedure, without creating a table or a view 
            default_view_to_replace = None
            default_destination_table = None

        # Default log name for FMRR Bench Carry Forward tasks
        log_task_name = task_info["task_id"].replace("-", "_").upper()
        logs_job_identifier = (
            f"RAMBO_PIPELINE/FMRR_BENCH_SEASONALITY_TABLES/{log_task_name}"
        )

        # Open the query file, and replace the query parameters if needed
        sql_file = open(FMRR_BENCH_SEASONALITY_TABLES_SQL_FOLDER + task_info["query_file"])
        sql_as_string = sql_file.read()
        if "query_params" in task_info.keys():
            sql_as_string = sql_as_string.format(**task_info["query_params"])

        # Base Operator 'Execute Bigquery' will do all the work of executing the queries and creating the
        # FMRR Bench Carry Forward tables/views
        query_task = GKEStartPodOperator(
            task_id=task_info["task_id"],
            name=task_info["task_id"],
            image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
            arguments=["python3", "main.py", "execute-bigquery"],
            do_xcom_push=False,
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "QUERY": sql_as_string,
                "DESTINATION_TABLE": task_info["destination_table"]
                if "destination_table" in task_info.keys()
                else default_destination_table,
                "VIEW_TO_REPLACE": task_info["view_to_replace"]
                if "view_to_replace" in task_info.keys()
                else default_view_to_replace,
                "XCOM_PUSH": "False",
                "WRITE_LOGS": "True",
                "LOGS_GCP_PROJECT": PROJECT_NAME,
                "LOGS_JOB_IDENTIFIER": logs_job_identifier,
                "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
                "WRITE_DISPOSITION": task_info["write_disposition"]
                if "write_disposition" in task_info.keys()
                else None,
                "TYPE_OF_PARTITIONING": task_info["type_of_partitioning"]
                if "type_of_partitioning" in task_info.keys()
                else None,
                "CLUSTERING_FIELDS": task_info["clustering_fields"]
                if "clustering_fields" in task_info.keys()
                else None,
                "PARTITION_COLUMN": task_info["partition_column"]
                if "partition_column" in task_info.keys()
                else None,
                "RANGE_START": task_info["range_start"]
                if "range_start" in task_info.keys()
                else None,
                "RANGE_END": task_info["range_end"]
                if "range_end" in task_info.keys()
                else None,
                "RANGE_INTERVAL": task_info["range_interval"]
                if "range_interval" in task_info.keys()
                else None,
            },
            dag=dag,
        )

        # Salving the task object to create the dependencies at the next step
        fmrr_bench_cf_tasks_dict[task_info["task_id"]] = query_task

        # With task groups the full_task_id is task_group_name.task_id (It changes the xcom_Pull)
        full_task_id = f'fmrr-bench-seasonality-tables.{task_info["task_id"]}'

        # Logic for sending the tables back to SQL Server using 'load-from-bq-to-ss' operator
        if task_info["send_back_to_ss"] if "send_back_to_ss" in task_info.keys() else False:
            transfer_table_back_to_ss = GKEStartPodOperator(
                task_id=task_info["back_to_ss_task_id"],
                name=task_info["back_to_ss_task_id"],
                image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
                secrets=[secret_db_username, secret_db_password],
                priority_weight=11,
                execution_timeout=timedelta(minutes=30),
                arguments=["python3", "main.py", "load-from-bq-to-ss"],
                resources=k8s.V1ResourceRequirements(requests={"cpu": "3000m", "memory": "2Gi"}),
                pool=ss_import_pool_name,
                env_vars={
                    "GCP_PROJECT": DATA_PROJECT_NAME,
                    "BQ_DATASET": DIM_FM_RR_SOURCE_DATASET,
                    "BQ_TABLE": task_info["destination_table"].split('.')[-1] + f"__{TABLE_SUFFIX}"
                        if "destination_table" in task_info.keys()
                        else f"{task_info['table_name']}__{TABLE_SUFFIX}",
                    "DB_SERVER": GETL01_SQL_SERVER,
                    "DB_NAME": "ras_DataMart_Analytics_Cloud",
                    "TABLE_NAME": task_info["sql_server_table_name"],
                    "WRITE_MODE": "overwrite",
                    "GCS_BUCKET_NAME": BUCKET_NAME,
                    "PARALLEL_PROCESSES": "2",
                    "CHUNKSIZE": task_info["chunksize"]
                    if "chunksize" in task_info.keys()
                    else "100000",
                    "COLUMN_MAPPING": task_info["column_mapping"],
                    "PARSE_DTYPES": task_info["parse_dtypes"]
                    if "parse_dtypes" in task_info.keys()
                    else "True",
                },
                dag=dag,
            )
            # The finished_seasonality_tables_reload dummy task won't depend on the Back to SS tasks (as the BQ processes can go on)
            query_task >> transfer_table_back_to_ss
            transfer_table_back_to_ss >> finished_seasonality_tables_transfer

    # Looping again to set the dependencies
    for task_info in fmrr_bench_cf_tasks:
        # Using the task object to set the dependencies
        if "task_dependencies" in task_info.keys():
            for dependency in task_info["task_dependencies"]:
                (
                    fmrr_bench_cf_tasks_dict[dependency]
                    >> fmrr_bench_cf_tasks_dict[task_info["task_id"]]
                )

        # All FMRR Bench Carry Forward tasks without dependencies (end leafs of Customer Segmentation)
        # must run before the flag 'finished_seasonality_tables_reload' run
        is_end_leaf = True
        for another_task in fmrr_bench_cf_tasks:
            if "task_dependencies" in another_task.keys():
                for dependency in another_task["task_dependencies"]:
                    if dependency == task_info["task_id"]:
                        is_end_leaf = False
        if is_end_leaf:
            (
                fmrr_bench_cf_tasks_dict[task_info["task_id"]]
                >> finished_seasonality_tables_reload
            )


######################################################
############ FMRR BENCH - GEO MODEL ##################
######################################################
with TaskGroup(group_id='fmrr-bench-geo-model', dag=dag) as fmrr_bench_geo_group:

    # this task is declared here in order to be used as a dependency by the client quartiles task right below
    create_fmrr_functions = GKEStartPodOperator(
        task_id="create-fmrr-functions",
        name="create-fmrr-functions",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(DIM_FMRR_SQL_FOLDER + "store_procedure/create_fmrr_functions.sql").read()
                    .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
                    .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME)
                    .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
                    .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
                    .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET),
        },
        dag=dag,
    )

    # task definitions
    populate_fmrr_query_template: str = open(
        DIM_FMRR_SQL_FOLDER + "fmrr_bench_geo_model/populate_fmrr_rouse_template.sql"
    ).read()
    populate_fmrr_cat_template: str = open(
        DIM_FMRR_SQL_FOLDER + "fmrr_bench_geo_model/populate_fmrr_cat_template.sql"
    ).read()

    populate_fmrr_company: str = open(
        DIM_FMRR_SQL_FOLDER + "fmrr_bench_geo_model/populate_fmrr_company.sql"
    ).read()

    xyz_query_template: str = open(
        DIM_FMRR_SQL_FOLDER + "fmrr_bench_geo_model/inject_xyz_clients.sql"
    ).read()
    xyz_query_template_company: str = open(
        DIM_FMRR_SQL_FOLDER + "fmrr_bench_geo_model/inject_xyz_clients_company.sql"
    ).read()
    inject_xyz_fmrr_cf_query: str = open(
        DIM_FMRR_SQL_FOLDER + "fmrr_bench_geo_model/inject_xyz_clients_cf.sql"
    ).read()
    cf_query_rouse_template: str = open(
        DIM_FMRR_SQL_FOLDER + "fmrr_bench_geo_model/carry_forward_rouse_template.sql"
    ).read()
    cf_query_cat_template: str = open(
        DIM_FMRR_SQL_FOLDER + "fmrr_bench_geo_model/carry_forward_cat_template.sql"
    ).read()
    cf_query_kom_template: str = open(
        DIM_FMRR_SQL_FOLDER + "fmrr_bench_geo_model/carry_forward_komatsu_template.sql"
    ).read()

    # table column mapping templates
    fmrr_bench_rouse_and_company_mapping_template = """
    {"fmrr_id": "FMRR_ID",
                "physical_ut_bench": "Physical Ut Bench", 
                "dollar_ut_bench": "Dollar Ut Bench",
                "fleet_age_bench": "Fleet Age Bench",
                "rrd_bench": "RRD Bench",
                "rad_bench": "RAD Bench", 
                "new_monthly_bench_rate": "New Monthly Bench Rate", 
                "exist_monthly_bench_rate": "Exist Monthly Bench Rate", 
                "monthly_bench_rate": "Monthly Bench Rate", 
                "weekly_bench_rate": "Weekly Bench Rate", 
                "daily_bench_rate": "Daily Bench Rate", 
                "monthly_top_q_client_rate": "Monthly TopQ Client Rate",
                "monthly_top_q_bench_rate": "Monthly TopQ Bench Rate", 
                "monthly_bot_q_client_rate": "Monthly BotQ Client Rate", 
                "monthly_bot_q_bench_rate": "Monthly BotQ Bench Rate",
                "weekly_top_q_client_rate": "Weekly TopQ Client Rate",  
                "weekly_top_q_bench_rate": "Weekly TopQ Bench Rate", 
                "weekly_bot_q_client_rate": "Weekly BotQ Client Rate", 
                "weekly_bot_q_bench_rate": "Weekly BotQ Bench Rate",
                "daily_top_q_client_rate": "Daily TopQ Client Rate",  
                "daily_top_q_bench_rate": "Daily TopQ Bench Rate",
                "daily_bot_q_client_rate": "Daily BotQ Client Rate",  
                "daily_bot_q_bench_rate": "Daily BotQ Bench Rate", 
                "new_monthly_spot_bench_rate": "New Monthly Spot Bench Rate", 
                "monthly_spot_bench_rate": "Monthly Spot Bench Rate", 
                "monthly_spot_top_q_bench_rate": "Monthly Spot TopQ Bench Rate", 
                "monthly_spot_bot_q_bench_rate": "Monthly Spot BotQ Bench Rate", 
                "weekly_spot_bench_rate": "Weekly Spot Bench Rate", 
                "weekly_spot_top_q_bench_rate": "Weekly Spot TopQ Bench Rate", 
                "weekly_spot_bot_q_bench_rate": "Weekly Spot BotQ Bench Rate", 
                "daily_spot_bench_rate": "Daily Spot Bench Rate", 
                "daily_spot_top_q_bench_rate": "Daily Spot TopQ Bench Rate", 
                "daily_spot_bot_q_bench_rate": "Daily Spot BotQ Bench Rate", 
                "new_monthly_core_bench_rate": "New Monthly Core Bench Rate", 
                "monthly_core_bench_rate": "Monthly Core Bench Rate", 
                "monthly_core_top_q_bench_rate": "Monthly Core TopQ Bench Rate", 
                "monthly_core_bot_q_bench_rate": "Monthly Core BotQ Bench Rate", 
                "weekly_core_bench_rate": "Weekly Core Bench Rate", 
                "weekly_core_top_q_bench_rate": "Weekly Core TopQ Bench Rate", 
                "weekly_core_bot_q_bench_rate": "Weekly Core BotQ Bench Rate", 
                "daily_core_bench_rate": "Daily Core Bench Rate", 
                "daily_core_top_q_bench_rate": "Daily Core TopQ Bench Rate", 
                "daily_core_bot_q_bench_rate": "Daily Core BotQ Bench Rate", 
                "monthly_min_bench_rate": "Monthly Min Bench Rate", 
                "monthly_max_bench_rate": "Monthly Max Bench Rate", 
                "weekly_min_bench_rate": "Weekly Min Bench Rate", 
                "weekly_max_bench_rate": "Weekly Max Bench Rate", 
                "daily_min_bench_rate": "Daily Min Bench Rate", 
                "daily_max_bench_rate": "Daily Max Bench Rate", 
                "monthly_spot_min_bench_rate": "Monthly Spot Min Bench Rate", 
                "monthly_spot_max_bench_rate": "Monthly Spot Max Bench Rate", 
                "weekly_spot_min_bench_rate": "Weekly Spot Min Bench Rate", 
                "weekly_spot_max_bench_rate": "Weekly Spot Max Bench Rate", 
                "daily_spot_min_bench_rate": "Daily Spot Min Bench Rate", 
                "daily_spot_max_bench_rate": "Daily Spot Max Bench Rate", 
                "monthly_core_min_bench_rate": "Monthly Core Min Bench Rate", 
                "monthly_core_max_bench_rate": "Monthly Core Max Bench Rate", 
                "weekly_core_min_bench_rate": "Weekly Core Min Bench Rate", 
                "weekly_core_max_bench_rate": "Weekly Core Max Bench Rate", 
                "daily_core_min_bench_rate": "Daily Core Min Bench Rate", 
                "daily_core_max_bench_rate": "Daily Core Max Bench Rate",
                "hourly_bench_rate": "Hourly Bench Rate",
                "hourly_top_q_bench_rate": "Hourly TopQ Bench Rate",
                "hourly_bot_q_bench_rate": "Hourly BotQ Bench Rate",
                "hourly_min_bench_rate": "Hourly Min Bench Rate",
                "hourly_max_bench_rate": "Hourly Max Bench Rate",
                "client_order": "ClientOrder" }
    """

    fmrr_bench_cat_and_komatsu_mapping_template = """
    {"fmrr_id": "FMRR_ID",
                "dollar_ut_bench": "Dollar Ut Bench",
                "new_monthly_bench_rate": "New Monthly Bench Rate", 
                "exist_monthly_bench_rate": "Exist Monthly Bench Rate", 
                "monthly_bench_rate": "Monthly Bench Rate", 
                "weekly_bench_rate": "Weekly Bench Rate", 
                "daily_bench_rate": "Daily Bench Rate",
                "monthly_top_q_bench_rate": "Monthly TopQ Bench Rate",
                "monthly_bot_q_bench_rate": "Monthly BotQ Bench Rate", 
                "weekly_top_q_bench_rate": "Weekly TopQ Bench Rate",  
                "weekly_bot_q_bench_rate": "Weekly BotQ Bench Rate",  
                "daily_top_q_bench_rate": "Daily TopQ Bench Rate", 
                "daily_bot_q_bench_rate": "Daily BotQ Bench Rate", 
                "new_monthly_spot_bench_rate": "New Monthly Spot Bench Rate", 
                "monthly_spot_bench_rate": "Monthly Spot Bench Rate", 
                "monthly_spot_top_q_bench_rate": "Monthly Spot TopQ Bench Rate", 
                "monthly_spot_bot_q_bench_rate": "Monthly Spot BotQ Bench Rate", 
                "weekly_spot_bench_rate": "Weekly Spot Bench Rate", 
                "weekly_spot_top_q_bench_rate": "Weekly Spot TopQ Bench Rate", 
                "weekly_spot_bot_q_bench_rate": "Weekly Spot BotQ Bench Rate", 
                "daily_spot_bench_rate": "Daily Spot Bench Rate", 
                "daily_spot_top_q_bench_rate": "Daily Spot TopQ Bench Rate", 
                "daily_spot_bot_q_bench_rate": "Daily Spot BotQ Bench Rate", 
                "new_monthly_core_bench_rate": "New Monthly Core Bench Rate", 
                "monthly_core_bench_rate": "Monthly Core Bench Rate", 
                "monthly_core_top_q_bench_rate": "Monthly Core TopQ Bench Rate", 
                "monthly_core_bot_q_bench_rate": "Monthly Core BotQ Bench Rate", 
                "weekly_core_bench_rate": "Weekly Core Bench Rate", 
                "weekly_core_top_q_bench_rate": "Weekly Core TopQ Bench Rate", 
                "weekly_core_bot_q_bench_rate": "Weekly Core BotQ Bench Rate", 
                "daily_core_bench_rate": "Daily Core Bench Rate", 
                "daily_core_top_q_bench_rate": "Daily Core TopQ Bench Rate", 
                "daily_core_bot_q_bench_rate": "Daily Core BotQ Bench Rate", 
                "monthly_min_bench_rate": "Monthly Min Bench Rate", 
                "monthly_max_bench_rate": "Monthly Max Bench Rate", 
                "weekly_min_bench_rate": "Weekly Min Bench Rate", 
                "weekly_max_bench_rate": "Weekly Max Bench Rate", 
                "daily_min_bench_rate": "Daily Min Bench Rate", 
                "daily_max_bench_rate": "Daily Max Bench Rate", 
                "monthly_spot_min_bench_rate": "Monthly Spot Min Bench Rate", 
                "monthly_spot_max_bench_rate": "Monthly Spot Max Bench Rate", 
                "weekly_spot_min_bench_rate": "Weekly Spot Min Bench Rate", 
                "weekly_spot_max_bench_rate": "Weekly Spot Max Bench Rate", 
                "daily_spot_min_bench_rate": "Daily Spot Min Bench Rate", 
                "daily_spot_max_bench_rate": "Daily Spot Max Bench Rate", 
                "monthly_core_min_bench_rate": "Monthly Core Min Bench Rate", 
                "monthly_core_max_bench_rate": "Monthly Core Max Bench Rate", 
                "weekly_core_min_bench_rate": "Weekly Core Min Bench Rate", 
                "weekly_core_max_bench_rate": "Weekly Core Max Bench Rate", 
                "daily_core_min_bench_rate": "Daily Core Min Bench Rate", 
                "daily_core_max_bench_rate": "Daily Core Max Bench Rate",
                "client_order": "ClientOrder" }
    """

    fmrr_bench_parameters = [
        {
            "geo_level": "Market",
            "field_string": "Market",
            "grouping_column": "rouse_market",
            "bench_rates_columns": ", rouse_market",
            "filtering_country": "",
            "query_template": populate_fmrr_query_template,
            "monthly_ratios_aggs_columns": ", rouse_market, rouse_district, rouse_region",
            "monthly_ratios_part_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/monthly_ratios_part/rouse_market.sql",
            "weekly_ratios_part_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/weekly_ratios_part/rouse_market.sql",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_market_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_market_apply_orders.sql",
            "xyz_client": True,
            "carry_forward_query_template": cf_query_rouse_template,
            "cf_geo_level": "Market",
            "ss_bench_table_name": "dbo.FMRR_Bench_Market",
            "ss_bench_table_template": fmrr_bench_rouse_and_company_mapping_template,
        },
        {
            "geo_level": "District",
            "field_string": "District",
            "grouping_column": "rouse_district",
            "bench_rates_columns": ", rouse_region, rouse_district, rouse_market",
            "filtering_country": "",
            "query_template": populate_fmrr_query_template,
            "monthly_ratios_aggs_columns": ", rouse_district, rouse_region",
            "monthly_ratios_part_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/monthly_ratios_part/rouse_district.sql",
            "weekly_ratios_part_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/weekly_ratios_part/rouse_district.sql",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_district_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_district_apply_orders.sql",
            "xyz_client": True,
            "carry_forward_query_template": cf_query_rouse_template,
            "cf_geo_level": "Total District",
            "ss_bench_table_name": "dbo.FMRR_Bench_District",
            "ss_bench_table_template": fmrr_bench_rouse_and_company_mapping_template,
        },
        {
            "geo_level": "Region",
            "field_string": "Region",
            "grouping_column": "rouse_region",
            "bench_rates_columns": ", rouse_region, rouse_district, rouse_market",
            "filtering_country": "",
            "query_template": populate_fmrr_query_template,
            "monthly_ratios_part_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/monthly_ratios_part/rouse_region.sql",
            "weekly_ratios_part_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/weekly_ratios_part/rouse_region.sql",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_region_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_region_apply_orders.sql",
            "monthly_ratios_aggs_columns": ", rouse_region",
            "xyz_client": True,
            "carry_forward_query_template": cf_query_rouse_template,
            "cf_geo_level": "Total Region",
            "ss_bench_table_name": "dbo.FMRR_Bench_Region",
            "ss_bench_table_template": fmrr_bench_rouse_and_company_mapping_template,
        },
        {
            "geo_level": "CATsubterr",
            "field_string": "CATsubterr",
            "grouping_column": "cat_subterritory",
            "bench_rates_columns": ",rouse_market as cat_subterritory",
            "filtering_country": "",
            "benchmark_rates_filter_column": "rouse_market",
            "dealer_type_id": "3",
            "query_template": populate_fmrr_cat_template,
            "monthly_ratios_part_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/monthly_ratios_part/cat_subterritory.sql",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_catsubterr_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_catsubterr_apply_orders.sql",
            "monthly_ratios_aggs_columns": ", cat_subterritory, cat_territory, cat_district",
            "carry_forward_query_template": cf_query_cat_template,
            "cf_geo_level": "CAT SubTerritory",
            "rouse_grouping_column": "rouse_market",
            "ss_bench_table_name": "dbo.FMRR_Bench_CATsubterr",
            "ss_bench_table_template": fmrr_bench_cat_and_komatsu_mapping_template,
        },
        {
            "geo_level": "CATterr",
            "field_string": "CATterr",
            "grouping_column": "cat_territory",
            "bench_rates_columns": ",rouse_district as cat_territory",
            "filtering_country": "",
            "benchmark_rates_filter_column": "rouse_district",
            "dealer_type_id": "3",
            "query_template": populate_fmrr_cat_template,
            "monthly_ratios_part_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/monthly_ratios_part/cat_territory.sql",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_catterr_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_catterr_apply_orders.sql",
            "monthly_ratios_aggs_columns": ", cat_territory, cat_district",
            "carry_forward_query_template": cf_query_cat_template,
            "cf_geo_level": "CAT Territory",
            "rouse_grouping_column": "rouse_district",
            "ss_bench_table_name": "dbo.FMRR_Bench_CATterr",
            "ss_bench_table_template": fmrr_bench_cat_and_komatsu_mapping_template,
        },
        {
            "geo_level": "CATdist",
            "field_string": "CATdist",
            "grouping_column": "cat_district",
            "bench_rates_columns": ",rouse_region as cat_district",
            "filtering_country": "",
            "benchmark_rates_filter_column": "rouse_region",
            "dealer_type_id": "3",
            "query_template": populate_fmrr_cat_template,
            "monthly_ratios_part_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/monthly_ratios_part/cat_district.sql",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_catdist_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_catdist_apply_orders.sql",
            "monthly_ratios_aggs_columns": ", cat_district",
            "carry_forward_query_template": cf_query_cat_template,
            "cf_geo_level": "CAT District",
            "rouse_grouping_column": "rouse_region",
            "ss_bench_table_name": "dbo.FMRR_Bench_CATdist",
            "ss_bench_table_template": fmrr_bench_cat_and_komatsu_mapping_template,
        },
        {
            "geo_level": "KOMterr",
            "field_string": "KOMterr",
            "grouping_column": "komatsu_territory",
            "bench_rates_columns": ",rouse_district as komatsu_territory",
            "filtering_country": "",
            "benchmark_rates_filter_column": "rouse_district",
            "dealer_type_id": "4",
            "query_template": populate_fmrr_cat_template,
            "monthly_ratios_part_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/monthly_ratios_part/komatsu_territory.sql",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_komterr_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_komterr_apply_orders.sql",
            "monthly_ratios_aggs_columns": ", komatsu_territory, komatsu_region",
            "carry_forward_query_template": cf_query_kom_template,
            "ss_bench_table_name": "dbo.FMRR_Bench_KOMterr",
            "ss_bench_table_template": fmrr_bench_cat_and_komatsu_mapping_template,
        },
        {
            "geo_level": "KOMsubterr",
            "field_string": "KOMsubterr",
            "grouping_column": "komatsu_subterritory",
            "bench_rates_columns": ",rouse_market as komatsu_subterritory",
            "filtering_country": "",
            "benchmark_rates_filter_column": "rouse_market",
            "dealer_type_id": "4",
            "query_template": populate_fmrr_cat_template,
            "monthly_ratios_part_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/monthly_ratios_part/komatsu_subterritory.sql",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_komsubterr_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_komsubterr_apply_orders.sql",
            "monthly_ratios_aggs_columns": ", komatsu_subterritory, komatsu_territory, komatsu_region",
            "carry_forward_query_template": cf_query_kom_template,
            "ss_bench_table_name": "dbo.FMRR_Bench_KOMsubterr",
            "ss_bench_table_template": fmrr_bench_cat_and_komatsu_mapping_template,
        },
        {
            "geo_level": "KOMreg",
            "field_string": "KOMreg",
            "grouping_column": "komatsu_region",
            "bench_rates_columns": ",rouse_region as komatsu_region",
            "filtering_country": "",
            "benchmark_rates_filter_column": "rouse_region",
            "dealer_type_id": "4",
            "query_template": populate_fmrr_cat_template,
            "monthly_ratios_part_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/monthly_ratios_part/komatsu_region.sql",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_komreg_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_komreg_apply_orders.sql",
            "monthly_ratios_aggs_columns": ", komatsu_region",
            "carry_forward_query_template": cf_query_kom_template,
            "ss_bench_table_name": "dbo.FMRR_Bench_KOMreg",
            "ss_bench_table_template": fmrr_bench_cat_and_komatsu_mapping_template,
        },
        {
            "geo_level": "Company",
            "field_string": "Company",
            "grouping_column": "country",
            "bench_rates_columns": "",
            "filtering_country": "USA",
            "benchmark_rates_filter_column": "",
            "query_template": populate_fmrr_company,
            "monthly_ratios_part_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/monthly_ratios_part/company.sql",
            "weekly_ratios_part_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/weekly_ratios_part/company.sql",
            "apply_orders_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_bench_company_apply_orders.sql",
            "apply_orders_cf_file": DIM_FMRR_SQL_FOLDER
            + "fmrr_bench_geo_model/apply_orders/fmrr_cf_company_apply_orders.sql",
            "monthly_ratios_aggs_columns": "",
            "xyz_client": True,
            "company_condition_xyz": "AND FMRR.country='USA'",
            "carry_forward_query_template": open(
                DIM_FMRR_SQL_FOLDER
                + "fmrr_bench_geo_model/carry_forward_company_template.sql"
            ).read(),
            "ss_bench_table_name": "dbo.FMRR_Bench_Company",
            "ss_bench_table_template": fmrr_bench_rouse_and_company_mapping_template,
        },
    ]

    adjust_spot_core_one_template: str = open(
        DIM_FMRR_SQL_FOLDER + "fmrr_bench_geo_model/adjust_spot_core_monthly.sql"
    ).read()

    adjust_spot_core_two_template: str = open(
        DIM_FMRR_SQL_FOLDER + "fmrr_bench_geo_model/adjust_spot_core_weekly.sql"
    ).read()

    adjust_spot_core_three_template: str = open(
        DIM_FMRR_SQL_FOLDER + "fmrr_bench_geo_model/adjust_spot_core_daily.sql"
    ).read()

    adjust_spot_core_four_template: str = open(
        DIM_FMRR_SQL_FOLDER + "fmrr_bench_geo_model/adjust_spot_core_hourly.sql"
    ).read()

    monthly_ratio_query_template: str = open(
        DIM_FMRR_SQL_FOLDER + "fmrr_bench_geo_model/apply_monthly_ratios.sql"
    ).read()

    weekly_ratio_query_template: str = open(
        DIM_FMRR_SQL_FOLDER + "fmrr_bench_geo_model/apply_weekly_ratios.sql"
    ).read()

    spot_core_parameters = {
        "Market": {
            "query_one_template": adjust_spot_core_one_template,
            "query_two_template": adjust_spot_core_two_template,
            "query_three_template": adjust_spot_core_three_template,
            "query_four_template": adjust_spot_core_four_template,
        },
        "District": {
            "query_one_template": adjust_spot_core_one_template,
            "query_two_template": adjust_spot_core_two_template,
            "query_three_template": adjust_spot_core_three_template,
            "query_four_template": adjust_spot_core_four_template,
        },
        "Region": {
            "query_one_template": adjust_spot_core_one_template,
            "query_two_template": adjust_spot_core_two_template,
            "query_three_template": adjust_spot_core_three_template,
            "query_four_template": adjust_spot_core_four_template,
        },
        "CATsubterr": {
            "query_one_template": adjust_spot_core_one_template,
            "query_two_template": adjust_spot_core_two_template,
            "query_three_template": adjust_spot_core_three_template,
        },
        "CATterr": {
            "query_one_template": adjust_spot_core_one_template,
            "query_two_template": adjust_spot_core_two_template,
            "query_three_template": adjust_spot_core_three_template,
        },
        "CATdist": {
            "query_one_template": adjust_spot_core_one_template,
            "query_two_template": adjust_spot_core_two_template,
            "query_three_template": adjust_spot_core_three_template,
        },
        "KOMterr": {
            "query_one_template": adjust_spot_core_one_template,
            "query_two_template": adjust_spot_core_two_template,
            "query_three_template": adjust_spot_core_three_template,
        },
        "KOMsubterr": {
            "query_one_template": adjust_spot_core_one_template,
            "query_two_template": adjust_spot_core_two_template,
            "query_three_template": adjust_spot_core_three_template,
        },
        "KOMreg": {
            "query_one_template": adjust_spot_core_one_template,
            "query_two_template": adjust_spot_core_two_template,
            "query_three_template": adjust_spot_core_three_template,
        },
        "Company": {
            "query_one_template": adjust_spot_core_one_template,
            "query_two_template": adjust_spot_core_two_template,
            "query_three_template": adjust_spot_core_three_template,
            "query_four_template": adjust_spot_core_four_template,
        },
    }

    for fmrr_bench_object in fmrr_bench_parameters:
        populate_fmrr_query: str = (
            fmrr_bench_object["query_template"]
            .replace(
                "{fmrr_bench_object['field_string']}", fmrr_bench_object["field_string"]
            )
            .replace(
                "{fmrr_bench_object['grouping_column']}",
                fmrr_bench_object["grouping_column"],
            )
            .replace("{fmrr_bench_object['geo_level']}", fmrr_bench_object["geo_level"])
            .replace(
                "{fmrr_bench_object['bench_rates_columns']}",
                fmrr_bench_object["bench_rates_columns"],
            )
            .replace(
                "{fmrr_bench_object['dealer_type_id']}",
                fmrr_bench_object.get("dealer_type_id", " "),
            )
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{AGGS_STAGE_DATASET_NAME}", AGGS_STAGE_DATASET_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace(
                "{fmrr_bench_object['field_string'].lower()}",
                fmrr_bench_object["field_string"].lower(),
            )
            .replace(
                "{fmrr_bench_object['benchmark_rates_filter_column']}",
                fmrr_bench_object.get("benchmark_rates_filter_column", ""),
            )
        )

        populate_fmrr_bench_table = GKEStartPodOperator(
            task_id=f"populate-fmrr-bench-{fmrr_bench_object['field_string'].lower()}",
            name=f"populate-fmrr-bench-{fmrr_bench_object['field_string'].lower()}",
            image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
            arguments=["python3", "main.py", "execute-bigquery"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "QUERY": populate_fmrr_query,
            },
            dag=dag,
        )

        spot_core_one_query: str = (
            spot_core_parameters[fmrr_bench_object["geo_level"]]["query_one_template"]
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{AGGS_STAGE_DATASET_NAME}", AGGS_STAGE_DATASET_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace(
                "{fmrr_bench_object['field_string'].lower()}",
                fmrr_bench_object["field_string"].lower(),
            )
            .replace("{grouping_column}", fmrr_bench_object["grouping_column"])
        )

        adjust_spot_core_one = GKEStartPodOperator(
            task_id=f"adjust-spot-core-one-{fmrr_bench_object['field_string'].lower()}",
            name=f"adjust-spot-core-one-{fmrr_bench_object['field_string'].lower()}",
            image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
            arguments=["python3", "main.py", "execute-bigquery"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "QUERY": spot_core_one_query,
            },
            dag=dag,
        )

        monthly_ratio_query: str = (
            monthly_ratio_query_template.replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_STAGE_DATASET_NAME}", AGGS_STAGE_DATASET_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace(
                "{fmrr_bench_object['field_string'].lower()}",
                fmrr_bench_object["field_string"].lower(),
            )
            .replace("{grouping_column}", fmrr_bench_object["grouping_column"])
            .replace(
                "{monthly_ratios_aggs_columns}",
                fmrr_bench_object["monthly_ratios_aggs_columns"],
            )
            .replace(
                "{mw_ratio_sql}", open(fmrr_bench_object["monthly_ratios_part_file"]).read()
            )
        )

        apply_monthly_ratios = GKEStartPodOperator(
            task_id=f"apply-monthly-ratios-{fmrr_bench_object['field_string'].lower()}",
            name=f"apply-monthly-ratios-{fmrr_bench_object['field_string'].lower()}",
            image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
            arguments=["python3", "main.py", "execute-bigquery"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "QUERY": monthly_ratio_query,
            },
            dag=dag,
        )

        if "weekly_ratios_part_file" in fmrr_bench_object.keys():
            weekly_ratio_query: str = (
                weekly_ratio_query_template.replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
                .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME)
                .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
                .replace("{AGGS_STAGE_DATASET_NAME}", AGGS_STAGE_DATASET_NAME)
                .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
                .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
                .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
                .replace(
                    "{fmrr_bench_object['field_string'].lower()}",
                    fmrr_bench_object["field_string"].lower(),
                )
                .replace("{grouping_column}", fmrr_bench_object["grouping_column"])
                .replace(
                    "{monthly_ratios_aggs_columns}",
                    fmrr_bench_object["monthly_ratios_aggs_columns"],
                )
                .replace(
                    "{wd_ratio_sql}", open(fmrr_bench_object["weekly_ratios_part_file"]).read()
                )
            )
            apply_weekly_ratios = GKEStartPodOperator(
                task_id=f"apply-weekly-ratios-{fmrr_bench_object['field_string'].lower()}",
                name=f"apply-weekly-ratios-{fmrr_bench_object['field_string'].lower()}",
                image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
                arguments=["python3", "main.py", "execute-bigquery"],
                resources=RESOURCES_SMALL_TASK,
                env_vars={
                    "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                    "QUERY": weekly_ratio_query,
                },
                dag=dag,
            )

            spot_core_four_query: str = (
                spot_core_parameters[fmrr_bench_object["geo_level"]]["query_four_template"]
                .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
                .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME)
                .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
                .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
                .replace("{AGGS_STAGE_DATASET_NAME}", AGGS_STAGE_DATASET_NAME)
                .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
                .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
                .replace(
                    "{fmrr_bench_object['field_string'].lower()}",
                    fmrr_bench_object["field_string"].lower(),
                )
                .replace("{grouping_column}", fmrr_bench_object["grouping_column"])
            )
            adjust_spot_core_four = GKEStartPodOperator(
                task_id=f"adjust-spot-core-hourly-{fmrr_bench_object['field_string'].lower()}",
                name=f"adjust-spot-core-hourly-{fmrr_bench_object['field_string'].lower()}",
                image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
                arguments=["python3", "main.py", "execute-bigquery"],
                resources=RESOURCES_SMALL_TASK,
                env_vars={
                    "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                    "QUERY": spot_core_four_query,
                },
                dag=dag,
            )            

        spot_core_two_query: str = (
            spot_core_parameters[fmrr_bench_object["geo_level"]]["query_two_template"]
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{AGGS_STAGE_DATASET_NAME}", AGGS_STAGE_DATASET_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace(
                "{fmrr_bench_object['field_string'].lower()}",
                fmrr_bench_object["field_string"].lower(),
            )
            .replace("{grouping_column}", fmrr_bench_object["grouping_column"])
        )

        adjust_spot_core_two = GKEStartPodOperator(
            task_id=f"adjust-spot-core-two-{fmrr_bench_object['field_string'].lower()}",
            name=f"adjust-spot-core-two-{fmrr_bench_object['field_string'].lower()}",
            image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
            arguments=["python3", "main.py", "execute-bigquery"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "QUERY": spot_core_two_query,
            },
            dag=dag,
        )

        spot_core_three_query: str = (
            spot_core_parameters[fmrr_bench_object["geo_level"]]["query_three_template"]
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{AGGS_STAGE_DATASET_NAME}", AGGS_STAGE_DATASET_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace(
                "{fmrr_bench_object['field_string'].lower()}",
                fmrr_bench_object["field_string"].lower(),
            )
            .replace("{grouping_column}", fmrr_bench_object["grouping_column"])
        )

        adjust_spot_core_three = GKEStartPodOperator(
            task_id=f"adjust-spot-core-three-{fmrr_bench_object['field_string'].lower()}",
            name=f"adjust-spot-core-three-{fmrr_bench_object['field_string'].lower()}",
            image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
            arguments=["python3", "main.py", "execute-bigquery"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "QUERY": spot_core_three_query,
            },
            dag=dag,
        )

        if "xyz_client" in fmrr_bench_object.keys():
            if fmrr_bench_object["geo_level"] == "Company":
                xyz_query_template_str = xyz_query_template_company
            else:
                xyz_query_template_str = xyz_query_template
            inject_xyz_records_query: str = (
                xyz_query_template_str.replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
                .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME)
                .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
                .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
                .replace("{AGGS_STAGE_DATASET_NAME}", AGGS_STAGE_DATASET_NAME)
                .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
                .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
                .replace(
                    "{fmrr_bench_object['field_string'].lower()}",
                    fmrr_bench_object["field_string"].lower(),
                )
                .replace("{grouping_column}", fmrr_bench_object["grouping_column"])
                .replace(
                    "{fmrr_bench_object['grouping_column']}",
                    fmrr_bench_object["grouping_column"],
                )
                .replace(
                    "{fmrr_bench_object['field_string']}", fmrr_bench_object["field_string"]
                )
                .replace(
                    "{fmrr_bench_object['grouping_column']}",
                    fmrr_bench_object["grouping_column"],
                )
                .replace("{fmrr_bench_object['geo_level']}", fmrr_bench_object["geo_level"])
                .replace("{RATES_DESTINATION_DATASET}", RATES_DATASET_NAME)
            )

            inject_xyz_records_into_fmrr_bench = GKEStartPodOperator(
                task_id=f"inject-xyz-clients-bench-{fmrr_bench_object['field_string'].lower()}",
                name=f"inject-xyz-clients-bench-{fmrr_bench_object['field_string'].lower()}",
                image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
                arguments=["python3", "main.py", "execute-bigquery"],
                resources=RESOURCES_SMALL_TASK,
                env_vars={
                    "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                    "QUERY": inject_xyz_records_query,
                },
                dag=dag,
            )

            inject_xyz_records_into_fmrr_cf_query: str = (
                inject_xyz_fmrr_cf_query.replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
                .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME)
                .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
                .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
                .replace("{AGGS_STAGE_DATASET_NAME}", AGGS_STAGE_DATASET_NAME)
                .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
                .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
                .replace(
                    "{fmrr_bench_object['field_string'].lower()}",
                    fmrr_bench_object["field_string"].lower(),
                )
                .replace("{grouping_column}", fmrr_bench_object["grouping_column"])
            )

            inject_xyz_records_into_fmrr_cf = GKEStartPodOperator(
                task_id=f"inject-xyz-clients-cf-{fmrr_bench_object['field_string'].lower()}",
                name=f"inject-xyz-clients-cf-{fmrr_bench_object['field_string'].lower()}",
                image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
                arguments=["python3", "main.py", "execute-bigquery"],
                resources=RESOURCES_SMALL_TASK,
                env_vars={
                    "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                    "QUERY": inject_xyz_records_into_fmrr_cf_query,
                },
                dag=dag,
            )

        carry_forward_query = (
            fmrr_bench_object["carry_forward_query_template"]
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{AGGS_STAGE_DATASET_NAME}", AGGS_STAGE_DATASET_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace(
                "{fmrr_bench_object['field_string'].lower()}",
                fmrr_bench_object["field_string"].lower(),
            )
            .replace("{grouping_column}", fmrr_bench_object["grouping_column"])
            .replace(
                "{rouse_grouping_column}",
                fmrr_bench_object.get("rouse_grouping_column", ""),
            )
            .replace("{field_string}", fmrr_bench_object["field_string"])
            .replace("{cf_geo_level}", fmrr_bench_object.get("cf_geo_level", ""))
        )

        carry_forward = GKEStartPodOperator(
            task_id=f"carry-forward-bench-{fmrr_bench_object['field_string'].lower()}",
            name=f"carry-forward-bench-{fmrr_bench_object['field_string'].lower()}",
            image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
            arguments=["python3", "main.py", "execute-bigquery"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "QUERY": carry_forward_query,
            },
            dag=dag,
        )

        # FMRR Bench and FMRR CF creation dependency
        (
            populate_fmrr_bench_table
            >> adjust_spot_core_one
            >> apply_monthly_ratios
            >> adjust_spot_core_two
        )
        if "weekly_ratios_part_file" in fmrr_bench_object.keys():
            adjust_spot_core_two >> apply_weekly_ratios >> adjust_spot_core_three >> adjust_spot_core_four
        else:
            adjust_spot_core_two >> adjust_spot_core_three

        if "xyz_client" in fmrr_bench_object.keys():
            (
                adjust_spot_core_four
                >> inject_xyz_records_into_fmrr_bench
                >> carry_forward
                >> inject_xyz_records_into_fmrr_cf
                >> fmrr_bench_geo_complete
            )
        else:
            (
                adjust_spot_core_three
                >> carry_forward
                >> fmrr_bench_geo_complete
            )
            (
                adjust_spot_core_three
                >> fmrr_bench_geo_complete
            )
        create_fmrr_functions >> populate_fmrr_bench_table


    # fmrr bench dependencies
    finished_seasonality_tables_reload >> create_fmrr_functions

    finished_benchmark_rates_in_bq >> insert_fmrr_bench_record
    [
        insert_fmrr_bench_record,
        fmrr_bench_geo_complete,
    ] >> update_fmrr_bench_record_when_clear

    populate_benchmark_revdist_pt_query = (
        open(DIM_FMRR_SQL_FOLDER + "fmrr_bench_geo_model/populate_benchmark_revdistpt.sql")
        .read()
        .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
        .replace("{RATES_DATASET_NAME}", RATES_DATASET_NAME)
        .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
        .replace("{AGGS_STAGE_DATASET_NAME}", AGGS_STAGE_DATASET_NAME)
        .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
        .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
        .replace("{table_suffix}", TABLE_SUFFIX)
    )

    populate_benchmark_revdist_pt = GKEStartPodOperator(
        task_id="populate-benchmark-revdist-pt",
        name="populate-benchmark-revdist-pt",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": populate_benchmark_revdist_pt_query,
        },
        dag=dag,
    )

    transfer_benchmark_revdist_pt = GKEStartPodOperator(
        task_id="transfer-benchmark-revdist-pt-to-ss",
        name="transfer-benchmark-revdist-pt-to-ss",
        priority_weight=11,
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(requests={"cpu": "3000m", "memory": "2Gi"}),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": DIM_FM_RR_STAGE_DATASET,
            "BQ_TABLE": f"benchmark_revdistpt_{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": f"dbo.Benchmark_RevDistPT",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": """
            {
                "geolevelid": "GeoLevelID",
                "monthid": "MonthID"
        ,"geolevel": "GeoLevel"
        ,"country": "Country"
        ,"rouse_region": "Rouse Region"
        ,"rouse_district": "Rouse District"
        ,"rouse_market": "Rouse Market"
        ,"rouse_product_type_id": "RouseProductTypeID"
        ,"pct_new_monthly_bench": "PctNewMonthlyBench"
        ,"pct_exist_monthly_bench": "PctExistMonthlyBench"
        ,"pct_weekly_bench": "PctWeeklyBench"
        ,"pct_daily_bench" : "PctDailyBench"
        ,"pct_other_bench": "PctOtherBench"
        ,"pct_excess_mile_bench": "PctExcessMileBench"
        ,"pct_unclass_bench": "PctUnclassBench"
        ,"pct_credits_bench": "PctCreditsBench"
        ,"pct_discounts_bench": "PctDiscountsBench"
        ,"pct_re_rent_bench": "PctReRentBench"
        ,"pct_ldwbench": "PctLDWBench"
        ,"pct_environmental_bench": "PctEnvironmentalBench"
        ,"pct_delivery_bench": "PctDeliveryBench"
        ,"pct_fuel_bench": "PctFuelBench"
            }
            """,
            "PARSE_DTYPES": "True",
        },
        dag=dag,
    )

    create_fmrr_functions >> populate_benchmark_revdist_pt >> transfer_benchmark_revdist_pt >> finished_rambo_benchmark_dag
    populate_benchmark_revdist_pt >> fmrr_bench_geo_complete


benchmark_rates_change_history = GKEStartPodOperator(
    task_id="benchmark-rates-change-history",
    name="benchmark-rates-change-history",
    image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
    arguments=["python3", "main.py", "execute-bigquery"],
    do_xcom_push=False,
    resources=RESOURCES_SMALL_TASK,
    env_vars={
        "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        "QUERY": open(DIM_FMRR_SQL_FOLDER+ "benchmark_qa_queries/benchmark_rates_change_history.sql")
            .read()
            .replace("{TABLE_SUFFIX}",TABLE_SUFFIX)
            .replace("{PREVIOUS_TABLE_SUFFIX}",PREVIOUS_TABLE_SUFFIX)
            .replace("{RATES_STAGE_DATASET_NAME}",RATES_STAGE_DATASET_NAME)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET),
        "XCOM_PUSH": "False",
        "WRITE_LOGS": "True",
        "LOGS_GCP_PROJECT": PROJECT_NAME,
        "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/BENCHMARK/BENCHMARK_RATES",
        "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        "WRITE_DISPOSITION": None,
    },
    dag=dag,
)
qa_benchmark_rates_history = GKEStartPodOperator(
    task_id="qa-benchmark-rates-history",
    name="qa-benchmark-rates-history",
    image=get_full_image_name("qa-check-bigquery", GCR_REGISTRY),
    arguments=["python3", "main.py", "run-qa-check"],
    resources=RESOURCES_SMALL_TASK,
    env_vars={
        "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        "CHECK_TYPE": "expect_empty_result",
        "QA_QUERY": open(DIM_FMRR_SQL_FOLDER + "benchmark_qa_queries/qa_benchmark_rates.sql")
            .read()
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
    },
    dag=dag,
)
get_rambo_previous_version >> populate_benchmark_rates_unified >> benchmark_rates_change_history >> qa_benchmark_rates_history


fmrr_bench_company_change_history = GKEStartPodOperator(
    task_id="fmrr-bench-company-change-history",
    name="fmrr-bench-company-change-history",
    image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
    arguments=["python3", "main.py", "execute-bigquery"],
    do_xcom_push=False,
    resources=RESOURCES_SMALL_TASK,
    env_vars={
        "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        "QUERY": open(
            DIM_FMRR_SQL_FOLDER
            + "benchmark_qa_queries/fmrr_bench_company_change_history.sql"
        )
        .read()
        .replace("{TABLE_SUFFIX}",TABLE_SUFFIX)
        .replace("{PREVIOUS_TABLE_SUFFIX}",PREVIOUS_TABLE_SUFFIX)
        .replace("{DIM_FM_RR_STAGE_DATASET}",DIM_FM_RR_STAGE_DATASET)
        .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET),
        "XCOM_PUSH": "False",
        "WRITE_LOGS": "True",
        "LOGS_GCP_PROJECT": PROJECT_NAME,
        "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/BENCHMARK/BENCHMARK_RATES",
        "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        "WRITE_DISPOSITION": None,
    },
    dag=dag,
)
qa_fmrr_bench_company_alert = GKEStartPodOperator(
    task_id="qa-fmrr-bench-company-alert",
    name="qa-fmrr-bench-company-alert",
    image=get_full_image_name("qa-check-bigquery", GCR_REGISTRY),
    arguments=["python3", "main.py", "run-qa-check"],
    resources=RESOURCES_SMALL_TASK,
    env_vars={
        "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        "CHECK_TYPE": "expect_empty_result",
        "QA_QUERY": open(DIM_FMRR_SQL_FOLDER + "benchmark_qa_queries/qa_fmrr_bench_company_alert.sql")
            .read()
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
    },
    dag=dag,
)
qa_fmrr_bench_company_flag = GKEStartPodOperator(
    task_id="qa-fmrr-bench-company-flag",
    name="qa-fmrr-bench-company-flag",
    image=get_full_image_name("qa-check-bigquery", GCR_REGISTRY),
    arguments=["python3", "main.py", "run-qa-check"],
    resources=RESOURCES_SMALL_TASK,
    env_vars={
        "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        "CHECK_TYPE": "expect_empty_result",
        "QA_QUERY": open(DIM_FMRR_SQL_FOLDER + "benchmark_qa_queries/qa_fmrr_bench_company_flag.sql")
            .read()
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
    },
    dag=dag,
)
get_rambo_previous_version >> fmrr_bench_geo_complete >> fmrr_bench_company_change_history >> [qa_fmrr_bench_company_alert,qa_fmrr_bench_company_flag]


update_rambo_record_when_clear = GKEStartPodOperator(
    task_id="update-rambo-record-when-clear",
    name="update-rambo-record-when-clear",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    trigger_rule="one_success",
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "rambo",
        "STATUS_VALUE": "RUNNING",
        "PK": "{{ dag_run.conf['rambo_pk'] }}",
        "BATCH": TABLE_SUFFIX,
    },
    dag=dag,
)


## Log error if anything fails
command = """gcloud logging write rambo_pipeline '{ "type":"RAMBO_PIPELINE/RAMBO_BENCHMARK", "version":"1.0.0", """
command += """ "payload":{ "event_group":"NOTIFY_PAGER_DUTY", "event":"ERROR", "event_type": "END", "additional_info":"", "duration_sec": null, "message":"Error found in some tasks of the dag", "dag_execution_date":"""
command += '"' + "{{ ts }}" + '"' + "} }'"
command += (
    f""" --payload-type=json --project="{PROJECT_NAME}" --severity=ERROR || true"""
)

log_error = BashOperator(
    task_id="log-error", bash_command=command, trigger_rule="one_failed", dag=dag
)

update_if_etl_failed = GKEStartPodOperator(
    task_id="failure-update",
    name="failure-update",
    dag=dag,
    execution_timeout=dt.timedelta(minutes=5),
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    secrets=[secret_db_username, secret_db_password],
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "rambo",
        "STATUS_VALUE": "FAILED",
        "PK": "{{ dag_run.conf['rambo_pk'] }}",
        "BATCH": TABLE_SUFFIX,
    },
)
log_error >> update_if_etl_failed

# According to the update_rambo_record_when_clear trigger_rule of one success, it should depend on the first and last task of the dag
[insert_bench_rates_record, finished_rambo_benchmark_dag, update_fmrr_bench_record_when_clear] >> update_rambo_record_when_clear


if ENVIRONMENT == 'prod':
    # Send slack message alert for the historical QA
    send_slack_alert_benchmark_qa = SlackWebhookOperator(
        task_id="send-slack-alert-benchmark-qa",
        dag=dag,
        trigger_rule="one_failed",
        http_conn_id="slack",
        channel=QA_SLACK_CHANNEL,
        webhook_token=BaseHook.get_connection("slack_client_projects").password,
        message=":warning: There are big differences between last 2 runs on Benchmark Rates, please review "
        f'<https://lookerstudio.google.com/reporting/f868417e-bf39-4158-ba6e-e775d1981026/page/p_6yuint9rld|this link.>',
        username="airflow"
    )
    qa_benchmark_rates_history >> send_slack_alert_benchmark_qa

    send_slack_alert_fmrr_bench_company = SlackWebhookOperator(
        task_id="send-slack-alert",
        dag=dag,
        trigger_rule="one_failed",
        http_conn_id="slack",
        channel=QA_SLACK_CHANNEL,
        webhook_token=BaseHook.get_connection("slack_client_projects").password,
        message=":warning: There are big differences between last 2 runs on FMRR Bench Company, please review "
        f'<https://lookerstudio.google.com/reporting/f868417e-bf39-4158-ba6e-e775d1981026/page/p_h9307kasld|this link.>',
        username="airflow"
    )
    [qa_fmrr_bench_company_flag,qa_fmrr_bench_company_alert] >> send_slack_alert_fmrr_bench_company

# Enable this after reviewing the results in prod for a few weeks. This will block rambo if QA fails
# qa_fmrr_bench_company_alert >> finished_benchmark_rates_in_bq

## Trigger next Dag
trigger_rambo_enhancement = TriggerDagRunOperator(
    task_id="trigger-rambo-enhancement",
    trigger_dag_id=f"rambo-enhancement-v{MAJOR_VERSION}.{MINOR_VERSION}",
    execution_date="{{ execution_date }}",
    conf={
            "rambo_pk": "{{ dag_run.conf['rambo_pk'] }}", 
            "table_suffix": TABLE_SUFFIX,
            "fmrr_bench_pk": '{{task_instance.xcom_pull(task_ids="insert-fmrr-bench-record",key="return_value")["pk"]}}',
            # When Rambo triggers the Rambo Enhancenment dag, it will use the regular Table suffix as version for the tables
            "pipeline_batch": TABLE_SUFFIX, 
            "parent_batch": None,
            "transfer_to_cloud_db": "True",
        },
    dag=dag,
)
[finished_benchmark_rates_in_bq,fmrr_bench_geo_complete] >> trigger_rambo_enhancement


[update_bench_rates_record,finished_seasonality_tables_transfer,trigger_rambo_enhancement] >> finished_rambo_benchmark_dag >> log_error

# This dummy will be an 'end leaf' of the dag, which will fail the dag if there is any error.
fail_dag_if_error = DummyOperator(task_id="fail-dag-if-error", dag=dag)
finished_rambo_benchmark_dag >> fail_dag_if_error

