import datetime as dt
import json
import os
import re
from datetime import timedelta, datetime

from airflow import DAG
from airflow.kubernetes.secret import Secret
from airflow.models import Variable
from airflow.operators.bash_operator import BashOperator
from airflow.operators.dagrun_operator import TriggerDagRunOperator
from airflow.operators.dummy_operator import DummyOperator
from airflow.providers.google.cloud.operators.kubernetes_engine import (
    GKEStartPodOperator,
)
from airflow.utils.task_group import TaskGroup
from airflow.hooks.base_hook import BaseHook
from airflow.contrib.operators.slack_webhook_operator import SlackWebhookOperator
from shared_libs.default_args import get_default_args
from shared_libs.image_versions import get_gcr_registry, get_full_image_name
from shared_libs.kubernetes import (
    standard_tolerations,
    standard_affinity,
    get_image_pull_policy,
)
from shared_libs.slack_callback import task_fail_slack_alert
from shared_libs.pagerduty_callback import dag_fail_pagerduty_alert
from kubernetes.client import models as k8s

# CONSTANTS
ENVIRONMENT = os.environ.get("COMPOSER_ENVIRONMENT", "dev")
CLUSTER_ZONE = "us-central1-b"
IMAGE_PULL_POLICY = get_image_pull_policy(ENVIRONMENT)
GCR_REGISTRY = get_gcr_registry(ENVIRONMENT)
RESOURCES = k8s.V1ResourceRequirements(
    requests={"cpu": "500m", "memory": "1.0Gi"},
)
MAJOR_VERSION = "1"
MINOR_VERSION = "1"
TABLE_SUFFIX = "{{dag_run.conf['table_suffix']}}"
RESOURCES_SMALL_TASK = k8s.V1ResourceRequirements(
    requests={"cpu": "500m", "memory": "1.0Gi"},
)
QA_SLACK_CHANNEL = '#data-analytics-alerts'

if ENVIRONMENT not in ['prod','dr']:
    DB_SECRET_NAME = "de-rambo-export-dev"
    BUCKET_NAME = "analytics-data-dev-62c5a2-rental-metrics-benchmark-aggs"
    PROJECT_NAME = "management-dev-d6ba4d"
    DATA_PROJECT_NAME = "analytics-data-dev-62c5a2"
    CLUSTER_NAME = "composer-jobs-v3-dev"

    # AGGS CONSTANTS
    AGGS_DESTINATION_DATASET = "rental_metrics_aggs_dev"
    AGGS_STAGE_DATASET_NAME = "rental_metrics_aggs_stage_dev"
    LOGGING_GCP_PROJECT = "management-dev-d6ba4d"
    PAGERDUTY_NOTIFY = False
    REPRESENTATION_JS_PATH: str = (
        "/usr/local/airflow/dags/constants/representation.json"
    )

    # import into SS pool
    ss_import_pool_name = "gdevetl01_bcp_export"

    # MATERIALIZED INPUT CONSTANTS
    # We are reusing the version database for aggs so that we reduce the amount of datasets
    MATERIALIZED_INPUTS_VERSION_DATASET_NAME = "rental_metrics_aggs_version_dev"
    GETL01_SQL_SERVER = "gdevetl01.rasgcp.net"

    # Customer Segmentation Constants
    CUSTOMER_SEGMENTATION_SQL_FOLDER = (
        "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies"
        "/customer_segmentation_queries/"
    )
    CUSSEG_DESTINATION_DATASET = "rental_metrics_aggs_dev"
    CUSSEG_VERSION_DATASET = "rental_metrics_aggs_version_dev"

    # DimFMRR constants
    DIM_FM_RR_DESTINATION_DATASET = "dim_fm_rr_dev"
    DIM_FM_RR_STAGE_DATASET = "dim_fm_rr_stage_dev"
    DIM_FM_RR_SOURCE_DATASET = "dim_fm_rr_version_dev"
    DIM_FMRR_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/dim_fm_rr_queries/"

    FMRR_BENCH_GROWTH_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/fmrr_bench_growth/"
    DAG_BASE_SQL_FOLDER = (
        "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/"
    )

    # DEMO CLIENTS constants
    DEMO_CLIENTS_QUERY_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/demo_clients/"
    RDO_PUBLISH_DATASET_NAME = "rdo_publish_dev"


    if ENVIRONMENT == "local":
        REPRESENTATION_JS_PATH: str = (
            "/usr/local/airflow/projects/rental-metrics-aggs/dags/representation.json"
        )
        CUSTOMER_SEGMENTATION_SQL_FOLDER = (
            "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies"
            "/customer_segmentation_queries/"
        )
        DIM_FMRR_SQL_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/dim_fm_rr_queries/"

        FMRR_BENCH_GROWTH_SQL_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/fmrr_bench_growth/"
        DAG_BASE_SQL_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/"
        DEMO_CLIENTS_QUERY_FOLDER = "/usr/local/airflow/projects/rental-metrics-aggs/dags/dag_file_dependencies/demo_clients/"

elif ENVIRONMENT == "dr":
    DB_SECRET_NAME = "de-rambo-export-prod"
    BUCKET_NAME = "dr-prod-ca5e7f-rental-metrics-benchmark-aggs"
    PROJECT_NAME = "dr-prod-ca5e7f"
    LOGGING_GCP_PROJECT = "dr-prod-ca5e7f"
    DATA_PROJECT_NAME = "dr-prod-ca5e7f"
    CLUSTER_NAME = "composer-jobs-v3-dev"

    # Aggs constants
    AGGS_DESTINATION_DATASET = "rental_metrics_aggs"
    AGGS_STAGE_DATASET_NAME = "rental_metrics_aggs_stage"
    PAGERDUTY_NOTIFY = True
    REPRESENTATION_JS_PATH: str = (
        "/usr/local/airflow/dags/constants/representation.json"
    )

    # import into SS pool
    ss_import_pool_name = "getl01_bcp_export"

    # MATERIALIZED INPUT CONSTANTS
    MATERIALIZED_INPUTS_VERSION_DATASET_NAME = "rental_metrics_aggs_version"
    GETL01_SQL_SERVER = "getl01.rasgcp.net"

    # Customer Segmentation Constants
    CUSTOMER_SEGMENTATION_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/customer_segmentation_queries/"
    CUSSEG_DESTINATION_DATASET = "rental_metrics_aggs"
    CUSSEG_VERSION_DATASET = "rental_metrics_aggs_version"

    # DimFMRR constants
    DIM_FM_RR_DESTINATION_DATASET = "dim_fm_rr"
    DIM_FM_RR_STAGE_DATASET = "dim_fm_rr_stage"
    DIM_FM_RR_SOURCE_DATASET = "dim_fm_rr_version"
    DIM_FMRR_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/dim_fm_rr_queries/"
    FMRR_BENCH_GROWTH_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/fmrr_bench_growth/"
    DAG_BASE_SQL_FOLDER = (
        "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/"
    )

    # demo clients constants
    DEMO_CLIENTS_QUERY_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/demo_clients/"
    RDO_PUBLISH_DATASET_NAME = "rdo_publish"



else:
    DB_SECRET_NAME = "de-rambo-export-prod"
    BUCKET_NAME = "analytics-data-prod-1e04f6-rental-metrics-benchmark-aggs"
    PROJECT_NAME = "management-prod-837a97"
    LOGGING_GCP_PROJECT = "management-prod-837a97"
    DATA_PROJECT_NAME = "analytics-data-prod-1e04f6"
    CLUSTER_NAME = "composer-jobs-v3-prod"

    # Aggs constants
    AGGS_DESTINATION_DATASET = "rental_metrics_aggs"
    AGGS_STAGE_DATASET_NAME = "rental_metrics_aggs_stage"
    PAGERDUTY_NOTIFY = True
    REPRESENTATION_JS_PATH: str = (
        "/usr/local/airflow/dags/constants/representation.json"
    )

    # import into SS pool
    ss_import_pool_name = "getl01_bcp_export"

    # MATERIALIZED INPUT CONSTANTS
    MATERIALIZED_INPUTS_VERSION_DATASET_NAME = "rental_metrics_aggs_version"
    GETL01_SQL_SERVER = "getl01.rasgcp.net"

    # Customer Segmentation Constants
    CUSTOMER_SEGMENTATION_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/customer_segmentation_queries/"
    CUSSEG_DESTINATION_DATASET = "rental_metrics_aggs"
    CUSSEG_VERSION_DATASET = "rental_metrics_aggs_version"
 
    # DimFMRR constants
    DIM_FM_RR_DESTINATION_DATASET = "dim_fm_rr"
    DIM_FM_RR_STAGE_DATASET = "dim_fm_rr_stage"
    DIM_FM_RR_SOURCE_DATASET = "dim_fm_rr_version"
    DIM_FMRR_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/dim_fm_rr_queries/"
    FMRR_BENCH_GROWTH_SQL_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/fmrr_bench_growth/"
    DAG_BASE_SQL_FOLDER = (
        "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/"
    )

    # demo clients constants
    DEMO_CLIENTS_QUERY_FOLDER = "/usr/local/airflow/dags/rental-metrics-aggs/dag_file_dependencies/demo_clients/"
    RDO_PUBLISH_DATASET_NAME = "rdo_publish"



namespace = "rental-metrics-aggs"
service_account_name = namespace
custom_args = {
    "owner": "<EMAIL>",
    "start_date": datetime(2021, 9, 20),
    "retries": 4,
    "retry_delay": timedelta(minutes=5),
    "on_failure_callback": task_fail_slack_alert,
    "project_id": PROJECT_NAME,
    "cluster_name": CLUSTER_NAME,
    "tolerations": standard_tolerations,
    "affinity": standard_affinity,
    "image_pull_policy": IMAGE_PULL_POLICY,
    "startup_timeout_seconds": 300,
    "is_delete_operator_pod": True,
    "execution_timeout": timedelta(hours=5),
    "namespace": "rental-metrics-aggs",
    "service_account_name": service_account_name,
    "depends_on_past": False,
    # our goal here is that we know exactly how we want to prioritize the tasks
    "weight_rule": "absolute",
}
default_args = get_default_args(custom_args)

dag = DAG(
    f"rambo-datamart-v{MAJOR_VERSION}.{MINOR_VERSION}",
    schedule_interval=None,
    default_args=default_args,
    max_active_runs=1,
    catchup=False,
    concurrency=50,
    description="""This dag will process all rambo logic before benchmark_rates. 
    This includes: Materialize Inputs (RentalRates and FleetMetrics), Customer Segmentation and Benchmark Aggs, in order.
    Also, after Materialize Inputs, we process FMRR base tables, ClientQuartiles, and FMRR Bench Growth and utils.
    After the bigquery tables finish computing, we trigger rambo_benchmark dag. 
    But some transfers to SQL Server continue running and this DAG finishes later.""",
    tags=["rambo"],
    on_failure_callback=dag_fail_pagerduty_alert(
        "compute_rental_metrics_aggs_pagerduty_api_key"
    ),
)

# secrets/passwords for the compute benchmark rates tasks
secret_db_username = Secret(
    deploy_type="env", deploy_target="WIN_USER", secret=DB_SECRET_NAME, key="username"
)

secret_db_password = Secret(
    deploy_type="env",
    deploy_target="WIN_PASSWORD",
    secret=DB_SECRET_NAME,
    key="password",
)


# This finished_rambo_datamart only takes into account the Bigquery Part, as the next dag can trigger.
# However, the transfers will continue to run and will trigger the 'update_record' tasks.
finished_rambo_datamart = DummyOperator(task_id="finished-rambo-datamart", dag=dag)


# Check if triggered ingestions also finish before starting rambo_datamart
check_for_pipeline_dependencies = GKEStartPodOperator(
    task_id="check-for-pipeline-dependencies",
    name="check-for-pipeline-dependencies",
    retries=480,  # 2 * 60 * 4 (we expect that this task should run at most for 4 hours).
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    arguments=["python3", "main.py", "check-if-rambo-dependencies-are-met"],
    execution_timeout=dt.timedelta(minutes=60),
    retry_delay=dt.timedelta(seconds=10),
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
    },
    dag=dag,
)

# populate_drpt_groups - Standalone view used later on the pipeline

# Open the query file, and replace the query parameters
sql_as_string = open(
    DAG_BASE_SQL_FOLDER + "reload_ctrl_tables_inputs/populate_drpt_groups.sql"
).read()
sql_as_string = sql_as_string.format(INGESTION_DATASET=AGGS_DESTINATION_DATASET)

populate_drpt_groups = GKEStartPodOperator(
    task_id="populate-drpt-groups",
    name="populate-drpt-groups",
    image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
    arguments=["python3", "main.py", "execute-bigquery"],
    do_xcom_push=False,
    resources=RESOURCES_SMALL_TASK,
    env_vars={
        "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        "QUERY": sql_as_string,
        "DESTINATION_TABLE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_SOURCE_DATASET}.drpt_groups__{TABLE_SUFFIX}",
        "VIEW_TO_REPLACE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_DESTINATION_DATASET}.drpt_groups",
        "XCOM_PUSH": "False",
        "WRITE_DISPOSITION": "WRITE_TRUNCATE",
    },
    dag=dag,
)
check_for_pipeline_dependencies >> populate_drpt_groups


# Open the query file, and replace the query parameters
sql_as_string = open(
    DAG_BASE_SQL_FOLDER + "reload_ctrl_tables_inputs/populate_dim_month_enhanced.sql"
).read()
sql_as_string = sql_as_string.format(INGESTION_DATASET=AGGS_DESTINATION_DATASET)

populate_dim_month_enhanced = GKEStartPodOperator(
    task_id="populate-dim-month-enhanced",
    name="populate-dim-month-enhanced",
    image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
    arguments=["python3", "main.py", "execute-bigquery"],
    do_xcom_push=False,
    resources=RESOURCES_SMALL_TASK,
    env_vars={
        "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        "QUERY": sql_as_string,
        "DESTINATION_TABLE": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.dim_month_enhanced__{TABLE_SUFFIX}",
        "VIEW_TO_REPLACE": f"{DATA_PROJECT_NAME}.{AGGS_DESTINATION_DATASET}.dim_month_enhanced",
        "XCOM_PUSH": "False",
        "WRITE_DISPOSITION": "WRITE_TRUNCATE",
    },
    dag=dag,
)
check_for_pipeline_dependencies >> populate_dim_month_enhanced

#######################################################
############### Materialize Inputs ####################
#######################################################

with TaskGroup(group_id="materialize-inputs", dag=dag) as mat_inputs_group:
    finished_materialized_inputs = DummyOperator(
        task_id="finished-materialized-inputs", dag=dag
    )

    compute_client_configurations = GKEStartPodOperator(
        task_id="compute-client-configurations",
        name="compute-client-configurations",
        image=get_full_image_name("rambo-materialize-inputs", GCR_REGISTRY),
        priority_weight=3,
        execution_timeout=timedelta(minutes=30),
        arguments=["python3", "main.py", "run-client-configurations"],
        resources=k8s.V1ResourceRequirements(requests={"cpu": "500m", "memory": "0.3Gi"}),
        do_xcom_push=False,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_DATASET_NAME": AGGS_DESTINATION_DATASET,
            "DESTINATION_TABLE": "ras_datamart_analytics_reporting_dbo_client_configurations",
            "VERSION_DATASET_NAME": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
            "CLIENT_CONFIGURATIONS_TABLE": f"ras_datamart_analytics_reporting_dbo_client_configurations__{TABLE_SUFFIX}",
            "LOG_NAME": "rambo_pipeline",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    # Open the query file, and replace the query parameters
    sql_as_string = open(
        DAG_BASE_SQL_FOLDER + "bench_preloads/reload_client_largest_market.sql"
    ).read()
    sql_as_string = sql_as_string.format(INGESTION_DATASET=AGGS_DESTINATION_DATASET)

    reload_client_largest_market = GKEStartPodOperator(
        task_id="reload-client-largest-market",
        name="reload-client-largest-market",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": sql_as_string,
            "DESTINATION_TABLE": f"{DATA_PROJECT_NAME}.{MATERIALIZED_INPUTS_VERSION_DATASET_NAME}.ras_datamart_analytics"
            f"_reporting_dbo_client_largest_market__{TABLE_SUFFIX}",
            "VIEW_TO_REPLACE": f"{DATA_PROJECT_NAME}.{AGGS_DESTINATION_DATASET}.ras_datamart_analytics_reporting"
            f"_dbo_client_largest_market",
            "XCOM_PUSH": "False",
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/MATERIALIZE_INPUTS_PROCESSING/RELOAD_CLIENT_LARGEST_MARKET",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
            "WRITE_DISPOSITION": "WRITE_TRUNCATE",
        },
        dag=dag,
    )

    fixed_pricing_table = "ras_datamart_analytics_reporting_dbo_client_fixed_pricing"
    regional_pricing_table = (
        "ras_datamart_analytics_reporting_dbo_client_regional_pricing"
    )

    compute_data_feed_rental_rates = GKEStartPodOperator(
        task_id="compute-data-feed-rental-rates",
        name="compute-data-feed-rental-rates",
        image=get_full_image_name("rambo-materialize-inputs", GCR_REGISTRY),
        priority_weight=3,
        execution_timeout=timedelta(minutes=45),
        arguments=["python3", "main.py", "run-datafeed-rental-rates"],
        resources=k8s.V1ResourceRequirements(requests={"cpu": "500m", "memory": "0.3Gi"}),
        do_xcom_push=False,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_DATASET_NAME": AGGS_DESTINATION_DATASET,
            "DESTINATION_TABLE": "ras_datamart_analytics_reporting_dbo_data_feed_rental_rates",
            "VERSION_DATASET_NAME": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
            "RDO_PUBLISH_DATASET_NAME": RDO_PUBLISH_DATASET_NAME, 
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DATA_FEED_RR_TABLE_NAME": "ras_datamart_analytics_reporting_dbo_data_feed_rental_rates",
            "LOG_NAME": "rambo_pipeline",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "DAG_EXECUTION_DATE": "{{ ts }}",
            "FIXED_PRICING_TABLE": fixed_pricing_table,
            "REGIONAL_PRICING_TABLE": regional_pricing_table,
        },
        dag=dag,
    )

    compute_data_feed_fleet_metrics = GKEStartPodOperator(
        task_id="compute-data-feed-fleet-metrics",
        name="compute-data-feed-fleet-metrics",
        image=get_full_image_name("rambo-materialize-inputs", GCR_REGISTRY),
        priority_weight=3,
        execution_timeout=timedelta(minutes=30),
        arguments=["python3", "main.py", "run-datafeed-fleet-metrics"],
        resources=k8s.V1ResourceRequirements(requests={"cpu": "500m", "memory": "0.3Gi"}),
        do_xcom_push=False,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_DATASET_NAME": AGGS_DESTINATION_DATASET,
            "DESTINATION_TABLE": "ras_datamart_analytics_reporting_dbo_data_feed_fleet_metrics",
            "VERSION_DATASET_NAME": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
            "DATA_FEED_FM_TABLE_NAME": "ras_datamart_analytics_reporting_dbo_data_feed_fleet_metrics",
            "CLIENT_TTM_YIELD_TABLE_NAME": "client_ttm_yield",
            "LOG_NAME": "rambo_pipeline",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    compute_data_feed_branch_info = GKEStartPodOperator(
        task_id="compute-data-feed-branch-info",
        name="compute-data-feed-branch-info",
        image=get_full_image_name("rambo-materialize-inputs", GCR_REGISTRY),
        priority_weight=3,
        execution_timeout=timedelta(minutes=30),
        arguments=["python3", "main.py", "run-datafeed-branch-info"],
        resources=k8s.V1ResourceRequirements(requests={"cpu": "500m", "memory": "0.3Gi"}),
        do_xcom_push=False,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_DATASET_NAME": AGGS_DESTINATION_DATASET,
            "DESTINATION_TABLE": "ras_datamart_analytics_reporting_dbo_data_feed_branch_info",
            "VERSION_DATASET_NAME": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
            "DATA_FEED_BRANCH_INFO_TABLE": f"ras_datamart_analytics_reporting_dbo_data_feed_branch_info__{TABLE_SUFFIX}",
            "LOG_NAME": "rambo_pipeline",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    compute_data_feed_fleet_metrics_wk = GKEStartPodOperator(
        task_id="compute-data-feed-fleet-metrics-wk",
        name="compute-data-feed-fleet-metrics-wk",
        image=get_full_image_name("rambo-materialize-inputs", GCR_REGISTRY),
        priority_weight=3,
        execution_timeout=timedelta(minutes=30),
        arguments=["python3", "main.py", "run-fleet-metrics-wk"],
        resources=k8s.V1ResourceRequirements(requests={"cpu": "500m", "memory": "0.3Gi"}),
        do_xcom_push=False,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "SOURCE_DATASET_NAME": AGGS_DESTINATION_DATASET,
            "DESTINATION_TABLE": "ras_datamart_analytics_reporting_dbo_data_feed_fleet_metrics_wk",
            "VERSION_DATASET_NAME": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
            "FLEET_METRICS_WK_TABLE": f"ras_datamart_analytics_reporting_dbo_data_feed_fleet_metrics_wk__{TABLE_SUFFIX}",
            "LOG_NAME": "rambo_pipeline",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    # Now I'll transfer the 'reload pricing attributes' tables back to SQL Server:
    transfer_largest_market_to_ss = GKEStartPodOperator(
        task_id="transfer-largest-market-to-ss",
        name="transfer-largest-market-to-ss",
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        execution_timeout=timedelta(minutes=30),
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(requests={"cpu": "2000m", "memory": "1Gi"}),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
            "BQ_TABLE": f"ras_datamart_analytics_reporting_dbo_client_largest_market__{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.ClientLargestMarket",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": """{
                        "clientid" : "ClientID",
                        "rouse_market" : "Rouse Market",
                        "rouse_district" : "Rouse District",
                        "rouse_region" : "Rouse Region",
                        "country" : "Country",
                        "cat_district" : "CAT District",
                        "cat_territory" : "CAT Territory",
                        "cat_subterritory" : "CAT SubTerritory",
                        "komatsu_region" : "Komatsu Region",
                        "komatsu_territory" : "Komatsu Territory",
                        "komatsu_subterritory" : "Komatsu SubTerritory"
                    }""",
            "PARSE_DTYPES": "True",
        },
        dag=dag,
    )
    transfer_fixed_pricing_to_ss = GKEStartPodOperator(
        task_id="transfer-fixed-pricing-to-ss",
        name="transfer-fixed-pricing-to-ss",
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        execution_timeout=timedelta(minutes=30),
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(requests={"cpu": "2000m", "memory": "2Gi"}),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
            "BQ_TABLE": fixed_pricing_table,
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.ClientFixedPricing",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": """{
                        "monthid" : "MonthID",
                        "clientid" : "ClientID",
                        "client_product_type" : "Client Product Type",
                        "customer_" : "Customer #",
                        "revenue" : "Revenue",
                        "contracts" : "Contracts",
                        "rate" : "Rate",
                        "t6_contracts" : "T6 Contracts",
                        "t6_count" : "T6 Count"
                    }""",
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )
    transfer_regional_pricing_to_ss = GKEStartPodOperator(
        task_id="transfer-regional-pricing-to-ss",
        name="transfer-regional-pricing-to-ss",
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        execution_timeout=timedelta(minutes=30),
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(requests={"cpu": "2000m", "memory": "2Gi"}),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": MATERIALIZED_INPUTS_VERSION_DATASET_NAME,
            "BQ_TABLE": regional_pricing_table,
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.ClientRegionalPricing",
            "WRITE_MODE": "overwrite",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": """{
                        "monthid" : "MonthID",
                        "clientid" : "ClientID",
                        "client_product_type" : "Client Product Type",
                        "customer_" : "Customer #",
                        "market_count" : "Market Count"
                    }""",
            "PARSE_DTYPES": "True",
        },
        dag=dag,
    )

    # Client_largest_market is created in its own task, while client_fixed_pricing and client_regional_pricing
    # are created inside DataFeed_Rental Rates
    reload_client_largest_market >> transfer_largest_market_to_ss
    compute_data_feed_rental_rates >> transfer_fixed_pricing_to_ss
    compute_data_feed_rental_rates >> transfer_regional_pricing_to_ss

    # materialized inputs dependencies
    check_for_pipeline_dependencies >> reload_client_largest_market

    (
        reload_client_largest_market
        >> [
            compute_data_feed_rental_rates,
            compute_data_feed_fleet_metrics,
            compute_data_feed_branch_info,
            compute_client_configurations,
            compute_data_feed_fleet_metrics_wk,
        ]
        >> finished_materialized_inputs
    )

##########################################################
############### Customer Segmentation ####################
##########################################################
with TaskGroup(
    group_id="customer-segmentation", dag=dag
) as customer_segmentation_group:
    finished_customer_segmentation = DummyOperator(
        task_id="finished-customer-segmentation", dag=dag
    )

    cusseg_tasks = [
        {
            "task_id": "load-customer-xmonth",
            "table_name": "ras_datamart_computation_dbo_cus_seg_customer_x_month",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["insert-cus-seg-record"],
            "query_file": "load_customer_xmonth.sql",
            "query_params": {"DESTINATION_DATASET": CUSSEG_DESTINATION_DATASET},
        },
        {
            "task_id": "load-current-customer-info",
            "table_name": "ras_datamart_computation_dbo_cus_seg_current_customer_info",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["load-customer-xmonth"],
            "query_file": "load_current_customer_info.sql",
            "query_params": {"DESTINATION_DATASET": CUSSEG_DESTINATION_DATASET},
        },
        {
            "task_id": "load-current-client-info",
            "table_name": "ras_datamart_computation_dbo_cus_seg_current_client_info",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["load-current-customer-info"],
            "query_file": "load_current_client_info.sql",
            "query_params": {"DESTINATION_DATASET": CUSSEG_DESTINATION_DATASET},
        },
        {
            "task_id": "load-ttm-customer-info",
            "table_name": "ras_datamart_computation_dbo_cus_seg_ttm_customer_info",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["load-current-client-info"],
            "query_file": "load_ttm_customer_info.sql",
            "query_params": {"DESTINATION_DATASET": CUSSEG_DESTINATION_DATASET},
        },
        {
            "task_id": "update-ttm-customer-info",
            "table_name": "ras_datamart_computation_dbo_cus_seg_ttm_customer_info",
            "write_disposition": "WRITE_TRUNCATE",
            # This task is an update that reads and writes the results on the same table,
            # so we can't recreate the table in this case
            "recreate_table": "False",
            "task_dependencies": ["load-ttm-customer-info"],
            "query_file": "update_ttm_customer_info.sql",
            "query_params": {"DESTINATION_DATASET": CUSSEG_DESTINATION_DATASET},
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-ttm-customer-info-to-ss",
            "sql_server_table_name": "dbo.CusSeg_TTMCustomerInfo",
            "NUMBER_OF_PARTITIONS": 6,
            "PARTITION_COLUMN": "clientid",
            "PARTITION_COLUMN_SS": "ClientID",
            "chunksize": "100000",
            "column_mapping": """{
                "clientid" : "ClientID",
                "monthid" : "MonthID",
                "customer_" : "Customer #",
                "avg_ttm_mo_rev" : "Avg TTM Mo Rev",
                "ttm_contract_rev_" : "TTM Contract Rev %",
                "ttm_cuspcttotal_rev" : "TTM CusPctTotal Rev",
                "avg_ttm_mo_unit_ct" : "Avg TTM Mo Unit CT",
                "ttm_market_ct" : "TTM Market CT",
                "ttm_avg_cyclebill" : "TTM Avg CycleBill",
                "ttm_monthly_" : "TTM Monthly %",
                "ttm_total_rev_" : "TTM Total Rev",
                "avg_ttm_mo_contract_ct" : "Avg TTM Mo Contract CT",
                "ttm_month_ct" : "TTM Month CT",
                "ttm_weekly_" : "TTM Weekly %",
                "ttm_daily_" : "TTM Daily %",
                "ttm_hourly_" : "TTM Hourly %",
                "ttm_other_" : "TTM Other %",
                "eq_aerial" : "EQ Aerial",
                "eq_earthmoving" : "EQ Earthmoving",
                "eq_forklifts" : "EQ Forklifts",
                "eq_other" : "EQ Other"  
            }""",
        },
        {
            "task_id": "load-evidence-large-recurring",
            "table_name": "ras_datamart_computation_dbo_cus_seg_evidence_large_recurring",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["load-dynamic-thresholds"],
            "query_file": "load_evidence_large_recurring.sql",
            "query_params": {"DESTINATION_DATASET": CUSSEG_DESTINATION_DATASET},
        },
        {
            "task_id": "load-evidence-small-infrequent",
            "table_name": "ras_datamart_computation_dbo_cus_seg_evidence_small_infrequent",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["load-dynamic-thresholds"],
            "query_file": "load_evidence_small_infrequent.sql",
            "query_params": {"DESTINATION_DATASET": CUSSEG_DESTINATION_DATASET},
        },
        {
            "task_id": "optimize-evidence-weights",
            # The task also modifies Dynamic Thresholds, but for the sake of Monitoring,
            # I'll log the count of rows from Group Assignment Table
            "table_name": "ras_datamart_computation_dbo_cus_seg_group_assignment",
            # Write disposition must be none because there are scripting that creates and
            # updates the tables instead of a select that saves the result as a table.
            "write_disposition": None,
            "task_dependencies": [
                "load-evidence-large-recurring",
                "load-evidence-small-infrequent",
            ],
            "query_file": "optimize_evidence_weights.sql",
            "query_params": {
                "DESTINATION_DATASET": CUSSEG_DESTINATION_DATASET,
                "VERSION_DATASET_NAME": CUSSEG_VERSION_DATASET,
                "GROUP_ASSIGNMENT_TABLE_NAME": f"ras_datamart_computation_dbo_cus_seg_group_assignment__{TABLE_SUFFIX}",
                # Table will not be versioned as the job starts with an update
                "DYNAMIC_THRESHOLDS_TABLE_NAME": "ras_datamart_computation_dbo_cus_seg_dynamic_thresholds",
            },
            "view_to_replace": None,
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-group-assignment-to-ss",
            "sql_server_table_name": "dbo.CusSeg_GroupAssignment",
            "NUMBER_OF_PARTITIONS": 4,
            "PARTITION_COLUMN": "clientid",
            "PARTITION_COLUMN_SS": "ClientID",
            "column_mapping": """{
                "monthid" : "MonthID",
                "clientid" : "ClientID",
                "customer_" : "Customer #",
                "customer_group" : "Customer Group",
                "customer_subgroup" : "Customer Subgroup",
                "lr_score" : "LR Score",
                "si_score" : "SI Score"  
            }""",
        },
        # Dynamic Thresholds runs before the evidence tables and optimize evidence weights,
        # but I'll put it here because I can only transfer it back after the optimize task.
        # This way, optimize-evidence-weights is already created when I create this transfer task.
        {
            "task_id": "load-dynamic-thresholds",
            "table_name": "ras_datamart_computation_dbo_cus_seg_dynamic_thresholds",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["update-ttm-customer-info"],
            "query_file": "load_dynamic_thresholds.sql",
            "query_params": {"DESTINATION_DATASET": CUSSEG_DESTINATION_DATASET},
            # Creating a default table because it will be updated in Optimize Evidence Weights task
            "destination_table": f"{DATA_PROJECT_NAME}.{CUSSEG_VERSION_DATASET}.ras_datamart_computation_dbo_cus_seg_dynamic_thresholds",
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-dynamic-thresholds-to-ss",
            "sql_server_table_name": "dbo.CusSeg_DynamicThresholds",
            "chunksize": "50000",
            "column_mapping": """{      
                "clientid" : "ClientID",
                "avg01" : "AVG01",
                "sd01" : "SD01",
                "avg05" : "AVG05",
                "sd05" : "SD05",
                "avg07" : "AVG07",
                "sd07" : "SD07",
                "avg09" : "AVG09",
                "sd09" : "SD09",
                "avg10" : "AVG10",
                "sd10" : "SD10",
                "lr_w01" : "LR_W01",
                "lr_w02" : "LR_W02",
                "lr_w03" : "LR_W03",
                "lr_w04" : "LR_W04",
                "lr_w05" : "LR_W05",
                "lr_w06" : "LR_W06",
                "lr_w07" : "LR_W07",
                "lr_w08" : "LR_W08",
                "lr_w09" : "LR_W09",
                "lr_w10" : "LR_W10",
                "si_w01" : "SI_W01",
                "si_w02" : "SI_W02",
                "si_w03" : "SI_W03",
                "si_w04" : "SI_W04",
                "si_w05" : "SI_W05",
                "si_w06" : "SI_W06",
                "si_w07" : "SI_W07",
                "si_w08" : "SI_W08",
                "si_w09" : "SI_W09",
                "si_w10" : "SI_W10"  
                }""",
        },
        {
            "task_id": "reload-current-group-variance",
            "table_name": "ras_datamart_computation_dbo_cus_seg_current_group_variance",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["optimize-evidence-weights"],
            "query_file": "reload_current_group_variance.sql",
            "query_params": {"DESTINATION_DATASET": CUSSEG_DESTINATION_DATASET},
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-current-group-variance-to-ss",
            "sql_server_table_name": "dbo.CusSeg_CurrentGroupVariance",
            "column_mapping": """{      
                "monthid" : "MonthID",
                "clientid" : "ClientID",
                "customer_group" : "Customer Group",
                "rouse_market" : "Rouse Market",
                "rouse_product_type" : "Rouse Product Type",
                "rate_type" : "Rate Type",
                "group_rate" : "Group Rate",
                "group_contracts" : "Group Contracts",
                "total_rate" : "Total Rate",
                "total_contracts" : "Total Contracts"  
            }""",
        },
        {
            "task_id": "reload-group-stats",
            "table_name": "ras_datamart_computation_dbo_cus_seg_group_stats",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["reload-current-group-variance"],
            "query_file": "reload_group_stats.sql",
            "query_params": {"DESTINATION_DATASET": CUSSEG_DESTINATION_DATASET},
            "send_back_to_ss": True,
            "back_to_ss_task_id": "transfer-group-stats-to-ss",
            "sql_server_table_name": "dbo.CusSeg_GroupStats",
            "column_mapping": """{
                "monthid" : "MonthID",
                "clientid" : "ClientID",
                "customer_group" : "Customer Group",
                "cus_cnt" : "Cus CNT",
                "rev_sum" : "Rev SUM",
                "rev_avg" : "Rev AVG",
                "rev_min" : "Rev MIN",
                "rev_max" : "Rev MAX",
                "rev_std" : "Rev STD",
                "avgct_eqid" : "AvgCT EQid",
                "avgct_market" : "AvgCT Market",
                "avg_cyclebill" : "Avg CycleBill",
                "rt_monthly" : "RT Monthly",
                "rt_weekly" : "RT Weekly",
                "rt_daily" : "RT Daily",
                "rt_hourly" : "RT Hourly",
                "rt_other" : "RT Other",
                "eq_aerial" : "EQ Aerial",
                "eq_earthmoving" : "EQ Earthmoving",
                "eq_forklifts" : "EQ Forklifts",
                "eq_other" : "EQ Other",
                "group_comp" : "Group Comp",
                "group_rev" : "Group Rev",
                "group_var" : "Group Var"
            }""",
        },
        {
            "task_id": "load-market-protype-variance",
            "table_name": "ras_datamart_computation_dbo_cus_seg_market_protype_variance",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["reload-current-group-variance"],
            "query_file": "load_market_protype_variance.sql",
            "query_params": {"DESTINATION_DATASET": CUSSEG_DESTINATION_DATASET},
        },
        {
            "task_id": "view-group-variance-fleet-co",
            "table_name": "ras_datamart_analytics_reporting_dbo_cus_seg_group_variance_fleet_co",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["reload-current-group-variance"],
            "query_file": "view_group_variance_fleet_co.sql",
            "query_params": {"DESTINATION_DATASET": CUSSEG_DESTINATION_DATASET},
        },
        {
            "task_id": "view-group-variance-protype-market",
            "table_name": "ras_datamart_analytics_reporting_dbo_cus_seg_group_variance_protype_market",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["reload-current-group-variance"],
            "query_file": "view_group_variance_protype_market.sql",
            "query_params": {"DESTINATION_DATASET": CUSSEG_DESTINATION_DATASET},
        },
        {
            "task_id": "view-latest-group-assignment",
            "table_name": "ras_datamart_analytics_reporting_dbo_cusseg_latest_group_assignment",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["optimize-evidence-weights"],
            "query_file": "view_latest_group_assignment.sql",
            "query_params": {"DESTINATION_DATASET": CUSSEG_DESTINATION_DATASET},
        },
        {
            "task_id": "view-latest-customer-bin",
            "table_name": "ras_datamart_reporting_dbo_cus_seg_latest_customer_bin",
            "write_disposition": "WRITE_TRUNCATE",
            "task_dependencies": ["update-ttm-customer-info"],
            "query_file": "view_latest_customer_bin.sql",
            "query_params": {"DESTINATION_DATASET": CUSSEG_DESTINATION_DATASET},
        },
    ]

    # The insert_cus_seg_record and update_cus_seg_record will create records
    # on a SQL Server table so we can inform the Customer Segmentation queries are over
    insert_cus_seg_record = GKEStartPodOperator(
        task_id="insert-cus-seg-record",
        name="insert-cus-seg-record",
        secrets=[secret_db_username, secret_db_password],
        do_xcom_push=True,
        image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
        arguments=["python3", "main.py", "update-control-table"],
        execution_timeout=timedelta(minutes=5),
        retry_delay=timedelta(seconds=10),
        env_vars={
            "TABLE_NAME": "benchmark_etl_run",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Computation",
            "PIPELINE": "cus_seg",
            "STATUS_VALUE": "RUNNING",
            "BATCH": TABLE_SUFFIX,
        },
        dag=dag,
    )
    update_cus_seg_record = GKEStartPodOperator(
        task_id="update-cus-seg-record",
        name="update-cus-seg-record",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
        do_xcom_push=True,
        arguments=["python3", "main.py", "update-control-table"],
        execution_timeout=timedelta(minutes=5),
        retry_delay=timedelta(seconds=10),
        env_vars={
            "TABLE_NAME": "benchmark_etl_run",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Computation",
            "PIPELINE": "cus_seg",
            "STATUS_VALUE": "DONE",
            "PK": '{{ task_instance.xcom_pull(task_ids="customer-segmentation.insert-cus-seg-record", key="return_value")["pk"] }}',
            "BATCH": TABLE_SUFFIX,
        },
        dag=dag,
    )
    update_cus_seg_record_error = GKEStartPodOperator(
        task_id="update-cus-seg-record-error",
        name="update-cus-seg-record-error",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
        do_xcom_push=True,
        arguments=["python3", "main.py", "update-control-table"],
        execution_timeout=timedelta(minutes=5),
        retry_delay=timedelta(seconds=10),
        env_vars={
            "TABLE_NAME": "benchmark_etl_run",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Computation",
            "PIPELINE": "cus_seg",
            "STATUS_VALUE": "FAILED",
            "PK": '{{ task_instance.xcom_pull(task_ids="customer-segmentation.insert-cus-seg-record", key="return_value")["pk"] }}',
            "BATCH": TABLE_SUFFIX,
        },
        trigger_rule="one_failed",
        dag=dag,
    )

    update_cus_seg_record_when_clear = GKEStartPodOperator(
        task_id="update-cus-seg-record-when-clear",
        name="update-cus-seg-record-when-clear",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
        do_xcom_push=True,
        arguments=["python3", "main.py", "update-control-table"],
        execution_timeout=timedelta(minutes=5),
        retry_delay=timedelta(seconds=10),
        trigger_rule="one_success",
        env_vars={
            "TABLE_NAME": "benchmark_etl_run",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Computation",
            "PIPELINE": "cus_seg",
            "STATUS_VALUE": "RUNNING",
            "PK": '{{ task_instance.xcom_pull(task_ids="customer-segmentation.insert-cus-seg-record", key="return_value")["pk"] }}',
            "BATCH": TABLE_SUFFIX,
        },
        dag=dag,
    )

    # Customer Segmentation will start after Materialize Inputs finishes.
    finished_materialized_inputs >> insert_cus_seg_record

    # Initializing the dict with insert-cus-seg-record task, and then I'll add the other tasks
    # as I create the task objects, to be used as dependencies for the next tasks.
    # 'update-cus-seg-record' has both the update_record and the update_error, as we have to
    # update the record as DONE if all tasks succeed, or as Failed if we have a failure.
    cusseg_task_dict = {
        "insert-cus-seg-record": insert_cus_seg_record,
        "update-cus-seg-record": [update_cus_seg_record, update_cus_seg_record_error],
    }

    (
        [insert_cus_seg_record, update_cus_seg_record_error]
        >> update_cus_seg_record_when_clear
        >> update_cus_seg_record
    )

    for task_info in cusseg_tasks:

        # Default view and default destination tables
        default_view_to_replace = f"{DATA_PROJECT_NAME}.{CUSSEG_DESTINATION_DATASET}.{task_info['table_name']}"
        default_destination_table = f"{DATA_PROJECT_NAME}.{CUSSEG_VERSION_DATASET}.{task_info['table_name']}__{TABLE_SUFFIX}"

        # Default log name for CusSeg tasks
        log_task_name = task_info["task_id"].replace("-", "_").upper()
        logs_job_identifier = f"RAMBO_PIPELINE/CUSTOMER_SEGMENTATION/{log_task_name}"

        # Open the query file, and replace the query parameters if needed
        sql_file = open(CUSTOMER_SEGMENTATION_SQL_FOLDER + task_info["query_file"])
        sql_as_string = sql_file.read()
        if "query_params" in task_info.keys():
            sql_as_string = sql_as_string.format(**task_info["query_params"])

        # Base Operator 'Execute Bigquery' will do all the work of executing the queries and creating the CusSeg tables/views
        query_task = GKEStartPodOperator(
            task_id=task_info["task_id"],
            name=task_info["task_id"],
            image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
            arguments=["python3", "main.py", "execute-bigquery"],
            do_xcom_push=False,
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "QUERY": sql_as_string,
                "DESTINATION_TABLE": task_info["destination_table"]
                if "destination_table" in task_info.keys()
                else default_destination_table,
                "VIEW_TO_REPLACE": task_info["view_to_replace"]
                if "view_to_replace" in task_info.keys()
                else default_view_to_replace,
                "XCOM_PUSH": "False",
                "WRITE_LOGS": "True",
                "LOGS_GCP_PROJECT": PROJECT_NAME,
                "LOGS_JOB_IDENTIFIER": logs_job_identifier,
                "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
                "WRITE_DISPOSITION": task_info["write_disposition"]
                if "write_disposition" in task_info.keys()
                else None,
                "RECREATE_TABLE": task_info["recreate_table"]
                if "recreate_table" in task_info.keys()
                else "True",
                "TYPE_OF_PARTITIONING": task_info["type_of_partitioning"]
                if "type_of_partitioning" in task_info.keys()
                else None,
                "CLUSTERING_FIELDS": task_info["clustering_fields"]
                if "clustering_fields" in task_info.keys()
                else None,
                "PARTITION_COLUMN": task_info["partition_column"]
                if "partition_column" in task_info.keys()
                else None,
                "RANGE_START": task_info["range_start"]
                if "range_start" in task_info.keys()
                else None,
                "RANGE_END": task_info["range_end"]
                if "range_end" in task_info.keys()
                else None,
                "RANGE_INTERVAL": task_info["range_interval"]
                if "range_interval" in task_info.keys()
                else None,
            },
            dag=dag,
        )

        # Salving the task object to create the dependencies at the next step
        cusseg_task_dict[task_info["task_id"]] = query_task

        # With task groups the full_task_id is task_group_name.task_id (It changes the xcom_Pull)
        full_task_id = f'customer-segmentation.{task_info["task_id"]}'

        # Logic for sending the tables back to SQL Server using 'load-from-bq-to-ss' operator
        if (
            task_info["send_back_to_ss"]
            if "send_back_to_ss" in task_info.keys()
            else False
        ):
                
            # If there's no partition_ammount, I'll do the regular transfer
            if "NUMBER_OF_PARTITIONS" not in task_info.keys():
                transfer_table_back_to_ss = GKEStartPodOperator(
                    task_id=task_info["back_to_ss_task_id"],
                    name=task_info["back_to_ss_task_id"],
                    image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
                    pool=ss_import_pool_name,
                    priority_weight=8,
                    secrets=[secret_db_username, secret_db_password],
                    execution_timeout=timedelta(minutes=240),
                    arguments=["python3", "main.py", "load-from-bq-to-ss"],
                    resources=k8s.V1ResourceRequirements(requests={"cpu": "3000m", "memory": "3Gi"}),
                    env_vars={
                        "GCP_PROJECT": DATA_PROJECT_NAME,
                        "BQ_DATASET": CUSSEG_VERSION_DATASET,
                        "BQ_TABLE": task_info["destination_table"].split('.')[-1]
                            if "destination_table" in task_info.keys()
                            else f"{task_info['table_name']}__{TABLE_SUFFIX}",
                        "DB_SERVER": GETL01_SQL_SERVER,
                        "DB_NAME": "ras_DataMart_Analytics_Cloud",
                        "TABLE_NAME": task_info["sql_server_table_name"],
                        "WRITE_MODE": "overwrite",
                        "PARALLEL_PROCESSES": "3",
                        "GCS_BUCKET_NAME": BUCKET_NAME,
                        "CHUNKSIZE": task_info["chunksize"]
                        if "chunksize" in task_info.keys()
                        else "100000",
                        "COLUMN_MAPPING": task_info["column_mapping"],
                    },
                    dag=dag,
                )
                # The finished_customer_segmentation dummy task won't depend on the
                # tasks to send data back to SS (as the BQ processes, like computing aggs can go on).
                # However, we will just update the CusSeg Record as DONE when we finish sending the tables back.
                (
                    query_task
                    >> transfer_table_back_to_ss
                    >> cusseg_task_dict["update-cus-seg-record"]
                )

                # I can only transfer the dynamic-thresholds table back after running the optimize evidence weights
                # task. So I'm adding this dependency here. The dynamic-thresholds task must come after
                # the optimize evidence weights on the list for this to work.
                if task_info["task_id"] == "load-dynamic-thresholds":
                    (
                        cusseg_task_dict["optimize-evidence-weights"]
                        >> transfer_table_back_to_ss
                    )
            else:

                # there is partition_ammount on the keys, so I'll first partition the table
                partition_task_id = f"partition-bq-table-{task_info['task_id']}"
                partition_bq_table = GKEStartPodOperator(
                    task_id=partition_task_id,
                    name=partition_task_id,
                    image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
                    arguments=["python3", "main.py", "execute-bigquery"],
                    do_xcom_push=False,
                    resources=RESOURCES_SMALL_TASK,
                    env_vars={
                        "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                        "QUERY": open(
                            DAG_BASE_SQL_FOLDER
                            + "push_to_prod/push_to_prod_preload/partition_bq_tables_template.sql"
                        )
                        .read()
                        .replace(
                            "{NUMBER_OF_PARTITIONS}",
                            str(task_info["NUMBER_OF_PARTITIONS"]),
                        )
                        .replace(
                            "{PARTITION_COLUMN}",
                            task_info["PARTITION_COLUMN"],
                        )
                        .replace(
                            "{TABLE_NAME}",
                            f"{CUSSEG_VERSION_DATASET}.{task_info['table_name']}__{TABLE_SUFFIX}",
                        ),
                        "XCOM_PUSH": "False",
                        "WRITE_LOGS": "True",
                        "LOGS_GCP_PROJECT": PROJECT_NAME,
                        "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/PUSH_TO_PROD/{task_info['table_name'].upper()}",
                        "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
                        "WRITE_DISPOSITION": None,
                    },
                    dag=dag,
                )
                create_stg_table = GKEStartPodOperator(
                    task_id=f"create-stg-table-{task_info['task_id']}",
                    name=f"create-stg-table-{task_info['task_id']}",
                    image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
                    secrets=[secret_db_username, secret_db_password],
                    arguments=["python3", "main.py", "create-stg-table"],
                    resources=RESOURCES_SMALL_TASK,
                    # Same priority as other RAMBO Push to prod tasks
                    priority_weight=task_info["PRIORITY_WEIGHT"]
                    if "PRIORITY_WEIGHT" in task_info.keys()
                    else 14,
                    env_vars={
                        "DB_SERVER": GETL01_SQL_SERVER,
                        "DB_NAME": "ras_DataMart_Analytics_Cloud",
                        "TABLE_NAME": task_info[
                            "sql_server_table_name"
                        ],
                    },
                    dag=dag,
                )

                # Depends on the last_task, but will only start the transfer after the preload fnishes
                query_task >> partition_bq_table >> create_stg_table

                staging_table_name = (
                    task_info["sql_server_table_name"] + "_STG"
                )

                recreate_stg_table_indexes = GKEStartPodOperator(
                    task_id=f"recreate-stg-table-indexes-{task_info['task_id']}",
                    name=f"recreate-stg-table-indexes-{task_info['task_id']}",
                    image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
                    secrets=[secret_db_username, secret_db_password],
                    arguments=["python3", "main.py", "recreate-stg-table-indexes"],
                    resources=RESOURCES_SMALL_TASK,
                    # Same priority as other RAMBO Push to prod tasks
                    priority_weight=task_info["PRIORITY_WEIGHT"]
                    if "PRIORITY_WEIGHT" in task_info.keys()
                    else 14,
                    env_vars={
                        "DB_SERVER": GETL01_SQL_SERVER,
                        "DB_NAME": "ras_DataMart_Analytics_Cloud",
                        "TABLE_NAME": task_info[
                            "sql_server_table_name"
                        ],
                        "REPLACE_TABLE": 'True',
                        "DO_QA_ROW_COUNT": "True",
                        "QA_GCP_PROJECT": DATA_PROJECT_NAME,
                        "BQ_QA_TABLE": f"{DATA_PROJECT_NAME}.{CUSSEG_VERSION_DATASET}.{task_info['table_name']}__{TABLE_SUFFIX}",
                        "SS_QA_TABLE": f"ras_DataMart_Analytics_Cloud.{staging_table_name}",
                        # In case there is a manual index set, I'll pass it to the transfer
                        "MANUAL_INDEX_COMMAND": None,
                    },
                    dag=dag,
                )

                for partition_id in range(
                    0, int(task_info["NUMBER_OF_PARTITIONS"])
                ):
                    delete_before_insert_query = (
                        open(
                            DAG_BASE_SQL_FOLDER
                            + "push_to_prod/push_to_prod_preload/delete_partition_before_inserting_template.sql"
                        )
                        .read()
                        .replace("{DB_NAME}", "ras_DataMart_Analytics_Cloud")
                        .replace("{TABLE_NAME}", staging_table_name)
                        .replace("{PARTITION_ID}", str(partition_id))
                        .replace(
                            "{NUMBER_OF_PARTITIONS}",
                            str(task_info["NUMBER_OF_PARTITIONS"]),
                        )
                        .replace(
                            "{PARTITION_COLUMN}",
                            task_info["PARTITION_COLUMN_SS"],
                        )
                    )

                    # I'll execute the transfers in 'append' mode for each partition
                    transfer_task_id = (
                        f"transfer-{task_info['task_id']}-p{partition_id}-to-prod-ss"
                    )
                    transfer_table_to_prod = GKEStartPodOperator(
                        task_id=transfer_task_id,
                        name=transfer_task_id,
                        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
                        secrets=[secret_db_username, secret_db_password],
                        arguments=["python3", "main.py", "load-from-bq-to-ss"],
                        resources=task_info["RESOURCES"]
                        if "RESOURCES" in task_info.keys()
                        else k8s.V1ResourceRequirements(requests={"cpu": "2000m", "memory": "2Gi"}),
                        pool=ss_import_pool_name,
                        execution_timeout=task_info[
                            "transfer_execution_timeout"
                        ]
                        if "transfer_execution_timeout"
                        in task_info.keys()
                        else timedelta(minutes=60),
                        # Same priority as other RAMBO Push to prod tasks
                        priority_weight=task_info["PRIORITY_WEIGHT"]
                        if "PRIORITY_WEIGHT" in task_info.keys()
                        else 14,
                        env_vars={
                            "GCP_PROJECT": DATA_PROJECT_NAME,
                            "BQ_DATASET": CUSSEG_VERSION_DATASET,
                            "BQ_TABLE": f"{task_info['table_name']}__{TABLE_SUFFIX}_p{partition_id}",
                            "DB_SERVER": GETL01_SQL_SERVER,
                            "DB_NAME": "ras_DataMart_Analytics_Cloud",
                            "TABLE_NAME": staging_table_name,
                            "WRITE_MODE": "append",
                            "DO_QA_ROW_COUNT": "False",  # We'll skip the QA row count because it is only one of the partitions
                            "EXECUTE_QUERY_BEFORE": delete_before_insert_query,
                            "GCS_BUCKET_NAME": BUCKET_NAME,
                            "CHUNKSIZE": task_info["CHUNKSIZE"]
                            if "CHUNKSIZE" in task_info.keys()
                            else "100000",
                            "COLUMN_MAPPING": task_info["column_mapping"],
                            "PARSE_DTYPES": "False",
                            "PARALLEL_PROCESSES": task_info[
                                "PARALLEL_PROCESSES"
                            ]
                            if "PARALLEL_PROCESSES" in task_info.keys()
                            else "2",
                        },
                        dag=dag,
                    )
                    create_stg_table >> transfer_table_to_prod >> recreate_stg_table_indexes

                recreate_stg_table_indexes >> cusseg_task_dict["update-cus-seg-record"]


    # Looping again to set the dependencies
    for task_info in cusseg_tasks:
        # Using the task object to set the dependencies
        if "task_dependencies" in task_info.keys():
            for dependency in task_info["task_dependencies"]:
                cusseg_task_dict[dependency] >> cusseg_task_dict[task_info["task_id"]]

        # All CusSeg tasks without dependencies (end leafs of Customer Segmentation)
        # must run before the flag 'finished_customer_segmentation' run
        is_end_leaf = True
        for another_task in cusseg_tasks:
            if "task_dependencies" in another_task.keys():
                for dependency in another_task["task_dependencies"]:
                    if dependency == task_info["task_id"]:
                        is_end_leaf = False
        if is_end_leaf:
            cusseg_task_dict[task_info["task_id"]] >> finished_customer_segmentation


#########################################
##### Compute Benchmark Aggs ############
#########################################
with TaskGroup(group_id="benchmark-aggs", dag=dag) as benchmark_aggs_group:
    finished_aggs = DummyOperator(task_id="finished-aggs", dag=dag)
    # truncate benchmark_aggs

    drop_benchmark_aggs = GKEStartPodOperator(
        task_id="drop-benchmark-aggs",
        name="drop-benchmark-aggs",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": f"drop table if exists `{DATA_PROJECT_NAME}.{AGGS_DESTINATION_DATASET}.benchmark_aggs`",
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/BENCHMARK_AGGS/TRUNCATE_BENCHMARK_AGGS",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    create_bottom_quartile_fn = GKEStartPodOperator(
        task_id="create-bottom-quartile-fn",
        name="create-bottom-quartile-fn",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(
                DAG_BASE_SQL_FOLDER + "udfs/create_bottom_quartile_fn.sql"
            ).read(),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    create_top_quartile_fn = GKEStartPodOperator(
        task_id="create-top-quartile-fn",
        name="create-top-quartile-fn",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(
                DAG_BASE_SQL_FOLDER + "udfs/create_top_quartile_fn.sql"
            ).read(),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    # create benchmark_aggs table
    create_benchmark_aggs = GKEStartPodOperator(
        task_id="create-benchmark-aggs-table",
        name="create-benchmark-aggs-table",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": f"""CREATE TABLE `{DATA_PROJECT_NAME}.{AGGS_DESTINATION_DATASET}.benchmark_aggs` (
                rate_type STRING,
                month DATE,
                clientcode STRING,
                rouse_region STRING,
                rouse_district STRING,
                rouse_market STRING,
                rouse_category STRING,
                rouse_product_type STRING,
                revenue NUMERIC,
                instances NUMERIC,
                _namespace1 INT64
            )
            PARTITION BY RANGE_BUCKET(_namespace1, GENERATE_ARRAY(0, 4000, 1))
            CLUSTER BY rate_type, month, rouse_region, rouse_district
            """,
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/BENCHMARK_AGGS/TRUNCATE_BENCHMARK_AGGS",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    benchmark_aggs_qa = GKEStartPodOperator(
        task_id="benchmark-aggs-qa",
        name="benchmark-aggs-qa",
        image=get_full_image_name("rambo-data-quality", GCR_REGISTRY),
        priority_weight=3,
        do_xcom_push=False,
        arguments=["python3", "main.py", "run-qa-check"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "BQ_DATASET_NAME": AGGS_DESTINATION_DATASET,
            "BQ_TABLE_NAME": "benchmark_aggs",
        },
        dag=dag,
    )

    # remove records with revenue null from benchmark_aggs and send the records to a separate table
    remove_benchmark_aggs_null_rows = GKEStartPodOperator(
        task_id="remove-benchmark-aggs-null-rows",
        name="remove-benchmark-aggs-null-rows",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": f"""CREATE OR REPLACE TABLE `{DATA_PROJECT_NAME}.{AGGS_DESTINATION_DATASET}.benchmark_aggs_null_rows` as
                SELECT * FROM `{DATA_PROJECT_NAME}.{AGGS_DESTINATION_DATASET}.benchmark_aggs` WHERE revenue IS NULL;
                
                DELETE FROM `{DATA_PROJECT_NAME}.{AGGS_DESTINATION_DATASET}.benchmark_aggs` WHERE revenue IS NULL;
            """,
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": "RAMBO_PIPELINE/BENCHMARK_AGGS/TRUNCATE_BENCHMARK_AGGS",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
        },
        dag=dag,
    )

    qa_benchmark_aggs_null_values = GKEStartPodOperator(
        task_id="qa-benchmark-aggs-null-values",
        name="qa-benchmark-aggs-null-values",
        image=get_full_image_name("qa-check-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "run-qa-check"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "CHECK_TYPE": "expect_empty_result",
            "QA_QUERY": f"""SELECT * FROM `{DATA_PROJECT_NAME}.{AGGS_DESTINATION_DATASET}.benchmark_aggs_null_rows`
                WHERE month > DATE_SUB(current_date(), INTERVAL 3 YEAR);
            """,
        },
        dag=dag,
    )

    if ENVIRONMENT == 'prod':
        # Send slack message alert for the historical QA
        send_slack_alert_benchmark_qa = SlackWebhookOperator(
            task_id="send-slack-alert-benchmark-aggs",
            dag=dag,
            trigger_rule="one_failed",
            http_conn_id="slack",
            channel=QA_SLACK_CHANNEL,
            webhook_token=BaseHook.get_connection("slack_client_projects").password,
            message=":warning: There are records with null revenue on the past 3 years in Benchmark Aggs. Please review "
            f'<https://lookerstudio.google.com/reporting/f868417e-bf39-4158-ba6e-e775d1981026/page/p_6yuint9rld|this link.>',
            username="airflow"
        )

        [qa_benchmark_aggs_null_values] >> send_slack_alert_benchmark_qa


    def clean_file_name_to_task(file_name: str):
        file_name = re.sub("([^a-zA-Z0-9\-.])+", "-", file_name)
        if file_name.endswith("-.ipynb"):
            # this is done so that tasks get a valid name on REGEX in GKE
            file_name = file_name.replace("-.ipynb", "x.ipynb")
        return file_name.lower()

    """ 
    This variable is used in order to keep the variables of our tasks defined in process_folder as a part of the global scope,
    this prevents the interpreter from garbage collect those variables which would make the DAG lose all tasks which aren't
    on the root folders of the packages.

    """
    TASKS_DICT = {}
    # this key is going to be used on tASKS_DICT to make the tasks available when the dag is being evaluated
    COMPUTE_KEY = "compute"
    TASKS_DICT[COMPUTE_KEY] = []

    def process_folder(prev_folder, folder_name, folder_info, is_root=True):
        """
        Create the tasks and set the dependencies among them for every folder represented in representation.json

        :param prev_folder: the path of the dependent folder, this is needed in order to set which tasks this folder
        will depend on.
        :param folder_name: The name of the folder which files are being processed and tasks defined.
        :param folder_info: The actual data containing the list of notebook files that are going to be run in the tasks.
        :param is_root: check if the task in question is the first task of a sequence
        :return:
        """

        # The reason this variable is global is because otherwise airflow won't be able to detect the objects
        # created when a recursive call is made.
        TASKS_DICT[folder_name] = []
        for file_name in folder_info:
            if isinstance(file_name, str) and not file_name.startswith("."):
                run_notebook = GKEStartPodOperator(
                    task_id=clean_file_name_to_task(file_name),
                    name=clean_file_name_to_task(file_name),
                    image=get_full_image_name("rental-metrics-aggs", GCR_REGISTRY),
                    resources=RESOURCES,
                    execution_timeout=timedelta(minutes=30),
                    env_vars={
                        "LOGGING_GCP_PROJECT": LOGGING_GCP_PROJECT,
                        "DAG_EXECUTION_DATE": "{{ ts }}",
                        "PROJECT_ID": DATA_PROJECT_NAME,
                        "DATASET_NAME": AGGS_DESTINATION_DATASET,
                        "STAGE_DATASET_NAME": AGGS_STAGE_DATASET_NAME,
                        "NOTEBOOK_PATH": folder_name + "/" + file_name,
                        "NOTEBOOK_PARAMETERS": "{}",
                    },
                    dag=dag,
                )

                # the compute rates portion of the pipeline is dependent on every notebook being run properly.
                run_notebook >> finished_aggs

                if is_root:
                    create_benchmark_aggs >> run_notebook

                # if the folder isn't a root folder, it's previous folder must have at least one task which is going to be
                # set as a dependency for all of it's tasks
                if prev_folder != "":
                    for element in TASKS_DICT[prev_folder]:
                        element.set_downstream(run_notebook)
                TASKS_DICT[folder_name].append(run_notebook)

        """
            For every file in the folder, we check whether or not it is a dictionary because in our representation.json
            every dictionary represents a folder, so if any of the elements presented in folder_info is a dictionary it 
            means it is a folder to be processed as a dependency of the current folder we are creating run_notebook tasks for.
        """
        for file_name in folder_info:
            if (
                isinstance(file_name, dict)
                and file_name[list(file_name.keys())[0]] is not None
            ):
                process_folder(
                    folder_name,
                    list(file_name.keys())[0],
                    file_name[list(file_name.keys())[0]],
                    False,
                )

    with open(REPRESENTATION_JS_PATH) as json_file:
        folder_info = json.load(json_file)

    if folder_info:
        for package_folder in folder_info.keys():
            process_folder("", package_folder, folder_info[package_folder])

    # setting dependency for truncate benchmark_aggs table
    finished_customer_segmentation >> drop_benchmark_aggs >> create_benchmark_aggs

    # Creating the UDFs that are used to compute aggs
    [create_bottom_quartile_fn, create_top_quartile_fn] >> create_benchmark_aggs

    # aggs and benchmarks dependencies
    finished_aggs >> remove_benchmark_aggs_null_rows >> benchmark_aggs_qa
    remove_benchmark_aggs_null_rows >> qa_benchmark_aggs_null_values


#########################################
#######   Compute FMRR Base   ###########
#########################################

insert_fmrr_base_record = GKEStartPodOperator(
    task_id="insert-fmrr-base-record",
    name="insert-fmrr-base-record",
    secrets=[secret_db_username, secret_db_password],
    do_xcom_push=True,
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "fmrr_base",
        "STATUS_VALUE": "RUNNING",
        "BATCH": TABLE_SUFFIX,
    },
    dag=dag,
)
update_fmrr_base_record = GKEStartPodOperator(
    task_id="update-fmrr-base-record",
    name="update-fmrr-base-record",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "fmrr_base",
        "STATUS_VALUE": "DONE",
        "PK": '{{ task_instance.xcom_pull(task_ids="insert-fmrr-base-record", key="return_value")["pk"] }}',
        "BATCH": TABLE_SUFFIX,
    },
    dag=dag,
)
update_fmrr_base_record_error = GKEStartPodOperator(
    task_id="update-fmrr-base-record-error",
    name="update-fmrr-base-record-error",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "fmrr_base",
        "STATUS_VALUE": "FAILED",
        "PK": '{{ task_instance.xcom_pull(task_ids="insert-fmrr-base-record", key="return_value")["pk"] }}',
        "BATCH": TABLE_SUFFIX,
    },
    trigger_rule="one_failed",
    dag=dag,
)

update_fmrr_base_record_when_clear = GKEStartPodOperator(
    task_id="update-fmrr-base-record-when-clear",
    name="update-fmrr-base-record-when-clear",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    trigger_rule="one_success",
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "fmrr_base",
        "STATUS_VALUE": "RUNNING",
        "PK": '{{ task_instance.xcom_pull(task_ids="insert-fmrr-base-record", key="return_value")["pk"] }}',
        "BATCH": TABLE_SUFFIX,
    },
    dag=dag,
)

with TaskGroup(group_id="fmrr-base", dag=dag) as fmrr_base_group:
    populate_fm_aggs = GKEStartPodOperator(
        task_id="populate-datafeed-fmaggs",
        name="populate-datafeed-fmaggs",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DIM_FMRR_SQL_FOLDER + "core_queries/populate_fm_aggs.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET),
            "DESTINATION_TABLE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.datafeed_fmaggs__{TABLE_SUFFIX}",
            "VIEW_TO_REPLACE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_DESTINATION_DATASET}.datafeed_fmaggs",
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "CLUSTERING_FIELDS": "clientid,monthid",
            "WRITE_DISPOSITION": "WRITE_TRUNCATE",
        },
        dag=dag,
    )

    populate_rr_aggs = GKEStartPodOperator(
        task_id="populate-datafeed-rraggs",
        name="populate-datafeed-rraggs",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DIM_FMRR_SQL_FOLDER + "core_queries/populate_rr_aggs.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET),
            "DESTINATION_TABLE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.datafeed_rraggs__{TABLE_SUFFIX}",
            "VIEW_TO_REPLACE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_DESTINATION_DATASET}.datafeed_rraggs",
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "CLUSTERING_FIELDS": "clientid,monthid",
            "WRITE_DISPOSITION": "WRITE_TRUNCATE",
        },
        dag=dag,
    )

    # The following tasks create empty xyz_rental_rates and xyz_fleet_metrics tables
    # if they don't exist already, so we can execute the 'inject-xyz-records' on dim_fm_rr.
    # On the first run we create them as empty tables, and after computing FMRR Bench we
    # populate the XYZ_Tables to be used on the next run.
    create_xyz_rental_rates_table = GKEStartPodOperator(
        task_id="create-xyz-rental-rates",
        name="create-xyz-rental-rates",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "create_xyz_rentalrates.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )
    create_xyz_fleet_metrics_table = GKEStartPodOperator(
        task_id="create-xyz-fleet-metrics",
        name="create-xyz-fleet-metrics",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DEMO_CLIENTS_QUERY_FOLDER + "create_xyz_fleetmetrics.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    compute_dim_fm_rr = GKEStartPodOperator(
        task_id="compute-dim-fm-rr",
        name="compute-dim-fm-rr",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        do_xcom_push=False,
        env_vars={
            "QUERY": open(DIM_FMRR_SQL_FOLDER + "core_queries/compute_dim_fmrr.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET),
            "DESTINATION_TABLE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.dim_fm_rr__{TABLE_SUFFIX}",
            "VIEW_TO_REPLACE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_DESTINATION_DATASET}.dim_fm_rr",
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "CLUSTERING_FIELDS": "clientid,monthid",
            "PARTITION_COLUMN": "clientid",
            "RANGE_START": "-100",
            "RANGE_END": "3500",
            "RANGE_INTERVAL": "5",
            "TYPE_OF_PARTITIONING": "RANGE",
            "XCOM_PUSH": "False",
            "WRITE_DISPOSITION": "WRITE_TRUNCATE",
        },
        dag=dag,
    )

    inject_xyz_records = GKEStartPodOperator(
        task_id="inject-xyz-records",
        name="inject-xyz-records",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DIM_FMRR_SQL_FOLDER + "core_queries/inject_xyz_records.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET),
            "DESTINATION_TABLE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.dim_fm_rr__{TABLE_SUFFIX}",
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "XCOM_PUSH": "False",
        },
        dag=dag,
    )

    # === DIMFMRR partitions migration
    migrate_stg_table_dim_fmrr = DummyOperator(
        task_id="migrate-stg-table-dim-fmrr", dag=dag
    )

    migrate_stg_table_dim_fmrr_end = DummyOperator(
        task_id="finish-stg-table-dimfmrr", dag=dag
    )

    dim_fmrr_partitions = 10
    staging_table_name = "dbo.DimFMRR_STG"
    partition_bq_dim_fmrr = GKEStartPodOperator(
        task_id="partition-dim-fmrr",
        name="partition-dim-fmrr",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                DAG_BASE_SQL_FOLDER
                + "push_to_prod/push_to_prod_preload/partition_bq_tables_template_with_ceiling.sql"
            )
            .read()
            .replace("{NUMBER_OF_PARTITIONS}",str(dim_fmrr_partitions))
            .replace("{PARTITION_COLUMN}","FMRR_ID")
            .replace("{TABLE_NAME}",f"{DIM_FM_RR_STAGE_DATASET}.dim_fm_rr__{TABLE_SUFFIX}"),
            "XCOM_PUSH": "False",
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/DATAMART/DIM_FMRR",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
            "WRITE_DISPOSITION": None,
        },
        dag=dag,
    )

    create_partition_bounds_table_dim_fmrr = GKEStartPodOperator(
        task_id=f"create-partitions-bounds-table-dim-fmrr",
        name=f"create-partitions-bounds-table-dim-fmrr",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("execute-sql-server", GCR_REGISTRY),
        execution_timeout=timedelta(hours=2),
        arguments=["python3", "main.py", "execute-sql-server"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "QUERY": "IF OBJECT_ID(N'[dbo].[DimFMRR_PartitionBounds]', N'U') IS NULL " +
                "BEGIN " + 
                "CREATE TABLE dbo.DimFMRR_PartitionBounds ( " + 
                "pid int NOT NULL, " + 
                "table_id int NOT NULL, " + 
                "min_fmrr_id bigint NOT NULL, " +
                "max_fmrr_id bigint NOT NULL, " +
                "CONSTRAINT PK_DimFMRR_PartitionBounds PRIMARY KEY (table_id) " +
                "); " +
                "END; "
        },
        dag=dag,
    )

    partition_bounds_bq_dim_fmrr = GKEStartPodOperator(
        task_id="partition-bounds-dim-fmrr",
        name="partition-bounds-dim-fmrr",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                DAG_BASE_SQL_FOLDER
                + "push_to_prod/push_to_prod_preload/partition_bounds_table.sql"
            )
            .read()
            .replace("{PARTITION_COLUMN}","FMRR_ID")
            .replace("{TABLE_NAME_BOUNDS}",f"{DIM_FM_RR_STAGE_DATASET}.dim_fmrr_partition_bounds_{TABLE_SUFFIX}")
            .replace("{TABLE_NAME}",f"{DIM_FM_RR_STAGE_DATASET}.dim_fm_rr__{TABLE_SUFFIX}"),
            "XCOM_PUSH": "False",
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/BENCHMARK/BENCHMARK_RATES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
            "WRITE_DISPOSITION": None,
        },
        dag=dag,
    )

    transfer_partition_bounds_dim_fmrr = GKEStartPodOperator(
        task_id=f"transfer-partition-bounds-dim-fmrr",
        name=f"transfer-partition-bounds-dim-fmrr",
        priority_weight=12,
        execution_timeout=timedelta(hours=3),
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(requests={"cpu": "3000m", "memory": "4Gi"}),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": DIM_FM_RR_STAGE_DATASET,
            "BQ_TABLE": f"dim_fmrr_partition_bounds_{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.DimFMRR_PartitionBounds",
            "WRITE_MODE": "overwrite",
            "DO_QA_ROW_COUNT": "False",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "PARALLEL_PROCESSES": "3",
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": """{
                "table_id": "table_id"
                , "pid": "pid"
                , "min_fmrr_id": "min_fmrr_id"
                , "max_fmrr_id": "max_fmrr_id"
            }""",
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )

    replace_main_with_stg_table_dim_fmrr = GKEStartPodOperator(
        task_id="replace-main-with-stg-table-dim-fmrr",
        name="replace-main-with-stg-table-dim-fmrr",
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "recreate-stg-table-indexes"],
        resources=RESOURCES_SMALL_TASK,
        execution_timeout=timedelta(hours=3),
        priority_weight=9,
        env_vars={
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.DimFMRR",
            "DO_QA_ROW_COUNT": "True",
            "QA_GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_QA_TABLE": f'{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.dim_fm_rr__{TABLE_SUFFIX}',
            "SS_QA_TABLE": f"ras_DataMart_Analytics_Cloud.{staging_table_name}",
            "REPLACE_TABLE": "True",
            "MANUAL_INDEX_COMMAND" : "--" # setting it as empty will not recreate the index but will replace the main table with the staging one
        },
        dag=dag,
    )
    
    partition_scheme_suffix = f"_partition_obj_{datetime.now().strftime('%Y%m%d%H%M%S')}" 
    create_stg_integrated_table_dim_fmrr = GKEStartPodOperator(
        task_id=f"create-stg-integrated-table-dim-fmrr",
        name=f"create-stg-integrated-table-dim-fmrr",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("execute-sql-server", GCR_REGISTRY),
        execution_timeout=timedelta(hours=2),
        arguments=["python3", "main.py", "execute-sql-server"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "QUERY": "EXECUTE [dbo].[Create_FMRR_StagingTable] " +
                "@table_name='DimFMRR', " + 
                "@staging_suffix='_STG', " + 
                f"@partition_suffix='{partition_scheme_suffix}', " + 
                "@partition_bounds='DimFMRR_PartitionBounds', " + 
                "@show_sql_only= 0;"
        },
        dag=dag,
    )

    (
        partition_bq_dim_fmrr >> 
        create_partition_bounds_table_dim_fmrr >>
        partition_bounds_bq_dim_fmrr >>
        transfer_partition_bounds_dim_fmrr >>
        create_stg_integrated_table_dim_fmrr >>
        migrate_stg_table_dim_fmrr
    )
    
    # last steps in the migration dim fmrr
    replace_main_with_stg_table_dim_fmrr >> migrate_stg_table_dim_fmrr_end

    for partition_id in range(0, dim_fmrr_partitions):

        transfer_dim_fmrr_partition = GKEStartPodOperator(
            task_id=f"transfer-dim-fmrr-p{partition_id}",
            name=f"transfer-dim-fmrr-p{partition_id}",
            priority_weight=12,
            execution_timeout=timedelta(hours=3),
            image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
            secrets=[secret_db_username, secret_db_password],
            arguments=["python3", "main.py", "migrate-bq-partition-and-index"],
            resources=k8s.V1ResourceRequirements(requests={"cpu": "3000m", "memory": "4Gi"}),
            pool=ss_import_pool_name,
            env_vars={
                "GCP_PROJECT": DATA_PROJECT_NAME,
                "BQ_DATASET": DIM_FM_RR_STAGE_DATASET,
                "BQ_TABLE": f"dim_fm_rr__{TABLE_SUFFIX}_p{partition_id}",
                "DB_SERVER": GETL01_SQL_SERVER,
                "DB_NAME": "ras_DataMart_Analytics_Cloud",
                "TABLE_NAME": "dbo.DimFMRR",
                "TABLE_SUFFIX": f"_part_{str(partition_id)}",
                "WRITE_MODE": "overwrite",
                "DO_QA_ROW_COUNT": "True",
                # in the case of having a particular table for each partition we do not need this qa
                # since the method already compares bq to ss.
                # "SS_QA_QUERY_OVERRIDE": qa_row_count_query,
                # we do not need to run a query before the migration in this scenario since it is moving the partition from bq to ss
                "EXECUTE_QUERY_BEFORE": "",
                "GCS_BUCKET_NAME": BUCKET_NAME,
                "PARALLEL_PROCESSES": "4",
                "CHUNKSIZE": "200000",
                "COLUMN_MAPPING": open(
                    DAG_BASE_SQL_FOLDER + "dim_fm_rr_queries/mapping_json/dim_fmrr.json"
                ).read(),
                "PARSE_DTYPES": "False",
                "SS_INDEX_PROCEDURE" : "IndexAndSwitch_FMRR_StagingTable",
                "PARTITION_INDEX" : f"{str(partition_id)}",
                "SS_DESTINATION_TABLE" : "DimFMRR_STG",
                "SS_PARTITION_BOUNDS_TABLE" : "DimFMRR_PartitionBounds"
            },
            dag=dag,
        )

        (
        migrate_stg_table_dim_fmrr >> 
        transfer_dim_fmrr_partition >> 
        replace_main_with_stg_table_dim_fmrr
        )

    populate_fmrr_fleetmetrics = GKEStartPodOperator(
        task_id="populate-fmrr-fleetmetrics",
        name="populate-fmrr-fleetmetrics",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(
                DIM_FMRR_SQL_FOLDER + "core_queries/populate_fmrr_fleetmetrics.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "XCOM_PUSH": "False",
        },
        dag=dag,
    )

    inject_xyz_fmrr_fleetmetrics = GKEStartPodOperator(
        task_id="inject-xyz-fmrr-fleetmetrics",
        name="inject-xyz-fmrr-fleetmetrics",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(
                DIM_FMRR_SQL_FOLDER + "core_queries/inject_xyz_fmrr_fleetmetrics.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "DESTINATION_TABLE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.fmrr_fleetmetrics__{TABLE_SUFFIX}",
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "XCOM_PUSH": "False",
        },
        dag=dag,
    )

    # === Fleet Metrics partitions migration
    migrate_stg_table_fleet_metrics = DummyOperator(
        task_id="migrate-stg-table-fmrr-fleet-metrics", dag=dag
    )

    migrate_stg_table_fleet_metrics_end = DummyOperator(
        task_id="finish-stg-table-fmrr-fleet-metrics", dag=dag
    )

    fmrr_fleet_metrics_partitions = 12
    staging_table_name = "dbo.FMRR_FleetMetrics_STG"
    partition_bq_fmrr_fleet_metrics = GKEStartPodOperator(
        task_id="partition-fmrr-fleet-metrics",
        name="partition-fmrr-fleet-metrics",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                DAG_BASE_SQL_FOLDER
                + "push_to_prod/push_to_prod_preload/partition_bq_tables_template_rental_rates.sql"
            )
            .read()
            .replace("{NUMBER_OF_PARTITIONS}",str(fmrr_fleet_metrics_partitions))
            .replace("{PARTITION_COLUMN}","fmrr_id")
            .replace("{TABLE_NAME}",f"{DIM_FM_RR_STAGE_DATASET}.fmrr_fleetmetrics__{TABLE_SUFFIX}"),
            "XCOM_PUSH": "False",
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/BENCHMARK/BENCHMARK_RATES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
            "WRITE_DISPOSITION": None,
        },
        dag=dag,
    )
    
    create_partition_bounds_table_fleet_metrics = GKEStartPodOperator(
        task_id=f"create-partitions-bounds-table-fleet-metrics",
        name=f"create-partitions-bounds-table-fleet-metrics",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("execute-sql-server", GCR_REGISTRY),
        execution_timeout=timedelta(hours=2),
        arguments=["python3", "main.py", "execute-sql-server"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "QUERY": "IF OBJECT_ID(N'[dbo].[FMRR_FleetMetrics_PartitionBounds]', N'U') IS NULL " +
                "BEGIN " + 
                "CREATE TABLE dbo.FMRR_FleetMetrics_PartitionBounds ( " + 
                "pid int NOT NULL, " + 
                "table_id int NOT NULL, " + 
                "min_fmrr_id bigint NOT NULL, " +
                "max_fmrr_id bigint NOT NULL, " +
                "CONSTRAINT PK_FMRR_FleetMetrics_PartitionBounds PRIMARY KEY (table_id) " +
                "); " +
                "END; "
        },
        dag=dag,
    )

    partition_bounds_bq_fmrr_fleet_metrics = GKEStartPodOperator(
        task_id="partition-bounds-fmrr-fleet-metrics",
        name="partition-bounds-fmrr-fleet-metrics",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                DAG_BASE_SQL_FOLDER
                + "push_to_prod/push_to_prod_preload/partition_bounds_table_rental_rates.sql"
            )
            .read()
            .replace("{PARTITION_COLUMN}","fmrr_id")
            .replace("{TABLE_NAME_BOUNDS}",f"{DIM_FM_RR_STAGE_DATASET}.fmrr_fleetmetrics_partition_bounds_{TABLE_SUFFIX}")
            .replace("{TABLE_NAME}",f"{DIM_FM_RR_STAGE_DATASET}.fmrr_fleetmetrics__{TABLE_SUFFIX}"),
            "XCOM_PUSH": "False",
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/BENCHMARK/BENCHMARK_RATES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
            "WRITE_DISPOSITION": None,
        },
        dag=dag,
    )

    transfer_partition_bounds_fleet_metrics = GKEStartPodOperator(
        task_id=f"transfer-partition-bounds-fleet-metrics",
        name=f"transfer-partition-bounds-fleet-metrics",
        priority_weight=12,
        execution_timeout=timedelta(hours=3),
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(requests={"cpu": "3000m", "memory": "4Gi"}),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": DIM_FM_RR_STAGE_DATASET,
            "BQ_TABLE": f"fmrr_fleetmetrics_partition_bounds_{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.FMRR_FleetMetrics_PartitionBounds",
            "WRITE_MODE": "overwrite",
            "DO_QA_ROW_COUNT": "False",
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "PARALLEL_PROCESSES": "3",
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": """{
                "table_id": "table_id"
                , "pid": "pid"
                , "min_fmrr_id": "min_fmrr_id"
                , "max_fmrr_id": "max_fmrr_id"
            }""",
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )

    replace_main_with_stg_table_fleet_metrics = GKEStartPodOperator(
        task_id="replace-main-with-stg-table-fmrr-fleet-metrics",
        name="replace-main-with-stg-table-fmrr-fleet-metrics",
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "recreate-stg-table-indexes"],
        resources=RESOURCES_SMALL_TASK,
        execution_timeout=timedelta(hours=3),
        priority_weight=9,
        env_vars={
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.FMRR_FleetMetrics",
            "DO_QA_ROW_COUNT": "True",
            "QA_GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_QA_TABLE": f'{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.fmrr_fleetmetrics__{TABLE_SUFFIX}',
            "SS_QA_TABLE": f"ras_DataMart_Analytics_Cloud.{staging_table_name}",
            "REPLACE_TABLE": "True",
            "MANUAL_INDEX_COMMAND" : "--" # setting it as empty will not recreate the index but will replace the main table with the staging one
        },
        dag=dag,
    )

    partition_scheme_suffix = f"_partition_obj_{datetime.now().strftime('%Y%m%d%H%M%S')}" 
    create_stg_integrated_table_fleet_metrics = GKEStartPodOperator(
        # pool="rambo_rdo_indexes",
        task_id=f"create-stg-integrated-table-fleet-metrics",
        name=f"create-stg-integrated-table-fleet-metrics",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("execute-sql-server", GCR_REGISTRY),
        execution_timeout=timedelta(hours=2),
        arguments=["python3", "main.py", "execute-sql-server"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "QUERY": "EXECUTE [dbo].[Create_FMRR_StagingTable] " +
                "@table_name='FMRR_FleetMetrics', " + 
                "@staging_suffix='_STG', " + 
                f"@partition_suffix='{partition_scheme_suffix}', " + 
                "@partition_bounds='FMRR_FleetMetrics_PartitionBounds', " + 
                "@show_sql_only= 0;"
        },
        dag=dag,
    )

    # start the dag definition
    partition_bq_fmrr_fleet_metrics >> create_partition_bounds_table_fleet_metrics >> partition_bounds_bq_fmrr_fleet_metrics >> transfer_partition_bounds_fleet_metrics
    transfer_partition_bounds_fleet_metrics >> create_stg_integrated_table_fleet_metrics
    create_stg_integrated_table_fleet_metrics >> migrate_stg_table_fleet_metrics

    
    # last steps in the migration
    replace_main_with_stg_table_fleet_metrics >> migrate_stg_table_fleet_metrics_end 

    for partition_id in range(0, fmrr_fleet_metrics_partitions):        

        transfer_fmrr_fleet_metrics_partition = GKEStartPodOperator(
            task_id=f"transfer-fmrr-fleet-metrics-p{partition_id}",
            name=f"transfer-fmrr-fleet-metrics-p{partition_id}",
            priority_weight=12,
            execution_timeout=timedelta(hours=3),
            image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
            secrets=[secret_db_username, secret_db_password],
            arguments=["python3", "main.py", "migrate-bq-partition-and-index"],
            resources=k8s.V1ResourceRequirements(requests={"cpu": "3000m", "memory": "4Gi"}),
            pool=ss_import_pool_name,
            env_vars={
                "GCP_PROJECT": DATA_PROJECT_NAME,
                "BQ_DATASET": DIM_FM_RR_STAGE_DATASET,
                "BQ_TABLE": f"fmrr_fleetmetrics__{TABLE_SUFFIX}_p{partition_id}",
                "DB_SERVER": GETL01_SQL_SERVER,
                "DB_NAME": "ras_DataMart_Analytics_Cloud",
                "TABLE_NAME": "dbo.FMRR_FleetMetrics",
                "TABLE_SUFFIX": f"_part_{str(partition_id)}",
                "WRITE_MODE": "overwrite",
                "DO_QA_ROW_COUNT": "True",
                # in the case of having a particular table for each partition we do not need this qa
                # since the method already compares bq to ss.
                # "SS_QA_QUERY_OVERRIDE": qa_row_count_query,
                # we do not need to run a query before the migration in this scenario since it is moving the partition from bq to ss
                "EXECUTE_QUERY_BEFORE": "",
                "GCS_BUCKET_NAME": BUCKET_NAME,
                "PARALLEL_PROCESSES": "4",
                "CHUNKSIZE": "200000",
                "COLUMN_MAPPING": open(
                    DAG_BASE_SQL_FOLDER + "dim_fm_rr_queries/mapping_json/fmrr_fleet_metrics.json"
                ).read(),
                "PARSE_DTYPES": "False",
                "SS_INDEX_PROCEDURE" : "IndexAndSwitch_FMRR_StagingTable",
                "PARTITION_INDEX" : f"{str(partition_id)}",
                "PARTITION_COLUMN" : "FMRR_ID",
                "SS_DESTINATION_TABLE" : "FMRR_FleetMetrics_STG",
                "SS_PARTITION_BOUNDS_TABLE" : "FMRR_FleetMetrics_PartitionBounds"
            },
            dag=dag,
        )

        (
        migrate_stg_table_fleet_metrics >> 
        transfer_fmrr_fleet_metrics_partition >> 
        replace_main_with_stg_table_fleet_metrics
        )

    # populate fmrr_rentalrates
    populate_fmrr_rentalrates = GKEStartPodOperator(
        task_id="populate-fmrr-rentalrates",
        name="populate-fmrr-rentalrates",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        resources=RESOURCES_SMALL_TASK,
        priority_weight=12,
        env_vars={
            "QUERY": open(
                DIM_FMRR_SQL_FOLDER + "core_queries/populate_fmrr_rentalrates.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX)
            .replace("{CUSSEG_DESTINATION_DATASET}", CUSSEG_DESTINATION_DATASET),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    inject_xyz_fmrr_rentalrates = GKEStartPodOperator(
        task_id="inject-xyz-fmrr-rentalrates",
        name="inject-xyz-fmrr-rentalrates",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(
                DIM_FMRR_SQL_FOLDER + "core_queries/inject_xyz_fmrr_rentalrates.sql"
            )
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "XCOM_PUSH": "False",
        },
        dag=dag,
    )

    # === FMRR rental rates partitions migration
    fmrr_rental_rates_partitions = 20
    staging_table_name = "dbo.FMRR_RentalRates_STG"
    partition_bq_fmrr_rental_rates = GKEStartPodOperator(
        task_id="partition-fmrr-rental-rates",
        name="partition-fmrr-rental-rates",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                DAG_BASE_SQL_FOLDER
                + "push_to_prod/push_to_prod_preload/partition_bq_tables_template_rental_rates.sql"
            )
            .read()
            .replace("{NUMBER_OF_PARTITIONS}",str(fmrr_rental_rates_partitions))
            .replace("{PARTITION_COLUMN}","fmrr_id")
            .replace("{TABLE_NAME}",f"{DIM_FM_RR_STAGE_DATASET}.fmrr_rentalrates__{TABLE_SUFFIX}"),
            "XCOM_PUSH": "False",
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/BENCHMARK/BENCHMARK_RATES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
            "WRITE_DISPOSITION": None,
        },
        dag=dag,
    )

    migrate_stg_table = DummyOperator(
        task_id="migrate-stg-table-fmrr-rental-rates", dag=dag
    )

    migrate_stg_table_end = DummyOperator(
        task_id="finish-stg-table-fmrr-rental-rates", dag=dag
    )

    create_partition_bounds_table = GKEStartPodOperator(
        task_id=f"create-partitions-bounds-table",
        name=f"create-partitions-bounds-table",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("execute-sql-server", GCR_REGISTRY),
        execution_timeout=timedelta(hours=2),
        arguments=["python3", "main.py", "execute-sql-server"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "QUERY": "IF OBJECT_ID(N'[dbo].[FMRR_RentalRates_PartitionBounds]', N'U') IS NULL " +
                "BEGIN " + 
                "CREATE TABLE dbo.FMRR_RentalRates_PartitionBounds ( " + 
                "pid int NOT NULL, " + 
                "table_id int NOT NULL, " + 
                "min_fmrr_id bigint NOT NULL, " +
                "max_fmrr_id bigint NOT NULL, " +
                "CONSTRAINT PK_FMRR_RentalRates_PartitionBounds PRIMARY KEY (table_id) " +
                "); " +
                "END; "
        },
        dag=dag,
    )

    partition_bounds_bq_fmrr_rental_rates = GKEStartPodOperator(
        task_id="partition-bounds-fmrr-rental-rates",
        name="partition-bounds-fmrr-rental-rates",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": open(
                DAG_BASE_SQL_FOLDER
                + "push_to_prod/push_to_prod_preload/partition_bounds_table_rental_rates.sql"
            )
            .read()
            .replace("{PARTITION_COLUMN}","fmrr_id")
            .replace("{TABLE_NAME_BOUNDS}",f"{DIM_FM_RR_STAGE_DATASET}.fmrr_rentalrates_partition_bounds_{TABLE_SUFFIX}")
            .replace("{TABLE_NAME}",f"{DIM_FM_RR_STAGE_DATASET}.fmrr_rentalrates__{TABLE_SUFFIX}"),
            "XCOM_PUSH": "False",
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/BENCHMARK/BENCHMARK_RATES",
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
            "WRITE_DISPOSITION": None,
        },
        dag=dag,
    )

    transfer_partition_bounds_rental_rates = GKEStartPodOperator(
        task_id=f"transfer-partition-bounds-rental-rates",
        name=f"transfer-partition-bounds-rental-rates",
        priority_weight=12,
        execution_timeout=timedelta(hours=3),
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "load-from-bq-to-ss"],
        resources=k8s.V1ResourceRequirements(requests={"cpu": "3000m", "memory": "4Gi"}),
        pool=ss_import_pool_name,
        env_vars={
            "GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_DATASET": DIM_FM_RR_STAGE_DATASET,
            "BQ_TABLE": f"fmrr_rentalrates_partition_bounds_{TABLE_SUFFIX}",
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.FMRR_RentalRates_PartitionBounds",
            "WRITE_MODE": "overwrite",
            "DO_QA_ROW_COUNT": "False",
            # "SS_QA_QUERY_OVERRIDE": qa_row_count_query,
            # "EXECUTE_QUERY_BEFORE": delete_before_insert_query,
            "GCS_BUCKET_NAME": BUCKET_NAME,
            "PARALLEL_PROCESSES": "3",
            "CHUNKSIZE": "100000",
            "COLUMN_MAPPING": """{
                "table_id": "table_id"
                , "pid": "pid"
                , "min_fmrr_id": "min_fmrr_id"
                , "max_fmrr_id": "max_fmrr_id"
            }""",
            "PARSE_DTYPES": "False",
        },
        dag=dag,
    )

    replace_main_with_stg_table = GKEStartPodOperator(
        task_id="replace-main-with-stg-table-fmrr-rental-rates",
        name="replace-main-with-stg-table-fmrr-rental-rates",
        image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
        secrets=[secret_db_username, secret_db_password],
        arguments=["python3", "main.py", "recreate-stg-table-indexes"],
        resources=RESOURCES_SMALL_TASK,
        execution_timeout=timedelta(hours=3),
        priority_weight=9,
        env_vars={
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "TABLE_NAME": "dbo.FMRR_RentalRates",
            "DO_QA_ROW_COUNT": "True",
            "QA_GCP_PROJECT": DATA_PROJECT_NAME,
            "BQ_QA_TABLE": f'{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.fmrr_rentalrates__{TABLE_SUFFIX}',
            "SS_QA_TABLE": f"ras_DataMart_Analytics_Cloud.{staging_table_name}",
            "REPLACE_TABLE": "True",
            "MANUAL_INDEX_COMMAND" : "--" # setting it as empty will not recreate the index but will replace the main table with the staging one
        },
        dag=dag,
    )

    partition_scheme_suffix = f"_partition_obj_{datetime.now().strftime('%Y%m%d%H%M%S')}" 
    create_stg_integrated_table = GKEStartPodOperator(
        # pool="rambo_rdo_indexes",
        task_id=f"create-stg-integrated-table",
        name=f"create-stg-integrated-table",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("execute-sql-server", GCR_REGISTRY),
        execution_timeout=timedelta(hours=2),
        arguments=["python3", "main.py", "execute-sql-server"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "QUERY": "EXECUTE [dbo].[Create_FMRR_StagingTable] " +
                "@table_name='FMRR_RentalRates', " + 
                "@staging_suffix='_STG', " + 
                f"@partition_suffix='{partition_scheme_suffix}', " + 
                "@partition_bounds='FMRR_RentalRates_PartitionBounds', " + 
                "@show_sql_only= 0;"
        },
        dag=dag,
    )

    # start the dag definition
    partition_bq_fmrr_rental_rates >> create_partition_bounds_table >> partition_bounds_bq_fmrr_rental_rates >> transfer_partition_bounds_rental_rates
    transfer_partition_bounds_rental_rates >> create_stg_integrated_table
    create_stg_integrated_table >> migrate_stg_table
    
    # last steps in the migration
    replace_main_with_stg_table >> migrate_stg_table_end

    for partition_id in range(0, fmrr_rental_rates_partitions):

        transfer_fmrr_rental_rates_partition = GKEStartPodOperator(
            task_id=f"transfer-fmrr-rental-rates-p{partition_id}",
            name=f"transfer-fmrr-rental-rates-p{partition_id}",
            priority_weight=12,
            execution_timeout=timedelta(hours=3),
            image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
            secrets=[secret_db_username, secret_db_password],
            arguments=["python3", "main.py", "migrate-bq-partition-and-index"],
            resources=k8s.V1ResourceRequirements(requests={"cpu": "3000m", "memory": "4Gi"}),
            pool=ss_import_pool_name,
            env_vars={
                "GCP_PROJECT": DATA_PROJECT_NAME,
                "BQ_DATASET": DIM_FM_RR_STAGE_DATASET,
                "BQ_TABLE": f"fmrr_rentalrates__{TABLE_SUFFIX}_p{partition_id}",
                "DB_SERVER": GETL01_SQL_SERVER,
                "DB_NAME": "ras_DataMart_Analytics_Cloud",
                "TABLE_NAME": "dbo.FMRR_RentalRates",
                "TABLE_SUFFIX": f"_part_{str(partition_id)}",
                "WRITE_MODE": "overwrite",
                "DO_QA_ROW_COUNT": "True",
                # in the case of having a particular table for each partition we do not need this qa
                # since the method already compares bq to ss.
                # "SS_QA_QUERY_OVERRIDE": qa_row_count_query,
                # we do not need to run a query before the migration in this scenario since it is moving the partition from bq to ss
                "EXECUTE_QUERY_BEFORE": "",
                "GCS_BUCKET_NAME": BUCKET_NAME,
                "PARALLEL_PROCESSES": "3",
                "CHUNKSIZE": "100000",
                "COLUMN_MAPPING": open(
                    DAG_BASE_SQL_FOLDER + "dim_fm_rr_queries/mapping_json/fmrr_rental_rates.json"
                ).read(),
                "PARSE_DTYPES": "False",
                "SS_INDEX_PROCEDURE" : "IndexAndSwitch_FMRR_StagingTable",
                "PARTITION_INDEX" : f"{str(partition_id)}",
                "PARTITION_COLUMN" : "FMRR_ID",
                "SS_DESTINATION_TABLE" : "FMRR_RentalRates_STG",
                "SS_PARTITION_BOUNDS_TABLE" : "FMRR_RentalRates_PartitionBounds"
            },
            dag=dag,
        )

        (migrate_stg_table >> transfer_fmrr_rental_rates_partition >> replace_main_with_stg_table)

    qa_dim_fm_rr_integrity = GKEStartPodOperator(
        task_id="qa-dim-fm-rr-integrity",
        name="qa-dim-fm-rr-integrity",
        secrets=[secret_db_username, secret_db_password],
        image=get_full_image_name("qa-check-ss", GCR_REGISTRY),
        do_xcom_push=False,
        arguments=["python3", "main.py", "run-qa-check"],
        execution_timeout=timedelta(minutes=15),
        retry_delay=timedelta(seconds=10),
        env_vars={
            "DB_SERVER": GETL01_SQL_SERVER,
            "DB_NAME": "ras_DataMart_Analytics_Cloud",
            "QA_QUERY": open(DIM_FMRR_SQL_FOLDER + "qa_queries/qa_dim_fm_rr_integrity.sql").read(),
            "CHECK_TYPE": "expect_empty_result"
        },
        dag=dag,
    )
    [migrate_stg_table_fleet_metrics_end, migrate_stg_table_end, migrate_stg_table_dim_fmrr_end] >> qa_dim_fm_rr_integrity
    qa_dim_fm_rr_integrity >> [update_fmrr_base_record, update_fmrr_base_record_error]

    
    # === migrate fmrr rental rates aggregations
    populate_fmrr_rraggs = GKEStartPodOperator(
        task_id="populate-fmrr-rraggs",
        name="populate-fmrr-rraggs",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DIM_FMRR_SQL_FOLDER + "core_queries/populate_fmrr_rraggs.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "XCOM_PUSH": "False",
        },
        dag=dag,
    )

    populate_fmrr_fmaggs = GKEStartPodOperator(
        task_id="populate-fmrr-fmaggs",
        name="populate-fmrr-fmaggs",
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        execution_timeout=timedelta(hours=3),
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "QUERY": open(DIM_FMRR_SQL_FOLDER + "core_queries/populate_fmrr_fmaggs.sql")
            .read()
            .replace("{DATA_PROJECT_NAME}", DATA_PROJECT_NAME)
            .replace("{DIM_FM_RR_DESTINATION_DATASET}", DIM_FM_RR_DESTINATION_DATASET)
            .replace("{AGGS_DESTINATION_DATASET}", AGGS_DESTINATION_DATASET)
            .replace("{DIM_FM_RR_STAGE_DATASET}", DIM_FM_RR_STAGE_DATASET)
            .replace("{TABLE_SUFFIX}", TABLE_SUFFIX),
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "XCOM_PUSH": "False",
        },
        dag=dag,
    )

    task_dicts_info = [
        {   
            "task_create_table": populate_fmrr_fmaggs, 
            "task_id": 'fmrr-fmaggs',
            "NUMBER_OF_PARTITIONS": 4,
            "PARTITION_COLUMN": "fmrr_id",
            "PARTITION_COLUMN_SS": "FMRR_ID",
            "table_name": 'fmrr_fmaggs',
            "sql_server_table_name": "dbo.FMRR_FMaggs",
            "PARSE_DTYPES": "False",
            "CHUNKSIZE": "100000",
            "column_mapping": """{
                "fmrr_id": "FMRR_ID",
                "isnotbench": "IsNotBench",
                "unit_count": "Unit Count",
                "rental_revenue": "Rental Revenue",
                "avg_daily_cost_on_rent": "Avg Daily Cost On Rent",
                "annualized_rental_revenue": "Annualized Rental Revenue",
                "avg_daily_cost_in_fleet": "Avg Daily Cost In Fleet",
                "avg_daily_cost_available": "Avg Daily Cost Available",
                "cost_x_age":"Cost x Age",
                "cost":"Cost",
                "days_on_rent":"Days On Rent",
                "days_in_fleet": "Days In Fleet",
                "days_available": "Days Available"
            }"""
        },
        {
            "task_create_table": populate_fmrr_rraggs, 
            "task_id": 'fmrr-rraggs',
            "NUMBER_OF_PARTITIONS": 4,
            "PARTITION_COLUMN": "fmrr_id",
            "PARTITION_COLUMN_SS": "FMRR_ID",
            "table_name": 'fmrr_rraggs',
            "sql_server_table_name": "dbo.FMRR_RRaggs",
            "PARSE_DTYPES": "False",
            "CHUNKSIZE": "100000",
            "column_mapping": """{
                "fmrr_id": "FMRR_ID",
                "trid_count": "TRID_Count",
                "isnotbench": "IsNotBench",
                "rental_revenue": "Rental Revenue",
                "total_book_revenue": "Total Book Revenue",
                "new_monthly_revenue": "New Monthly Revenue",
                "new_monthly_book_revenue": "New Monthly Book Revenue",
                "new_months": "New Months",
                "existing_monthly_revenue": "Existing Monthly Revenue",
                "existing_monthly_book_revenue": "Existing Monthly Book Revenue",
                "existing_months": "Existing Months",
                "monthly_revenue": "Monthly Revenue",
                "monthly_book_revenue": "Monthly Book Revenue",
                "months": "Months",
                "weekly_revenue": "Weekly Revenue",
                "weekly_book_revenue": "Weekly Book Revenue",
                "weeks": "Weeks",
                "daily_revenue": "Daily Revenue",
                "daily_book_revenue": "Daily Book Revenue",
                "days": "Days",
                "hourly_revenue": "Hourly Revenue",
                "hourly_book_revenue": "Hourly Book Revenue",
                "hours": "Hours",
                "minimum_revenue": "Minimum Revenue",
                "minimum_book_revenue": "Minimum Book Revenue",
                "minimums": "Minimums",
                "rerentrevenue": "ReRentRevenue",
                "creditamount": "CreditAmount",
                "excessmileagerevenue": "ExcessMileageRevenue",
                "unclassifiedrevenue": "UnclassifiedRevenue"
            }""",
        }
    ]

    for task_info in task_dicts_info:
        
        partition_task_id = f"partition-bq-table-fmrr-{task_info['task_id']}"
        partition_bq_table = GKEStartPodOperator(
            task_id=partition_task_id,
            name=partition_task_id,
            image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
            arguments=["python3", "main.py", "execute-bigquery"],
            do_xcom_push=False,
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "QUERY": open(
                    DAG_BASE_SQL_FOLDER
                    + "push_to_prod/push_to_prod_preload/partition_bq_tables_template.sql"
                )
                .read()
                .replace(
                    "{NUMBER_OF_PARTITIONS}",
                    str(task_info["NUMBER_OF_PARTITIONS"]),
                )
                .replace(
                    "{PARTITION_COLUMN}",'fmrr_id',
                )
                .replace(
                    "{TABLE_NAME}",
                    f"{DIM_FM_RR_STAGE_DATASET}.{task_info['table_name']}__{TABLE_SUFFIX}",
                ),
                "XCOM_PUSH": "False",
                "WRITE_LOGS": "True",
                "LOGS_GCP_PROJECT": PROJECT_NAME,
                "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/PUSH_TO_PROD/{task_info['table_name'].upper()}",
                "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
                "WRITE_DISPOSITION": None,
            },
            dag=dag,
        )
        create_stg_table = GKEStartPodOperator(
            task_id=f"create-stg-table-fmrr-{task_info['task_id']}",
            name=f"create-stg-table-fmrr-{task_info['task_id']}",
            image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
            secrets=[secret_db_username, secret_db_password],
            arguments=["python3", "main.py", "create-stg-table"],
            resources=RESOURCES_SMALL_TASK,
            # Same priority as other RAMBO Push to prod tasks
            priority_weight=task_info["PRIORITY_WEIGHT"]
            if "PRIORITY_WEIGHT" in task_info.keys()
            else 14,
            env_vars={
                "DB_SERVER": GETL01_SQL_SERVER,
                "DB_NAME": "ras_DataMart_Analytics_Cloud",
                "TABLE_NAME": task_info[
                    "sql_server_table_name"
                ],
            },
            dag=dag,
        )

        # Depends on the last_task, but will only start the transfer after the preload fnishes
        task_info["task_create_table"] >> partition_bq_table >> create_stg_table

        staging_table_name = (
            task_info["sql_server_table_name"] + "_STG"
        )

        recreate_stg_table_indexes = GKEStartPodOperator(
            task_id=f"recreate-stg-table-indexes-fmrr-{task_info['task_id']}",
            name=f"recreate-stg-table-indexes-fmrr-{task_info['task_id']}",
            image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
            secrets=[secret_db_username, secret_db_password],
            arguments=["python3", "main.py", "recreate-stg-table-indexes"],
            resources=RESOURCES_SMALL_TASK,
            # Same priority as other RAMBO Push to prod tasks
            priority_weight=task_info["PRIORITY_WEIGHT"]
            if "PRIORITY_WEIGHT" in task_info.keys()
            else 14,
            env_vars={
                "DB_SERVER": GETL01_SQL_SERVER,
                "DB_NAME": "ras_DataMart_Analytics_Cloud",
                "TABLE_NAME": task_info[
                    "sql_server_table_name"
                ],
                "REPLACE_TABLE": 'True',
                "DO_QA_ROW_COUNT": "True",
                "QA_GCP_PROJECT": DATA_PROJECT_NAME,
                "BQ_QA_TABLE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_STAGE_DATASET}.{task_info['table_name']}__{TABLE_SUFFIX}",
                "SS_QA_TABLE": f"ras_DataMart_Analytics_Cloud.{staging_table_name}",
                # In case there is a manual index set, I'll pass it to the transfer
                "MANUAL_INDEX_COMMAND": None,
            },
            dag=dag,
        )

        for partition_id in range(
            0, int(task_info["NUMBER_OF_PARTITIONS"])
        ):
            delete_before_insert_query = (
                open(
                    DAG_BASE_SQL_FOLDER
                    + "push_to_prod/push_to_prod_preload/delete_partition_before_inserting_template.sql"
                )
                .read()
                .replace("{DB_NAME}", "ras_DataMart_Analytics_Cloud")
                .replace("{TABLE_NAME}", staging_table_name)
                .replace("{PARTITION_ID}", str(partition_id))
                .replace(
                    "{NUMBER_OF_PARTITIONS}",
                    str(task_info["NUMBER_OF_PARTITIONS"]),
                )
                .replace(
                    "{PARTITION_COLUMN}",
                    task_info["PARTITION_COLUMN_SS"],
                )
            )

            # I'll execute the transfers in 'append' mode for each partition
            transfer_task_id = (
                f"transfer-fmrr-{task_info['task_id']}-p{partition_id}-to-prod-ss"
            )
            transfer_table_to_prod = GKEStartPodOperator(
                task_id=transfer_task_id,
                name=transfer_task_id,
                image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
                secrets=[secret_db_username, secret_db_password],
                arguments=["python3", "main.py", "load-from-bq-to-ss"],
                resources=task_info["RESOURCES"]
                if "RESOURCES" in task_info.keys()
                else k8s.V1ResourceRequirements(requests={"cpu": "2000m", "memory": "2Gi"}),
                pool=ss_import_pool_name,
                execution_timeout=task_info[
                    "transfer_execution_timeout"
                ]
                if "transfer_execution_timeout"
                in task_info.keys()
                else timedelta(minutes=60),
                # Same priority as other RAMBO Push to prod tasks
                priority_weight=task_info["PRIORITY_WEIGHT"]
                if "PRIORITY_WEIGHT" in task_info.keys()
                else 14,
                env_vars={
                    "GCP_PROJECT": DATA_PROJECT_NAME,
                    "BQ_DATASET": DIM_FM_RR_STAGE_DATASET,
                    "BQ_TABLE": f"{task_info['table_name']}__{TABLE_SUFFIX}_p{partition_id}",
                    "DB_SERVER": GETL01_SQL_SERVER,
                    "DB_NAME": "ras_DataMart_Analytics_Cloud",
                    "TABLE_NAME": staging_table_name,
                    "WRITE_MODE": "append",
                    "DO_QA_ROW_COUNT": "False",  # We'll skip the QA row count because it is only one of the partitions
                    "EXECUTE_QUERY_BEFORE": delete_before_insert_query,
                    "GCS_BUCKET_NAME": BUCKET_NAME,
                    "CHUNKSIZE": task_info["CHUNKSIZE"]
                    if "CHUNKSIZE" in task_info.keys()
                    else "100000",
                    "COLUMN_MAPPING": task_info["column_mapping"],
                    "PARSE_DTYPES": "False",
                    "PARALLEL_PROCESSES": task_info[
                        "PARALLEL_PROCESSES"
                    ]
                    if "PARALLEL_PROCESSES" in task_info.keys()
                    else "2",
                },
                dag=dag,
            )
            create_stg_table >> transfer_table_to_prod >> recreate_stg_table_indexes

        recreate_stg_table_indexes >> [update_fmrr_base_record,update_fmrr_base_record_error]

with TaskGroup(group_id="client-quartiles", dag=dag) as client_quartiles_group:
    delete_client_quartiles_table = GKEStartPodOperator(
        task_id="delete-fmrr-client-quartiles",
        name="delete-fmrr-client-quartiles",
        image=get_full_image_name("client-quartiles-rambo", GCR_REGISTRY),
        arguments=["python3", "main.py", "delete-client-quartiles-table"],
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
        },
        dag=dag,
    )

    client_quartiles_parameters = [
        {
            "RATE_TYPE": "MonthlyTopQMarket",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_district, rouse_market",
            "TIME_INSTANCE_COLUMN": "months",
            "REVENUE_COLUMN": "monthly_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_market",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_district, rouse_market, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.TopQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "top_q_multiplier",
        },
        {
            "RATE_TYPE": "MonthlyBotQMarket",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_district, rouse_market",
            "TIME_INSTANCE_COLUMN": "months",
            "REVENUE_COLUMN": "monthly_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_market",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_district, rouse_market, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.BotQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "bot_q_multiplier",
        },
        {
            "RATE_TYPE": "WeeklyTopQMarket",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_district, rouse_market",
            "TIME_INSTANCE_COLUMN": "weeks",
            "REVENUE_COLUMN": "weekly_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_market",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_district, rouse_market, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.TopQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "top_q_multiplier",
        },
        {
            "RATE_TYPE": "WeeklyBotQMarket",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_district, rouse_market",
            "TIME_INSTANCE_COLUMN": "weeks",
            "REVENUE_COLUMN": "weekly_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_market",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_district, rouse_market, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.BotQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "bot_q_multiplier",
        },
        {
            "RATE_TYPE": "DailyTopQMarket",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_district, rouse_market",
            "TIME_INSTANCE_COLUMN": "days",
            "REVENUE_COLUMN": "daily_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_market",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_district, rouse_market, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.TopQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "top_q_multiplier",
        },
        {
            "RATE_TYPE": "DailyBotQMarket",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_district, rouse_market",
            "TIME_INSTANCE_COLUMN": "days",
            "REVENUE_COLUMN": "daily_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_market",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_district, rouse_market, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.BotQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "bot_q_multiplier",
        },
        {
            "RATE_TYPE": "MonthlyTopQDistrict",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_district, rouse_district AS rouse_market",
            "TIME_INSTANCE_COLUMN": "months",
            "REVENUE_COLUMN": "monthly_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_district",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_district, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.TopQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "top_q_multiplier",
        },
        {
            "RATE_TYPE": "MonthlyBotQDistrict",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_district, rouse_district AS rouse_market",
            "TIME_INSTANCE_COLUMN": "months",
            "REVENUE_COLUMN": "monthly_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_district",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_district, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.BotQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "bot_q_multiplier",
        },
        {
            "RATE_TYPE": "WeeklyTopQDistrict",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_district, rouse_district AS rouse_market",
            "TIME_INSTANCE_COLUMN": "weeks",
            "REVENUE_COLUMN": "weekly_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_district",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_district, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.TopQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "top_q_multiplier",
        },
        {
            "RATE_TYPE": "WeeklyBotQDistrict",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_district, rouse_district AS rouse_market",
            "TIME_INSTANCE_COLUMN": "weeks",
            "REVENUE_COLUMN": "weekly_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_district",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_district, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.BotQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "bot_q_multiplier",
        },
        {
            "RATE_TYPE": "DailyTopQDistrict",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_district, rouse_district AS rouse_market",
            "TIME_INSTANCE_COLUMN": "days",
            "REVENUE_COLUMN": "daily_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_district",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_district, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.TopQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "top_q_multiplier",
        },
        {
            "RATE_TYPE": "DailyBotQDistrict",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_district, rouse_district AS rouse_market",
            "TIME_INSTANCE_COLUMN": "days",
            "REVENUE_COLUMN": "daily_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_district",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_district, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.BotQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "bot_q_multiplier",
        },
        {
            "RATE_TYPE": "MonthlyTopQRegion",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_region AS rouse_district, rouse_region AS rouse_market",
            "TIME_INSTANCE_COLUMN": "months",
            "REVENUE_COLUMN": "monthly_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_region",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.TopQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "top_q_multiplier",
        },
        {
            "RATE_TYPE": "MonthlyBotQRegion",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_region AS rouse_district, rouse_region AS rouse_market",
            "TIME_INSTANCE_COLUMN": "months",
            "REVENUE_COLUMN": "monthly_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_region",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.BotQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "bot_q_multiplier",
        },
        {
            "RATE_TYPE": "WeeklyTopQRegion",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_region AS rouse_district, rouse_region AS rouse_market",
            "TIME_INSTANCE_COLUMN": "weeks",
            "REVENUE_COLUMN": "weekly_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_region",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.TopQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "top_q_multiplier",
        },
        {
            "RATE_TYPE": "WeeklyBotQRegion",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_region AS rouse_district, rouse_region AS rouse_market",
            "TIME_INSTANCE_COLUMN": "weeks",
            "REVENUE_COLUMN": "weekly_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_region",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.BotQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "bot_q_multiplier",
        },
        {
            "RATE_TYPE": "DailyTopQRegion",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_region AS rouse_district, rouse_region AS rouse_market",
            "TIME_INSTANCE_COLUMN": "days",
            "REVENUE_COLUMN": "daily_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_region",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.TopQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "top_q_multiplier",
        },
        {
            "RATE_TYPE": "DailyBotQRegion",
            "RATE_GROUPING_COLUMNS": "rouse_region, rouse_region AS rouse_district, rouse_region AS rouse_market",
            "TIME_INSTANCE_COLUMN": "days",
            "REVENUE_COLUMN": "daily_revenue",
            "GEOGRAPHIC_LEVEL": "rouse_region",
            "GROUPING_COLUMNS": "month, client_code, rouse_region, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.BotQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "bot_q_multiplier",
        },
        {
            "RATE_TYPE": "MonthlyTopQCompany",
            "RATE_GROUPING_COLUMNS": "'Total Company' AS rouse_region,'Total Company' AS rouse_district, 'Total Company' AS rouse_market",
            "TIME_INSTANCE_COLUMN": "months",
            "REVENUE_COLUMN": "monthly_revenue",
            "GEOGRAPHIC_LEVEL": "",
            "GROUPING_COLUMNS": "month, client_code, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.TopQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "top_q_multiplier",
            "COUNTRY": "USA",
        },
        {
            "RATE_TYPE": "MonthlyBotQCompany",
            "RATE_GROUPING_COLUMNS": "'Total Company' AS rouse_region,'Total Company' AS rouse_district, 'Total Company' AS rouse_market",
            "TIME_INSTANCE_COLUMN": "months",
            "REVENUE_COLUMN": "monthly_revenue",
            "GEOGRAPHIC_LEVEL": "",
            "GROUPING_COLUMNS": "month, client_code, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.BotQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "bot_q_multiplier",
            "COUNTRY": "USA",
        },
        {
            "RATE_TYPE": "WeeklyTopQCompany",
            "RATE_GROUPING_COLUMNS": "'Total Company' AS rouse_region,'Total Company' AS rouse_district, 'Total Company' AS rouse_market",
            "TIME_INSTANCE_COLUMN": "weeks",
            "REVENUE_COLUMN": "weekly_revenue",
            "GEOGRAPHIC_LEVEL": "",
            "GROUPING_COLUMNS": "month, client_code, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.TopQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "top_q_multiplier",
            "COUNTRY": "USA",
        },
        {
            "RATE_TYPE": "WeeklyBotQCompany",
            "RATE_GROUPING_COLUMNS": "'Total Company' AS rouse_region,'Total Company' AS rouse_district, 'Total Company' AS rouse_market",
            "TIME_INSTANCE_COLUMN": "weeks",
            "REVENUE_COLUMN": "weekly_revenue",
            "GEOGRAPHIC_LEVEL": "",
            "GROUPING_COLUMNS": "month, client_code, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.BotQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "bot_q_multiplier",
            "COUNTRY": "USA",
        },
        {
            "RATE_TYPE": "DailyTopQCompany",
            "RATE_GROUPING_COLUMNS": "'Total Company' AS rouse_region,'Total Company' AS rouse_district, 'Total Company' AS rouse_market",
            "TIME_INSTANCE_COLUMN": "days",
            "REVENUE_COLUMN": "daily_revenue",
            "GEOGRAPHIC_LEVEL": "",
            "GROUPING_COLUMNS": "month, client_code, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.TopQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "top_q_multiplier",
            "COUNTRY": "USA",
        },
        {
            "RATE_TYPE": "DailyBotQCompany",
            "RATE_GROUPING_COLUMNS": "'Total Company' AS rouse_region,'Total Company' AS rouse_district, 'Total Company' AS rouse_market",
            "TIME_INSTANCE_COLUMN": "days",
            "REVENUE_COLUMN": "daily_revenue",
            "GEOGRAPHIC_LEVEL": "",
            "GROUPING_COLUMNS": "month, client_code, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.BotQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "bot_q_multiplier",
            "COUNTRY": "USA",
        },
        {
            "RATE_TYPE": "MonthlyTopQCompanyCAN",
            "RATE_GROUPING_COLUMNS": "'Total Company' AS rouse_region,'Total Company' AS rouse_district, 'Total Company' AS rouse_market",
            "TIME_INSTANCE_COLUMN": "months",
            "REVENUE_COLUMN": "monthly_revenue",
            "GEOGRAPHIC_LEVEL": "",
            "GROUPING_COLUMNS": "month, client_code, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.TopQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "top_q_multiplier",
            "COUNTRY": "CAN",
        },
        {
            "RATE_TYPE": "MonthlyBotQCompanyCAN",
            "RATE_GROUPING_COLUMNS": "'Total Company' AS rouse_region,'Total Company' AS rouse_district, 'Total Company' AS rouse_market",
            "TIME_INSTANCE_COLUMN": "months",
            "REVENUE_COLUMN": "monthly_revenue",
            "GEOGRAPHIC_LEVEL": "",
            "GROUPING_COLUMNS": "month, client_code, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.BotQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "bot_q_multiplier",
            "COUNTRY": "CAN",
        },
        {
            "RATE_TYPE": "WeeklyTopQCompanyCAN",
            "RATE_GROUPING_COLUMNS": "'Total Company' AS rouse_region,'Total Company' AS rouse_district, 'Total Company' AS rouse_market",
            "TIME_INSTANCE_COLUMN": "weeks",
            "REVENUE_COLUMN": "weekly_revenue",
            "GEOGRAPHIC_LEVEL": "",
            "GROUPING_COLUMNS": "month, client_code, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.TopQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "top_q_multiplier",
            "COUNTRY": "CAN",
        },
        {
            "RATE_TYPE": "WeeklyBotQCompanyCAN",
            "RATE_GROUPING_COLUMNS": "'Total Company' AS rouse_region,'Total Company' AS rouse_district, 'Total Company' AS rouse_market",
            "TIME_INSTANCE_COLUMN": "weeks",
            "REVENUE_COLUMN": "weekly_revenue",
            "GEOGRAPHIC_LEVEL": "",
            "GROUPING_COLUMNS": "month, client_code, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.BotQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "bot_q_multiplier",
            "COUNTRY": "CAN",
        },
        {
            "RATE_TYPE": "DailyTopQCompanyCAN",
            "RATE_GROUPING_COLUMNS": "'Total Company' AS rouse_region,'Total Company' AS rouse_district, 'Total Company' AS rouse_market",
            "TIME_INSTANCE_COLUMN": "days",
            "REVENUE_COLUMN": "daily_revenue",
            "GEOGRAPHIC_LEVEL": "",
            "GROUPING_COLUMNS": "month, client_code, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.TopQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "top_q_multiplier",
            "COUNTRY": "CAN",
        },
        {
            "RATE_TYPE": "DailyBotQCompanyCAN",
            "RATE_GROUPING_COLUMNS": "'Total Company' AS rouse_region,'Total Company' AS rouse_district, 'Total Company' AS rouse_market",
            "TIME_INSTANCE_COLUMN": "days",
            "REVENUE_COLUMN": "daily_revenue",
            "GEOGRAPHIC_LEVEL": "",
            "GROUPING_COLUMNS": "month, client_code, rouse_category, rouse_product_type",
            "QUARTILE_FUNCTION": "udfs.BotQuartileMultiplier",
            "QUARTILE_COLUMN_NAME": "bot_q_multiplier",
            "COUNTRY": "CAN",
        },
    ]

    for client_quartiles_rate_type in client_quartiles_parameters:
        insert_client_quartiles_rows = GKEStartPodOperator(
            task_id=f"insert-client-quartiles-{client_quartiles_rate_type['RATE_TYPE'].lower()}",
            name=f"insert-client-quartiles-{client_quartiles_rate_type['RATE_TYPE'].lower()}",
            image=get_full_image_name("client-quartiles-rambo", GCR_REGISTRY),
            arguments=["python3", "main.py", "load-client-quartiles"],
            resources=RESOURCES_SMALL_TASK,
            env_vars={
                "DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
                "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                "AGGS_DATASET_NAME": AGGS_DESTINATION_DATASET,
                "RATE_TYPE": client_quartiles_rate_type["RATE_TYPE"],
                "RATE_GROUPING_COLUMNS": client_quartiles_rate_type[
                    "RATE_GROUPING_COLUMNS"
                ],
                "TIME_INSTANCE_COLUMN": client_quartiles_rate_type[
                    "TIME_INSTANCE_COLUMN"
                ],
                "REVENUE_COLUMN": client_quartiles_rate_type["REVENUE_COLUMN"],
                "GEOGRAPHIC_LEVEL": client_quartiles_rate_type["GEOGRAPHIC_LEVEL"],
                "GROUPING_COLUMNS": client_quartiles_rate_type["GROUPING_COLUMNS"],
                "QUARTILE_FUNCTION": client_quartiles_rate_type["QUARTILE_FUNCTION"],
                "QUARTILE_COLUMN_NAME": client_quartiles_rate_type[
                    "QUARTILE_COLUMN_NAME"
                ],
                "COUNTRY": client_quartiles_rate_type.get("COUNTRY"),
            },
            dag=dag,
        )
        delete_client_quartiles_table >> insert_client_quartiles_rows
        # in order to make sure fmrr_bench tables do not start before fmrr_client_quartiles is totally filled
        insert_client_quartiles_rows >> [
            finished_rambo_datamart,
            update_fmrr_base_record,
            update_fmrr_base_record_error,
        ]

# client quartiles dependencies
finished_materialized_inputs >> [
    delete_client_quartiles_table,
]

# dim fmrr dependencies
(
    finished_materialized_inputs
    >> insert_fmrr_base_record
    >> [populate_fm_aggs, populate_rr_aggs]
    >> compute_dim_fm_rr
)
(
    inject_xyz_records
    >> populate_fmrr_fleetmetrics
    >> inject_xyz_fmrr_fleetmetrics
    >> partition_bq_fmrr_fleet_metrics
)
(
    inject_xyz_records
    >> populate_fmrr_rentalrates
    >> inject_xyz_fmrr_rentalrates
    >> partition_bq_fmrr_rental_rates
)
inject_xyz_records >> partition_bq_dim_fmrr
inject_xyz_fmrr_rentalrates >> populate_fmrr_rraggs
inject_xyz_fmrr_fleetmetrics >> populate_fmrr_fmaggs

[
    compute_dim_fm_rr,
    create_xyz_fleet_metrics_table,
    create_xyz_rental_rates_table,
] >> inject_xyz_records


[insert_fmrr_base_record, update_fmrr_base_record] >> update_fmrr_base_record_when_clear
[
    partition_bq_dim_fmrr,
    partition_bq_fmrr_rental_rates,
    partition_bq_fmrr_fleet_metrics,
] >> update_fmrr_base_record_error


###########################################################
################# FMRR BENCH GROWTH  ######################
###########################################################


bench_growth_dependencies_finished = DummyOperator(
    task_id="bench-growth-dependencies-finished", dag=dag
)
[
    compute_dim_fm_rr,
    inject_xyz_records,
    inject_xyz_fmrr_fleetmetrics,
    populate_fmrr_fleetmetrics,
    populate_fmrr_fmaggs,
] >> bench_growth_dependencies_finished

# This dict will have all the tasks for FMRR Bench and will be used for the dependencies.
rambo_tasks_dict = {
    "bench-growth-dependencies-finished": bench_growth_dependencies_finished,
    "finished-materialized-inputs": finished_materialized_inputs,
}

# ---------------------------------------

# FMRR BENCH GROWTH AND UTILS
fmrr_bench_growth_and_utils_group = TaskGroup(
    group_id="fmrr-bench-growth-and-utils", dag=dag
)
rambo_tasks_dict["fmrr-bench-growth-and-utils"] = fmrr_bench_growth_and_utils_group
fmrr_bench_growth_and_utils_tasks = [
    {
        "task_id": "preload-fmrr-growth",
        "table_name": "preload_fmrr_growth",
        "write_disposition": "WRITE_TRUNCATE",
        "task_dependencies": ["bench-growth-dependencies-finished"],
        "query_file": "preload_fmrr_growth.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
    },
    {
        "task_id": "preload-fmrr-growth-re-rent",
        "table_name": "preload_fmrr_growth_re_rent",
        "write_disposition": "WRITE_TRUNCATE",
        "task_dependencies": ["preload-fmrr-growth",],
        "query_file": "preload_fmrr_growth_re_rent.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
    },
    {
        "task_id": "fmrr-growth-dif-yoy",
        "table_name": "fmrr_growth_dif_yoy",
        "write_disposition": "WRITE_TRUNCATE",
        "task_dependencies": ["preload-fmrr-growth"],
        "query_file": "fmrr_growth_dif_yoy.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
        "send_back_to_ss": True,
        "back_to_ss_task_id": "transfer-fmrr-growth-dif-yoy",
        "sql_server_table_name": "dbo.FMRR_Growth_DiF_YoY",
        "NUMBER_OF_PARTITIONS": 6,
        "PARTITION_COLUMN": "fmrr_id",
        "PARTITION_COLUMN_SS": "FMRR_ID",
        "column_mapping": """{
                            "fmrr_id" : "FMRR_ID",
                            "dif_norm" : "DiF Norm",
                            "yoy_dif_norm" : "YoY DiF Norm",
                            "yoy_cost_in_fleet" : "YoY Cost In Fleet",
                            "catclassmarket_yoy_dif_num" : "CatClassMarket YoY DiF Num",
                            "catclassmarket_yoy_dif_den" : "CatClassMarket YoY DiF Den",
                            "catclassdistrict_yoy_dif_num" : "CatClassDistrict YoY DiF Num",
                            "catclassdistrict_yoy_dif_den" : "CatClassDistrict YoY DiF Den",
                            "catclassregion_yoy_dif_num" : "CatClassRegion YoY DiF Num",
                            "catclassregion_yoy_dif_den" : "CatClassRegion YoY DiF Den",
                            "catclassnational_yoy_dif_num" : "CatClassNational YoY DiF Num",
                            "catclassnational_yoy_dif_den" : "CatClassNational YoY DiF Den",
                            "market_yoy_dif_client" : "Market YoY DiF Client",
                            "market_yoy_dif_bench" : "Market YoY DiF Bench",
                            "market_yoy_dif_den" : "Market YoY DiF Den",
                            "district_yoy_dif_client" : "District YoY DiF Client",
                            "district_yoy_dif_bench" : "District YoY DiF Bench",
                            "district_yoy_dif_den" : "District YoY DiF Den",
                            "region_yoy_dif_client" : "Region YoY DiF Client",
                            "region_yoy_dif_bench" : "Region YoY DiF Bench",
                            "region_yoy_dif_den" : "Region YoY DiF Den",
                            "national_yoy_dif_client" : "National YoY DiF Client",
                            "national_yoy_dif_bench" : "National YoY DiF Bench",
                            "national_yoy_dif_den" : "National YoY DiF Den"
                        }""",
    },
    {
        "task_id": "fmrr-growth-dif-mom",
        "table_name": "fmrr_growth_dif_mom",
        "write_disposition": "WRITE_TRUNCATE",
        "task_dependencies": ["preload-fmrr-growth"],
        "query_file": "fmrr_growth_dif_mom.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
        "send_back_to_ss": True,
        "back_to_ss_task_id": "transfer-fmrr-growth-dif-mom",
        "sql_server_table_name": "dbo.FMRR_Growth_DiF_MoM",
        "NUMBER_OF_PARTITIONS": 6,
        "PARTITION_COLUMN": "fmrr_id",
        "PARTITION_COLUMN_SS": "FMRR_ID",
        "column_mapping": """{
                            "fmrr_id" : "FMRR_ID",
                            "dif_norm" : "DiF Norm",
                            "mom_dif_norm" : "MoM DiF Norm",
                            "mom_cost_in_fleet" : "MoM Cost In Fleet",
                            "catclassmarket_mom_dif_num" : "CatClassMarket MoM DiF Num",
                            "catclassmarket_mom_dif_den" : "CatClassMarket MoM DiF Den",
                            "catclassdistrict_mom_dif_num" : "CatClassDistrict MoM DiF Num",
                            "catclassdistrict_mom_dif_den" : "CatClassDistrict MoM DiF Den",
                            "catclassregion_mom_dif_num" : "CatClassRegion MoM DiF Num",
                            "catclassregion_mom_dif_den" : "CatClassRegion MoM DiF Den",
                            "catclassnational_mom_dif_num" : "CatClassNational MoM DiF Num",
                            "catclassnational_mom_dif_den" : "CatClassNational MoM DiF Den",
                            "market_mom_dif_client" : "Market MoM DiF Client",
                            "market_mom_dif_bench" : "Market MoM DiF Bench",
                            "market_mom_dif_den" : "Market MoM DiF Den",
                            "district_mom_dif_client" : "District MoM DiF Client",
                            "district_mom_dif_bench" : "District MoM DiF Bench",
                            "district_mom_dif_den" : "District MoM DiF Den",
                            "region_mom_dif_client" : "Region MoM DiF Client",
                            "region_mom_dif_bench" : "Region MoM DiF Bench",
                            "region_mom_dif_den" : "Region MoM DiF Den",
                            "national_mom_dif_client" : "National MoM DiF Client",
                            "national_mom_dif_bench" : "National MoM DiF Bench",
                            "national_mom_dif_den" : "National MoM DiF Den"
                        }""",
    },
    {
        "task_id": "fmrr-growth-dor-yoy",
        "table_name": "fmrr_growth_dor_yoy",
        "write_disposition": "WRITE_TRUNCATE",
        "task_dependencies": ["preload-fmrr-growth"],
        "query_file": "fmrr_growth_dor_yoy.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
        "send_back_to_ss": True,
        "back_to_ss_task_id": "transfer-fmrr-growth-dor-yoy",
        "sql_server_table_name": "dbo.FMRR_Growth_DoR_YoY",
        "NUMBER_OF_PARTITIONS": 6,
        "PARTITION_COLUMN": "fmrr_id",
        "PARTITION_COLUMN_SS": "FMRR_ID",
        "column_mapping": """{
                            "fmrr_id" : "FMRR_ID",
                            "dor_norm" : "DoR Norm",
                            "yoy_dor_norm" : "YoY DoR Norm",
                            "yoy_cost_on_rent" : "YoY Cost On Rent",
                            "catclassmarket_yoy_dor_num" : "CatClassMarket YoY DoR Num",
                            "catclassmarket_yoy_dor_den" : "CatClassMarket YoY DoR Den",
                            "catclassdistrict_yoy_dor_num" : "CatClassDistrict YoY DoR Num",
                            "catclassdistrict_yoy_dor_den" : "CatClassDistrict YoY DoR Den",
                            "catclassregion_yoy_dor_num" : "CatClassRegion YoY DoR Num",
                            "catclassregion_yoy_dor_den" : "CatClassRegion YoY DoR Den",
                            "catclassnational_yoy_dor_num" : "CatClassNational YoY DoR Num",
                            "catclassnational_yoy_dor_den" : "CatClassNational YoY DoR Den",
                            "market_yoy_dor_client" : "Market YoY DoR Client",
                            "market_yoy_dor_bench" : "Market YoY DoR Bench",
                            "market_yoy_dor_den" : "Market YoY DoR Den",
                            "district_yoy_dor_client" : "District YoY DoR Client",
                            "district_yoy_dor_bench" : "District YoY DoR Bench",
                            "district_yoy_dor_den" : "District YoY DoR Den",
                            "region_yoy_dor_client" : "Region YoY DoR Client",
                            "region_yoy_dor_bench" : "Region YoY DoR Bench",
                            "region_yoy_dor_den" : "Region YoY DoR Den",
                            "national_yoy_dor_client" : "National YoY DoR Client",
                            "national_yoy_dor_bench" : "National YoY DoR Bench",
                            "national_yoy_dor_den" : "National YoY DoR Den"
                        }""",
    },
    {
        "task_id": "fmrr-growth-dor-mom",
        "table_name": "fmrr_growth_dor_mom",
        "write_disposition": "WRITE_TRUNCATE",
        "task_dependencies": ["preload-fmrr-growth"],
        "query_file": "fmrr_growth_dor_mom.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
        "send_back_to_ss": True,
        "back_to_ss_task_id": "transfer-fmrr-growth-dor-mom",
        "sql_server_table_name": "dbo.FMRR_Growth_DoR_MoM",
        "NUMBER_OF_PARTITIONS": 6,
        "PARTITION_COLUMN": "fmrr_id",
        "PARTITION_COLUMN_SS": "FMRR_ID",
        "column_mapping": """{
                            "fmrr_id" : "FMRR_ID",
                            "dor_norm" : "DoR Norm",
                            "mom_dor_norm" : "MoM DoR Norm",
                            "mom_cost_on_rent" : "MoM Cost On Rent",
                            "catclassmarket_mom_dor_num" : "CatClassMarket MoM DoR Num",
                            "catclassmarket_mom_dor_den" : "CatClassMarket MoM DoR Den",
                            "catclassdistrict_mom_dor_num" : "CatClassDistrict MoM DoR Num",
                            "catclassdistrict_mom_dor_den" : "CatClassDistrict MoM DoR Den",
                            "catclassregion_mom_dor_num" : "CatClassRegion MoM DoR Num",
                            "catclassregion_mom_dor_den" : "CatClassRegion MoM DoR Den",
                            "catclassnational_mom_dor_num" : "CatClassNational MoM DoR Num",
                            "catclassnational_mom_dor_den" : "CatClassNational MoM DoR Den",
                            "market_mom_dor_client" : "Market MoM DoR Client",
                            "market_mom_dor_bench" : "Market MoM DoR Bench",
                            "market_mom_dor_den" : "Market MoM DoR Den",
                            "district_mom_dor_client" : "District MoM DoR Client",
                            "district_mom_dor_bench" : "District MoM DoR Bench",
                            "district_mom_dor_den" : "District MoM DoR Den",
                            "region_mom_dor_client" : "Region MoM DoR Client",
                            "region_mom_dor_bench" : "Region MoM DoR Bench",
                            "region_mom_dor_den" : "Region MoM DoR Den",
                            "national_mom_dor_client" : "National MoM DoR Client",
                            "national_mom_dor_bench" : "National MoM DoR Bench",
                            "national_mom_dor_den" : "National MoM DoR Den"
                        }""",
    },
    {
        "task_id": "fmrr-growth-komschema",
        "table_name": "fmrr_growth_komschema",
        "write_disposition": "WRITE_TRUNCATE",
        "task_dependencies": ["preload-fmrr-growth"],
        "query_file": "fmrr_growth_komschema.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
    },
    {
        "task_id": "fmrr-growth-komschema-territory",
        "table_name": "fmrr_growth_komschema",
        "write_disposition": "WRITE_TRUNCATE",
        "recreate_table": "False",
        "task_dependencies": ["fmrr-growth-komschema"],
        "query_file": "fmrr_growth_komschema_territory.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
    },
    {
        "task_id": "fmrr-growth-komschema-region",
        "table_name": "fmrr_growth_komschema",
        "write_disposition": "WRITE_TRUNCATE",
        "recreate_table": "False",
        "task_dependencies": ["fmrr-growth-komschema-territory"],
        "query_file": "fmrr_growth_komschema_region.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
        "send_back_to_ss": True,
        "back_to_ss_task_id": "transfer-fmrr-growth-komschema",
        "sql_server_table_name": "dbo.FMRR_Growth_KOMschema",
        "column_mapping": """{
                            "fmrr_id" : "FMRR_ID",
                            "dif_norm" : "DiF Norm",
                            "dor_norm" : "DoR Norm",
                            "yoy_dif_norm" : "YoY DiF Norm",
                            "yoy_dor_norm" : "YoY DoR Norm",
                            "mom_dif_norm" : "MoM DiF Norm",
                            "mom_dor_norm" : "MoM DoR Norm",
                            "yoy_cost_in_fleet" : "YoY Cost In Fleet",
                            "yoy_cost_on_rent" : "YoY Cost On Rent",
                            "mom_cost_in_fleet" : "MoM Cost In Fleet",
                            "mom_cost_on_rent" : "MoM Cost On Rent",
                            "territory_yoy_dif_client" : "Territory YoY DiF Client",
                            "territory_yoy_dif_bench" : "Territory YoY DiF Bench",
                            "territory_yoy_dif_den" : "Territory YoY DiF Den",
                            "territory_yoy_dor_client" : "Territory YoY DoR Client",
                            "territory_yoy_dor_bench" : "Territory YoY DoR Bench",
                            "territory_yoy_dor_den" : "Territory YoY DoR Den",
                            "territory_mom_dif_client" : "Territory MoM DiF Client",
                            "territory_mom_dif_bench" : "Territory MoM DiF Bench",
                            "territory_mom_dif_den" : "Territory MoM DiF Den",
                            "territory_mom_dor_client" : "Territory MoM DoR Client",
                            "territory_mom_dor_bench" : "Territory MoM DoR Bench",
                            "territory_mom_dor_den" : "Territory MoM DoR Den",
                            "territory_util_client" : "Territory Util Client",
                            "territory_util_bench" : "Territory Util Bench",
                            "territory_avail_client" : "Territory Avail Client",
                            "territory_avail_bench" : "Territory Avail Bench",
                            "territory_unavail_client" : "Territory Unavail Client",
                            "territory_unavail_bench" : "Territory Unavail Bench",
                            "territory_fleetage_client" : "Territory FleetAge Client",
                            "territory_fleetage_bench" : "Territory FleetAge Bench",
                            "region_yoy_dif_client" : "Region YoY DiF Client",
                            "region_yoy_dif_bench" : "Region YoY DiF Bench",
                            "region_yoy_dif_den" : "Region YoY DiF Den",
                            "region_yoy_dor_client" : "Region YoY DoR Client",
                            "region_yoy_dor_bench" : "Region YoY DoR Bench",
                            "region_yoy_dor_den" : "Region YoY DoR Den",
                            "region_mom_dif_client" : "Region MoM DiF Client",
                            "region_mom_dif_bench" : "Region MoM DiF Bench",
                            "region_mom_dif_den" : "Region MoM DiF Den",
                            "region_mom_dor_client" : "Region MoM DoR Client",
                            "region_mom_dor_bench" : "Region MoM DoR Bench",
                            "region_mom_dor_den" : "Region MoM DoR Den",
                            "region_util_client" : "Region Util Client",
                            "region_util_bench" : "Region Util Bench",
                            "region_avail_client" : "Region Avail Client",
                            "region_avail_bench" : "Region Avail Bench",
                            "region_unavail_client" : "Region Unavail Client",
                            "region_unavail_bench" : "Region Unavail Bench",
                            "region_fleetage_client" : "Region FleetAge Client",
                            "region_fleetage_bench" : "Region FleetAge Bench"
                        }""",
    },
    {
        "task_id": "fmrr-growth-catschema",
        "table_name": "fmrr_growth_catschema",
        "write_disposition": "WRITE_TRUNCATE",
        "task_dependencies": ["preload-fmrr-growth"],
        "query_file": "fmrr_growth_catschema.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
    },
    {
        "task_id": "fmrr-growth-catschema-territory",
        "table_name": "fmrr_growth_catschema",
        "write_disposition": "WRITE_TRUNCATE",
        "recreate_table": "False",
        "task_dependencies": ["fmrr-growth-catschema"],
        "query_file": "fmrr_growth_catschema_territory.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
    },
    {
        "task_id": "fmrr-growth-catschema-district",
        "table_name": "fmrr_growth_catschema",
        "write_disposition": "WRITE_TRUNCATE",
        "recreate_table": "False",
        "task_dependencies": ["fmrr-growth-catschema-territory"],
        "query_file": "fmrr_growth_catschema_district.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
        "send_back_to_ss": True,
        "back_to_ss_task_id": "transfer-fmrr-growth-catschema",
        "sql_server_table_name": "dbo.FMRR_Growth_CATschema",
        "NUMBER_OF_PARTITIONS": 3,
        "PARTITION_COLUMN": "fmrr_id",
        "PARTITION_COLUMN_SS": "FMRR_ID",
        "column_mapping": """{
                            "fmrr_id" : "FMRR_ID",
                            "dif_norm" : "DiF Norm",
                            "dor_norm" : "DoR Norm",
                            "yoy_dif_norm" : "YoY DiF Norm",
                            "yoy_dor_norm" : "YoY DoR Norm",
                            "mom_dif_norm" : "MoM DiF Norm",
                            "mom_dor_norm" : "MoM DoR Norm",
                            "yoy_cost_in_fleet" : "YoY Cost In Fleet",
                            "yoy_cost_on_rent" : "YoY Cost On Rent",
                            "mom_cost_in_fleet" : "MoM Cost In Fleet",
                            "mom_cost_on_rent" : "MoM Cost On Rent",
                            "territory_yoy_dif_client" : "Territory YoY DiF Client",
                            "territory_yoy_dif_bench" : "Territory YoY DiF Bench",
                            "territory_yoy_dif_den" : "Territory YoY DiF Den",
                            "territory_yoy_dor_client" : "Territory YoY DoR Client",
                            "territory_yoy_dor_bench" : "Territory YoY DoR Bench",
                            "territory_yoy_dor_den" : "Territory YoY DoR Den",
                            "territory_mom_dif_client" : "Territory MoM DiF Client",
                            "territory_mom_dif_bench" : "Territory MoM DiF Bench",
                            "territory_mom_dif_den" : "Territory MoM DiF Den",
                            "territory_mom_dor_client" : "Territory MoM DoR Client",
                            "territory_mom_dor_bench" : "Territory MoM DoR Bench",
                            "territory_mom_dor_den" : "Territory MoM DoR Den",
                            "territory_util_client" : "Territory Util Client",
                            "territory_util_bench" : "Territory Util Bench",
                            "territory_avail_client" : "Territory Avail Client",
                            "territory_avail_bench" : "Territory Avail Bench",
                            "territory_unavail_client" : "Territory Unavail Client",
                            "territory_unavail_bench" : "Territory Unavail Bench",
                            "territory_fleetage_client" : "Territory FleetAge Client",
                            "territory_fleetage_bench" : "Territory FleetAge Bench",
                            "catdist_yoy_dif_client" : "CATdist YoY DiF Client",
                            "catdist_yoy_dif_bench" : "CATdist YoY DiF Bench",
                            "catdist_yoy_dif_den" : "CATdist YoY DiF Den",
                            "catdist_yoy_dor_client" : "CATdist YoY DoR Client",
                            "catdist_yoy_dor_bench" : "CATdist YoY DoR Bench",
                            "catdist_yoy_dor_den" : "CATdist YoY DoR Den",
                            "catdist_mom_dif_client" : "CATdist MoM DiF Client",
                            "catdist_mom_dif_bench" : "CATdist MoM DiF Bench",
                            "catdist_mom_dif_den" : "CATdist MoM DiF Den",
                            "catdist_mom_dor_client" : "CATdist MoM DoR Client",
                            "catdist_mom_dor_bench" : "CATdist MoM DoR Bench",
                            "catdist_mom_dor_den" : "CATdist MoM DoR Den",
                            "catdist_util_client" : "CATdist Util Client",
                            "catdist_util_bench" : "CATdist Util Bench",
                            "catdist_avail_client" : "CATdist Avail Client",
                            "catdist_avail_bench" : "CATdist Avail Bench",
                            "catdist_unavail_client" : "CATdist Unavail Client",
                            "catdist_unavail_bench" : "CATdist Unavail Bench",
                            "catdist_fleetage_client" : "CATdist FleetAge Client",
                            "catdist_fleetage_bench" : "CATdist FleetAge Bench"
                        }""",
    },
    {
        "task_id": "fmrr-util",
        "table_name": "fmrr_util",
        "write_disposition": "WRITE_TRUNCATE",
        "task_dependencies": ["preload-fmrr-growth","preload-fmrr-growth-re-rent"],
        "query_file": "fmrr_util.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
    },
    {
        "task_id": "fmrr-util-international",
        "table_name": "fmrr_util",
        "write_disposition": "WRITE_TRUNCATE",
        "recreate_table": "False",
        "task_dependencies": ["fmrr-util"],
        "query_file": "fmrr_util_international.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
        "send_back_to_ss": True,
        "back_to_ss_task_id": "transfer-fmrr-util",
        "resources": k8s.V1ResourceRequirements(requests={"cpu": "3000m", "memory": "2Gi"}),
        "parallel_processes": "4",
        "sql_server_table_name": "dbo.FMRR_Util",
        "NUMBER_OF_PARTITIONS": 6,
        "PARTITION_COLUMN": "fmrr_id",
        "PARTITION_COLUMN_SS": "FMRR_ID",
        "column_mapping": """{
                            "fmrr_id" : "FMRR_ID",
                            "catclass_util" : "CatClass Util",
                            "market_util_client" : "Market Util Client",
                            "market_util_bench" : "Market Util Bench",
                            "district_util_client" : "District Util Client",
                            "district_util_bench" : "District Util Bench",
                            "region_util_client" : "Region Util Client",
                            "region_util_bench" : "Region Util Bench",
                            "national_util_client" : "National Util Client",
                            "national_util_bench" : "National Util Bench",
                            "catclass_avail" : "CatClass Avail",
                            "market_avail_client" : "Market Avail Client",
                            "market_avail_bench" : "Market Avail Bench",
                            "district_avail_client" : "District Avail Client",
                            "district_avail_bench" : "District Avail Bench",
                            "region_avail_client" : "Region Avail Client",
                            "region_avail_bench" : "Region Avail Bench",
                            "national_avail_client" : "National Avail Client",
                            "national_avail_bench" : "National Avail Bench",
                            "catclass_unavail" : "CatClass Unavail",
                            "market_unavail_client" : "Market Unavail Client",
                            "market_unavail_bench" : "Market Unavail Bench",
                            "district_unavail_client" : "District Unavail Client",
                            "district_unavail_bench" : "District Unavail Bench",
                            "region_unavail_client" : "Region Unavail Client",
                            "region_unavail_bench" : "Region Unavail Bench",
                            "national_unavail_client" : "National Unavail Client",
                            "national_unavail_bench" : "National Unavail Bench"
                        }""",
    },
    {
        "task_id": "fmrr-util-xuri",
        "table_name": "fmrr_util_xuri",
        "write_disposition": "WRITE_TRUNCATE",
        "task_dependencies": ["preload-fmrr-growth","preload-fmrr-growth-re-rent"],
        "query_file": "fmrr_util_xuri.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
        "send_back_to_ss": True,
        "back_to_ss_task_id": "transfer-fmrr-util-xuri",
        "sql_server_table_name": "dbo.FMRR_Util_xURI",
        "column_mapping": """{
                            "fmrr_id" : "FMRR_ID",
                            "market_util_bench" : "Market Util Bench",
                            "district_util_bench" : "District Util Bench",
                            "region_util_bench" : "Region Util Bench",
                            "national_util_bench" : "National Util Bench",
                            "market_avail_bench" : "Market Avail Bench",
                            "district_avail_bench" : "District Avail Bench",
                            "region_avail_bench" : "Region Avail Bench",
                            "national_avail_bench" : "National Avail Bench",
                            "market_unavail_bench" : "Market Unavail Bench",
                            "district_unavail_bench" : "District Unavail Bench",
                            "region_unavail_bench" : "Region Unavail Bench",
                            "national_unavail_bench" : "National Unavail Bench"
                        }""",
    },
  {
        "task_id": "fmrr-util-majors", 
        "table_name": "fmrr_util_majors",
        "write_disposition": "WRITE_TRUNCATE",
        "task_dependencies": ["preload-fmrr-growth","preload-fmrr-growth-re-rent"],
        "query_file": "fmrr_util_majors.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
        "send_back_to_ss": True,
        "back_to_ss_task_id": "transfer-fmrr-util-majors",
        "sql_server_table_name": "dbo.FMRR_Util_Majors",
        "NUMBER_OF_PARTITIONS": 3,
        "PARTITION_COLUMN": "fmrr_id",
        "PARTITION_COLUMN_SS": "FMRR_ID",
        "column_mapping": """{
                            "fmrr_id" : "FMRR_ID",
                            "market_util_bench" : "Market Util Bench",
                            "district_util_bench" : "District Util Bench",
                            "region_util_bench" : "Region Util Bench",
                            "national_util_bench" : "National Util Bench",
                            "market_avail_bench" : "Market Avail Bench",
                            "district_avail_bench" : "District Avail Bench",
                            "region_avail_bench" : "Region Avail Bench",
                            "national_avail_bench" : "National Avail Bench",
                            "market_unavail_bench" : "Market Unavail Bench",
                            "district_unavail_bench" : "District Unavail Bench",
                            "region_unavail_bench" : "Region Unavail Bench",
                            "national_unavail_bench" : "National Unavail Bench"
                        }""",
    },    
    {
        "task_id": "fmrr-fleetage",
        "table_name": "fmrr_fleetage",
        "write_disposition": "WRITE_TRUNCATE",
        "task_dependencies": ["preload-fmrr-growth","preload-fmrr-growth-re-rent"],
        "query_file": "fmrr_fleetage.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
        },
        "send_back_to_ss": True,
        "back_to_ss_task_id": "transfer-fmrr-fleetage",
        "sql_server_table_name": "dbo.FMRR_FleetAge",
        "NUMBER_OF_PARTITIONS": 3,
        "PARTITION_COLUMN": "fmrr_id",
        "PARTITION_COLUMN_SS": "FMRR_ID",
        "column_mapping": """{
                            "fmrr_id" : "FMRR_ID",
                            "catclass_fleetage" : "CatClass FleetAge",
                            "market_fleetage_client" : "Market FleetAge Client",
                            "market_fleetage_bench" : "Market FleetAge Bench",
                            "district_fleetage_client" : "District FleetAge Client",
                            "district_fleetage_bench" : "District FleetAge Bench",
                            "region_fleetage_client" : "Region FleetAge Client",
                            "region_fleetage_bench" : "Region FleetAge Bench",
                            "national_fleetage_client" : "National FleetAge Client",
                            "national_fleetage_bench" : "National FleetAge Bench"
                        }""",
    },
]

fmrr_bench_growth_and_utils_complete = DummyOperator(
    task_id="fmrr-bench-growth-and-utils-complete",
    dag=dag,
    task_group=fmrr_bench_growth_and_utils_group,
)
rambo_tasks_dict[
    "fmrr-bench-growth-and-utils-complete"
] = fmrr_bench_growth_and_utils_complete

# Initializing parameters common to all tasks in FMRR Bench Growth and Utils
for task_info in fmrr_bench_growth_and_utils_tasks:
    log_task_name = task_info["task_id"].replace("-", "_").upper()
    task_info[
        "logs_job_identifier"
    ] = f"RAMBO_PIPELINE/FMRR_BENCH_GROWTH_AND_UTILS/{log_task_name}"

    task_info["sql_folder"] = FMRR_BENCH_GROWTH_SQL_FOLDER

    task_info["end_task"] = "fmrr-bench-growth-and-utils-complete"

    task_info["task_group"] = "fmrr-bench-growth-and-utils"

# ---------------------------------------

# BENCHMARK GROWTH
benchmark_growth_group = TaskGroup(group_id="benchmark-growth", dag=dag)
rambo_tasks_dict["benchmark-growth"] = benchmark_growth_group
fmrr_benchmark_growth_tasks = [
    {
        "task_id": "create-benchmark-growth-table",
        "table_name": "benchmark_growth",
        "task_dependencies": ["bench-growth-dependencies-finished"],
        "query_file": "create_benchmark_growth_table.sql",
        "query_params": {
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
    {
        "task_id": "benchmark-growth-bestav",
        "table_name": "benchmark_growth",
        "task_dependencies": [
            "create-benchmark-growth-table",
            "benchmark-growth-market",
            "benchmark-growth-district",
        ],
        "query_file": "benchmark_growth_bestav.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
    {
        "task_id": "benchmark-growth-kom-reg",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_kom_reg.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
    {
        "task_id": "benchmark-growth-region-xuri",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_region_xuri.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
    {
        "task_id": "benchmark-growth-region-majors",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_region_majors.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },    
    {
        "task_id": "benchmark-growth-cat-dist",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_cat_dist.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
    {
        "task_id": "benchmark-growth-kom-subterr",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_kom_subterr.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
    {
        "task_id": "benchmark-growth-cat-subterr",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_cat_subterr.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
    {
        "task_id": "benchmark-growth-kom-terr",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_kom_terr.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
    {
        "task_id": "benchmark-growth-us-xuri",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_us_xuri.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
    {
        "task_id": "benchmark-growth-us-majors",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_us_majors.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },    
    {
        "task_id": "benchmark-growth-cat-terr",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_cat_terr.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
    {
        "task_id": "benchmark-growth-market",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_market.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
    {
        "task_id": "benchmark-growth-country",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_country.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
    {
        "task_id": "benchmark-growth-market-xuri",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_market_xuri.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
    {
        "task_id": "benchmark-growth-market-majors",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_market_majors.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },    
    {
        "task_id": "benchmark-growth-district",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_district.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
    {
        "task_id": "benchmark-growth-mrdc-bestav-xuri",
        "table_name": "benchmark_growth",
        "task_dependencies": [
            "create-benchmark-growth-table",
            "benchmark-growth-region-xuri",
            "benchmark-growth-us-xuri",
            "benchmark-growth-market-xuri",
            "benchmark-growth-district-xuri",
        ],
        "query_file": "benchmark_growth_mrdc_bestav_xuri.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
    {
        "task_id": "benchmark-growth-mrdc-bestav-majors",
        "table_name": "benchmark_growth",
        "task_dependencies": [
            "create-benchmark-growth-table",
            "benchmark-growth-region-majors",
            "benchmark-growth-us-majors",
            "benchmark-growth-market-majors",
            "benchmark-growth-district-majors",
        ],
        "query_file": "benchmark_growth_mrdc_bestav_majors.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },    
    {
        "task_id": "benchmark-growth-district-xuri",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_district_xuri.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
    {
        "task_id": "benchmark-growth-district-majors",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_district_majors.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },    
    {
        "task_id": "benchmark-growth-region",
        "table_name": "benchmark_growth",
        "task_dependencies": ["create-benchmark-growth-table"],
        "query_file": "benchmark_growth_region.sql",
        "query_params": {
            "INGESTION_DATASET": AGGS_DESTINATION_DATASET,
            "DIM_FM_RR_DESTINATION_DATASET": DIM_FM_RR_DESTINATION_DATASET,
            "DIM_FM_RR_SOURCE_DATASET": DIM_FM_RR_SOURCE_DATASET,
            "BENCHMARK_GROWTH_TABLE_NAME": f"benchmark_growth__{TABLE_SUFFIX}",
        },
    },
]

benchmark_growth_complete = DummyOperator(
    task_id="benchmark-growth-complete", dag=dag, task_group=benchmark_growth_group
)
rambo_tasks_dict["benchmark-growth-complete"] = benchmark_growth_complete

# I'll have to create this task and dependency 'manually' here as it don't fit the model of tasks in dicts.
transfer_benchmark_growth_back_to_ss = GKEStartPodOperator(
    task_id="transfer-benchmark-growth-to-ss",
    name="transfer-benchmark-growth-to-ss",
    image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
    priority_weight=11,
    secrets=[secret_db_username, secret_db_password],
    task_group=benchmark_growth_group,
    arguments=["python3", "main.py", "load-from-bq-to-ss"],
    resources=k8s.V1ResourceRequirements(requests={"cpu": "3000m", "memory": "2Gi"}),
    pool=ss_import_pool_name,
    env_vars={
        "GCP_PROJECT": DATA_PROJECT_NAME,
        "BQ_DATASET": DIM_FM_RR_SOURCE_DATASET,
        "BQ_TABLE": f"benchmark_growth__{TABLE_SUFFIX}",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Analytics_Cloud",
        "TABLE_NAME": "dbo.Benchmark_Growth",
        "WRITE_MODE": "overwrite",
        "GCS_BUCKET_NAME": BUCKET_NAME,
        "CHUNKSIZE": "100000",
        "COLUMN_MAPPING": """{  "geolevelid" : "GeoLevelID",
                                    "monthid" : "MonthID",
                                    "geolevel" : "GeoLevel",
                                    "country" : "Country",
                                    "rouse_region" : "Rouse Region",
                                    "rouse_district" : "Rouse District",
                                    "rouse_market" : "Rouse Market",
                                    "rouseproducttypeid" : "RouseProductTypeID",
                                    "growth_mom_dor_bench" : "Growth MoM DoR Bench",
                                    "growth_mom_dif_bench" : "Growth MoM DiF Bench",
                                    "growth_yoy_dor_bench" : "Growth YoY DoR Bench",
                                    "growth_yoy_dif_bench" : "Growth YoY DiF Bench"
                                }""",
    },
    dag=dag,
)
benchmark_growth_complete >> transfer_benchmark_growth_back_to_ss
rambo_tasks_dict[
    "transfer-benchmark-growth-to-ss"
] = transfer_benchmark_growth_back_to_ss

# Initializing parameters common to all tasks in Benchmark Growth
for task_info in fmrr_benchmark_growth_tasks:
    log_task_name = task_info["task_id"].replace("-", "_").upper()
    task_info[
        "logs_job_identifier"
    ] = f"RAMBO_PIPELINE/BENCHMARK_GROWTH/{log_task_name}"

    task_info["sql_folder"] = FMRR_BENCH_GROWTH_SQL_FOLDER
    task_info["write_disposition"] = None
    task_info["end_task"] = "benchmark-growth-complete"
    task_info["task_group"] = "benchmark-growth"


# ---------------------------------------

# CREATE THE TASKS AND DEPENDENCIES

fmrr_bench_tasks = fmrr_bench_growth_and_utils_tasks + fmrr_benchmark_growth_tasks

# Loop first to create the tasks
for task_info in fmrr_bench_tasks:

    # Default view and default destination tables
    default_view_to_replace = (
        f"{DATA_PROJECT_NAME}.{DIM_FM_RR_DESTINATION_DATASET}.{task_info['table_name']}"
    )
    default_destination_table = f"{DATA_PROJECT_NAME}.{DIM_FM_RR_SOURCE_DATASET}.{task_info['table_name']}__{TABLE_SUFFIX}"

    # Open the query file, and replace the query parameters if needed
    sql_file = open(task_info["sql_folder"] + task_info["query_file"])
    sql_as_string = sql_file.read()
    if "query_params" in task_info.keys():
        sql_as_string = sql_as_string.format(**task_info["query_params"])

    if "column_mapping_file" in task_info.keys():
        mapping_as_string = open(
            task_info["sql_folder"] + task_info["column_mapping_file"]
        )
        task_info["column_mapping"] = mapping_as_string.read()

    # Base Operator 'Execute Bigquery' will do all the work of executing the queries and creating the FMRR Bench Carry Forward tables/views
    query_task = GKEStartPodOperator(
        task_id=task_info["task_id"],
        name=task_info["task_id"],
        image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
        arguments=["python3", "main.py", "execute-bigquery"],
        do_xcom_push=False,
        task_group=rambo_tasks_dict[task_info["task_group"]]
        if "task_group" in task_info.keys()
        else None,
        resources=RESOURCES_SMALL_TASK,
        env_vars={
            "GCP_PROJECT_ID": DATA_PROJECT_NAME,
            "QUERY": sql_as_string,
            "DESTINATION_TABLE": task_info["destination_table"]
            if "destination_table" in task_info.keys()
            else default_destination_table,
            "VIEW_TO_REPLACE": task_info["view_to_replace"]
            if "view_to_replace" in task_info.keys()
            else default_view_to_replace,
            "XCOM_PUSH": "False",
            "WRITE_LOGS": "True",
            "LOGS_GCP_PROJECT": PROJECT_NAME,
            "LOGS_JOB_IDENTIFIER": task_info["logs_job_identifier"],
            "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
            "WRITE_DISPOSITION": task_info["write_disposition"]
            if "write_disposition" in task_info.keys()
            else None,
            "RECREATE_TABLE": task_info["recreate_table"]
            if "recreate_table" in task_info.keys()
            else "True",
            "TYPE_OF_PARTITIONING": task_info["type_of_partitioning"]
            if "type_of_partitioning" in task_info.keys()
            else None,
            "CLUSTERING_FIELDS": task_info["clustering_fields"]
            if "clustering_fields" in task_info.keys()
            else None,
            "PARTITION_COLUMN": task_info["partition_column"]
            if "partition_column" in task_info.keys()
            else None,
            "RANGE_START": task_info["range_start"]
            if "range_start" in task_info.keys()
            else None,
            "RANGE_END": task_info["range_end"]
            if "range_end" in task_info.keys()
            else None,
            "RANGE_INTERVAL": task_info["range_interval"]
            if "range_interval" in task_info.keys()
            else None,
        },
        dag=dag,
    )

    # Salving the task object to create the dependencies at the next step
    rambo_tasks_dict[task_info["task_id"]] = query_task

    # With task groups the full_task_id is task_group_name.task_id (It changes the xcom_Pull)
    # If the task isn't in a task group, its just the regular task_id
    full_task_id = (
        f'{task_info["task_group"]}.{task_info["task_id"]}'
        if "task_group" in task_info.keys()
        else task_info["task_id"]
    )

    # Logic for sending the tables back to SQL Server using 'bq-to-ss' operator
    if task_info["send_back_to_ss"] if "send_back_to_ss" in task_info.keys() else False:


        # If there's no partition_ammount, I'll do the regular transfer
        if "NUMBER_OF_PARTITIONS" not in task_info.keys():
            transfer_table_back_to_ss = GKEStartPodOperator(
                task_id=task_info["back_to_ss_task_id"],
                name=task_info["back_to_ss_task_id"],
                image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
                execution_timeout=timedelta(hours=3),
                task_group=rambo_tasks_dict[task_info["task_group"]]
                if "task_group" in task_info.keys()
                else None,
                # The priority for all FMRR Bench is 11, but for CTRL Tables it is higher
                priority_weight=task_info["priority_weight_transfer"]
                if "priority_weight_transfer" in task_info.keys()
                else 11,
                secrets=[secret_db_username, secret_db_password],
                arguments=["python3", "main.py", "load-from-bq-to-ss"],
                resources=task_info["resources"]
                if "resources" in task_info.keys()
                else k8s.V1ResourceRequirements(requests={"cpu": "2000m", "memory": "2.5Gi"}),
                pool=ss_import_pool_name,
                env_vars={
                    "GCP_PROJECT": DATA_PROJECT_NAME,
                    "BQ_DATASET": DIM_FM_RR_SOURCE_DATASET,
                    "BQ_TABLE": task_info["destination_table"].split('.')[-1] + f"__{TABLE_SUFFIX}"
                            if "destination_table" in task_info.keys()
                            else f"{task_info['table_name']}__{TABLE_SUFFIX}",
                    "DB_SERVER": GETL01_SQL_SERVER,
                    "DB_NAME": "ras_DataMart_Analytics_Cloud",
                    "TABLE_NAME": task_info["sql_server_table_name"],
                    "WRITE_MODE": "overwrite",
                    "GCS_BUCKET_NAME": BUCKET_NAME,
                    "CHUNKSIZE": task_info["chunksize"]
                    if "chunksize" in task_info.keys()
                    else "100000",
                    "PARALLEL_PROCESSES": task_info["parallel_processes"]
                    if "parallel_processes" in task_info.keys()
                    else "2",
                    "COLUMN_MAPPING": task_info["column_mapping"],
                    "PARSE_DTYPES": task_info["parse_dtypes"]
                    if "parse_dtypes" in task_info.keys()
                    else "False",
                },
                dag=dag,
            )
            # The finished_fmrr_bench_growth dummy task won't depend on the Back to SS tasks (as the BQ processes can go on)
            query_task >> transfer_table_back_to_ss

            if "end_transfer_task" in task_info.keys():
                transfer_table_back_to_ss >> task_info["end_transfer_task"]
            else:
                transfer_table_back_to_ss >> [
                    update_fmrr_base_record,
                    update_fmrr_base_record_error,
            ]
        else:

            partition_task_group=rambo_tasks_dict[task_info["task_group"]] if "task_group" in task_info.keys() else None
            # there is partition_ammount on the keys, so I'll first partition the table
            partition_task_id = f"partition-bq-table-{task_info['task_id']}"
            partition_bq_table = GKEStartPodOperator(
                task_id=partition_task_id,
                name=partition_task_id,
                image=get_full_image_name("execute-bigquery", GCR_REGISTRY),
                arguments=["python3", "main.py", "execute-bigquery"],
                do_xcom_push=False,
                resources=RESOURCES_SMALL_TASK,
                task_group=partition_task_group,
                env_vars={
                    "GCP_PROJECT_ID": DATA_PROJECT_NAME,
                    "QUERY": open(
                        DAG_BASE_SQL_FOLDER
                        + "push_to_prod/push_to_prod_preload/partition_bq_tables_template.sql"
                    )
                    .read()
                    .replace(
                        "{NUMBER_OF_PARTITIONS}",
                        str(task_info["NUMBER_OF_PARTITIONS"]),
                    )
                    .replace(
                        "{PARTITION_COLUMN}",
                        task_info["PARTITION_COLUMN"],
                    )
                    .replace(
                        "{TABLE_NAME}",
                        f"{DIM_FM_RR_SOURCE_DATASET}.{task_info['table_name']}__{TABLE_SUFFIX}",
                    ),
                    "XCOM_PUSH": "False",
                    "WRITE_LOGS": "True",
                    "LOGS_GCP_PROJECT": PROJECT_NAME,
                    "LOGS_JOB_IDENTIFIER": f"RAMBO_PIPELINE/PUSH_TO_PROD/{task_info['table_name'].upper()}",
                    "LOGS_DAG_EXECUTION_DATE": "{{ ts }}",
                    "WRITE_DISPOSITION": None,
                },
                dag=dag,
            )
            create_stg_table = GKEStartPodOperator(
                task_id=f"create-stg-table-{task_info['task_id']}",
                name=f"create-stg-table-{task_info['task_id']}",
                image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
                secrets=[secret_db_username, secret_db_password],
                arguments=["python3", "main.py", "create-stg-table"],
                resources=RESOURCES_SMALL_TASK,
                task_group=partition_task_group,
                # Same priority as other RAMBO Push to prod tasks
                priority_weight=task_info["PRIORITY_WEIGHT"]
                if "PRIORITY_WEIGHT" in task_info.keys()
                else 14,
                env_vars={
                    "DB_SERVER": GETL01_SQL_SERVER,
                    "DB_NAME": "ras_DataMart_Analytics_Cloud",
                    "TABLE_NAME": task_info[
                        "sql_server_table_name"
                    ],
                },
                dag=dag,
            )
            # Used on some dependencies
            rambo_tasks_dict[
                f"create-stg-table-{task_info['task_id']}"
            ] = create_stg_table

            # Depends on the last_task, but will only start the transfer after the preload fnishes
            query_task >> partition_bq_table >> create_stg_table

            staging_table_name = (
                task_info["sql_server_table_name"] + "_STG"
            )

            recreate_stg_table_indexes = GKEStartPodOperator(
                task_id=f"recreate-stg-table-indexes-{task_info['task_id']}",
                name=f"recreate-stg-table-indexes-{task_info['task_id']}",
                image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
                secrets=[secret_db_username, secret_db_password],
                arguments=["python3", "main.py", "recreate-stg-table-indexes"],
                resources=RESOURCES_SMALL_TASK,
                task_group=partition_task_group,
                # Same priority as other RAMBO Push to prod tasks
                priority_weight=task_info["PRIORITY_WEIGHT"]
                if "PRIORITY_WEIGHT" in task_info.keys()
                else 14,
                env_vars={
                    "DB_SERVER": GETL01_SQL_SERVER,
                    "DB_NAME": "ras_DataMart_Analytics_Cloud",
                    "TABLE_NAME": task_info[
                        "sql_server_table_name"
                    ],
                    "REPLACE_TABLE": 'True',
                    "DO_QA_ROW_COUNT": "True",
                    "QA_GCP_PROJECT": DATA_PROJECT_NAME,
                    "BQ_QA_TABLE": f"{DATA_PROJECT_NAME}.{DIM_FM_RR_SOURCE_DATASET}.{task_info['table_name']}__{TABLE_SUFFIX}",
                    "SS_QA_TABLE": f"ras_DataMart_Analytics_Cloud.{staging_table_name}",
                    # In case there is a manual index set, I'll pass it to the transfer
                    "MANUAL_INDEX_COMMAND": None,
                },
                dag=dag,
            )

            for partition_id in range(
                0, int(task_info["NUMBER_OF_PARTITIONS"])
            ):
                delete_before_insert_query = (
                    open(
                        DAG_BASE_SQL_FOLDER
                        + "push_to_prod/push_to_prod_preload/delete_partition_before_inserting_template.sql"
                    )
                    .read()
                    .replace("{DB_NAME}", "ras_DataMart_Analytics_Cloud")
                    .replace("{TABLE_NAME}", staging_table_name)
                    .replace("{PARTITION_ID}", str(partition_id))
                    .replace(
                        "{NUMBER_OF_PARTITIONS}",
                        str(task_info["NUMBER_OF_PARTITIONS"]),
                    )
                    .replace(
                        "{PARTITION_COLUMN}",
                        task_info["PARTITION_COLUMN_SS"],
                    )
                )

                # I'll execute the transfers in 'append' mode for each partition
                transfer_task_id = (
                    f"transfer-{task_info['task_id']}-p{partition_id}-to-prod-ss"
                )
                transfer_table_to_prod = GKEStartPodOperator(
                    task_id=transfer_task_id,
                    name=transfer_task_id,
                    image=get_full_image_name("bq-to-ss", GCR_REGISTRY),
                    secrets=[secret_db_username, secret_db_password],
                    arguments=["python3", "main.py", "load-from-bq-to-ss"],
                    resources=task_info["RESOURCES"]
                    if "RESOURCES" in task_info.keys()
                    else k8s.V1ResourceRequirements(requests={"cpu": "2000m", "memory": "2Gi"}),
                    task_group=partition_task_group,
                    pool=ss_import_pool_name,
                    execution_timeout=task_info[
                        "transfer_execution_timeout"
                    ]
                    if "transfer_execution_timeout"
                    in task_info.keys()
                    else timedelta(minutes=60),
                    # Same priority as other RAMBO Push to prod tasks
                    priority_weight=task_info["PRIORITY_WEIGHT"]
                    if "PRIORITY_WEIGHT" in task_info.keys()
                    else 14,
                    env_vars={
                        "GCP_PROJECT": DATA_PROJECT_NAME,
                        "BQ_DATASET": DIM_FM_RR_SOURCE_DATASET,
                        "BQ_TABLE": f"{task_info['table_name']}__{TABLE_SUFFIX}_p{partition_id}",
                        "DB_SERVER": GETL01_SQL_SERVER,
                        "DB_NAME": "ras_DataMart_Analytics_Cloud",
                        "TABLE_NAME": staging_table_name,
                        "WRITE_MODE": "append",
                        "DO_QA_ROW_COUNT": "False",  # We'll skip the QA row count because it is only one of the partitions
                        "EXECUTE_QUERY_BEFORE": delete_before_insert_query,
                        "GCS_BUCKET_NAME": BUCKET_NAME,
                        "CHUNKSIZE": task_info["CHUNKSIZE"]
                        if "CHUNKSIZE" in task_info.keys()
                        else "100000",
                        "COLUMN_MAPPING": task_info["column_mapping"],
                        "PARSE_DTYPES": "False",
                        "PARALLEL_PROCESSES": task_info[
                            "PARALLEL_PROCESSES"
                        ]
                        if "PARALLEL_PROCESSES" in task_info.keys()
                        else "2",
                    },
                    dag=dag,
                )
                create_stg_table >> transfer_table_to_prod >> recreate_stg_table_indexes
                rambo_tasks_dict[transfer_task_id] = transfer_table_to_prod

            
            if "end_transfer_task" in task_info.keys():
                recreate_stg_table_indexes >> task_info["end_transfer_task"]
            else:
                recreate_stg_table_indexes >> [
                    update_fmrr_base_record,
                    update_fmrr_base_record_error,
            ]

        

# Looping again to set the dependencies
for task_info in fmrr_bench_tasks:
    # Using the task object to set the dependencies
    if "task_dependencies" in task_info.keys():
        for dependency in task_info["task_dependencies"]:
            rambo_tasks_dict[dependency] >> rambo_tasks_dict[task_info['task_id']]

    # All FMRR Bench Carry Forward tasks without dependencies (end leafs of Customer Segmentation)
    # must run before the flag 'finished_fmrr_bench_growth' run
    is_end_leaf = True
    for another_task in fmrr_bench_tasks:
        if "task_dependencies" in another_task.keys():
            for dependency in another_task["task_dependencies"]:
                if dependency == task_info["task_id"]:
                    is_end_leaf = False
    if is_end_leaf:
        (
            rambo_tasks_dict[task_info["task_id"]]
            >> rambo_tasks_dict[task_info["end_task"]]
        )

# This finished_rambo_datamart only takes into account the Bigquery Part, as the next dag can trigger.
# However, the transfers will continue to run and will trigger the 'update_record' tasks.
[
    benchmark_growth_complete,
    finished_aggs,
    fmrr_bench_growth_and_utils_complete,
    populate_drpt_groups,
    populate_dim_month_enhanced,
] >> finished_rambo_datamart

trigger_rambo_benchmark = TriggerDagRunOperator(
    task_id="trigger-rambo-benchmark",
    trigger_dag_id=f"rambo-benchmark-v{MAJOR_VERSION}.{MINOR_VERSION}",
    execution_date="{{ execution_date }}",
    conf={"rambo_pk": "{{ dag_run.conf['rambo_pk'] }}", "table_suffix": TABLE_SUFFIX},
    dag=dag,
)
finished_rambo_datamart >> trigger_rambo_benchmark

command = """gcloud logging write rambo_pipeline '{ "type":"RAMBO_PIPELINE/RAMBO_DATAMART", "version":"1.0.0", """
command += """ "payload":{ "event_group":"NOTIFY_PAGER_DUTY", "event":"ERROR", "event_type": "END", "additional_info":"", "duration_sec": null, "message":"Error found in some tasks of the dag", "dag_execution_date":"""
command += '"' + "{{ ts }}" + '"' + "} }'"
command += (
    f""" --payload-type=json --project="{PROJECT_NAME}" --severity=ERROR || true"""
)
log_error = BashOperator(
    task_id="log-error", bash_command=command, trigger_rule="one_failed", dag=dag
)
[finished_rambo_datamart, update_fmrr_base_record, trigger_rambo_benchmark, update_cus_seg_record] >> log_error

update_rambo_record_when_clear = GKEStartPodOperator(
    task_id="update-rambo-record-when-clear",
    name="update-rambo-record-when-clear",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    trigger_rule="one_success",
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "rambo",
        "STATUS_VALUE": "RUNNING",
        "PK": "{{ dag_run.conf['rambo_pk'] }}",
        "BATCH": TABLE_SUFFIX,
    },
    dag=dag,
)

# update_rambo_record_when_clear has a trigger rule of 'ONE SUCCESS', so it will trigger as soon as any 'start task' finishes,
# but it will also trigger if anyone does a clear downstream in any task (as it also depends on finished_rambo_datamart).
[check_for_pipeline_dependencies, log_error] >> update_rambo_record_when_clear
update_fmrr_base_record_when_clear >> update_rambo_record_when_clear

update_if_etl_failed = GKEStartPodOperator(
    task_id="failure-update",
    name="failure-update",
    secrets=[secret_db_username, secret_db_password],
    image=get_full_image_name("rambo-ssis-dependency-table", GCR_REGISTRY),
    do_xcom_push=True,
    arguments=["python3", "main.py", "update-control-table"],
    execution_timeout=timedelta(minutes=5),
    retry_delay=timedelta(seconds=10),
    env_vars={
        "TABLE_NAME": "benchmark_etl_run",
        "DB_SERVER": GETL01_SQL_SERVER,
        "DB_NAME": "ras_DataMart_Computation",
        "PIPELINE": "rambo",
        "STATUS_VALUE": "FAILED",
        "PK": "{{ dag_run.conf['rambo_pk'] }}",
        "BATCH": TABLE_SUFFIX,
    },
    dag=dag,
)
log_error >> update_if_etl_failed

# This dummy will be an 'end leaf' of the dag, which will fail the dag if there is any error.
fail_dag_if_error = DummyOperator(task_id="fail-dag-if-error", dag=dag)
[
    finished_rambo_datamart,
    update_fmrr_base_record,
    trigger_rambo_benchmark,
] >> fail_dag_if_error
