DECLARE modified_months ARRAY<DATE>;
IF EXISTS(
  SELECT
    1
  FROM
    `{PROJECT_ID}.ml_for_rouse{DATASET_ENV_SUFFIX}.__TABLES__`
  WHERE
    table_id = 'ml_for_rouse'
) THEN
  SET modified_months = (
    SELECT
      ARRAY_AGG(DISTINCT DATE_TRUNC(published_date, MONTH))
    FROM
        `{PROJECT_ID}.ml_for_rouse_stage{DATASET_ENV_SUFFIX}.ml_for_rouse_{VERSION_ID}`
  );
  ALTER TABLE
    `{PROJECT_ID}.ml_for_rouse{DATASET_ENV_SUFFIX}.ml_for_rouse`
  ADD COLUMN IF NOT EXISTS flv_confidence_score FLOAT64,
  ADD COLUMN IF NOT EXISTS fmv_confidence_score FLOAT64,
  ADD COLUMN IF NOT EXISTS fmv_schedule_percentage BIGNUMERIC,
  ADD COLUMN IF NOT EXISTS flv_schedule_percentage BIGNUMERIC,
  ADD COLUMN IF NOT EXISTS blended_factor FLOAT64,
  ADD COLUMN IF NOT EXISTS flv_from_blending FLOAT64,
  ADD COLUMN IF NOT EXISTS fmv_from_blending FLOAT64
  ;
  DELETE FROM
    `{PROJECT_ID}.ml_for_rouse{DATASET_ENV_SUFFIX}.ml_for_rouse`
  WHERE
    DATE_TRUNC(published_date, MONTH) IN UNNEST(modified_months)
  ;
  INSERT INTO
    `{PROJECT_ID}.ml_for_rouse{DATASET_ENV_SUFFIX}.ml_for_rouse`
  ( -- Order of columns should respect output of bq_transformation_query.sql
    valuation_equipment_type_id,
    published_date,
    category_id,
    category_name,
    subcategory_id,
    subcategory_name,
    make_id,
    make_name,
    model_id,
    model_name,
    year,
    country,
    cost,
    meter_avg,
    meter_units,
    flv,
    fmv,
    flv_from_ml4r,
    fmv_from_ml4r,
    model_version,
    flv_confidence_score,
    fmv_confidence_score,
    fmv_schedule_percentage,
    flv_schedule_percentage,
    blended_factor,
    flv_from_blending,
    fmv_from_blending,
    created_date,
    modified_date,
    _version_id
  )
  SELECT
    *
  FROM
    `{PROJECT_ID}.ml_for_rouse_stage{DATASET_ENV_SUFFIX}.ml_for_rouse_{VERSION_ID}`
  ;
ELSE
  CREATE SCHEMA IF NOT EXISTS
    `{PROJECT_ID}.ml_for_rouse{DATASET_ENV_SUFFIX}`;
  CREATE TABLE
    `{PROJECT_ID}.ml_for_rouse{DATASET_ENV_SUFFIX}.ml_for_rouse`
  PARTITION BY
    DATE_TRUNC(published_date, MONTH)
  CLUSTER BY
    country
  AS
  SELECT
    *
  FROM
    `{PROJECT_ID}.ml_for_rouse_stage{DATASET_ENV_SUFFIX}.ml_for_rouse_{VERSION_ID}`
  ;
END IF
;
