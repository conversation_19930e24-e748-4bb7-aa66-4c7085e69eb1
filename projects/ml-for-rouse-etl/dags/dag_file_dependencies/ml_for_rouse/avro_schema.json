{
    "type": "record",
    "name": "Root",
    "fields": [
        {"name": "category_id", "type": ["null", "int"]},
        {"name": "subcategory_id", "type": ["null", "int"]},
        {"name": "model_id", "type": ["null", "int"]},
        {"name": "make_id", "type": ["null", "int"]},
        {
            "name": "auc_end_time",
            "type": ["null", {"type": "long", "logicalType": "timestamp-micros"}],
        },
        {"name": "category", "type": ["null", "string"]},
        {"name": "type", "type": ["null", "string"]},
        {"name": "make", "type": ["null", "string"]},
        {"name": "model", "type": ["null", "string"]},
        {"name": "mfg_year", "type": ["null", "long"]},
        {"name": "item_country", "type": ["null", "string"]},
        {"name": "abcost", "type": ["null", "double"]},
        {"name": "meter_reading", "type": ["null", "double"]},
        {"name": "meter_units", "type": ["null", "string"]},
        {"name": "continent", "type": ["null", "string"]},
        {"name": "subcontinent", "type": ["null", "string"]},
        {"name": "conversion_rate", "type": ["null", "double"]},
        {"name": "auc_end_date", "type": ["null", {"type": "int", "logicalType": "date"}]},
        {"name": "days_since_auc", "type": ["null", "long"]},
        {"name": "auc_end_year", "type": ["null", "int"]},
        {"name": "auc_end_month", "type": ["null", "int"]},
        {"name": "auc_end_quarter", "type": ["null", "int"]},
        {"name": "auc_year", "type": ["null", "long"]},
        {"name": "item_age_yrs", "type": ["null", "long"]},
        {"name": "env", "type": ["null", "string"]},
        {"name": "flv", "type": ["null", "double"]},
        {"name": "fmv", "type": ["null", "double"]},
        {"name": "flv_orig", "type": ["null", "double"]},
        {"name": "fmv_orig", "type": ["null", "double"]},
        {"name": "year_diff", "type": ["null", "long"]},
        {"name": "flv_baseline", "type": ["null", "double"]},
        {"name": "fmv_baseline", "type": ["null", "double"]},
        {"name": "min_channel_diff", "type": ["null", "double"]},
        {"name": "flv_ti", "type": ["null", "double"]},
        {"name": "fmv_ti", "type": ["null", "double"]},
        {"name": "channel_flv_changed", "type": ["null", "boolean"]},
        {"name": "channel_fmv_changed", "type": ["null", "boolean"]},
        {"name": "year_adjust_flv", "type": ["null", "double"]},
        {"name": "year_adjust_fmv", "type": ["null", "double"]},
        {"name": "year_flv_changed", "type": ["null", "boolean"]},
        {"name": "year_fmv_changed", "type": ["null", "boolean"]},
        {"name": "flv_confidence_score", "type": ["null", "double"]},
        {"name": "fmv_confidence_score", "type": ["null", "double"]},
        {"name": "fmv_schedule_percentage", "type": ["null", "double"]},
        {"name": "flv_schedule_percentage", "type": ["null", "double"]},
        {"name": "blended_factor", "type": ["null", "double"]},
        {"name": "flv_weighted_average", "type": ["null", "double"]},
        {"name": "fmv_weighted_average", "type": ["null", "double"]},
    ],
}