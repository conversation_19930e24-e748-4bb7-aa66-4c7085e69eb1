DECLARE
  max_book_id INT64 DEFAULT (
  SELECT
    MAX(AppraisalBookIssueID)
  FROM
    `{PROJECT_ID}.ss_export_ras_sas{DATASET_ENV_SUFFIX}.dbo_AppraisalBookIssue`) ;

CREATE SCHEMA IF NOT EXISTS
  `{PROJECT_ID}.ml_for_rouse_stage{DATASET_ENV_SUFFIX}`;
DROP TABLE IF EXISTS
  `{PROJECT_ID}.ml_for_rouse_stage{DATASET_ENV_SUFFIX}.ml_for_rouse_{VERSION_ID}`;
CREATE TABLE IF NOT EXISTS
  `{PROJECT_ID}.ml_for_rouse_stage{DATASET_ENV_SUFFIX}.ml_for_rouse_{VERSION_ID}`
OPTIONS(expiration_timestamp=TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL {BQ_EXPIRATION_DAYS} DAY))
AS
WITH
  schedule_data AS (
  SELECT
    --abiet.AppraisalBookIssueID appraisal_book_issue_id,
    ec.category_id,
    ec.subcategory_id,
    ec.make_id,
    ec.model_id,
    esi_fmv.ModelYear model_year,
    esi_fmv.CostPercent fmv_schedule_percentage,
    esi_flv.CostPercent flv_schedule_percentage
  FROM
    `{PROJECT_ID}.ss_export_ras_sas{DATASET_ENV_SUFFIX}.dbo_EquipmentType` et
  INNER JOIN
    `{PROJECT_ID}.equipment_classification{DATASET_ENV_SUFFIX}.equipment_classification` ec
  ON
    et.CatSubCatID = ec.subcategory_id
    AND et.MakeModelID = ec.model_id
  INNER JOIN
    `{PROJECT_ID}.ss_export_ras_sas{DATASET_ENV_SUFFIX}.dbo_AppraisalBookIssueEquipmentType` abiet
  ON
    abiet.EquipmentTypeID = et.EquipmentTypeID
    AND abiet.AppraisalBookIssueID = max_book_id
  INNER JOIN
    `{PROJECT_ID}.ss_export_ras_sas{DATASET_ENV_SUFFIX}.dbo_AppraisalBookIssue` abi
  ON
    abi.AppraisalBookIssueID = abiet.AppraisalBookIssueID
  INNER JOIN
    `{PROJECT_ID}.ss_export_ras_sas{DATASET_ENV_SUFFIX}.dbo_EffectiveScheduleItem` esi_fmv
  ON
    et.EffectiveFMVScheduleID = esi_fmv.EffectiveScheduleID
  INNER JOIN
    `{PROJECT_ID}.ss_export_ras_sas{DATASET_ENV_SUFFIX}.dbo_EffectiveScheduleItem` esi_flv
  ON
    et.EffectiveFLVScheduleID = esi_flv.EffectiveScheduleID
    AND esi_fmv.ModelYear = esi_flv.ModelYear
  WHERE
    ec.category NOT IN ( 'FirstCostValued',
      'Miscellaneous',
      'Not Appraised' )
    AND ec.subcategory NOT IN ( 'FirstCostValued',
      'Miscellaneous' )
    AND ec.make NOT IN ( 'Miscellaneous',
      'Not Attributed' )
    AND ec.model NOT IN ( 'Miscellaneous',
      'Not Attributed' )
  ),
  rouse_taxonomy AS (
  SELECT
    ROW_NUMBER() OVER (PARTITION BY a.item_country ORDER BY a.category_id, a.subcategory_id, a.make_id, a.model_id) valuation_equipment_type_id,
    a.auc_end_date published_date,
    a.category_id category_id,
    a.category category_name,
    a.subcategory_id subcategory_id,
    a.type subcategory_name,
    a.make_id make_id,
    a.make make_name,
    a.model_id model_id,
    a.model model_name,
    a.item_country country,
    a.mfg_year year,
    ROUND(CAST(CAST(a.abcost AS STRING) AS BIGNUMERIC),4) cost,
    CAST(CAST(a.meter_reading AS STRING) AS BIGNUMERIC) meter_avg,
    a.meter_units,
    ROUND(CAST(CAST(a.flv AS STRING) AS BIGNUMERIC), 18) flv,
    ROUND(CAST(CAST(a.fmv AS STRING) AS BIGNUMERIC), 18) fmv,
    ROUND(CAST(CAST(a.flv_orig AS STRING) AS BIGNUMERIC), 18) flv_from_ml4r,
    ROUND(CAST(CAST(a.fmv_orig AS STRING) AS BIGNUMERIC), 18) fmv_from_ml4r,
    a.env,
    a.flv_confidence_score,
    a.fmv_confidence_score,
    b.fmv_schedule_percentage,
    b.flv_schedule_percentage,
    a.blended_factor,
    a.flv_weighted_average flv_from_blending,
    a.fmv_weighted_average fmv_from_blending
  FROM
    `{PROJECT_ID}.ml_for_rouse_version{DATASET_ENV_SUFFIX}.ml_for_rouse_{VERSION_ID}` a
  LEFT JOIN
    schedule_data b
  ON
    a.category_id = b.category_id
    AND a.subcategory_id = b.subcategory_id
    AND a.make_id = b.make_id
    AND a.model_id = b.model_id
    AND a.mfg_year = b.model_year
  )
  SELECT
    --id
    valuation_equipment_type_id,
    published_date,
    category_id,
    category_name,
    subcategory_id,
    subcategory_name,
    make_id,
    make_name,
    model_id,
    model_name,
    year,
    country,
    cost,
    meter_avg,
    meter_units,
    flv,
    fmv,
    flv_from_ml4r,
    fmv_from_ml4r,
    env model_version,
    flv_confidence_score,
    fmv_confidence_score,
    fmv_schedule_percentage,
    flv_schedule_percentage,
    blended_factor,
    flv_from_blending,
    fmv_from_blending,
    CURRENT_TIMESTAMP() created_date,
    CURRENT_TIMESTAMP() modified_date,
    '{VERSION_ID}' _version_id
  FROM
    rouse_taxonomy
  ;
