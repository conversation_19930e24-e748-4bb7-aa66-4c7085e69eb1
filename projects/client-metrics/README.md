### Client Metrics

This pipeline aggregates client metrics accross Fleet Manager, Rental Insights and Salesforce (for “upcoming” and “potential” client) in BigQuery table.

Produced data will be user for reporting dashboard for auditing where RB is growing internationally and plan accordingly for performance support of our various infrastructure worldwide.

[Link to dashboard](https://lookerstudio.google.com/reporting/314d7f60-f0af-4779-ae4b-8af6d24f12a8/page/tEnnC)

Responsible Engineer: <PERSON><PERSON><PERSON> <<EMAIL>>

---

### Business Impact

_In the event that failure of this pipeline might negatively impact our clients you should notify any internal or
interested parties at Rouse or Ritchie Bros that would be responsible for customer support. It is important that the
appropriate internal resources at our company are brought up-to-date on the issue and its business impacts before our
clients might be impacted by the issue so that we can be pro-active in our client support._

On delay or failure of this pipeline the following clients and business functions may be impacted:

* Dashboard reporting can present stale data

On delay or failure of this pipeline the following dependent pipelines may be impacted:

* None

---

#### Pipeline Triggers

This pipeline is triggered by schedule and it runs 8:30 AM PST.

#### Pipeline Diagram

![Pipeline diagram](docs/client_metrics_simple.svg)

---

#### Detailed Pipeline Diagram

![Detailed pipeline diagram](docs/client_metrics_detailed.svg)


[Editable version of this Diagram](https://drive.google.com/file/d/1qdnQecr7MTl0taTmHLh70hl8fYmZHmdr/view?usp=sharing)

---

#### Sources


| Source     | Database/Project                                | Table                                                                                                    |
|------------|-------------------------------------------------|----------------------------------------------------------------------------------------------------------|
| SQL Server | salesglobal01-prod.ras_Messages_Global          | dbo.LoginHistory                                                                                         |
| BigQuery   | sales-data-prod-2b1264.ras_identity             | identity_user, identity_user_to_role, identity_client, identity_role_to_permission, identity_application |
| BigQuery   | sales-data-prod-2b1264.rfm_equipment            | equipment_all                                                                                            |
| BigQuery   | sales-data-prod-2b1264.sales_rfm_user_config    | fleet_users, user_logins                                                                                 |
| BigQuery   | analytics-data-prod-1e04f6.rental_metrics_aggs  | salesforce_svc_opportunity, salesforce_svc_account                                                       |
| BigQuery   | analytics-data-prod-1e04f6.rfm_extractions      | current_utilization_by_equipment                                                                         |
| BigQuery   | analytics-data-prod-1e04f6.rdo                  | dim_client_branch, dim_client                                                                            |

---

#### Quality Assurance Checks 

The following quality assurance checks are in place for client_metrics version table in BigQuery:

    * Checks if the columns has any nulls: `application_name, client_id, activity_status`
    * Checks if any not null `country` column has more then 3 characters, meaning it should have a mapping implemented in upstream tables
    * Checkf if have any duplicated data for keys: `application_name, client_id`
        * in case this is found, upstream tables (`fleet_manager_client_metrics, rental_insights_client_metrics, salesforce_client_metrics`) join logics
        * `client_id` can be ignored in sale cases for `salesforce` source, since not all clients has a valid id
    * Check if there is any client with more than country.

---

#### Troubleshooting

If quality checks fail, the most likely cause is that data we some join issues and duplicated some records

