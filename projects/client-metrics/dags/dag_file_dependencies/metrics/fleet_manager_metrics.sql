CREATE OR REPLACE TABLE `{SALES_PROJECT_ID}.{DATASET_VERSION_ID}.fleet_manager_client_metrics_{VERSION_ID}` AS
WITH
assets AS (
  SELECT
    * except(rn)
  FROM (
    SELECT
      e.client_id AS client_id_rouse_sales,
      fc.fleet_customer_name,
      e.branch_country AS country,
      SUM(COUNT(1)) OVER (PARTITION BY e.client_id) AS asset_count,
      ROW_NUMBER() OVER (PARTITION BY e.client_id ORDER BY COUNT(1) DESC) AS rn
    FROM `{SALES_PROJECT_ID}.rfm_equipment{BQ_DATASET_ENV}.equipment_all` e
    JOIN `{SALES_PROJECT_ID}.fleet_manager{BQ_DATASET_ENV}.fleet_customers` fc
      ON fc.fleet_customer_id = e.fleet_customer_id
     AND COALESCE(fc.fleet_customer_market_segment, '') not in ('test_client', 'demo_client')
    GROUP BY ALL
  ) as t
  WHERE t.rn = 1
)
, user_logins AS (
  SELECT
     fu.client_id_rouse_sales
    ,MAX(ul.created_date) AS last_login_date
    ,COUNT(1) as login_count_year
    ,SUM(CASE
      WHEN ul.created_date >= TIMESTAMP_TRUNC(TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY), DAY, "UTC")
      THEN 1
      ELSE 0
    END) AS login_count_30_day
    ,SUM(CASE
      WHEN ul.created_date >= TIMESTAMP_TRUNC(TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 180 DAY), DAY, "UTC")
      THEN 1
      ELSE 0
    END) AS login_count_180_day
  FROM `{SALES_PROJECT_ID}.sales_rfm_user_config{BQ_DATASET_ENV}.user_logins` AS ul
  JOIN `{SALES_PROJECT_ID}.sales_rfm_user_config{BQ_DATASET_ENV}.fleet_users` AS fu
    ON fu.identity_user_id = ul.identity_user_id
  WHERE LOWER(ul.user_name) NOT LIKE 'test.%'
    AND ul.created_date >= TIMESTAMP_TRUNC(TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 365 DAY), DAY, "UTC")
  GROUP BY ALL
)
SELECT
   CONCAT('sales_client_id|', a.client_id_rouse_sales) AS client_id
  ,a.fleet_customer_name client_name
  ,a.country
  ,COALESCE(a.asset_count, 0) AS asset_count
  ,ul.last_login_date AS last_login_date
  ,ul.login_count_30_day AS user_count_30_day
  ,ul.login_count_180_day AS user_count_180_day
  ,ul.login_count_year AS user_count_year
  ,CASE
    WHEN ul.login_count_year > 0 AND a.asset_count > 0 THEN 'Active'
    ELSE 'Inactive'
  END AS activity_status
  ,{VERSION_ID} AS _version
FROM assets a
LEFT JOIN user_logins ul
  ON a.client_id_rouse_sales = ul.client_id_rouse_sales
;
