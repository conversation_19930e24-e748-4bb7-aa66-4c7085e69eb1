CREATE OR REPLACE TABLE `{SALES_PROJECT_ID}.{DATASET_VERSION_ID}.rental_insights_client_metrics_{VERSION_ID}` AS
WITH
ri_assets AS (
  SELECT
    * except(rn)
  FROM (
    SELECT
       e.client_id as client_id_rouse_analytics
      ,CASE addr.country
          WHEN 'US' THEN 'USA'
          WHEN 'AU' THEN 'AUS'
          WHEN 'DE' THEN 'DEU'
          WHEN 'IE' THEN 'IRL'
          WHEN 'JP' THEN 'JPN'
          WHEN 'UK' THEN 'GBR'
          ELSE addr.country
       END AS country
      ,SUM(COUNT(1)) OVER (PARTITION BY e.client_id) AS asset_count
      ,ROW_NUMBER() OVER (PARTITION BY e.client_id ORDER BY COUNT(1) DESC) AS rn
    FROM `{ANALYTICS_PROJECT_ID}.rfm_extractions{BQ_DATASET_ENV}.current_utilization_by_equipment` e
    JOIN `{ANALYTICS_PROJECT_ID}.rdo{BQ_DATASET_RDO_ENV}.dim_client_branch` addr
      ON addr.client_branch_id = e.client_branch_id
    WHERE
      e.client_id > 0 -- ignore test clients
      AND COALESCE(e.is_sold, False) = False
    GROUP BY ALL
  ) as t
  WHERE t.rn = 1
)
, assets_client_id_formatting AS (
  SELECT
    COALESCE(
      IF(c.sales_client_id IS NOT NULL, CONCAT('sales_client_id|', c.sales_client_id), NULL),
      IF(a.client_id_rouse_analytics IS NOT NULL, CONCAT('analytics_client_id|', a.client_id_rouse_analytics), NULL)
    ) AS client_id
    ,c.client_long_name
    ,country
    ,SUM(asset_count) AS asset_count
  FROM ri_assets a
  JOIN `{ANALYTICS_PROJECT_ID}.rdo{BQ_DATASET_RDO_ENV}.dim_client` as c
    ON c.client_id = a.client_id_rouse_analytics
  GROUP BY ALL
)
, assets AS (
  SELECT
    * except(rn)
  FROM (
    SELECT
       client_id
      ,client_long_name
      ,country
      ,SUM(asset_count) OVER (PARTITION BY client_id) AS asset_count
      ,ROW_NUMBER() OVER (PARTITION BY client_id ORDER BY asset_count DESC) AS rn
    FROM assets_client_id_formatting
  ) AS t
  WHERE t.rn = 1
)
, user_logins as (
  SELECT
     COALESCE(
      IF(c.sales_client_id IS NOT NULL, CONCAT('sales_client_id|', c.sales_client_id), NULL),
      IF(t.client_id_rouse_analytics IS NOT NULL, CONCAT('analytics_client_id|', t.client_id_rouse_analytics), NULL)
    ) AS client_id
    ,MAX(last_login_date) AS last_login_date
    ,COUNT(1) as login_count_year
    ,SUM(CASE
      WHEN last_login_date >= TIMESTAMP_TRUNC(TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY), DAY, "UTC")
      THEN 1
      ELSE 0
    END) AS login_count_30_day
    ,SUM(CASE
      WHEN last_login_date >= TIMESTAMP_TRUNC(TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 180 DAY), DAY, "UTC")
      THEN 1
      ELSE 0
    END) AS login_count_180_day
  FROM (
    SELECT
      u.user_id as identity_user_id
      ,c.identifier as client_id_rouse_analytics
      ,ul.last_login_date
      ,ROW_NUMBER() OVER (
        PARTITION BY
          u.user_id
        ORDER BY
          CASE WHEN ac.client_id IS NOT NULL THEN 0 ELSE 1 END, -- prioritize sales clients that exists in RI data
          ul.last_login_date DESC,
          c.identifier
      ) AS rn
    FROM
      `{SALES_PROJECT_ID}.ras_identity{BQ_DATASET_ENV}.identity_user` u
    INNER JOIN
      `{SALES_PROJECT_ID}.client_metrics{BQ_DATASET_ENV}.rdo_last_user_logins` ul
    ON
      ul.identity_user_id = u.user_id
    INNER JOIN
      `{SALES_PROJECT_ID}.ras_identity{BQ_DATASET_ENV}.identity_user_to_role` ur
    ON
      ur.user_id = u.user_id
    INNER JOIN
      `{SALES_PROJECT_ID}.ras_identity{BQ_DATASET_ENV}.identity_client` c
    ON
          c.business_unit_id = ur.business_unit_id
      AND c.client_id = ur.client_id
    INNER JOIN
      `{SALES_PROJECT_ID}.ras_identity{BQ_DATASET_ENV}.identity_role_to_permission` rp
    ON
          rp.business_unit_id = ur.business_unit_id
      AND rp.client_id = ur.client_id
      AND rp.role_id = ur.role_id
    INNER JOIN
      `{SALES_PROJECT_ID}.ras_identity{BQ_DATASET_ENV}.identity_application` a
    ON
          a.business_unit_id = ur.business_unit_id
      AND a.application_id = rp.application_id
    LEFT JOIN assets_client_id_formatting ac
    ON
      ac.client_id = CONCAT('sales_client_id|', c.identifier)
    WHERE a.code = 'rdo'
      and lower(coalesce(u.email, u.name)) not like 'test.%'
      AND ul.last_login_date >= TIMESTAMP_TRUNC(TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 365 DAY), DAY, "UTC")
  ) as t
  LEFT JOIN `{ANALYTICS_PROJECT_ID}.rdo{BQ_DATASET_RDO_ENV}.dim_client` as c
    ON c.client_id = t.client_id_rouse_analytics
  WHERE t.rn = 1
  GROUP BY ALL
)
SELECT
   a.client_id
  ,a.client_long_name client_name
  ,a.country
  ,COALESCE(a.asset_count, 0) AS asset_count
  ,ul.last_login_date AS last_login_date
  ,ul.login_count_30_day AS user_count_30_day
  ,ul.login_count_180_day AS user_count_180_day
  ,ul.login_count_year AS user_count_year
  ,CASE
    WHEN ul.login_count_year > 0 AND a.asset_count > 0 THEN 'Active'
    ELSE 'Inactive'
  END AS activity_status
  ,{VERSION_ID} AS _version
FROM assets a
LEFT JOIN user_logins ul
  ON a.client_id = ul.client_id
;