CREATE OR REPLACE TABLE `{SALES_PROJECT_ID}.{DATASET_VERSION_ID}.client_metrics_{VERSION_ID}` AS
WITH
all_metrics AS (
  SELECT
    'Fleet Manager' AS application_name
    ,client_id
    ,client_name
    ,country
    ,activity_status
    ,asset_count
    ,last_login_date
    ,user_count_30_day
    ,user_count_180_day
    ,user_count_year
    ,null AS record_type_id
    ,null AS close_date
    ,null AS rental_insights_stage
    ,null AS sales_stage
    ,activity_status AS new_activity_status
    ,null AS last_modified_date
    ,{VERSION_ID} AS _version
  FROM `{SALES_PROJECT_ID}.{DATASET_VERSION_ID}.fleet_manager_client_metrics_{VERSION_ID}`
  UNION ALL
  SELECT
    'Rental Insights' AS application_name
    ,client_id
    ,client_name
    ,country
    ,activity_status
    ,asset_count
    ,last_login_date
    ,user_count_30_day
    ,user_count_180_day
    ,user_count_year
    ,null AS record_type_id
    ,null AS close_date
    ,null AS rental_insights_stage
    ,null AS sales_stage
    ,activity_status AS new_activity_status
    ,null AS last_modified_date
    ,{VERSION_ID} AS _version
  FROM `{SALES_PROJECT_ID}.{DATASET_VERSION_ID}.rental_insights_client_metrics_{VERSION_ID}`
  UNION ALL
  SELECT
    'Salesforce' AS application_name
    ,client_id
    ,client_name
    ,country
    ,activity_status
    ,null AS asset_count
    ,null AS last_login_date
    ,null AS user_count_30_day
    ,null AS user_count_180_day
    ,null AS user_count_year
    ,record_type_id
    ,close_date
    ,rental_insights_stage
    ,sales_stage
    ,new_activity_status
    ,last_modified_date
    ,{VERSION_ID} AS _version
  FROM `{SALES_PROJECT_ID}.{DATASET_VERSION_ID}.salesforce_client_metrics_{VERSION_ID}`
)
, countries AS (
  SELECT
     client_id
    ,MAX(country) AS country
  FROM all_metrics
  GROUP BY ALL
)
SELECT
  m.* except(country)
  , COALESCE(m.country, c.country) AS country
FROM all_metrics m
LEFT JOIN countries c
  ON c.client_id = m.client_id