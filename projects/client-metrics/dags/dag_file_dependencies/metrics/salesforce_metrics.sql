CREATE OR REPLACE TABLE `{SALES_PROJECT_ID}.{DATASET_VERSION_ID}.salesforce_client_metrics_{VERSION_ID}` AS
WITH
  ri_stage_progress_level AS (
  -- account object stages
    SELECT
      7 level,
      stage,
    FROM
      UNNEST(['In Discussions','In Discussions - Stalled']) AS stage
    UNION ALL
    SELECT
      6 level,
      stage,
    FROM
      UNNEST(['Reviewing Contract','Reviewing Contract - Stalled']) AS stage
    UNION ALL
    SELECT
      5 level,
      stage,
    FROM
      UNNEST(['Integrating','Integrating - Stalled','Trial Ready','Integrating - Data Flowing']) AS stage
    UNION ALL
    SELECT
      4 level,
      stage,
    FROM
      UNNEST(['Benchmark Trial','Extended Trial']) AS stage
    UNION ALL
    SELECT
      3 level,
      stage,
    FROM
      UNNEST(['Unassigned']) AS stage
    UNION ALL
    SELECT
      2 level,
      stage,
    FROM
      UNNEST(['No Order','Ordering Ad Hoc','Special Order']) AS stage
    UNION ALL
    SELECT
      1 level,
      stage,
    FROM
      UNNEST(['Active','Ordering Quarterly','Ordering Monthly - Markets','Ordering Monthly - Districts','Ordering Monthly - Markets and Districts','Ordering Monthly - Markets, Districts and Regions','Ordering Monthly - CAT Territories','Ordering Monthly - CAT Territories & Districts','Ordering Monthly - Full']) AS stage
  ), sales_stage_progress_level AS (
    -- account object stages, can also be used for Equipment Insights
    SELECT
      7 level,
      stage,
    FROM
      UNNEST(['In Discussions','In Discussions - Stalled']) AS stage
    UNION ALL
    SELECT
      6 level,
      stage,
    FROM
      UNNEST(['Reviewing Contract']) AS stage
    UNION ALL
    SELECT
      5 level,
      stage,
    FROM
      UNNEST(['Integrating','Integrating - Data Flowing','Integrating - Stalled','Trial Ready']) AS stage
    UNION ALL
    SELECT
      4 level,
      stage,
    FROM
      UNNEST(['Trial Period','Extended Trial']) AS stage
    UNION ALL
    SELECT
      3 level,
      stage,
    FROM
      UNNEST(['Other']) AS stage
    UNION ALL
    SELECT
      2 level,
      stage,
    FROM
      UNNEST(['Paused']) AS stage
    UNION ALL
    SELECT
      1 level,
      stage,
    FROM
      UNNEST(['Active']) AS stage
  ), opportunity_stage_progress_level AS (
    -- opportunity object stage
    SELECT
      7 level,
      stage,
    FROM
      UNNEST(['Proposal/Price Quote','In Discussions','In Discussions - Deferred']) AS stage
    UNION ALL
    SELECT
      6 level,
      stage,
    FROM
      UNNEST(['Business/Technical Requirements Review','Negotiation/Review','On Hold','Reviewing Contract']) AS stage
    UNION ALL
    SELECT
      5 level,
      stage,
    FROM
      UNNEST(['Integrating']) AS stage
    UNION ALL
    SELECT
      4 level,
      stage,
    FROM
      UNNEST(['Trial Period']) AS stage
    UNION ALL
    SELECT
      1 level,
      stage,
    FROM
      UNNEST(['Closed Won']) AS stage
  ), salesforce_accounts as (
  SELECT
     A.ID
    ,A.Sales_Client_ID__c
    ,A.Name
    ,O.StageName
    ,O.LastModifiedDate
    ,CASE
      WHEN BillingCountry = 'United States'
      THEN
        ShippingCountry
      ELSE
        BillingCountry
    END AS ReferenceCountry
    ,O.RecordTypeId
    ,O.CloseDate
    ,CASE
      WHEN A.Rental_Insights_Stage__c IN ('No Order','Ordering Ad Hoc','Ordering Quarterly','Ordering Monthly - Markets','Ordering Monthly - Districts','Ordering Monthly - Markets and Districts','Ordering Monthly - Markets, Districts and Regions','Ordering Monthly - CAT Territories','Ordering Monthly - CAT Territories & Districts','Ordering Monthly - Full','Special Order') THEN 'Active'
      ELSE
        A.Rental_Insights_Stage__c
     END Rental_Insights_Stage__c
    ,A.Sales_Stage__c
  FROM
    `{ANALYTICS_PROJECT_ID}.rental_metrics_aggs{BQ_DATASET_ENV}.salesforce_svc_account` A
  LEFT JOIN
    `{ANALYTICS_PROJECT_ID}.rental_metrics_aggs{BQ_DATASET_ENV}.salesforce_svc_opportunity` O
  ON
    O.AccountId = A.Id
    AND O.RecordTypeId = '012WR000008EWRkYAO'
  WHERE
    O.StageName NOT IN ('Closed Lost', 'In Discussions - Decline', 'Rouse Decline')
    OR O.AccountId IS NULL
), filtered_accounts AS (
  SELECT
    *
    ,CASE
      WHEN COALESCE(rispl.level, 100) <= COALESCE(sspl.level, 100) THEN COALESCE(rispl.stage, A.StageName)
      WHEN COALESCE(sspl.level, 100) <= COALESCE(rispl.level, 100) THEN COALESCE(sspl.stage, A.StageName)
      ELSE
        -- opportunity stage name
        A.StageName
     END status
     ,CASE
      WHEN COALESCE(rispl.level, 100) <= COALESCE(sspl.level, 100) THEN IF(rispl.stage IS NOT NULL, 'Rental Insights', 'Opportunity')
      WHEN COALESCE(sspl.level, 100) <= COALESCE(rispl.level, 100) THEN IF(sspl.stage IS NOT NULL, 'Sales', 'Opportunity')
      ELSE
        -- opportunity stage name
        'Opportunity'
     END status_source
  FROM
    salesforce_accounts A
  LEFT JOIN
    ri_stage_progress_level rispl
  ON
    A.Rental_Insights_Stage__c = rispl.stage
  LEFT JOIN
    sales_stage_progress_level sspl
  ON
    A.Sales_Stage__c = sspl.stage    
), salesforce_clients AS (
  SELECT DISTINCT
      CONCAT('sales_client_id|', COALESCE(A.Sales_Client_ID__c, A.status, A.StageName)) AS client_id
     ,A.Name AS client_name
     ,CASE A.ReferenceCountry
          WHEN 'United States' THEN 'USA'
          WHEN 'Canada' THEN 'CAN'
          WHEN 'Belgium' THEN 'BEL'
          WHEN 'United Kingdom' THEN 'GBR'
          WHEN 'China' THEN 'CHN'
          WHEN 'Germany' THEN 'DEU'
          WHEN 'Netherlands' THEN 'NLD'
          WHEN 'Brazil' THEN 'BRA'
          WHEN 'France' THEN 'FRA'
          WHEN 'Mexico' THEN 'MEX'
          WHEN 'Sweden' THEN 'SWE'
          WHEN 'Spain' THEN 'ESP'
          WHEN 'Chile' THEN 'CHL'
          WHEN 'Ecuador' THEN 'ECU'
          WHEN 'Ireland' THEN 'IRL'
          WHEN 'Australia' THEN 'AUS'
          WHEN 'Poland' THEN 'POL'
          WHEN 'Denmark' THEN 'DNK'
          WHEN 'Italy' THEN 'ITA'
          WHEN 'Dominican Republic' THEN 'DOM'
          WHEN 'South Africa' THEN 'ZAF'
          WHEN 'Israel' THEN 'ISR'
          WHEN 'Switzerland' THEN 'CHE'
          WHEN 'Trinidad and Tobago' THEN 'TTO'
          WHEN 'Peru' THEN 'PER'
          WHEN 'Japan' THEN 'JPN'
          WHEN 'Finland' THEN 'FIN'
          WHEN 'Honduras' THEN 'HND'
          WHEN 'Lithuania' THEN 'LTU'
          WHEN 'Romania' THEN 'ROU'
          WHEN 'Norway' THEN 'NOR'
          WHEN 'Argentina' THEN 'ARG'
          WHEN 'Czech Republic' THEN 'CZE'
          WHEN 'Portugal' THEN 'PRT'
          WHEN 'Colombia' THEN 'COL'
          WHEN 'Cayman Islands' THEN 'CYM'
          WHEN 'Luxembourg' THEN 'LUX'
          WHEN 'New Zealand' THEN 'NZL'
          WHEN 'Austria' THEN 'AUT'
          WHEN 'India' THEN 'IND'
          WHEN 'Qatar' THEN 'QAT'
          WHEN 'Puerto Rico' THEN 'PRI'
          WHEN 'Mozambique' THEN 'MOZ'
          WHEN 'U.S. Virgin Islands' THEN 'VIR'
          WHEN 'Panama' THEN 'PAN'
          WHEN 'United Arab Emirates' THEN 'ARE'
          ELSE A.ReferenceCountry
     END AS country
     ,A.status AS new_activity_status
     ,A.RecordTypeId AS record_type_id
     ,A.CloseDate AS close_date
     ,A.StageName AS activity_status
     ,A.Rental_Insights_Stage__c AS rental_insights_stage
     ,A.Sales_Stage__c AS sales_stage
     ,A.LastModifiedDate AS last_modified_date
     ,ROW_NUMBER() OVER (PARTITION BY A.Name ORDER BY A.LastModifiedDate DESC) AS rn
     ,{VERSION_ID} AS _version
  FROM
    filtered_accounts A
  WHERE
    NOT (
      status_source = 'Rental Insights'
      AND A.status IN ('No Contact','No Recent Contact','Attempted Contact')
    ) AND NOT (
      status_source = 'Sales'
      AND A.status IN ('Prior Contact, Not Active','No Contact, High Priority','No Contact, Low Priority','Meeting Scheduled','Attempted Contact')
    ) AND NOT (
      status_source = 'Opportunity'
      AND A.status IN ('Prospecting')
    )
)
SELECT
  * except(rn)
FROM
  salesforce_clients
WHERE
  rn = 1
;