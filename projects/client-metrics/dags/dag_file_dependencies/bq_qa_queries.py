# BigQuery QA

bq_qa_dupe_client_query = """
WITH
  get_dupes AS (
  SELECT
    application_name,
    client_id,
    client_name,
    COUNT(*)
  FROM
    `{SALES_PROJECT_ID}.{DATASET_VERSION_ID}.client_metrics_{VERSION_ID}`
  WHERE
    SAFE_CAST(
      REPLACE(REPLACE(client_id, 'analytics_client_id|', ''), 'sales_client_id|', '')
    AS INTEGER) IS NOT NULL
  GROUP BY
    ALL
  HAVING
    COUNT(*)>1
  )
SELECT
  COUNT(*) count
FROM
  get_dupes
"""

bq_qa_null_count_query = """
SELECT
  COUNT(*) count
FROM
  `{SALES_PROJECT_ID}.{DATASET_VERSION_ID}.client_metrics_{VERSION_ID}`
WHERE
   application_name IS NULL
OR client_id IS NULL
OR new_activity_status IS NULL
"""

bq_qa_country_code_query = """
SELECT
  COUNT(*) count
FROM
  `{SALES_PROJECT_ID}.{DATASET_VERSION_ID}.client_metrics_{VERSION_ID}`
WHERE
   country IS NOT NULL
AND LENGTH(country) > 3
"""

bq_qa_country_count_query = """
WITH
  client_count AS (
    SELECT
      application_name,
      client_name,
      client_id,
      COUNT(*) count
    FROM
      `{SALES_PROJECT_ID}.{DATASET_VERSION_ID}.client_metrics_{VERSION_ID}`
    GROUP BY
      ALL
  )
SELECT
  COUNT(*) count
FROM
  client_count
WHERE
  count > 1
"""

# Fail if true
bq_qa_queries = [
    (
        bq_qa_dupe_client_query,
        "gt",
        0,
        "Table contains duplicate for application_name and client_id",
    ),
    (bq_qa_null_count_query, "gt", 0, "Table contains a column with NULL"),
    (
        bq_qa_country_code_query,
        "gt",
        0,
        "Table contains country with wrong length ( > 3 )",
    ),
    (
        bq_qa_country_count_query,
        "gt",
        0,
        "Table contains different countries for a single client",
    ),
]
