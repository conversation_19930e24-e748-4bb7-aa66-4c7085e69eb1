import datetime as dt
import importlib
import json
import os
from pathlib import Path

import pendulum
from airflow import DAG
from airflow.kubernetes.secret import Secret
from airflow.providers.google.cloud.operators.kubernetes_engine import (
    GKEStartPodOperator,
)
from airflow.utils.task_group import TaskGroup

from shared_libs.bigquery_dataset import refresh_bq_dataset_list, CreateBigQueryDataset
from shared_libs.default_args import (
    get_default_args,
)
from shared_libs.image_versions import get_full_image_name, get_gcr_registry
from shared_libs.pagerduty_callback import dag_fail_pagerduty_alert
from shared_libs.slack_callback import task_fail_slack_alert

environment = os.environ.get("COMPOSER_ENVIRONMENT", "dev")
gcr_registry = get_gcr_registry(environment)


version_id = "{{ execution_date.strftime('%Y%m%d%H%M%S') }}"
local_tz = pendulum.timezone("America/Los_Angeles")

namespace = service_account_name = "client-metrics"

PWD = Path(os.path.dirname(os.path.realpath(__file__)))
SQL_FOLDER = PWD / "dag_file_dependencies"

start_date = dt.datetime(2025, 3, 18, tzinfo=local_tz)
schedule_interval = "30 8 * * *"

if environment != "prod":
    salesglobal01_instance_conn_string = (
        "services-dev-525bf6:us-central1:salesglobal01-dev"
    )
    db_secret_name = "de-client-metrics-ras-messages-global-dev"
    sales_project_id = "sales-data-dev-9ffb6c"
    analytics_project_id = "analytics-data-dev-62c5a2"
    bq_dataset_env = "_dev"
    bq_dataset_rdo_env = "_stage"
    path_suffix = "-dev"

else:  # PRODUCTION
    salesglobal01_instance_conn_string = (
        "services-prod-e6fffc:us-central1:salesglobal01-prod"
    )
    db_secret_name = "de-client-metrics-ras-messages-global-prod"
    sales_project_id = "sales-data-prod-2b1264"
    analytics_project_id = "analytics-data-prod-1e04f6"
    bq_dataset_env = ""
    bq_dataset_rdo_env = ""
    path_suffix = ""

bq_dataset_version_id = f"client_metrics_version{bq_dataset_env}"
bq_dataset_view_id = f"client_metrics{bq_dataset_env}"

bq_qa_queries = importlib.import_module(
    ("client-metrics." if environment != "local" else "")
    + "dag_file_dependencies.bq_qa_queries"
).bq_qa_queries

# default dag args
custom_args = {
    "owner": "<EMAIL>",
    "start_date": start_date,
    "retries": 3,
    "retry_delay": dt.timedelta(minutes=2),
    "namespace": namespace,
    "service_account_name": service_account_name,
    "image": get_full_image_name("client-metrics", gcr_registry),
    "on_failure_callback": task_fail_slack_alert,
}
default_args = get_default_args(custom_args)

secret_db_username = Secret(
    deploy_type="env",
    deploy_target="WIN_USER",
    secret=db_secret_name,
    key="username",
)

secret_db_password = Secret(
    deploy_type="env",
    deploy_target="WIN_PASSWORD",
    secret=db_secret_name,
    key="password",
)


def read_file(dependency_path, folder=SQL_FOLDER):
    with open(folder / dependency_path) as f:
        local_file = f.read()
    return local_file


DAG_ID = "client-metrics"
with DAG(
    DAG_ID,
    default_args=default_args,
    description="Create customer metrics BigQuery datasets to feed reports",
    schedule_interval=schedule_interval,
    catchup=False,
    dagrun_timeout=dt.timedelta(minutes=90),
    max_active_runs=1,
    on_failure_callback=dag_fail_pagerduty_alert(
        "sales_client_metrics_pagerduty_api_key"
    ),
) as dag:
    variable_dict = {
        "SALES_PROJECT_ID": sales_project_id,
        "ANALYTICS_PROJECT_ID": analytics_project_id,
        "VERSION_ID": version_id,
        "DATASET_VERSION_ID": bq_dataset_version_id,
        "DATASET_VIEW_ID": bq_dataset_view_id,
        "BQ_DATASET_ENV": bq_dataset_env,
        "BQ_DATASET_RDO_ENV": bq_dataset_rdo_env,
    }

    create_refresh_datasets = refresh_bq_dataset_list(
        dag,
        sales_project_id,
        datasets=[
            CreateBigQueryDataset(
                dataset=bq_dataset_view_id,
                project_id=sales_project_id,
                table_expiration_days=None,
            ),
            CreateBigQueryDataset(
                dataset=bq_dataset_version_id,
                project_id=sales_project_id,
            ),
        ],
    )

    with TaskGroup(group_id="extract-rdo-user-logins") as rdo_user_logins:
        table_name = "rdo_last_user_logins"
        gcs_path = (
            f"gs://{sales_project_id}-raw-files/pipeline/{DAG_ID}/{version_id}/"
            f"rdo-user-logins/avro/export.mssql.{table_name}.{version_id}.avro"
        )

        qa_columns = {"num_rows": [], "sum": ["identity_user_id"]}

        mssql_to_gcs = GKEStartPodOperator(
            task_id="mssql-to-gcs",
            name="mssql-to-gcs",
            image=get_full_image_name("ss-to-gcs", gcr_registry),
            do_xcom_push=True,
            arguments=["python3", "main.py", "load-from-query"],
            secrets=[secret_db_username, secret_db_password],
            env_vars={
                "DB_SERVER": "127.0.0.1",
                "USE_PROXY_SQL_INSTANCE": salesglobal01_instance_conn_string,
                "DB_NAME": "ras_Messages_Global",
                "QUERY": read_file("extraction/extract_rdo_last_user_login.sql").format(
                    **variable_dict
                ),
                "GCP_PROJECT": sales_project_id,
                "GCS_PATH": gcs_path,
                "WRITE_LOGS": "False",
                "QA_COLUMNS": str(qa_columns),
                "ENGINE": "sqlalchemy",
                "AVRO_SCHEMA_STRING": json.dumps(
                    {
                        "type": "record",
                        "name": "Root",
                        "fields": [
                            {"name": "identity_user_id", "type": ["null", "int"]},
                            {
                                "name": "last_login_date",
                                "type": [
                                    "null",
                                    {"type": "long", "logicalType": "timestamp-millis"},
                                ],
                            },
                            {"name": "_version", "type": ["null", "long"]},
                        ],
                    }
                ),
            },
            dag=dag,
        )

        gcs_to_bq = GKEStartPodOperator(
            task_id="gcs-to-bq",
            name="gcs-to-bq",
            dag=dag,
            image=get_full_image_name("avro-to-bq", gcr_registry),
            arguments=["python3", "main.py", "load-in-bq"],
            do_xcom_push=True,
            execution_timeout=dt.timedelta(minutes=30),
            env_vars={
                "SOURCE_URIS": gcs_path,
                "WRITE_DISPOSITION": "WRITE_TRUNCATE",
                "GCP_PROJECT_ID": sales_project_id,
                "DESTINATION": f"{bq_dataset_version_id}.{table_name}_{version_id}",
                "VIEW_DESTINATION": f"{sales_project_id}.{bq_dataset_view_id}.{table_name}",
            },
        )

        mssql_to_bq_qa = GKEStartPodOperator(
            task_id="qa",
            name="qa",
            dag=dag,
            image=get_full_image_name("ss-to-bq-qa", gcr_registry),
            execution_timeout=dt.timedelta(minutes=30),
            arguments=["python3", "-m", "src.cli", "qa"],
            env_vars={
                "PROJECT_ID": sales_project_id,
                "DATASET_ID": bq_dataset_version_id,
                "VERSION": version_id,
                "TABLE_NAME": f"{table_name}_{version_id}",
                "QA_COLUMNS": str(qa_columns),
                "METRICS": (
                    "{{ task_instance.xcom_pull("
                    f"task_ids='{mssql_to_gcs.task_id}'"
                    ")['metrics'] }}"
                ),
            },
        )

        mssql_to_gcs >> gcs_to_bq >> mssql_to_bq_qa

    with TaskGroup(group_id="create-system-metrics") as system_metrics:
        for table in [
            "fleet_manager_metrics",
            "rental_insights_metrics",
            "salesforce_metrics",
        ]:
            GKEStartPodOperator(
                task_id=f"{table}-version",
                name=f"{table}-version",
                dag=dag,
                execution_timeout=dt.timedelta(minutes=10),
                image=get_full_image_name("execute-bigquery", gcr_registry),
                arguments=["python3", "main.py", "execute-bigquery"],
                env_vars={
                    "QUERY": read_file(f"metrics/{table}.sql").format(**variable_dict),
                    "GCP_PROJECT_ID": sales_project_id,
                },
            )

    metrics_version = GKEStartPodOperator(
        task_id="create-metrics-version",
        name="create-metrics-version",
        dag=dag,
        execution_timeout=dt.timedelta(minutes=10),
        image=get_full_image_name("execute-bigquery", gcr_registry),
        arguments=["python3", "main.py", "execute-bigquery"],
        env_vars={
            "QUERY": read_file("metrics/clients_metrics.sql").format(**variable_dict),
            "GCP_PROJECT_ID": sales_project_id,
        },
    )

    qa = GKEStartPodOperator(
        task_id="qa",
        name="qa",
        dag=dag,
        image=get_full_image_name("bq-qa", gcr_registry),
        arguments=["python3", "-m", "src.cli", "qa"],
        env_vars={
            "PROJECT_ID": sales_project_id,
            "BQ_QA_QUERIES": str(bq_qa_queries).format(**variable_dict),
        },
    )

    publish_version = GKEStartPodOperator(
        task_id="publish-metrics-version",
        name="publish-metrics-version",
        dag=dag,
        execution_timeout=dt.timedelta(minutes=10),
        image=get_full_image_name("execute-bigquery", gcr_registry),
        arguments=["python3", "main.py", "execute-bigquery"],
        env_vars={
            "QUERY": """
                CREATE OR REPLACE VIEW `{SALES_PROJECT_ID}.{DATASET_VIEW_ID}.client_metrics` AS
                SELECT * FROM `{SALES_PROJECT_ID}.{DATASET_VERSION_ID}.client_metrics_{VERSION_ID}`;

                CREATE OR REPLACE VIEW `{SALES_PROJECT_ID}.{DATASET_VIEW_ID}.fleet_manager_client_metrics` AS
                SELECT * FROM `{SALES_PROJECT_ID}.{DATASET_VERSION_ID}.fleet_manager_client_metrics_{VERSION_ID}`;

                CREATE OR REPLACE VIEW `{SALES_PROJECT_ID}.{DATASET_VIEW_ID}.rental_insights_client_metrics` AS
                SELECT * FROM `{SALES_PROJECT_ID}.{DATASET_VERSION_ID}.rental_insights_client_metrics_{VERSION_ID}`;

                CREATE OR REPLACE VIEW `{SALES_PROJECT_ID}.{DATASET_VIEW_ID}.salesforce_client_metrics` AS
                SELECT * FROM `{SALES_PROJECT_ID}.{DATASET_VERSION_ID}.salesforce_client_metrics_{VERSION_ID}`;
            """.format(
                **variable_dict
            ),
            "GCP_PROJECT_ID": sales_project_id,
        },
    )

    (
        create_refresh_datasets
        >> rdo_user_logins
        >> system_metrics
        >> metrics_version
        >> qa
        >> publish_version
    )
