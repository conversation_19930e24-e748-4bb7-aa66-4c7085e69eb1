client_mappings = {
    "1upuk": {
        "analytics_client_code": "1upuk",
        "analytics_client_id": 573,
        "appraisals_client_id": 2409,
        "client_code": "1upuk",
        "client_version": 1,
        "fleet_customer_id": 10450,
        "ims_conversion_status": "ims",
        "name": "1 Up Access",
        "sales_client_code": "1upuk",
        "sales_client_id": 10450,
        "sales_pipeline_type": "nightly",
    },
    "a1id": {
        "analytics_client_code": "a1id",
        "analytics_client_id": 526,
        "appraisals_client_id": 2411,
        "client_code": "a1id",
        "client_version": 1,
        "fleet_customer_id": 10452,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "A1 Rental Idaho",
        "sales_client_code": "a1id",
        "sales_client_id": 10452,
        "sales_pipeline_type": "nightly",
    },
    "a1mo": {
        "analytics_client_code": "a1mo",
        "analytics_client_id": 386,
        "appraisals_client_id": 2412,
        "client_code": "a1mo",
        "client_version": 1,
        "fleet_customer_id": 10453,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "A1 Tool \u0026 Equipment",
        "sales_client_code": "a1mo",
        "sales_client_id": 10453,
        "sales_pipeline_type": "nightly",
    },
    "a1ri": {
        "analytics_client_code": "a1ri",
        "analytics_client_id": 459,
        "appraisals_client_id": 2410,
        "client_code": "a1ri",
        "client_version": 1,
        "fleet_customer_id": 10451,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "A1 Rent It",
        "sales_client_code": "a1ri",
        "sales_client_id": 10451,
        "sales_pipeline_type": "nightly",
    },
    "aaa": {
        "analytics_client_code": "aaa",
        "analytics_client_id": 273,
        "appraisals_client_id": 2414,
        "client_code": "aaa",
        "client_version": 1,
        "fleet_customer_id": 10455,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "AAA Rent-All",
        "sales_client_code": "aaa",
        "sales_client_id": 10455,
        "sales_pipeline_type": "nightly",
    },
    "aabg": {
        "analytics_client_code": "aabg",
        "analytics_client_id": 558,
        "appraisals_client_id": 2301,
        "client_code": "aabg",
        "client_version": 1,
        "fleet_customer_id": 10401,
        "ims_conversion_status": "ims",
        "name": "Aabergs Equipment",
        "sales_client_code": "aabg",
        "sales_client_id": 10401,
        "sales_pipeline_type": "nightly",
    },
    "aact": {
        "analytics_client_code": "aact",
        "analytics_client_id": 473,
        "appraisals_client_id": 1854,
        "client_code": "aact",
        "client_version": 1,
        "fleet_customer_id": 10215,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "AAction Rents",
        "sales_client_code": "aact",
        "sales_client_id": 10215,
        "sales_pipeline_type": "nightly",
    },
    "aada": {
        "analytics_client_code": "aada",
        "analytics_client_id": 369,
        "appraisals_client_id": 2413,
        "client_code": "aada",
        "client_version": 1,
        "fleet_customer_id": 10454,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "AA Rental of Dallas",
        "sales_client_code": "aada",
        "sales_client_id": 10454,
        "sales_pipeline_type": "nightly",
    },
    "aae": {
        "analytics_client_code": "aae",
        "analytics_client_id": 58,
        "client_code": "aae",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Aerial Access",
        "sales_pipeline_type": "vod",
    },
    "aai": {
        "analytics_client_code": "aai",
        "analytics_client_id": 206,
        "client_code": "aai",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Atlantic Aerials Inc",
        "sales_pipeline_type": "vod",
    },
    "abc": {
        "analytics_client_code": "abc",
        "analytics_client_id": -2,
        "client_code": "abc",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "ABC Rentals",
        "sales_pipeline_type": "vod",
    },
    "abce": {
        "analytics_client_code": "abce",
        "analytics_client_id": 350,
        "appraisals_client_id": 2416,
        "client_code": "abce",
        "client_version": 1,
        "fleet_customer_id": 10457,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "ABC Equipment Rental",
        "sales_client_code": "abce",
        "sales_client_id": 10457,
        "sales_pipeline_type": "nightly",
    },
    "abcm": {
        "analytics_client_code": "abcm",
        "analytics_client_id": 458,
        "client_code": "abcm",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "ABC Equipment Rental Maryland",
        "sales_pipeline_type": "vod",
    },
    "abl": {
        "analytics_client_code": "abl",
        "analytics_client_id": 71,
        "appraisals_client_id": 324,
        "client_code": "abl",
        "client_version": 1,
        "fleet_customer_id": 192,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Able Equipment",
        "sales_client_code": "abl",
        "sales_client_id": 71,
        "sales_pipeline_type": "nightly",
    },
    "ablt": {
        "analytics_client_code": "ablt",
        "analytics_client_id": 497,
        "appraisals_client_id": 2417,
        "client_code": "ablt",
        "client_version": 1,
        "fleet_customer_id": 10458,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Able Tool \u0026 Equipment",
        "sales_client_code": "ablt",
        "sales_client_id": 10458,
        "sales_pipeline_type": "nightly",
    },
    "abtr": {
        "analytics_client_code": "abtr",
        "analytics_client_id": 315,
        "appraisals_client_id": 2415,
        "client_code": "abtr",
        "client_version": 1,
        "fleet_customer_id": 10456,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "AB Tool Rentals",
        "sales_client_code": "abtr",
        "sales_client_id": 10456,
        "sales_pipeline_type": "nightly",
    },
    "acca": {
        "analytics_client_code": "acca",
        "analytics_client_id": 503,
        "client_code": "acca",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Action Rental Calgary",
        "sales_pipeline_type": "vod",
    },
    "achr": {
        "analytics_client_code": "achr",
        "analytics_client_id": 409,
        "appraisals_client_id": 1946,
        "client_code": "achr",
        "client_version": 1,
        "fleet_customer_id": 10261,
        "ims_conversion_status": "ims",
        "name": "All Choice Rentals",
        "sales_client_code": "achr",
        "sales_client_id": 10261,
        "sales_pipeline_type": "nightly",
    },
    "acmb": {
        "analytics_client_code": "acmb",
        "analytics_client_id": 222,
        "client_code": "acmb",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Acme Ops Bobcat",
        "sales_pipeline_type": "vod",
    },
    "acme": {
        "client_code": "acme",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "ACME Lift Co.",
        "sales_pipeline_type": "vod",
    },
    "acml": {
        "analytics_client_code": "acml",
        "analytics_client_id": 529,
        "appraisals_client_id": 982,
        "client_code": "acml",
        "client_version": 1,
        "fleet_customer_id": 10459,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Acme Lift",
        "sales_client_code": "acml",
        "sales_client_id": 10459,
        "sales_pipeline_type": "nightly",
    },
    "acmr": {
        "analytics_client_code": "acmr",
        "analytics_client_id": 442,
        "appraisals_client_id": 1472,
        "client_code": "acmr",
        "client_version": 1,
        "fleet_customer_id": 10092,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Acme Rents",
        "sales_client_code": "acmr",
        "sales_client_id": 10092,
        "sales_pipeline_type": "nightly",
    },
    "acrs": {
        "analytics_client_code": "acrs",
        "analytics_client_id": 354,
        "appraisals_client_id": 2418,
        "client_code": "acrs",
        "client_version": 1,
        "fleet_customer_id": 10460,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Action Rentals and Sales",
        "sales_client_code": "acrs",
        "sales_client_id": 10460,
        "sales_pipeline_type": "nightly",
    },
    "acsr": {
        "analytics_client_code": "acsr",
        "analytics_client_id": 534,
        "appraisals_client_id": 1596,
        "client_code": "acsr",
        "client_version": 1,
        "fleet_customer_id": 10157,
        "ims_conversion_status": "ims",
        "name": "Acquired Clients for Sales Reporting",
        "sales_client_code": "acsr",
        "sales_client_id": 10157,
        "sales_pipeline_type": "nightly",
    },
    "act": {
        "analytics_client_code": "act",
        "analytics_client_id": 141,
        "appraisals_client_id": 851,
        "client_code": "act",
        "client_version": 1,
        "fleet_customer_id": 92,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Action Rental (Pennsylvania)",
        "primary_oem": "-",
        "sales_client_code": "act",
        "sales_client_id": 141,
        "sales_pipeline_type": "nightly",
    },
    "actr": {
        "analytics_client_code": "actr",
        "analytics_client_id": 498,
        "appraisals_client_id": 1089,
        "client_code": "actr",
        "client_version": 1,
        "fleet_customer_id": 10064,
        "ims_conversion_status": "ims",
        "market_segment": "Financial",
        "name": "ACT Research",
        "sales_client_code": "actr",
        "sales_client_id": 990,
        "sales_pipeline_type": "nightly",
    },
    "adm": {
        "analytics_client_code": "adm",
        "analytics_client_id": 74,
        "appraisals_client_id": 2419,
        "client_code": "adm",
        "client_version": 1,
        "fleet_customer_id": 10461,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Admar",
        "sales_client_code": "adm",
        "sales_client_id": 10461,
        "sales_pipeline_type": "nightly",
    },
    "advt": {
        "analytics_client_code": "advt",
        "analytics_client_id": -500,
        "client_code": "advt",
        "client_version": 1,
        "fleet_customer_id": 117,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "AdvanceTrak Equipment",
        "primary_oem": "-",
        "sales_client_code": "advt",
        "sales_client_id": 921,
        "sales_pipeline_type": "vod",
    },
    "advuk": {
        "analytics_client_code": "advuk",
        "analytics_client_id": 563,
        "appraisals_client_id": 2330,
        "client_code": "advuk",
        "client_version": 1,
        "fleet_customer_id": 10409,
        "ims_conversion_status": "ims",
        "name": "Advanced Access Platforms",
        "sales_client_code": "advuk",
        "sales_client_id": 10409,
        "sales_pipeline_type": "nightly",
    },
    "aee": {
        "analytics_client_code": "aee",
        "analytics_client_id": 248,
        "client_code": "aee",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "AE Equipment",
        "sales_pipeline_type": "vod",
    },
    "aer": {
        "analytics_client_code": "aer",
        "appraisals_client_id": 1947,
        "client_code": "aer",
        "client_version": 1,
        "fleet_customer_id": 10262,
        "ims_conversion_status": "ims",
        "name": "AER Rents (UK)",
        "sales_client_code": "aer",
        "sales_client_id": 10262,
        "sales_pipeline_type": "nightly",
    },
    "afiuk": {
        "analytics_client_code": "afiuk",
        "analytics_client_id": 278,
        "appraisals_client_id": 2340,
        "client_code": "afiuk",
        "client_version": 1,
        "fleet_customer_id": 10413,
        "ims_conversion_status": "ims",
        "name": "AFIUplift",
        "sales_client_code": "afiuk",
        "sales_client_id": 10413,
        "sales_pipeline_type": "nightly",
    },
    "aflau": {
        "analytics_client_code": "aflau",
        "analytics_client_id": 584,
        "client_code": "aflau",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "All Lift Forklifts",
        "sales_pipeline_type": "vod",
    },
    "agg": {
        "analytics_client_code": "agg",
        "analytics_client_id": 121,
        "client_code": "agg",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Aggreko",
        "sales_pipeline_type": "vod",
    },
    "aham": {
        "analytics_client_code": "aham",
        "analytics_client_id": 337,
        "appraisals_client_id": 2427,
        "client_code": "aham",
        "client_version": 1,
        "fleet_customer_id": 10469,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Allingham",
        "sales_client_code": "aham",
        "sales_client_id": 10469,
        "sales_pipeline_type": "nightly",
    },
    "ainsuk": {
        "analytics_client_code": "ainsuk",
        "analytics_client_id": 568,
        "appraisals_client_id": 2420,
        "client_code": "ainsuk",
        "client_version": 1,
        "fleet_customer_id": 10462,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Ainscough Crane Hire",
        "sales_client_code": "ainsuk",
        "sales_client_id": 10462,
        "sales_pipeline_type": "nightly",
    },
    "air": {
        "analytics_client_code": "air",
        "analytics_client_id": 65,
        "client_code": "air",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Airgas",
        "sales_pipeline_type": "vod",
    },
    "ais": {
        "analytics_client_code": "ais",
        "analytics_client_id": 235,
        "appraisals_client_id": 876,
        "client_code": "ais",
        "client_version": 1,
        "fleet_customer_id": 200,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "AIS Construction Equipment Corp",
        "primary_oem": "John Deere",
        "sales_client_code": "ais",
        "sales_client_id": 235,
        "sales_pipeline_type": "nightly",
    },
    "alac": {
        "analytics_client_code": "alac",
        "analytics_client_id": 642,
        "appraisals_client_id": 2194,
        "client_code": "alac",
        "client_version": 1,
        "fleet_customer_id": 10360,
        "ims_conversion_status": "ims",
        "name": "All Access Rentals",
        "sales_client_code": "alac",
        "sales_client_id": 10360,
        "sales_pipeline_type": "nightly",
    },
    "alb": {
        "analytics_client_code": "alb",
        "analytics_client_id": 24,
        "client_code": "alb",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Alban Cat",
        "sales_pipeline_type": "vod",
    },
    "aleg": {
        "analytics_client_code": "aleg",
        "analytics_client_id": 597,
        "appraisals_client_id": 2425,
        "client_code": "aleg",
        "client_version": 1,
        "fleet_customer_id": 10467,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Allegiance Crane",
        "sales_client_code": "aleg",
        "sales_client_id": 10467,
        "sales_pipeline_type": "nightly",
    },
    "aliduk": {
        "analytics_client_code": "aliduk",
        "analytics_client_id": 729,
        "appraisals_client_id": 2859,
        "client_code": "aliduk",
        "client_version": 1,
        "fleet_customer_id": 10681,
        "ims_conversion_status": "ims",
        "name": "Alide Plant Services Ltd",
        "sales_client_code": "aliduk",
        "sales_client_id": 10681,
        "sales_pipeline_type": "nightly",
    },
    "allau": {
        "analytics_client_code": "allau",
        "analytics_client_id": 268,
        "appraisals_client_id": 2424,
        "client_code": "allau",
        "client_version": 1,
        "fleet_customer_id": 10466,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Allcott Hire",
        "sales_client_code": "allau",
        "sales_client_id": 10466,
        "sales_pipeline_type": "nightly",
    },
    "alll": {
        "analytics_client_code": "alll",
        "analytics_client_id": 508,
        "appraisals_client_id": 1585,
        "client_code": "alll",
        "client_version": 1,
        "fleet_customer_id": 10151,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "All Lift Service Company",
        "sales_client_code": "alll",
        "sales_client_id": 10151,
        "sales_pipeline_type": "nightly",
    },
    "almi": {
        "analytics_client_code": "almi",
        "analytics_client_id": 394,
        "appraisals_client_id": 2426,
        "client_code": "almi",
        "client_version": 1,
        "fleet_customer_id": 10468,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Allied Rent-All",
        "sales_client_code": "almi",
        "sales_client_id": 10468,
        "sales_pipeline_type": "nightly",
    },
    "alpr": {
        "analytics_client_code": "alpr",
        "analytics_client_id": 467,
        "appraisals_client_id": 2429,
        "client_code": "alpr",
        "client_version": 1,
        "fleet_customer_id": 10471,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Alpine Equipment Rentals",
        "sales_client_code": "alpr",
        "sales_client_id": 10471,
        "sales_pipeline_type": "nightly",
    },
    "alr": {
        "client_code": "alr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "market_segment": "OEM",
        "name": "All Roads Equipment - do not use",
        "primary_oem": "Komatsu",
        "sales_pipeline_type": "vod",
    },
    "alro": {
        "analytics_client_code": "alro",
        "analytics_client_id": 456,
        "appraisals_client_id": 1386,
        "client_code": "alro",
        "client_version": 1,
        "fleet_customer_id": 196,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "All Roads Equipment",
        "primary_oem": "Komatsu",
        "sales_client_code": "alro",
        "sales_client_id": 456,
        "sales_pipeline_type": "nightly",
    },
    "als": {
        "analytics_client_code": "als",
        "analytics_client_id": 221,
        "appraisals_client_id": 582,
        "client_code": "als",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Atlantic Lift Systems Inc",
        "sales_pipeline_type": "vod",
    },
    "als2": {
        "analytics_client_code": "als2",
        "analytics_client_id": 530,
        "appraisals_client_id": 1599,
        "client_code": "als2",
        "client_version": 1,
        "fleet_customer_id": 10165,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Atlantic Lift Systems",
        "sales_client_code": "als2",
        "sales_client_id": 10165,
        "sales_pipeline_type": "nightly",
    },
    "alstau": {
        "analytics_client_code": "alstau",
        "analytics_client_id": 648,
        "appraisals_client_id": 2422,
        "client_code": "alstau",
        "client_version": 1,
        "fleet_customer_id": 10464,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "All Star Access Hire",
        "sales_client_code": "alstau",
        "sales_client_id": 10464,
        "sales_pipeline_type": "nightly",
    },
    "alstc": {
        "analytics_client_code": "alstc",
        "analytics_client_id": 698,
        "client_code": "alstc",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "All State Crane \u0026 Rigging",
        "sales_pipeline_type": "vod",
    },
    "alt": {
        "analytics_client_code": "alt",
        "analytics_client_id": 94,
        "appraisals_client_id": 468,
        "client_code": "alt",
        "client_version": 1,
        "fleet_customer_id": 17,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Altorfer Industries",
        "primary_oem": "Caterpillar",
        "sales_client_code": "alt",
        "sales_client_id": 94,
        "sales_pipeline_type": "nightly",
    },
    "alta": {
        "analytics_client_code": "alta",
        "analytics_client_id": 293,
        "appraisals_client_id": 1098,
        "client_code": "alta",
        "client_version": 1,
        "fleet_customer_id": 203,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Alta Equipment",
        "primary_oem": "Volvo",
        "sales_client_code": "alta",
        "sales_client_id": 293,
        "sales_pipeline_type": "nightly",
    },
    "altac": {
        "analytics_client_code": "altac",
        "analytics_client_id": 710,
        "client_code": "altac",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Alta Canada",
        "sales_pipeline_type": "vod",
    },
    "ame": {
        "analytics_client_code": "ame",
        "appraisals_client_id": 403,
        "client_code": "ame",
        "client_version": 1,
        "fleet_customer_id": 10056,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "AMECO",
        "sales_client_code": "ame",
        "sales_client_id": 864,
        "sales_pipeline_type": "nightly",
    },
    "ameco": {
        "analytics_client_code": "ameco",
        "analytics_client_id": 488,
        "client_code": "ameco",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "AMECO",
        "sales_pipeline_type": "vod",
    },
    "amecoc": {
        "analytics_client_code": "amecoc",
        "analytics_client_id": 624,
        "client_code": "amecoc",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "AMECO Canada",
        "sales_pipeline_type": "vod",
    },
    "amer": {
        "analytics_client_code": "amer",
        "analytics_client_id": 333,
        "appraisals_client_id": 2141,
        "client_code": "amer",
        "client_version": 1,
        "fleet_customer_id": 10344,
        "ims_conversion_status": "ims",
        "name": "American Rentals",
        "sales_client_code": "amer",
        "sales_client_id": 10344,
        "sales_pipeline_type": "nightly",
    },
    "amil": {
        "analytics_client_code": "amil",
        "analytics_client_id": 463,
        "appraisals_client_id": 2431,
        "client_code": "amil",
        "client_version": 1,
        "fleet_customer_id": 10473,
        "ims_conversion_status": "ims",
        "name": "American Rental IL",
        "sales_client_code": "amil",
        "sales_client_id": 10473,
        "sales_pipeline_type": "nightly",
    },
    "amos": {
        "analytics_client_code": "amos",
        "analytics_client_id": 290,
        "client_code": "amos",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Amos Metz Rentals",
        "sales_pipeline_type": "vod",
    },
    "andm": {
        "analytics_client_code": "andm",
        "analytics_client_id": 557,
        "appraisals_client_id": 2432,
        "client_code": "andm",
        "client_version": 1,
        "fleet_customer_id": 10474,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Anderson Machinery",
        "sales_client_code": "andm",
        "sales_client_id": 10474,
        "sales_pipeline_type": "nightly",
    },
    "angwa": {
        "analytics_client_code": "angwa",
        "analytics_client_id": 761,
        "appraisals_client_id": 2953,
        "client_code": "angwa",
        "client_version": 1,
        "fleet_customer_id": 10751,
        "ims_conversion_status": "ims",
        "name": "Angeles Rentals",
        "sales_client_code": "angwa",
        "sales_client_id": 10751,
        "sales_pipeline_type": "nightly",
    },
    "aphr": {
        "analytics_client_code": "aphr",
        "analytics_client_id": 357,
        "appraisals_client_id": 2433,
        "client_code": "aphr",
        "client_version": 1,
        "fleet_customer_id": 10475,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Apex Hesperia Rentals",
        "sales_client_code": "aphr",
        "sales_client_id": 10475,
        "sales_pipeline_type": "nightly",
    },
    "api": {
        "analytics_client_code": "api",
        "analytics_client_id": 302,
        "appraisals_client_id": 1774,
        "client_code": "api",
        "client_version": 1,
        "fleet_customer_id": 10181,
        "ims_conversion_status": "ims",
        "name": "APi Supply Lifts",
        "sales_client_code": "api",
        "sales_client_id": 10181,
        "sales_pipeline_type": "nightly",
    },
    "aple": {
        "appraisals_client_id": 1484,
        "client_code": "aple",
        "client_version": 1,
        "fleet_customer_id": 10065,
        "ims_conversion_status": "ims",
        "market_segment": "Residuals",
        "name": "Apple Bank for Savings",
        "sales_client_code": "aple",
        "sales_client_id": 992,
        "sales_pipeline_type": "vod",
    },
    "ar": {
        "analytics_client_code": "ar",
        "analytics_client_id": 6,
        "appraisals_client_id": 859,
        "client_code": "ar",
        "client_version": 1,
        "fleet_customer_id": 166,
        "ims_conversion_status": "ims",
        "market_segment": "National",
        "name": "Ahern Rentals Inc.",
        "sales_client_code": "ar",
        "sales_client_id": 12,
        "sales_pipeline_type": "nightly",
    },
    "ara": {
        "analytics_client_code": "ara",
        "analytics_client_id": 255,
        "appraisals_client_id": 2436,
        "client_code": "ara",
        "client_version": 1,
        "fleet_customer_id": 10478,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Aspen RentAll",
        "sales_client_code": "ara",
        "sales_client_id": 10478,
        "sales_pipeline_type": "nightly",
    },
    "arduk": {
        "analytics_client_code": "arduk",
        "analytics_client_id": 540,
        "appraisals_client_id": 1768,
        "client_code": "arduk",
        "client_version": 1,
        "fleet_customer_id": 10177,
        "ims_conversion_status": "ims",
        "name": "Ardent Hire",
        "sales_client_code": "arduk",
        "sales_client_id": 10177,
        "sales_pipeline_type": "nightly",
    },
    "are": {
        "analytics_client_code": "are",
        "analytics_client_id": 34,
        "client_code": "are",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Area Equip",
        "sales_pipeline_type": "vod",
    },
    "arva": {
        "analytics_client_code": "arva",
        "analytics_client_id": 428,
        "appraisals_client_id": 2435,
        "client_code": "arva",
        "client_version": 1,
        "fleet_customer_id": 10477,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Arvada Rent-Alls",
        "sales_client_code": "arva",
        "sales_client_id": 10477,
        "sales_pipeline_type": "nightly",
    },
    "arvh": {
        "analytics_client_code": "arvh",
        "analytics_client_id": 750,
        "appraisals_client_id": 2907,
        "client_code": "arvh",
        "client_version": 1,
        "fleet_customer_id": 10726,
        "ims_conversion_status": "ims",
        "name": "10726",
        "sales_client_code": "arvh",
        "sales_client_id": 10726,
        "sales_pipeline_type": "nightly",
    },
    "arw": {
        "analytics_client_code": "arw",
        "analytics_client_id": 253,
        "client_code": "arw",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Arrow Lift",
        "sales_pipeline_type": "vod",
    },
    "arw2": {
        "analytics_client_code": "arw2",
        "analytics_client_id": 800,
        "client_code": "arw2",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Arrow Lift Rentals",
        "sales_pipeline_type": "vod",
    },
    "arwr": {
        "analytics_client_code": "arwr",
        "analytics_client_id": 471,
        "appraisals_client_id": 2434,
        "client_code": "arwr",
        "client_version": 1,
        "fleet_customer_id": 10476,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Arrow Rental",
        "sales_client_code": "arwr",
        "sales_client_id": 10476,
        "sales_pipeline_type": "nightly",
    },
    "asc": {
        "analytics_client_code": "asc",
        "analytics_client_id": 119,
        "appraisals_client_id": 636,
        "client_code": "asc",
        "client_version": 1,
        "fleet_customer_id": 70,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Ascendum Construction Equipment",
        "primary_oem": "Volvo",
        "sales_client_code": "asc",
        "sales_client_id": 119,
        "sales_pipeline_type": "nightly",
    },
    "asco": {
        "analytics_client_code": "asco",
        "analytics_client_id": 180,
        "appraisals_client_id": 737,
        "client_code": "asco",
        "client_version": 1,
        "fleet_customer_id": 73,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "ASCO Equipment",
        "primary_oem": "volvo",
        "sales_client_code": "asco",
        "sales_client_id": 180,
        "sales_pipeline_type": "nightly",
    },
    "ase": {
        "analytics_client_code": "ase",
        "analytics_client_id": 197,
        "appraisals_client_id": 2423,
        "client_code": "ase",
        "client_version": 1,
        "fleet_customer_id": 10465,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "All Star Equipment",
        "sales_client_code": "ase",
        "sales_client_id": 10465,
        "sales_pipeline_type": "nightly",
    },
    "ash": {
        "analytics_client_code": "ash",
        "analytics_client_id": 172,
        "appraisals_client_id": 2208,
        "client_code": "ash",
        "client_version": 1,
        "fleet_customer_id": 10366,
        "ims_conversion_status": "ims",
        "name": "Asheville Hwy Rental",
        "sales_client_code": "ash",
        "sales_client_id": 10366,
        "sales_pipeline_type": "nightly",
    },
    "asl": {
        "analytics_client_code": "ascl",
        "analytics_client_id": 576,
        "appraisals_client_id": 1870,
        "client_code": "asl",
        "client_version": 1,
        "fleet_customer_id": 10220,
        "ims_conversion_status": "ims",
        "name": "American Scissor Lift",
        "sales_client_code": "asl",
        "sales_client_id": 10220,
        "sales_pipeline_type": "nightly",
    },
    "asr": {
        "analytics_client_code": "asr",
        "analytics_client_id": 9,
        "client_code": "asr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Allstar Rents",
        "sales_pipeline_type": "vod",
    },
    "asr2": {
        "analytics_client_code": "asr2",
        "analytics_client_id": 612,
        "appraisals_client_id": 2129,
        "client_code": "asr2",
        "client_version": 1,
        "fleet_customer_id": 10337,
        "ims_conversion_status": "ims",
        "name": "All Star Rents",
        "sales_client_code": "asr2",
        "sales_client_id": 10337,
        "sales_pipeline_type": "nightly",
    },
    "atoz": {
        "analytics_client_code": "atoz",
        "analytics_client_id": 252,
        "client_code": "atoz",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "AtoZEquipmentRentalsandSales",
        "sales_pipeline_type": "vod",
    },
    "atrau": {
        "analytics_client_code": "atrau",
        "analytics_client_id": 417,
        "appraisals_client_id": 2428,
        "client_code": "atrau",
        "client_version": 1,
        "fleet_customer_id": 10470,
        "ims_conversion_status": "ims",
        "name": "Alltracks Plant Hire",
        "sales_client_code": "atrau",
        "sales_client_id": 10470,
        "sales_pipeline_type": "nightly",
    },
    "atsr": {
        "analytics_client_code": "atsr",
        "analytics_client_id": 246,
        "appraisals_client_id": 1446,
        "client_code": "atsr",
        "client_version": 1,
        "fleet_customer_id": 10016,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "A Tool Shed Rentals",
        "sales_client_code": "atsr",
        "sales_client_id": 10016,
        "sales_pipeline_type": "nightly",
    },
    "aurr": {
        "analytics_client_code": "aurr",
        "analytics_client_id": 469,
        "appraisals_client_id": 2036,
        "client_code": "aurr",
        "client_version": 1,
        "fleet_customer_id": 10302,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Aurora Rents",
        "sales_client_code": "aurr",
        "sales_client_id": 10302,
        "sales_pipeline_type": "nightly",
    },
    "aznc": {
        "analytics_client_code": "aznc",
        "analytics_client_id": 445,
        "appraisals_client_id": 1777,
        "client_code": "aznc",
        "client_version": 1,
        "fleet_customer_id": 10183,
        "ims_conversion_status": "ims",
        "name": "A to Z Equipment Rentals of North Carolina",
        "sales_client_code": "aznc",
        "sales_client_id": 10183,
        "sales_pipeline_type": "nightly",
    },
    "bacn": {
        "analytics_client_code": "bacn",
        "analytics_client_id": 570,
        "appraisals_client_id": 2028,
        "client_code": "bacn",
        "client_version": 1,
        "fleet_customer_id": 10296,
        "ims_conversion_status": "ims",
        "name": "Bacon Universal",
        "sales_client_code": "bacn",
        "sales_client_id": 10296,
        "sales_pipeline_type": "nightly",
    },
    "badr": {
        "analytics_client_code": "badr",
        "analytics_client_id": 345,
        "appraisals_client_id": 2437,
        "client_code": "badr",
        "client_version": 1,
        "fleet_customer_id": 10479,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Badger Rental Services",
        "sales_client_code": "badr",
        "sales_client_id": 10479,
        "sales_pipeline_type": "nightly",
    },
    "bai": {
        "analytics_client_code": "bail",
        "analytics_client_id": 484,
        "appraisals_client_id": 1445,
        "client_code": "bai",
        "client_version": 1,
        "fleet_customer_id": 10015,
        "ims_conversion_status": "ims",
        "name": "The Bailey Company",
        "sales_client_code": "bai",
        "sales_client_id": 10015,
        "sales_pipeline_type": "nightly",
    },
    "balouk": {
        "analytics_client_code": "balouk",
        "analytics_client_id": 654,
        "appraisals_client_id": 2407,
        "client_code": "balouk",
        "client_version": 1,
        "fleet_customer_id": 10448,
        "ims_conversion_status": "ims",
        "name": "Balloo Hire Centre Limited",
        "sales_client_code": "balouk",
        "sales_client_id": 10448,
        "sales_pipeline_type": "nightly",
    },
    "batm": {
        "analytics_client_code": "batm",
        "analytics_client_id": 117,
        "appraisals_client_id": 2438,
        "client_code": "batm",
        "client_version": 1,
        "fleet_customer_id": 10480,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Battlefield CAT Manitoba",
        "sales_client_code": "batm",
        "sales_client_id": 10480,
        "sales_pipeline_type": "nightly",
    },
    "batn": {
        "analytics_client_code": "batn",
        "analytics_client_id": 118,
        "appraisals_client_id": 2439,
        "client_code": "batn",
        "client_version": 1,
        "fleet_customer_id": 10481,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Battlefield CAT Newfoundland",
        "sales_client_code": "batn",
        "sales_client_id": 10481,
        "sales_pipeline_type": "nightly",
    },
    "bato": {
        "analytics_client_code": "bato",
        "analytics_client_id": 116,
        "appraisals_client_id": 2440,
        "client_code": "bato",
        "client_version": 1,
        "fleet_customer_id": 10482,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Battlefield CAT Ontario",
        "sales_client_code": "bato",
        "sales_client_id": 10482,
        "sales_pipeline_type": "nightly",
    },
    "batq": {
        "analytics_client_code": "batq",
        "analytics_client_id": 254,
        "appraisals_client_id": 2441,
        "client_code": "batq",
        "client_version": 1,
        "fleet_customer_id": 10483,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Battlefield CAT Quebec",
        "sales_client_code": "batq",
        "sales_client_id": 10483,
        "sales_pipeline_type": "nightly",
    },
    "bbef": {
        "analytics_client_code": "bbef",
        "analytics_client_id": 986,
        "client_code": "bbef",
        "client_version": 1,
        "fleet_customer_id": 128,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Bell Bank Equipment Finance",
        "primary_oem": "-",
        "sales_client_code": "bbef",
        "sales_client_id": 986,
        "sales_pipeline_type": "vod",
    },
    "bci": {
        "analytics_client_code": "bci",
        "analytics_client_id": 162,
        "appraisals_client_id": 883,
        "client_code": "bci",
        "client_version": 1,
        "fleet_customer_id": 195,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Berry Companies",
        "primary_oem": "Komatsu",
        "sales_client_code": "bci",
        "sales_client_id": 162,
        "sales_pipeline_type": "nightly",
    },
    "bcon": {
        "analytics_client_code": "bcon",
        "analytics_client_id": 505,
        "appraisals_client_id": 2102,
        "client_code": "bcon",
        "client_version": 1,
        "fleet_customer_id": 10328,
        "ims_conversion_status": "ims",
        "name": "Bercon Rentals",
        "sales_client_code": "bcon",
        "sales_client_id": 10328,
        "sales_pipeline_type": "nightly",
    },
    "bcvt": {
        "analytics_client_code": "bcvt",
        "analytics_client_id": 371,
        "appraisals_client_id": 2475,
        "client_code": "bcvt",
        "client_version": 1,
        "fleet_customer_id": 10517,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "EOC Holdings",
        "sales_client_code": "bcvt",
        "sales_client_id": 10517,
        "sales_pipeline_type": "nightly",
    },
    "bder": {
        "analytics_client_code": "bder",
        "analytics_client_id": 263,
        "appraisals_client_id": 889,
        "client_code": "bder",
        "client_version": 1,
        "fleet_customer_id": 212,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Black Diamond Equipment Rental",
        "sales_client_code": "bder",
        "sales_client_id": 263,
        "sales_pipeline_type": "nightly",
    },
    "bec": {
        "analytics_client_code": "bec",
        "analytics_client_id": 155,
        "client_code": "bec",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Bingham Equipment",
        "sales_pipeline_type": "vod",
    },
    "bee": {
        "analytics_client_code": "bee",
        "analytics_client_id": 472,
        "appraisals_client_id": 2442,
        "client_code": "bee",
        "client_version": 1,
        "fleet_customer_id": 10484,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Bee Equipment Sales",
        "sales_client_code": "bee",
        "sales_client_id": 10484,
        "sales_pipeline_type": "nightly",
    },
    "beer": {
        "analytics_client_code": "beer",
        "analytics_client_id": 336,
        "appraisals_client_id": 1128,
        "client_code": "beer",
        "client_version": 1,
        "fleet_customer_id": 10049,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Blue Eagle Rentals",
        "sales_client_code": "beer",
        "sales_client_id": 336,
        "sales_pipeline_type": "nightly",
    },
    "beps": {
        "analytics_client_code": "beps",
        "analytics_client_id": 408,
        "appraisals_client_id": 1487,
        "client_code": "beps",
        "client_version": 1,
        "fleet_customer_id": 10101,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Buckeye Power Sales",
        "sales_client_code": "beps",
        "sales_client_id": 10101,
        "sales_pipeline_type": "nightly",
    },
    "ber": {
        "analytics_client_code": "ber",
        "analytics_client_id": 96,
        "appraisals_client_id": 2449,
        "client_code": "ber",
        "client_version": 1,
        "fleet_customer_id": 10491,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Broadline Equipment Rentals LTD",
        "sales_client_code": "ber",
        "sales_client_id": 10491,
        "sales_pipeline_type": "nightly",
    },
    "best": {
        "analytics_client_code": "best",
        "analytics_client_id": 209,
        "appraisals_client_id": 825,
        "client_code": "best",
        "client_version": 1,
        "fleet_customer_id": 13,
        "ims_conversion_status": "ims",
        "name": "Best Line Equipment",
        "sales_client_code": "best",
        "sales_client_id": 209,
        "sales_pipeline_type": "nightly",
    },
    "bfl": {
        "analytics_client_code": "bfl",
        "analytics_client_id": 231,
        "client_code": "bfl",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "DJM Equipment",
        "sales_pipeline_type": "vod",
    },
    "bigge": {
        "analytics_client_code": "bigge",
        "analytics_client_id": 455,
        "appraisals_client_id": 2444,
        "client_code": "bigge",
        "client_version": 1,
        "fleet_customer_id": 10486,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Bigge",
        "sales_client_code": "bigge",
        "sales_client_id": 10486,
        "sales_pipeline_type": "nightly",
    },
    "bis": {
        "analytics_client_code": "bis",
        "analytics_client_id": -500,
        "client_code": "bis",
        "client_version": 1,
        "fleet_customer_id": 123,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Black Iron Sales",
        "primary_oem": "-",
        "sales_client_code": "bis",
        "sales_client_id": 980,
        "sales_pipeline_type": "vod",
    },
    "bla": {
        "analytics_client_code": "bla",
        "analytics_client_id": 22,
        "appraisals_client_id": 408,
        "client_code": "bla",
        "client_version": 1,
        "fleet_customer_id": 7,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Blanchard Machinery Co.",
        "primary_oem": "Caterpillar",
        "sales_client_code": "bla",
        "sales_client_id": 22,
        "sales_pipeline_type": "nightly",
    },
    "blc": {
        "analytics_client_code": "blc",
        "analytics_client_id": 46,
        "client_code": "blc",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "BlueLine Rental Canada",
        "sales_pipeline_type": "vod",
    },
    "ble": {
        "analytics_client_code": "ble",
        "analytics_client_id": 93,
        "appraisals_client_id": 845,
        "client_code": "ble",
        "client_version": 1,
        "fleet_customer_id": 211,
        "ims_conversion_status": "ims",
        "name": "Bottom Line Equipment LLC",
        "sales_client_code": "ble",
        "sales_client_id": 106,
        "sales_pipeline_type": "nightly",
    },
    "blr": {
        "analytics_client_code": "blr",
        "analytics_client_id": 10,
        "client_code": "blr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "BlueLine Rental",
        "sales_pipeline_type": "vod",
    },
    "bltz": {
        "analytics_client_code": "bltz",
        "analytics_client_id": -500,
        "client_code": "bltz",
        "client_version": 1,
        "fleet_customer_id": 133,
        "ims_conversion_status": "ims",
        "market_segment": "financial",
        "name": "Sales January 2022 Blitz",
        "primary_oem": "-",
        "sales_client_code": "bltz",
        "sales_client_id": 999,
        "sales_pipeline_type": "vod",
    },
    "bmc": {
        "analytics_client_code": "bmc",
        "analytics_client_id": 169,
        "appraisals_client_id": 823,
        "client_code": "bmc",
        "client_version": 1,
        "fleet_customer_id": 66,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Butler Machinery Company",
        "primary_oem": "caterpillar",
        "sales_client_code": "bmc",
        "sales_client_id": 169,
        "sales_pipeline_type": "nightly",
    },
    "bme": {
        "analytics_client_code": "bme",
        "analytics_client_id": 139,
        "appraisals_client_id": 590,
        "client_code": "bme",
        "client_version": 1,
        "fleet_customer_id": 10040,
        "ims_conversion_status": "ims",
        "name": "B\u0026M Equipment Rental \u0026 Sales",
        "sales_client_code": "bme",
        "sales_client_id": 139,
        "sales_pipeline_type": "nightly",
    },
    "bntt": {
        "analytics_client_code": "bntt",
        "analytics_client_id": 733,
        "appraisals_client_id": 2868,
        "client_code": "bntt",
        "client_version": 1,
        "fleet_customer_id": 10685,
        "ims_conversion_status": "ims",
        "name": "Bennett Equipment \u0026 Supply",
        "sales_client_code": "bntt",
        "sales_client_id": 10685,
        "sales_pipeline_type": "nightly",
    },
    "boco": {
        "analytics_client_code": "boco",
        "analytics_client_id": 620,
        "client_code": "boco",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "BoomCo Rental",
        "sales_pipeline_type": "vod",
    },
    "boon": {
        "analytics_client_code": "boon",
        "analytics_client_id": 378,
        "appraisals_client_id": 2445,
        "client_code": "boon",
        "client_version": 1,
        "fleet_customer_id": 10487,
        "ims_conversion_status": "ims",
        "name": "Boone Rent-All \u0026 Parties Too",
        "sales_client_code": "boon",
        "sales_client_id": 10487,
        "sales_pipeline_type": "nightly",
    },
    "bor": {
        "analytics_client_code": "bor",
        "analytics_client_id": 274,
        "appraisals_client_id": 1442,
        "client_code": "bor",
        "client_version": 1,
        "fleet_customer_id": 10013,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Big Orange Rental",
        "sales_client_code": "bor",
        "sales_client_id": 274,
        "sales_pipeline_type": "nightly",
    },
    "borc": {
        "analytics_client_code": "borc",
        "analytics_client_id": 436,
        "appraisals_client_id": 2446,
        "client_code": "borc",
        "client_version": 1,
        "fleet_customer_id": 10488,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Borsheim Crane Service",
        "sales_client_code": "borc",
        "sales_client_id": 10488,
        "sales_pipeline_type": "nightly",
    },
    "boyd": {
        "analytics_client_code": "boyd",
        "analytics_client_id": 234,
        "appraisals_client_id": 875,
        "client_code": "boyd",
        "client_version": 1,
        "fleet_customer_id": 46,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "CL Boyd Company",
        "primary_oem": "John Deere",
        "sales_client_code": "boyd",
        "sales_client_id": 234,
        "sales_pipeline_type": "nightly",
    },
    "bphuk": {
        "analytics_client_code": "bphuk",
        "analytics_client_id": 717,
        "appraisals_client_id": 2846,
        "client_code": "bphuk",
        "client_version": 1,
        "fleet_customer_id": 10677,
        "ims_conversion_status": "ims",
        "name": "BPH Construction Equipment Ltd",
        "sales_client_code": "bphuk",
        "sales_client_id": 10677,
        "sales_pipeline_type": "nightly",
    },
    "bra": {
        "analytics_client_code": "bra",
        "analytics_client_id": 47,
        "appraisals_client_id": 603,
        "client_code": "bra",
        "client_version": 1,
        "fleet_customer_id": 185,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Bramco, Inc.",
        "primary_oem": "Komatsu",
        "sales_client_code": "bra",
        "sales_client_id": 47,
        "sales_pipeline_type": "nightly",
    },
    "brco": {
        "analytics_client_code": "brco",
        "analytics_client_id": 453,
        "appraisals_client_id": 2443,
        "client_code": "brco",
        "client_version": 1,
        "fleet_customer_id": 10485,
        "ims_conversion_status": "ims",
        "name": "Best Rental",
        "sales_client_code": "brco",
        "sales_client_id": 10485,
        "sales_pipeline_type": "nightly",
    },
    "brd": {
        "analytics_client_code": "brd",
        "analytics_client_id": 75,
        "appraisals_client_id": 612,
        "client_code": "brd",
        "client_version": 1,
        "fleet_customer_id": 161,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Beard Equipment",
        "primary_oem": "john deere",
        "sales_client_code": "brd",
        "sales_client_id": 75,
        "sales_pipeline_type": "nightly",
    },
    "brg": {
        "analytics_client_code": "brg",
        "analytics_client_id": 130,
        "appraisals_client_id": 897,
        "client_code": "brg",
        "client_version": 1,
        "fleet_customer_id": 158,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Briggs Equipment Company",
        "sales_client_code": "brg",
        "sales_client_id": 130,
        "sales_pipeline_type": "nightly",
    },
    "brguk": {
        "analytics_client_code": "brguk",
        "analytics_client_id": 232,
        "client_code": "brguk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Briggs Equipment UK",
        "sales_pipeline_type": "vod",
    },
    "brguk2": {
        "analytics_client_code": "brguk2",
        "analytics_client_id": 668,
        "appraisals_client_id": 2448,
        "client_code": "brguk2",
        "client_version": 1,
        "fleet_customer_id": 10490,
        "ims_conversion_status": "ims",
        "name": "Briggs Equipment UK",
        "sales_client_code": "brguk2",
        "sales_client_id": 10490,
        "sales_pipeline_type": "nightly",
    },
    "bros": {
        "analytics_client_code": "bros",
        "analytics_client_id": 510,
        "appraisals_client_id": 2265,
        "client_code": "bros",
        "client_version": 1,
        "fleet_customer_id": 10386,
        "ims_conversion_status": "ims",
        "name": "Equipments Brossard",
        "sales_client_code": "bros",
        "sales_client_id": 10386,
        "sales_pipeline_type": "nightly",
    },
    "brtvw": {
        "analytics_client_code": "brtvw",
        "analytics_client_id": 788,
        "appraisals_client_id": 3028,
        "client_code": "brtvw",
        "client_version": 1,
        "fleet_customer_id": 10783,
        "ims_conversion_status": "ims",
        "name": "BrightView Landscapes LLC",
        "sales_client_code": "brtvw",
        "sales_client_id": 10783,
        "sales_pipeline_type": "nightly",
    },
    "brunk": {
        "analytics_client_code": "brunk",
        "analytics_client_id": 779,
        "appraisals_client_id": 3040,
        "client_code": "brunk",
        "client_version": 1,
        "fleet_customer_id": 10790,
        "ims_conversion_status": "ims",
        "name": "Brunswick Lift Rentals LTD",
        "sales_client_code": "brunk",
        "sales_client_id": 10790,
        "sales_pipeline_type": "nightly",
    },
    "bser": {
        "analytics_client_code": "bser",
        "analytics_client_id": 507,
        "appraisals_client_id": 2450,
        "client_code": "bser",
        "client_version": 1,
        "fleet_customer_id": 10492,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "BSE Rents",
        "sales_client_code": "bser",
        "sales_client_id": 10492,
        "sales_pipeline_type": "nightly",
    },
    "bsp": {
        "analytics_client_code": "bsp",
        "analytics_client_id": 499,
        "appraisals_client_id": 1521,
        "client_code": "bsp",
        "client_version": 1,
        "fleet_customer_id": 10116,
        "ims_conversion_status": "ims",
        "name": "Business Special Projects",
        "sales_client_code": "bsp",
        "sales_client_id": 10116,
        "sales_pipeline_type": "nightly",
    },
    "bstl": {
        "analytics_client_code": "bstl",
        "analytics_client_id": 201,
        "appraisals_client_id": 746,
        "client_code": "bstl",
        "client_version": 1,
        "fleet_customer_id": 44,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Bobcat of St Louis",
        "primary_oem": "Bobcat",
        "sales_client_code": "bstl",
        "sales_client_id": 201,
        "sales_pipeline_type": "nightly",
    },
    "buhsuk": {
        "analytics_client_code": "buhsuk",
        "analytics_client_id": 749,
        "client_code": "buhsuk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Buckhurst Plant Hire Ltd",
        "sales_pipeline_type": "vod",
    },
    "bull": {
        "analytics_client_code": "bull",
        "analytics_client_id": 291,
        "appraisals_client_id": 1120,
        "client_code": "bull",
        "client_version": 1,
        "fleet_customer_id": 10043,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Bullet Rental",
        "sales_client_code": "bull",
        "sales_client_id": 291,
        "sales_pipeline_type": "nightly",
    },
    "but": {
        "analytics_client_code": "but",
        "analytics_client_id": 48,
        "client_code": "but",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Butler Rental",
        "sales_pipeline_type": "vod",
    },
    "cab": {
        "analytics_client_code": "cab",
        "analytics_client_id": 694,
        "appraisals_client_id": 2827,
        "client_code": "cab",
        "client_version": 1,
        "fleet_customer_id": 10658,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "C\u0026B Material Handling",
        "sales_client_code": "cab",
        "sales_client_id": 10658,
        "sales_pipeline_type": "nightly",
    },
    "calf": {
        "analytics_client_code": "calf",
        "analytics_client_id": 381,
        "client_code": "calf",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "CanLift",
        "sales_pipeline_type": "vod",
    },
    "calw": {
        "analytics_client_code": "calw",
        "analytics_client_id": 495,
        "appraisals_client_id": 2451,
        "client_code": "calw",
        "client_version": 1,
        "fleet_customer_id": 10493,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Cal West Rentals",
        "sales_client_code": "calw",
        "sales_client_id": 10493,
        "sales_pipeline_type": "nightly",
    },
    "canl": {
        "appraisals_client_id": 1130,
        "client_code": "canl",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "market_segment": "Independent",
        "name": "CanLift Equipment Ltd.",
        "sales_pipeline_type": "vod",
    },
    "cannuk": {
        "analytics_client_code": "cannuk",
        "analytics_client_id": 621,
        "appraisals_client_id": 2040,
        "client_code": "cannuk",
        "client_version": 1,
        "fleet_customer_id": 10305,
        "ims_conversion_status": "ims",
        "name": "Cannon Access",
        "sales_client_code": "cannuk",
        "sales_client_id": 10305,
        "sales_pipeline_type": "nightly",
    },
    "cap": {
        "analytics_client_code": "cap",
        "analytics_client_id": 43,
        "client_code": "cap",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Capital Rentals",
        "sales_pipeline_type": "vod",
    },
    "car": {
        "analytics_client_code": "car",
        "analytics_client_id": 15,
        "appraisals_client_id": 530,
        "client_code": "car",
        "client_version": 1,
        "fleet_customer_id": 19,
        "ims_conversion_status": "ims",
        "name": "Carolina Tractor \u0026 Equipment Company",
        "sales_client_code": "car",
        "sales_client_id": 704,
        "sales_pipeline_type": "nightly",
    },
    "cbro": {
        "analytics_client_code": "cbro",
        "analytics_client_id": 754,
        "appraisals_client_id": 2920,
        "client_code": "cbro",
        "client_version": 1,
        "fleet_customer_id": 10736,
        "ims_conversion_status": "ims",
        "name": "Cianbro Equipment",
        "sales_client_code": "cbro",
        "sales_client_id": 10736,
        "sales_pipeline_type": "nightly",
    },
    "cbs": {
        "analytics_client_code": "cbs",
        "analytics_client_id": 68,
        "client_code": "cbs",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Contractors Building Supply",
        "sales_pipeline_type": "vod",
    },
    "cc": {
        "analytics_client_code": "cc",
        "analytics_client_id": -79,
        "appraisals_client_id": 801,
        "client_code": "cc",
        "client_version": 1,
        "fleet_customer_id": 83,
        "ims_conversion_status": "ims",
        "market_segment": "lookup",
        "name": "Conserv Capital",
        "sales_client_code": "cc",
        "sales_client_id": 615,
        "sales_pipeline_type": "vod",
    },
    "cclt": {
        "analytics_client_code": "cclt",
        "analytics_client_id": 536,
        "appraisals_client_id": 1559,
        "client_code": "cclt",
        "client_version": 1,
        "fleet_customer_id": 10136,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "C\u0026C Lift Truck",
        "sales_client_code": "cclt",
        "sales_client_id": 10136,
        "sales_pipeline_type": "nightly",
    },
    "cea": {
        "analytics_client_code": "cea",
        "analytics_client_id": -500,
        "client_code": "cea",
        "client_version": 1,
        "fleet_customer_id": 120,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Certified Equipment Appraisals",
        "primary_oem": "-",
        "sales_client_code": "cea",
        "sales_client_id": 962,
        "sales_pipeline_type": "vod",
    },
    "cec": {
        "analytics_client_code": "cec",
        "analytics_client_id": 91,
        "client_code": "cec",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Contractors Equipment Center",
        "sales_pipeline_type": "vod",
    },
    "ceda": {
        "analytics_client_code": "ceda",
        "analytics_client_id": 550,
        "appraisals_client_id": 1584,
        "client_code": "ceda",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Cedar Equipment Rental",
        "sales_pipeline_type": "vod",
    },
    "cedr": {
        "client_code": "cedr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "market_segment": "Independent",
        "name": "Cedar Equipment Rental",
        "sales_pipeline_type": "vod",
    },
    "cer": {
        "analytics_client_code": "cer",
        "analytics_client_id": 174,
        "appraisals_client_id": 1449,
        "client_code": "cer",
        "client_version": 1,
        "fleet_customer_id": 10018,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "C\u0026E Rentals",
        "sales_client_code": "cer",
        "sales_client_id": 10018,
        "sales_pipeline_type": "nightly",
    },
    "cesp": {
        "analytics_client_code": "cesp",
        "analytics_client_id": 443,
        "appraisals_client_id": 136,
        "client_code": "cesp",
        "client_version": 1,
        "fleet_customer_id": 10052,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "CES Power",
        "sales_client_code": "cesp",
        "sales_client_id": 441,
        "sales_pipeline_type": "vod",
    },
    "cgr": {
        "analytics_client_code": "cgr",
        "analytics_client_id": 126,
        "client_code": "cgr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "WesternOne CGR",
        "sales_pipeline_type": "vod",
    },
    "chet": {
        "analytics_client_code": "chet",
        "analytics_client_id": 215,
        "client_code": "chet",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Chet's Rent-All",
        "sales_pipeline_type": "vod",
    },
    "chet2": {
        "analytics_client_code": "chet2",
        "analytics_client_id": 625,
        "appraisals_client_id": 2014,
        "client_code": "chet2",
        "client_version": 1,
        "fleet_customer_id": 10290,
        "ims_conversion_status": "ims",
        "name": "Chet's Rent-All",
        "sales_client_code": "chet2",
        "sales_client_id": 10290,
        "sales_pipeline_type": "nightly",
    },
    "chi": {
        "analytics_client_code": "chi",
        "analytics_client_id": 332,
        "appraisals_client_id": 2458,
        "client_code": "chi",
        "client_version": 1,
        "fleet_customer_id": 10500,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Construction Heaters Inc",
        "sales_client_code": "chi",
        "sales_client_id": 10500,
        "sales_pipeline_type": "nightly",
    },
    "chpuk": {
        "analytics_client_code": "chpuk",
        "analytics_client_id": 492,
        "client_code": "chpuk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Chippindale",
        "sales_pipeline_type": "vod",
    },
    "chr": {
        "analytics_client_code": "chr",
        "analytics_client_id": 200,
        "client_code": "chr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Champion Rentals",
        "sales_pipeline_type": "vod",
    },
    "chris": {
        "analytics_client_code": "chris",
        "analytics_client_id": 726,
        "appraisals_client_id": 2843,
        "client_code": "chris",
        "client_version": 1,
        "fleet_customer_id": 10674,
        "ims_conversion_status": "ims",
        "name": "Christopher Equipment Inc.",
        "sales_client_code": "chris",
        "sales_client_id": 10674,
        "sales_pipeline_type": "nightly",
    },
    "chsc": {
        "analytics_client_code": "chsc",
        "analytics_client_id": 303,
        "client_code": "chsc",
        "client_version": 1,
        "fleet_customer_id": 98,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "ChaseCo",
        "primary_oem": "-",
        "sales_client_code": "chsc",
        "sales_client_id": 303,
        "sales_pipeline_type": "vod",
    },
    "cis": {
        "analytics_client_code": "cis",
        "analytics_client_id": 44,
        "appraisals_client_id": 799,
        "client_code": "cis",
        "client_version": 1,
        "fleet_customer_id": 12,
        "ims_conversion_status": "ims",
        "name": "Cisco Equipment Rentals, LLC",
        "sales_client_code": "cis",
        "sales_client_id": 44,
        "sales_pipeline_type": "nightly",
    },
    "city": {
        "analytics_client_code": "city",
        "analytics_client_id": 283,
        "appraisals_client_id": 2452,
        "client_code": "city",
        "client_version": 1,
        "fleet_customer_id": 10494,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "City Rentals",
        "sales_client_code": "city",
        "sales_client_id": 10494,
        "sales_pipeline_type": "nightly",
    },
    "clb": {
        "analytics_client_code": "clb",
        "analytics_client_id": 11,
        "appraisals_client_id": 472,
        "client_code": "clb",
        "client_version": 1,
        "fleet_customer_id": 42,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Cleveland Brothers Equipment Co., Inc.",
        "primary_oem": "caterpillar",
        "sales_client_code": "clb",
        "sales_client_id": 717,
        "sales_pipeline_type": "nightly",
    },
    "clenau": {
        "analytics_client_code": "clenau",
        "analytics_client_id": 683,
        "appraisals_client_id": 3060,
        "client_code": "clenau",
        "client_version": 1,
        "fleet_customer_id": 10843,
        "ims_conversion_status": "ims",
        "name": "Clennett Hire",
        "sales_client_code": "clenau",
        "sales_client_id": 10843,
        "sales_pipeline_type": "nightly",
    },
    "clk": {
        "analytics_client_code": "clk",
        "analytics_client_id": 646,
        "appraisals_client_id": 2453,
        "client_code": "clk",
        "client_version": 1,
        "fleet_customer_id": 10495,
        "ims_conversion_status": "ims",
        "name": "Clark Equipment Rental \u0026 Sales",
        "sales_client_code": "clk",
        "sales_client_id": 10495,
        "sales_pipeline_type": "nightly",
    },
    "clm": {
        "analytics_client_code": "clm",
        "analytics_client_id": 319,
        "appraisals_client_id": 2454,
        "client_code": "clm",
        "client_version": 1,
        "fleet_customer_id": 10496,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "CLM Equipment",
        "sales_client_code": "clm",
        "sales_client_id": 10496,
        "sales_pipeline_type": "nightly",
    },
    "cmpe": {
        "analytics_client_code": "cmpe",
        "analytics_client_id": 401,
        "appraisals_client_id": 2457,
        "client_code": "cmpe",
        "client_version": 1,
        "fleet_customer_id": 10499,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Complete Equipment",
        "sales_client_code": "cmpe",
        "sales_client_id": 10499,
        "sales_pipeline_type": "nightly",
    },
    "cnh": {
        "analytics_client_code": "cnh",
        "analytics_client_id": 643,
        "appraisals_client_id": 2084,
        "client_code": "cnh",
        "client_version": 1,
        "fleet_customer_id": 10326,
        "ims_conversion_status": "ims",
        "name": "CNH Industrial Capital America LLC",
        "sales_client_code": "cnh",
        "sales_client_id": 10326,
        "sales_pipeline_type": "nightly",
    },
    "cnw": {
        "analytics_client_code": "cnw",
        "analytics_client_id": 341,
        "appraisals_client_id": 938,
        "client_code": "cnw",
        "client_version": 1,
        "fleet_customer_id": 47,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "CN Wood",
        "primary_oem": "komatsu",
        "sales_client_code": "cnw",
        "sales_client_id": 978,
        "sales_pipeline_type": "nightly",
    },
    "coatau": {
        "analytics_client_code": "coatau",
        "analytics_client_id": 559,
        "appraisals_client_id": 2455,
        "client_code": "coatau",
        "client_version": 1,
        "fleet_customer_id": 10497,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Coates Hire",
        "sales_client_code": "coatau",
        "sales_client_id": 10497,
        "sales_pipeline_type": "nightly",
    },
    "coca": {
        "analytics_client_code": "coca",
        "appraisals_client_id": 1502,
        "client_code": "coca",
        "client_version": 1,
        "fleet_customer_id": 10107,
        "ims_conversion_status": "ims",
        "market_segment": "financial",
        "name": "Commercial Capital Company",
        "sales_client_code": "coca",
        "sales_client_id": 10107,
        "sales_pipeline_type": "vod",
    },
    "coke": {
        "analytics_client_code": "coke",
        "analytics_client_id": 422,
        "appraisals_client_id": 2456,
        "client_code": "coke",
        "client_version": 1,
        "fleet_customer_id": 10498,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Coker Rental Company",
        "sales_client_code": "coke",
        "sales_client_id": 10498,
        "sales_pipeline_type": "nightly",
    },
    "col": {
        "analytics_client_code": "col",
        "analytics_client_id": 156,
        "appraisals_client_id": 634,
        "client_code": "col",
        "client_version": 1,
        "fleet_customer_id": 165,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Columbus Equipment",
        "primary_oem": "komatsu",
        "sales_client_code": "col",
        "sales_client_id": 156,
        "sales_pipeline_type": "nightly",
    },
    "compau": {
        "analytics_client_code": "compau",
        "analytics_client_id": 684,
        "client_code": "compau",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Complete Hire Equipment",
        "sales_pipeline_type": "vod",
    },
    "cont": {
        "analytics_client_code": "cont",
        "analytics_client_id": 106,
        "client_code": "cont",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Contractors Rental Supply",
        "sales_pipeline_type": "vod",
    },
    "contuk": {
        "analytics_client_code": "contuk",
        "analytics_client_id": 639,
        "appraisals_client_id": 2202,
        "client_code": "contuk",
        "client_version": 1,
        "fleet_customer_id": 10362,
        "ims_conversion_status": "ims",
        "name": "Contract Plant Hire",
        "sales_client_code": "contuk",
        "sales_client_id": 10362,
        "sales_pipeline_type": "nightly",
    },
    "coop": {
        "analytics_client_code": "coop",
        "analytics_client_id": 164,
        "appraisals_client_id": 337,
        "client_code": "coop",
        "client_version": 1,
        "fleet_customer_id": 11,
        "ims_conversion_status": "ims",
        "name": "Cooper Equipment Rentals",
        "sales_client_code": "coop",
        "sales_client_id": 164,
        "sales_pipeline_type": "nightly",
    },
    "cow": {
        "analytics_client_code": "cow",
        "analytics_client_id": 28,
        "appraisals_client_id": 635,
        "client_code": "cow",
        "client_version": 1,
        "fleet_customer_id": 215,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Cowin",
        "primary_oem": "Volvo",
        "sales_client_code": "cow",
        "sales_client_id": 28,
        "sales_pipeline_type": "nightly",
    },
    "cra": {
        "analytics_client_code": "cra",
        "analytics_client_id": 216,
        "client_code": "cra",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Casale Rent-All",
        "sales_pipeline_type": "vod",
    },
    "crguy": {
        "analytics_client_code": "crguy",
        "analytics_client_id": 681,
        "client_code": "crguy",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Crane Guys",
        "sales_pipeline_type": "vod",
    },
    "crham": {
        "analytics_client_code": "crham",
        "analytics_client_id": 797,
        "appraisals_client_id": 3059,
        "client_code": "crham",
        "client_version": 1,
        "fleet_customer_id": 10842,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "CRH Americas Materials, Inc.",
        "sales_client_code": "crham",
        "sales_client_id": 10842,
        "sales_pipeline_type": "nightly",
    },
    "croc": {
        "analytics_client_code": "croc",
        "analytics_client_id": 419,
        "appraisals_client_id": 2459,
        "client_code": "croc",
        "client_version": 1,
        "fleet_customer_id": 10501,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Crane Rental Service (CA)",
        "sales_client_code": "croc",
        "sales_client_id": 10501,
        "sales_pipeline_type": "nightly",
    },
    "crr": {
        "analytics_client_code": "crr",
        "analytics_client_id": -7,
        "client_code": "crr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "CRR Rentals",
        "sales_pipeline_type": "vod",
    },
    "crs": {
        "analytics_client_code": "crs",
        "analytics_client_id": 32,
        "appraisals_client_id": 533,
        "client_code": "crs",
        "client_version": 1,
        "fleet_customer_id": 213,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Cresco",
        "primary_oem": "caterpillar",
        "sales_client_code": "crs",
        "sales_client_id": 32,
        "sales_pipeline_type": "nightly",
    },
    "crwn": {
        "analytics_client_code": "crwn",
        "analytics_client_id": 260,
        "appraisals_client_id": 1984,
        "client_code": "crwn",
        "client_version": 1,
        "fleet_customer_id": 10275,
        "ims_conversion_status": "ims",
        "name": "Crown Equipment",
        "sales_client_code": "crwn",
        "sales_client_id": 10275,
        "sales_pipeline_type": "nightly",
    },
    "csh": {
        "analytics_client_code": "csh",
        "analytics_client_id": 62,
        "appraisals_client_id": 414,
        "client_code": "csh",
        "client_version": 1,
        "fleet_customer_id": 22,
        "ims_conversion_status": "ims",
        "name": "Cashman Equipment",
        "sales_client_code": "csh",
        "sales_client_id": 62,
        "sales_pipeline_type": "nightly",
    },
    "csnuk": {
        "analytics_client_code": "csnuk",
        "analytics_client_id": 457,
        "appraisals_client_id": 2341,
        "client_code": "csnuk",
        "client_version": 1,
        "fleet_customer_id": 10414,
        "ims_conversion_status": "ims",
        "name": "2 Cousins Powered Access",
        "sales_client_code": "csnuk",
        "sales_client_id": 10414,
        "sales_pipeline_type": "nightly",
    },
    "cstc": {
        "analytics_client_code": "cstc",
        "analytics_client_id": 470,
        "appraisals_client_id": 1419,
        "client_code": "cstc",
        "client_version": 1,
        "fleet_customer_id": 10054,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Coast Capital",
        "sales_client_code": "cstc",
        "sales_client_id": 470,
        "sales_pipeline_type": "vod",
    },
    "ctr": {
        "analytics_client_code": "ctr",
        "analytics_client_id": 19,
        "appraisals_client_id": 406,
        "client_code": "ctr",
        "client_version": 1,
        "fleet_customer_id": 21,
        "ims_conversion_status": "ims",
        "name": "Carter Machinery Company, Inc.",
        "sales_client_code": "ctr",
        "sales_client_id": 540,
        "sales_pipeline_type": "nightly",
    },
    "cts": {
        "analytics_client_code": "cts",
        "analytics_client_id": 511,
        "appraisals_client_id": 1162,
        "client_code": "cts",
        "client_version": 1,
        "fleet_customer_id": 226,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Chappell Tractor Sales",
        "sales_client_code": "cts",
        "sales_client_id": 918,
        "sales_pipeline_type": "nightly",
    },
    "cwb": {
        "analytics_client_code": "cwb",
        "analytics_client_id": 690,
        "appraisals_client_id": 2157,
        "client_code": "cwb",
        "client_version": 1,
        "fleet_customer_id": 10348,
        "ims_conversion_status": "ims",
        "name": "CWB National Leasing",
        "sales_client_code": "cwb",
        "sales_client_id": 10348,
        "sales_pipeline_type": "nightly",
    },
    "cwl": {
        "analytics_client_code": "cwl",
        "analytics_client_id": 149,
        "appraisals_client_id": 848,
        "client_code": "cwl",
        "client_version": 1,
        "fleet_customer_id": 48,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Company Wrench Ltd.",
        "sales_client_code": "cwl",
        "sales_client_id": 149,
        "sales_pipeline_type": "nightly",
    },
    "cwx": {
        "analytics_client_code": "cwx",
        "analytics_client_id": 202,
        "appraisals_client_id": 1961,
        "client_code": "cwx",
        "client_version": 1,
        "fleet_customer_id": 94,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Craneworks",
        "primary_oem": "-",
        "sales_client_code": "cwx",
        "sales_client_id": 202,
        "sales_pipeline_type": "nightly",
    },
    "daba": {
        "analytics_client_code": "daba",
        "analytics_client_id": 379,
        "client_code": "daba",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Aba Daba",
        "sales_pipeline_type": "vod",
    },
    "dahl": {
        "analytics_client_code": "dahl",
        "analytics_client_id": 724,
        "appraisals_client_id": 2845,
        "client_code": "dahl",
        "client_version": 1,
        "fleet_customer_id": 10676,
        "ims_conversion_status": "ims",
        "name": "Dahl's Equipment Rentals",
        "sales_client_code": "dahl",
        "sales_client_id": 10676,
        "sales_pipeline_type": "nightly",
    },
    "darr": {
        "analytics_client_code": "darr",
        "analytics_client_id": 692,
        "appraisals_client_id": 2927,
        "client_code": "darr",
        "client_version": 1,
        "fleet_customer_id": 10740,
        "ims_conversion_status": "ims",
        "name": "Darr Equipment",
        "sales_client_code": "darr",
        "sales_client_id": 10740,
        "sales_pipeline_type": "nightly",
    },
    "dbrs": {
        "analytics_client_code": "dbrs",
        "analytics_client_id": 265,
        "appraisals_client_id": 2460,
        "client_code": "dbrs",
        "client_version": 1,
        "fleet_customer_id": 10502,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "D\u0026B Rental",
        "sales_client_code": "dbrs",
        "sales_client_id": 10502,
        "sales_pipeline_type": "nightly",
    },
    "deck": {
        "analytics_client_code": "deck",
        "analytics_client_id": 285,
        "appraisals_client_id": 2462,
        "client_code": "deck",
        "client_version": 1,
        "fleet_customer_id": 10504,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Decker Tool Rental",
        "sales_client_code": "deck",
        "sales_client_id": 10504,
        "sales_pipeline_type": "nightly",
    },
    "defa": {
        "analytics_client_code": "defa",
        "analytics_client_id": 251,
        "appraisals_client_id": 2015,
        "client_code": "defa",
        "client_version": 1,
        "fleet_customer_id": 10291,
        "ims_conversion_status": "ims",
        "name": "Defatte Equipment",
        "sales_client_code": "defa",
        "sales_client_id": 10291,
        "sales_pipeline_type": "nightly",
    },
    "dilt": {
        "analytics_client_code": "dilt",
        "analytics_client_id": 659,
        "appraisals_client_id": 2238,
        "client_code": "dilt",
        "client_version": 1,
        "fleet_customer_id": 10378,
        "ims_conversion_status": "ims",
        "name": "Dillon Toyota Lift",
        "sales_client_code": "dilt",
        "sales_client_id": 10378,
        "sales_pipeline_type": "nightly",
    },
    "dlusa": {
        "analytics_client_code": "dlusa",
        "analytics_client_id": 657,
        "appraisals_client_id": 2186,
        "client_code": "dlusa",
        "client_version": 1,
        "fleet_customer_id": 10358,
        "ims_conversion_status": "ims",
        "name": "Deutsche Leasing USA Inc",
        "sales_client_code": "dlusa",
        "sales_client_id": 10358,
        "sales_pipeline_type": "nightly",
    },
    "dmh": {
        "analytics_client_code": "dmh",
        "analytics_client_id": 147,
        "appraisals_client_id": 2463,
        "client_code": "dmh",
        "client_version": 1,
        "fleet_customer_id": 10505,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Delta Materials Handling, Inc.",
        "sales_client_code": "dmh",
        "sales_client_id": 10505,
        "sales_pipeline_type": "nightly",
    },
    "dmnd": {
        "analytics_client_code": "dmnd",
        "analytics_client_id": 304,
        "appraisals_client_id": 2464,
        "client_code": "dmnd",
        "client_version": 1,
        "fleet_customer_id": 10506,
        "ims_conversion_status": "ims",
        "name": "Diamond Rental",
        "sales_client_code": "dmnd",
        "sales_client_id": 10506,
        "sales_pipeline_type": "nightly",
    },
    "dobb": {
        "analytics_client_code": "dobb",
        "analytics_client_id": 188,
        "appraisals_client_id": 1848,
        "client_code": "dobb",
        "client_version": 1,
        "fleet_customer_id": 10214,
        "ims_conversion_status": "ims",
        "name": "Dobbs Equipment",
        "sales_client_code": "dobb",
        "sales_client_id": 10214,
        "sales_pipeline_type": "nightly",
    },
    "dogg": {
        "analytics_client_code": "dogg",
        "analytics_client_id": 194,
        "appraisals_client_id": 701,
        "client_code": "dogg",
        "client_version": 1,
        "fleet_customer_id": 49,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Doggett Equipment Services",
        "primary_oem": "John Deere",
        "sales_client_code": "dogg",
        "sales_client_id": 194,
        "sales_pipeline_type": "nightly",
    },
    "dogt": {
        "analytics_client_code": "dogt",
        "analytics_client_id": 313,
        "appraisals_client_id": 2465,
        "client_code": "dogt",
        "client_version": 1,
        "fleet_customer_id": 10507,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Doggett Toyota",
        "sales_client_code": "dogt",
        "sales_client_id": 10507,
        "sales_pipeline_type": "nightly",
    },
    "dons": {
        "analytics_client_code": "dons",
        "analytics_client_id": 405,
        "appraisals_client_id": 2467,
        "client_code": "dons",
        "client_version": 1,
        "fleet_customer_id": 10509,
        "ims_conversion_status": "ims",
        "name": "Don's Rental",
        "sales_client_code": "dons",
        "sales_client_id": 10509,
        "sales_pipeline_type": "nightly",
    },
    "dots": {
        "analytics_client_code": "dots",
        "analytics_client_id": 306,
        "appraisals_client_id": 2468,
        "client_code": "dots",
        "client_version": 1,
        "fleet_customer_id": 10510,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Dot's Rentals",
        "sales_client_code": "dots",
        "sales_client_id": 10510,
        "sales_pipeline_type": "nightly",
    },
    "doz": {
        "analytics_client_code": "doz",
        "analytics_client_id": -500,
        "client_code": "doz",
        "client_version": 1,
        "fleet_customer_id": 96,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Dozers.Com",
        "primary_oem": "-",
        "sales_client_code": "doz",
        "sales_client_id": 229,
        "sales_pipeline_type": "vod",
    },
    "dpn": {
        "analytics_client_code": "dpn",
        "analytics_client_id": 399,
        "appraisals_client_id": 2461,
        "client_code": "dpn",
        "client_version": 1,
        "fleet_customer_id": 10503,
        "ims_conversion_status": "ims",
        "name": "D P Nicoli",
        "sales_client_code": "dpn",
        "sales_client_id": 10503,
        "sales_pipeline_type": "nightly",
    },
    "dpp": {
        "analytics_client_code": "dpp",
        "analytics_client_id": -500,
        "client_code": "dpp",
        "client_version": 1,
        "fleet_customer_id": 112,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Doosan Portable Power",
        "primary_oem": "doosan",
        "sales_client_code": "dpp",
        "sales_client_id": 915,
        "sales_pipeline_type": "vod",
    },
    "drogie": {
        "analytics_client_code": "drogie",
        "analytics_client_id": 489,
        "client_code": "drogie",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Drogheda Hire and Sales",
        "sales_pipeline_type": "vod",
    },
    "drr": {
        "analytics_client_code": "drr",
        "analytics_client_id": 100,
        "appraisals_client_id": 2469,
        "client_code": "drr",
        "client_version": 1,
        "fleet_customer_id": 10511,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Double R Rentals Ltd.",
        "sales_client_code": "drr",
        "sales_client_id": 10511,
        "sales_pipeline_type": "nightly",
    },
    "dsm": {
        "analytics_client_code": "dsm",
        "analytics_client_id": 418,
        "appraisals_client_id": 1229,
        "client_code": "dsm",
        "client_version": 1,
        "fleet_customer_id": 127,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Diesel Machinery",
        "primary_oem": "-",
        "sales_client_code": "dsm",
        "sales_client_id": 985,
        "sales_pipeline_type": "nightly",
    },
    "dtst": {
        "analytics_client_code": "dtst",
        "analytics_client_id": 390,
        "appraisals_client_id": 2466,
        "client_code": "dtst",
        "client_version": 1,
        "fleet_customer_id": 10508,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Doggett Toyota Lift of South Texas",
        "sales_client_code": "dtst",
        "sales_client_id": 10508,
        "sales_pipeline_type": "nightly",
    },
    "duit": {
        "analytics_client_code": "duit",
        "analytics_client_id": 587,
        "appraisals_client_id": 1886,
        "client_code": "duit",
        "client_version": 1,
        "fleet_customer_id": 10227,
        "ims_conversion_status": "ims",
        "market_segment": "contractor",
        "name": "Duit Construction Co., Inc.",
        "sales_client_code": "duit",
        "sales_client_id": 10227,
        "sales_pipeline_type": "nightly",
    },
    "duke": {
        "analytics_client_code": "duke",
        "analytics_client_id": 145,
        "appraisals_client_id": 744,
        "client_code": "duke",
        "client_version": 1,
        "fleet_customer_id": 224,
        "ims_conversion_status": "ims",
        "name": "The Duke Company",
        "sales_client_code": "duke",
        "sales_client_id": 145,
        "sales_pipeline_type": "nightly",
    },
    "dur": {
        "analytics_client_code": "dur",
        "analytics_client_id": 23,
        "appraisals_client_id": 1491,
        "client_code": "dur",
        "client_version": 1,
        "fleet_customer_id": 10103,
        "ims_conversion_status": "ims",
        "name": "Durante Rentals, LLC",
        "sales_client_code": "dur",
        "sales_client_id": 10103,
        "sales_pipeline_type": "nightly",
    },
    "durf": {
        "analytics_client_code": "durf",
        "analytics_client_id": 267,
        "appraisals_client_id": 1476,
        "client_code": "durf",
        "client_version": 1,
        "fleet_customer_id": 10096,
        "ims_conversion_status": "ims",
        "name": "Durante Florida",
        "sales_client_code": "durf",
        "sales_client_id": 10096,
        "sales_pipeline_type": "nightly",
    },
    "dynr": {
        "analytics_client_code": "dynr",
        "analytics_client_id": 385,
        "appraisals_client_id": 2470,
        "client_code": "dynr",
        "client_version": 1,
        "fleet_customer_id": 10512,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Dynamic Equipment",
        "sales_client_code": "dynr",
        "sales_client_id": 10512,
        "sales_pipeline_type": "nightly",
    },
    "eabo": {
        "analytics_client_code": "eabo",
        "analytics_client_id": 446,
        "appraisals_client_id": 2471,
        "client_code": "eabo",
        "client_version": 1,
        "fleet_customer_id": 10513,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Earthborne Inc",
        "sales_client_code": "eabo",
        "sales_client_id": 10513,
        "sales_pipeline_type": "nightly",
    },
    "ecc": {
        "analytics_client_code": "ecc",
        "analytics_client_id": 54,
        "appraisals_client_id": 1156,
        "client_code": "ecc",
        "client_version": 1,
        "fleet_customer_id": 207,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Ecco Equipment",
        "sales_client_code": "ecc",
        "sales_client_id": 54,
        "sales_pipeline_type": "nightly",
    },
    "ecco": {
        "analytics_client_code": "ecco",
        "analytics_client_id": -500,
        "client_code": "ecco",
        "client_version": 1,
        "fleet_customer_id": 111,
        "ims_conversion_status": "ims",
        "market_segment": "contractor",
        "name": "ECCO III Enterprises",
        "primary_oem": "-",
        "sales_client_code": "ecco",
        "sales_client_id": 914,
        "sales_pipeline_type": "vod",
    },
    "ecfl": {
        "analytics_client_code": "ecfl",
        "analytics_client_id": 509,
        "appraisals_client_id": 2473,
        "client_code": "ecfl",
        "client_version": 1,
        "fleet_customer_id": 10515,
        "ims_conversion_status": "ims",
        "name": "Eco Rentals",
        "sales_client_code": "ecfl",
        "sales_client_id": 10515,
        "sales_pipeline_type": "nightly",
    },
    "efin": {
        "analytics_client_code": "efin",
        "analytics_client_id": 477,
        "appraisals_client_id": 1510,
        "client_code": "efin",
        "client_version": 1,
        "fleet_customer_id": 10110,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Equipment Finders",
        "sales_client_code": "efin",
        "sales_client_id": 10110,
        "sales_pipeline_type": "nightly",
    },
    "ehe": {
        "analytics_client_code": "ehe",
        "analytics_client_id": -500,
        "client_code": "ehe",
        "client_version": 1,
        "fleet_customer_id": 101,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Expert Heavy Equipment",
        "primary_oem": "-",
        "sales_client_code": "ehe",
        "sales_client_id": 409,
        "sales_pipeline_type": "vod",
    },
    "elcl": {
        "analytics_client_code": "elcl",
        "analytics_client_id": 375,
        "appraisals_client_id": 1278,
        "client_code": "elcl",
        "client_version": 1,
        "fleet_customer_id": 157,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "El Cheapo Lifts",
        "sales_client_code": "elcl",
        "sales_client_id": 375,
        "sales_pipeline_type": "nightly",
    },
    "elite": {
        "analytics_client_code": "elite",
        "analytics_client_id": 641,
        "appraisals_client_id": 2078,
        "client_code": "elite",
        "client_version": 1,
        "fleet_customer_id": 10324,
        "ims_conversion_status": "ims",
        "name": "Elite Material Handling",
        "sales_client_code": "elite",
        "sales_client_id": 10324,
        "sales_pipeline_type": "nightly",
    },
    "eltnuk": {
        "analytics_client_code": "eltnuk",
        "analytics_client_id": 732,
        "appraisals_client_id": 2867,
        "client_code": "eltnuk",
        "client_version": 1,
        "fleet_customer_id": 10684,
        "ims_conversion_status": "ims",
        "name": "Elavation Ltd",
        "sales_client_code": "eltnuk",
        "sales_client_id": 10684,
        "sales_pipeline_type": "nightly",
    },
    "emp": {
        "analytics_client_code": "emp",
        "analytics_client_id": 40,
        "appraisals_client_id": 830,
        "client_code": "emp",
        "client_version": 1,
        "fleet_customer_id": 22,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Empire CAT",
        "primary_oem": "Caterpillar",
        "sales_client_code": "emp",
        "sales_client_id": 40,
        "sales_pipeline_type": "nightly",
    },
    "emrg": {
        "analytics_client_code": "emrg",
        "analytics_client_id": 393,
        "appraisals_client_id": 1227,
        "client_code": "emrg",
        "client_version": 1,
        "fleet_customer_id": 65,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Empower Rental Group",
        "sales_client_code": "emrg",
        "sales_client_id": 393,
        "sales_pipeline_type": "nightly",
    },
    "emry": {
        "analytics_client_code": "emry",
        "analytics_client_id": 301,
        "appraisals_client_id": 2474,
        "client_code": "emry",
        "client_version": 1,
        "fleet_customer_id": 10516,
        "ims_conversion_status": "ims",
        "name": "Emery Equipment",
        "sales_client_code": "emry",
        "sales_client_id": 10516,
        "sales_pipeline_type": "nightly",
    },
    "epc": {
        "analytics_client_code": "epc",
        "analytics_client_id": 59,
        "client_code": "epc",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Equipco",
        "sales_pipeline_type": "vod",
    },
    "eptuk": {
        "analytics_client_code": "eptuk",
        "analytics_client_id": 275,
        "client_code": "eptuk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Explore Transport UK",
        "sales_pipeline_type": "vod",
    },
    "eqd": {
        "analytics_client_code": "eqd",
        "analytics_client_id": 51,
        "appraisals_client_id": 680,
        "client_code": "eqd",
        "client_version": 1,
        "fleet_customer_id": 10,
        "ims_conversion_status": "ims",
        "name": "Equipment Depot",
        "sales_client_code": "eqd",
        "sales_client_id": 51,
        "sales_pipeline_type": "nightly",
    },
    "eqinc": {
        "analytics_client_code": "eqinc",
        "analytics_client_id": 774,
        "appraisals_client_id": 2987,
        "client_code": "eqinc",
        "client_version": 1,
        "fleet_customer_id": 10767,
        "ims_conversion_status": "ims",
        "name": "Equipment Inc.",
        "sales_client_code": "eqinc",
        "sales_client_id": 10767,
        "sales_pipeline_type": "nightly",
    },
    "eqptnz": {
        "analytics_client_code": "eqptnz",
        "analytics_client_id": 720,
        "client_code": "eqptnz",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Equipt Plant Hire",
        "sales_pipeline_type": "vod",
    },
    "erb": {
        "analytics_client_code": "erb",
        "analytics_client_id": 272,
        "client_code": "erb",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Erb Equipment",
        "sales_pipeline_type": "vod",
    },
    "ergs": {
        "analytics_client_code": "ergs",
        "analytics_client_id": 565,
        "client_code": "ergs",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Empower Group Supplemental",
        "sales_pipeline_type": "vod",
    },
    "ers": {
        "analytics_client_code": "ers",
        "analytics_client_id": 41,
        "client_code": "ers",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Excavator Rental",
        "sales_pipeline_type": "vod",
    },
    "es": {
        "analytics_client_code": "es",
        "analytics_client_id": 537,
        "appraisals_client_id": 705,
        "client_code": "es",
        "client_version": 1,
        "fleet_customer_id": 104,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "EquipmentShare",
        "primary_oem": "-",
        "sales_client_code": "es",
        "sales_client_id": 573,
        "sales_pipeline_type": "nightly",
    },
    "ess": {
        "analytics_client_code": "ess",
        "analytics_client_id": 798,
        "appraisals_client_id": 2894,
        "client_code": "ess",
        "client_version": 1,
        "fleet_customer_id": 10720,
        "ims_conversion_status": "ims",
        "name": "Emery Sapp \u0026 Sons",
        "sales_client_code": "ess",
        "sales_client_id": 10720,
        "sales_pipeline_type": "nightly",
    },
    "et2": {
        "client_code": "et2",
        "client_version": 1,
        "fleet_customer_id": 164,
        "ims_conversion_status": "ims",
        "name": "Engineering Test Client 2",
        "sales_pipeline_type": "vod",
    },
    "etc": {
        "analytics_client_code": "etc",
        "analytics_client_id": -101,
        "appraisals_client_id": 1196,
        "client_code": "etc",
        "client_version": 1,
        "fleet_customer_id": 50,
        "ims_conversion_status": "ims",
        "market_segment": "National",
        "name": "Engineering Test Client",
        "sales_client_code": "etc",
        "sales_client_id": 10001,
        "sales_pipeline_type": "nightly",
    },
    "etca01": {
        "analytics_client_code": "etca01",
        "analytics_client_id": -21,
        "client_code": "etca01",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "ETCA01 Rentals",
        "sales_pipeline_type": "vod",
    },
    "etcalloy": {
        "analytics_client_code": "etcalloy",
        "analytics_client_id": -115,
        "appraisals_client_id": 3072,
        "client_code": "etcalloy",
        "client_version": 1,
        "fleet_customer_id": 10847,
        "ims_conversion_status": "ims",
        "name": "Engineering Test Client - Alloy DB",
        "sales_client_code": "etcalloy",
        "sales_client_id": 10847,
        "sales_pipeline_type": "nightly",
    },
    "etcbad": {
        "analytics_client_code": "etcbad",
        "analytics_client_id": -112,
        "appraisals_client_id": 2837,
        "client_code": "etcbad",
        "client_version": 1,
        "fleet_customer_id": 10672,
        "ims_conversion_status": "ims",
        "market_segment": "test_client",
        "name": "Engineering Test Client - Bad Data",
        "sales_client_code": "etcbad",
        "sales_client_id": 10672,
        "sales_pipeline_type": "nightly",
    },
    "etcd": {
        "analytics_client_code": "etcd",
        "analytics_client_id": -103,
        "appraisals_client_id": 1559,
        "client_code": "etcd",
        "client_version": 1,
        "fleet_customer_id": 10130,
        "ims_conversion_status": "ims",
        "market_segment": "National",
        "name": "Engineering Test Client - Shard",
        "sales_client_code": "etcd",
        "sales_client_id": 10130,
        "sales_pipeline_type": "nightly",
    },
    "etcinsi": {
        "analytics_client_code": "etcinsi",
        "analytics_client_id": -108,
        "appraisals_client_id": 2262,
        "client_code": "etcinsi",
        "client_version": 1,
        "fleet_customer_id": 10383,
        "ims_conversion_status": "ims",
        "name": "Engineering Test Client - ClientInsights",
        "sales_client_code": "etcinsi",
        "sales_client_id": 10383,
        "sales_pipeline_type": "nightly",
    },
    "etcint": {
        "analytics_client_code": "etcint",
        "analytics_client_id": -109,
        "appraisals_client_id": 2284,
        "client_code": "etcint",
        "client_version": 1,
        "fleet_customer_id": 10391,
        "ims_conversion_status": "ims",
        "name": "Engineering Test Client - Integrations",
        "sales_client_code": "etcint",
        "sales_client_id": 10391,
        "sales_pipeline_type": "nightly",
    },
    "etcl": {
        "analytics_client_code": "etcl",
        "analytics_client_id": -104,
        "appraisals_client_id": 1762,
        "client_code": "etcl",
        "client_version": 1,
        "fleet_customer_id": 10175,
        "ims_conversion_status": "ims",
        "name": "Engineering Test Client - Large",
        "sales_client_code": "etcl",
        "sales_client_id": 10175,
        "sales_pipeline_type": "nightly",
    },
    "etcm": {
        "analytics_client_code": "etcm",
        "analytics_client_id": -102,
        "appraisals_client_id": 1440,
        "client_code": "etcm",
        "client_version": 1,
        "fleet_customer_id": 209,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Engineering Test Client - Medium",
        "sales_client_code": "etcm",
        "sales_client_id": 898989,
        "sales_pipeline_type": "nightly",
    },
    "etcquarter": {
        "analytics_client_code": "etcquarter",
        "analytics_client_id": -110,
        "appraisals_client_id": 2355,
        "client_code": "etcquarter",
        "client_version": 1,
        "fleet_customer_id": 10424,
        "ims_conversion_status": "ims",
        "market_segment": "test_client",
        "name": "Engineering Test Client - Quarter",
        "sales_client_code": "etcquarter",
        "sales_client_id": 10424,
        "sales_pipeline_type": "nightly",
    },
    "etcr": {
        "analytics_client_code": "etcr",
        "analytics_client_id": -106,
        "appraisals_client_id": 2072,
        "client_code": "etcr",
        "client_version": 1,
        "fleet_customer_id": 10319,
        "ims_conversion_status": "ims",
        "market_segment": "test client",
        "name": "Engineering Test Client - Regression",
        "sales_client_code": "etcr",
        "sales_client_id": 10319,
        "sales_pipeline_type": "nightly",
    },
    "etcri": {
        "analytics_client_code": "etcri",
        "analytics_client_id": -20,
        "client_code": "etcri",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "ETCRI Rental Insights",
        "sales_pipeline_type": "vod",
    },
    "etcrxl": {
        "analytics_client_code": "etcrxl",
        "analytics_client_id": -107,
        "appraisals_client_id": 2073,
        "client_code": "etcrxl",
        "client_version": 1,
        "fleet_customer_id": 10320,
        "ims_conversion_status": "ims",
        "market_segment": "test client",
        "name": "Engineering Test Client - Regression XLarge",
        "sales_client_code": "etcrxl",
        "sales_client_id": 10320,
        "sales_pipeline_type": "nightly",
    },
    "etcs": {
        "appraisals_client_id": 10002,
        "client_code": "etcs",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "market_segment": "National",
        "name": "Engineering Test Client 2",
        "sales_pipeline_type": "vod",
    },
    "etcserv": {
        "analytics_client_code": "etcserv",
        "analytics_client_id": -114,
        "appraisals_client_id": 3055,
        "client_code": "etcserv",
        "client_version": 1,
        "fleet_customer_id": 10839,
        "ims_conversion_status": "ims",
        "market_segment": "test_client",
        "name": "Engineering Test Client - Services",
        "sales_client_code": "etc_serv",
        "sales_client_id": 10839,
        "sales_pipeline_type": "nightly",
    },
    "etcuk": {
        "analytics_client_code": "etcuk",
        "analytics_client_id": -111,
        "appraisals_client_id": 2367,
        "client_code": "etcuk",
        "client_version": 1,
        "fleet_customer_id": 10432,
        "ims_conversion_status": "ims",
        "market_segment": "test_client",
        "name": "Engineering Test Client - UK",
        "sales_client_code": "etcuk",
        "sales_client_id": 10432,
        "sales_pipeline_type": "nightly",
    },
    "etcxl": {
        "analytics_client_code": "etcxl",
        "analytics_client_id": -105,
        "appraisals_client_id": 2063,
        "client_code": "etcxl",
        "client_version": 1,
        "fleet_customer_id": 10315,
        "ims_conversion_status": "ims",
        "market_segment": "test client",
        "name": "Engineering Test Client - XLarge",
        "sales_client_code": "etcxl",
        "sales_client_id": 10315,
        "sales_pipeline_type": "nightly",
    },
    "etcxlalloy": {
        "analytics_client_code": "etcxlalloy",
        "analytics_client_id": -116,
        "appraisals_client_id": 3112,
        "client_code": "etcxlalloy",
        "client_version": 1,
        "fleet_customer_id": 10853,
        "ims_conversion_status": "ims",
        "market_segment": "test_client",
        "name": "Engineering Test Client - XLarge Alloy DB",
        "sales_client_code": "etcxlalloy",
        "sales_client_id": 10853,
        "sales_pipeline_type": "nightly",
    },
    "etra": {
        "analytics_client_code": "etra",
        "analytics_client_id": 259,
        "appraisals_client_id": 2472,
        "client_code": "etra",
        "client_version": 1,
        "fleet_customer_id": 10514,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "East Tenn Rent-Alls",
        "sales_client_code": "etra",
        "sales_client_id": 10514,
        "sales_pipeline_type": "nightly",
    },
    "eut": {
        "analytics_client_code": "eut",
        "analytics_client_id": 506,
        "appraisals_client_id": 1497,
        "client_code": "eut",
        "client_version": 1,
        "fleet_customer_id": 10106,
        "ims_conversion_status": "ims",
        "market_segment": "Rental",
        "name": "Eutaw Construction Co Inc",
        "sales_client_code": "eut",
        "sales_client_id": 10106,
        "sales_pipeline_type": "nightly",
    },
    "ewal": {
        "analytics_client_code": "ewal",
        "analytics_client_id": 311,
        "appraisals_client_id": 1505,
        "client_code": "ewal",
        "client_version": 1,
        "fleet_customer_id": 10109,
        "ims_conversion_status": "ims",
        "market_segment": "Rental",
        "name": "Ewald Kubota",
        "sales_client_code": "ewal",
        "sales_client_id": 10109,
        "sales_pipeline_type": "nightly",
    },
    "expd": {
        "analytics_client_code": "expd",
        "analytics_client_id": 461,
        "appraisals_client_id": 2476,
        "client_code": "expd",
        "client_version": 1,
        "fleet_customer_id": 10518,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Expedition Rentals",
        "sales_client_code": "expd",
        "sales_client_id": 10518,
        "sales_pipeline_type": "nightly",
    },
    "eze": {
        "analytics_client_code": "eze",
        "analytics_client_id": 184,
        "client_code": "eze",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "EZE Rent-It Centre Ltd",
        "sales_pipeline_type": "vod",
    },
    "ezyau": {
        "analytics_client_code": "ezyau",
        "analytics_client_id": 685,
        "client_code": "ezyau",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Ezy Up Hire",
        "sales_pipeline_type": "vod",
    },
    "fab": {
        "analytics_client_code": "fab",
        "analytics_client_id": 53,
        "appraisals_client_id": 430,
        "client_code": "fab",
        "client_version": 1,
        "fleet_customer_id": 23,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "John Fabick Tractor Company",
        "primary_oem": "Caterpillar",
        "sales_client_code": "fab",
        "sales_client_id": 53,
        "sales_pipeline_type": "nightly",
    },
    "fari": {
        "analytics_client_code": "fari",
        "analytics_client_id": 435,
        "appraisals_client_id": 2478,
        "client_code": "fari",
        "client_version": 1,
        "fleet_customer_id": 10520,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Faris Machinery",
        "sales_client_code": "fari",
        "sales_client_id": 10520,
        "sales_pipeline_type": "nightly",
    },
    "fbr": {
        "analytics_client_code": "fbr",
        "analytics_client_id": 430,
        "appraisals_client_id": 1464,
        "client_code": "fbr",
        "client_version": 1,
        "fleet_customer_id": 10080,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "F\u0026B Rentals",
        "sales_client_code": "fbr",
        "sales_client_id": 10080,
        "sales_pipeline_type": "nightly",
    },
    "fec": {
        "analytics_client_code": "fec",
        "analytics_client_id": 105,
        "client_code": "fec",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Flint Equipment Company",
        "sales_pipeline_type": "nightly",
    },
    "fin": {
        "analytics_client_code": "fin",
        "analytics_client_id": 120,
        "appraisals_client_id": 523,
        "client_code": "fin",
        "client_version": 1,
        "fleet_customer_id": 91,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Finning Cat",
        "primary_oem": "caterpillar",
        "sales_client_code": "fin",
        "sales_client_id": 120,
        "sales_pipeline_type": "nightly",
    },
    "fks": {
        "analytics_client_code": "fks",
        "analytics_client_id": 170,
        "appraisals_client_id": 2392,
        "client_code": "fks",
        "client_version": 1,
        "fleet_customer_id": 10441,
        "ims_conversion_status": "ims",
        "name": "Foley Equipment Company",
        "sales_client_code": "fks",
        "sales_client_id": 170,
        "sales_pipeline_type": "nightly",
    },
    "flexau": {
        "analytics_client_code": "flexau",
        "analytics_client_id": 553,
        "appraisals_client_id": 2480,
        "client_code": "flexau",
        "client_version": 1,
        "fleet_customer_id": 10522,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Flexihire",
        "sales_client_code": "flexau",
        "sales_client_id": 10522,
        "sales_pipeline_type": "nightly",
    },
    "flr": {
        "analytics_client_code": "flr",
        "analytics_client_id": -5,
        "client_code": "flr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "FLR Rentals",
        "sales_pipeline_type": "vod",
    },
    "flwy": {
        "analytics_client_code": "flwy",
        "analytics_client_id": -500,
        "client_code": "flwy",
        "client_version": 1,
        "fleet_customer_id": 131,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Fleetway Capital Corp",
        "primary_oem": "-",
        "sales_client_code": "flwy",
        "sales_client_id": 989,
        "sales_pipeline_type": "vod",
    },
    "fme": {
        "analytics_client_code": "fme",
        "analytics_client_id": 138,
        "appraisals_client_id": 604,
        "client_code": "fme",
        "client_version": 1,
        "fleet_customer_id": 52,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Komatsu East",
        "primary_oem": "komatsu",
        "sales_client_code": "fme",
        "sales_client_id": 138,
        "sales_pipeline_type": "nightly",
    },
    "fol": {
        "analytics_client_code": "fol",
        "analytics_client_id": 37,
        "appraisals_client_id": 428,
        "client_code": "fol",
        "client_version": 1,
        "fleet_customer_id": 24,
        "ims_conversion_status": "ims",
        "name": "Foley, Inc.",
        "sales_client_code": "fol",
        "sales_client_id": 37,
        "sales_pipeline_type": "nightly",
    },
    "folu": {
        "analytics_client_code": "folu",
        "analytics_client_id": -37,
        "appraisals_client_id": 1310,
        "client_code": "folu",
        "client_version": 1,
        "fleet_customer_id": 162,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Foley Used",
        "primary_oem": "CAT",
        "sales_client_code": "folu",
        "sales_client_id": 994,
        "sales_pipeline_type": "nightly",
    },
    "fra": {
        "analytics_client_code": "fra",
        "analytics_client_id": 136,
        "client_code": "fra",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Franklin Equipment LLC",
        "sales_pipeline_type": "vod",
    },
    "fraz": {
        "analytics_client_code": "fraz",
        "analytics_client_id": 494,
        "appraisals_client_id": 1882,
        "client_code": "fraz",
        "client_version": 1,
        "fleet_customer_id": 10224,
        "ims_conversion_status": "ims",
        "name": "Fraza Group",
        "sales_client_code": "fraz",
        "sales_client_id": 10224,
        "sales_pipeline_type": "nightly",
    },
    "frcd": {
        "analytics_client_code": "frcd",
        "analytics_client_id": 589,
        "appraisals_client_id": 2477,
        "client_code": "frcd",
        "client_version": 1,
        "fleet_customer_id": 10519,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Fairchild Equipment",
        "sales_client_code": "frcd",
        "sales_client_id": 10519,
        "sales_pipeline_type": "nightly",
    },
    "frd": {
        "analytics_client_code": "frd",
        "analytics_client_id": -500,
        "client_code": "frd",
        "client_version": 1,
        "fleet_customer_id": 114,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Furukawa Rock Drill",
        "primary_oem": "-",
        "sales_client_code": "frd",
        "sales_client_id": 917,
        "sales_pipeline_type": "vod",
    },
    "friv": {
        "analytics_client_code": "friv",
        "analytics_client_id": 331,
        "appraisals_client_id": 814,
        "client_code": "friv",
        "client_version": 1,
        "fleet_customer_id": 194,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "4 Rivers Equipment",
        "primary_oem": "John Deere",
        "sales_client_code": "friv",
        "sales_client_id": 331,
        "sales_pipeline_type": "nightly",
    },
    "frse": {
        "analytics_client_code": "frse",
        "analytics_client_id": 500,
        "appraisals_client_id": 2481,
        "client_code": "frse",
        "client_version": 1,
        "fleet_customer_id": 10523,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Fresno Equipment",
        "sales_client_code": "frse",
        "sales_client_id": 10523,
        "sales_pipeline_type": "nightly",
    },
    "fse": {
        "analytics_client_code": "fse",
        "analytics_client_id": 190,
        "appraisals_client_id": 683,
        "client_code": "fse",
        "client_version": 1,
        "fleet_customer_id": 55,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Five Star Equipment",
        "primary_oem": "john deere",
        "sales_client_code": "jd_fse",
        "sales_client_id": 190,
        "sales_pipeline_type": "nightly",
    },
    "fser": {
        "analytics_client_code": "fser",
        "analytics_client_id": 460,
        "appraisals_client_id": 2479,
        "client_code": "fser",
        "client_version": 1,
        "fleet_customer_id": 10521,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "FirstSource",
        "sales_client_code": "fser",
        "sales_client_id": 10521,
        "sales_pipeline_type": "nightly",
    },
    "gccs": {
        "analytics_client_code": "gccs",
        "analytics_client_id": 502,
        "appraisals_client_id": 2490,
        "client_code": "gccs",
        "client_version": 1,
        "fleet_customer_id": 10532,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Gulf Coast Crane",
        "sales_client_code": "gccs",
        "sales_client_id": 10532,
        "sales_pipeline_type": "nightly",
    },
    "gec": {
        "analytics_client_code": "gec",
        "analytics_client_id": 223,
        "client_code": "gec",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Grand Equipment Company",
        "sales_pipeline_type": "vod",
    },
    "gec2": {
        "analytics_client_code": "gec2",
        "analytics_client_id": 376,
        "appraisals_client_id": 2486,
        "client_code": "gec2",
        "client_version": 1,
        "fleet_customer_id": 10528,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Grand Equipment (GEC2)",
        "sales_client_code": "gec2",
        "sales_client_id": 10528,
        "sales_pipeline_type": "nightly",
    },
    "gee": {
        "analytics_client_code": "gee",
        "analytics_client_id": 606,
        "appraisals_client_id": 1957,
        "client_code": "gee",
        "client_version": 1,
        "fleet_customer_id": 10265,
        "ims_conversion_status": "ims",
        "name": "Gee Heavy Equipment",
        "sales_client_code": "gee",
        "sales_client_id": 10265,
        "sales_pipeline_type": "nightly",
    },
    "gera": {
        "analytics_client_code": "gera",
        "analytics_client_id": 481,
        "appraisals_client_id": 1602,
        "client_code": "gera",
        "client_version": 1,
        "fleet_customer_id": 10166,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "General RentAll",
        "sales_client_code": "gera",
        "sales_client_id": 10166,
        "sales_pipeline_type": "nightly",
    },
    "ges": {
        "analytics_client_code": "ges",
        "analytics_client_id": 183,
        "appraisals_client_id": 727,
        "client_code": "ges",
        "client_version": 1,
        "fleet_customer_id": 216,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "General Equpment",
        "primary_oem": "Komatsu",
        "sales_client_code": "ges",
        "sales_client_id": 183,
        "sales_pipeline_type": "nightly",
    },
    "gfl": {
        "analytics_client_code": "gfl",
        "analytics_client_id": 650,
        "appraisals_client_id": 2137,
        "client_code": "gfl",
        "client_version": 1,
        "fleet_customer_id": 10341,
        "ims_conversion_status": "ims",
        "name": "GFL Environmental Services Inc",
        "sales_client_code": "gfl",
        "sales_client_id": 10341,
        "sales_pipeline_type": "nightly",
    },
    "gfr": {
        "client_code": "gfr",
        "client_version": 1,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Giffin Rentals",
        "sales_pipeline_type": "vod",
    },
    "gmer": {
        "analytics_client_code": "gmer",
        "analytics_client_id": 295,
        "appraisals_client_id": 2485,
        "client_code": "gmer",
        "client_version": 1,
        "fleet_customer_id": 10527,
        "ims_conversion_status": "ims",
        "name": "GM Equipment",
        "sales_client_code": "gmer",
        "sales_client_id": 10527,
        "sales_pipeline_type": "nightly",
    },
    "gnie": {
        "analytics_client_code": "gnie",
        "analytics_client_id": 512,
        "appraisals_client_id": 1132,
        "client_code": "gnie",
        "client_version": 1,
        "fleet_customer_id": 10062,
        "ims_conversion_status": "ims",
        "market_segment": "Financial",
        "name": "Genie Lift",
        "sales_client_code": "gnie",
        "sales_client_id": 964,
        "sales_pipeline_type": "nightly",
    },
    "gnj": {
        "analytics_client_code": "gnj",
        "analytics_client_id": 699,
        "client_code": "gnj",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "G \u0026 J Equipment Rental",
        "sales_pipeline_type": "vod",
    },
    "gonz": {
        "analytics_client_code": "gonz",
        "analytics_client_id": 347,
        "appraisals_client_id": 1059,
        "client_code": "gonz",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Gonzalez Trading",
        "sales_pipeline_type": "vod",
    },
    "goruk": {
        "analytics_client_code": "goruk",
        "analytics_client_id": 535,
        "appraisals_client_id": 2286,
        "client_code": "goruk",
        "client_version": 1,
        "fleet_customer_id": 10392,
        "ims_conversion_status": "ims",
        "name": "Gordon Bow",
        "sales_client_code": "goruk",
        "sales_client_id": 10392,
        "sales_pipeline_type": "nightly",
    },
    "gouk": {
        "analytics_client_code": "gouk",
        "analytics_client_id": 769,
        "appraisals_client_id": 2970,
        "client_code": "gouk",
        "client_version": 1,
        "fleet_customer_id": 10760,
        "ims_conversion_status": "ims",
        "name": "GoHire (Hull) Ltd",
        "sales_client_code": "gouk",
        "sales_client_id": 10760,
        "sales_pipeline_type": "nightly",
    },
    "gpc": {
        "analytics_client_code": "gpc",
        "analytics_client_id": 98,
        "appraisals_client_id": 542,
        "client_code": "gpc",
        "client_version": 1,
        "fleet_customer_id": 186,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Gregory Poole Equipment Company",
        "primary_oem": "caterpillar",
        "sales_client_code": "gpc",
        "sales_client_id": 98,
        "sales_pipeline_type": "nightly",
    },
    "gper": {
        "analytics_client_code": "gper",
        "analytics_client_id": 212,
        "appraisals_client_id": 2487,
        "client_code": "gper",
        "client_version": 1,
        "fleet_customer_id": 10529,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Great Plains Rentals",
        "sales_client_code": "gper",
        "sales_client_id": 10529,
        "sales_pipeline_type": "nightly",
    },
    "grof": {
        "analytics_client_code": "grof",
        "analytics_client_id": 213,
        "appraisals_client_id": 336,
        "client_code": "grof",
        "client_version": 1,
        "fleet_customer_id": 63,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Groff Tractor and Equipment LLC",
        "primary_oem": "case",
        "sales_client_code": "grof",
        "sales_client_id": 213,
        "sales_pipeline_type": "nightly",
    },
    "grr": {
        "analytics_client_code": "grr",
        "analytics_client_id": 465,
        "appraisals_client_id": 1409,
        "client_code": "grr",
        "client_version": 1,
        "fleet_customer_id": 204,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Giffin Rentals",
        "sales_client_code": "grr",
        "sales_client_id": 465,
        "sales_pipeline_type": "nightly",
    },
    "gsb": {
        "analytics_client_code": "gsb",
        "analytics_client_id": 195,
        "appraisals_client_id": 2483,
        "client_code": "gsb",
        "client_version": 1,
        "fleet_customer_id": 10525,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Garden State Bobcat Group",
        "sales_client_code": "gsb",
        "sales_client_id": 10525,
        "sales_pipeline_type": "nightly",
    },
    "gscr": {
        "analytics_client_code": "gscr",
        "analytics_client_id": 343,
        "appraisals_client_id": 2484,
        "client_code": "gscr",
        "client_version": 1,
        "fleet_customer_id": 10526,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "General Steel Crane",
        "sales_client_code": "gscr",
        "sales_client_id": 10526,
        "sales_pipeline_type": "nightly",
    },
    "gsec": {
        "analytics_client_code": "gsec",
        "analytics_client_id": 316,
        "appraisals_client_id": 2488,
        "client_code": "gsec",
        "client_version": 1,
        "fleet_customer_id": 10530,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Great Southern",
        "sales_client_code": "gsec",
        "sales_client_id": 10530,
        "sales_pipeline_type": "nightly",
    },
    "gta": {
        "analytics_client_code": "gta",
        "analytics_client_id": 233,
        "appraisals_client_id": 1463,
        "client_code": "gta",
        "client_version": 1,
        "fleet_customer_id": 10079,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "GTA Equipment Rentals",
        "sales_client_code": "gta",
        "sales_client_id": 10079,
        "sales_pipeline_type": "nightly",
    },
    "gtauk": {
        "analytics_client_code": "gtauk",
        "analytics_client_id": 533,
        "appraisals_client_id": 2075,
        "client_code": "gtauk",
        "client_version": 1,
        "fleet_customer_id": 10322,
        "ims_conversion_status": "ims",
        "name": "GT Access",
        "sales_client_code": "gtauk",
        "sales_client_id": 10322,
        "sales_pipeline_type": "nightly",
    },
    "gtmuk": {
        "analytics_client_code": "gtmuk",
        "analytics_client_id": 579,
        "appraisals_client_id": 2343,
        "client_code": "gtmuk",
        "client_version": 1,
        "fleet_customer_id": 10416,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "GTM Heavy Rentals",
        "sales_client_code": "gtmuk",
        "sales_client_id": 10416,
        "sales_pipeline_type": "nightly",
    },
    "gtr": {
        "analytics_client_code": "gtr",
        "analytics_client_id": 256,
        "client_code": "gtr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "George Tool Rental",
        "sales_pipeline_type": "vod",
    },
    "gwe": {
        "analytics_client_code": "gwe",
        "analytics_client_id": 298,
        "appraisals_client_id": 2482,
        "client_code": "gwe",
        "client_version": 1,
        "fleet_customer_id": 10524,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "G\u0026W Equipment",
        "sales_client_code": "gwe",
        "sales_client_id": 10524,
        "sales_pipeline_type": "nightly",
    },
    "gweq": {
        "analytics_client_code": "gweq",
        "analytics_client_id": 266,
        "appraisals_client_id": 2489,
        "client_code": "gweq",
        "client_version": 1,
        "fleet_customer_id": 10531,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Great West Equipment",
        "sales_client_code": "gweq",
        "sales_client_id": 10531,
        "sales_pipeline_type": "nightly",
    },
    "gwyuk": {
        "analytics_client_code": "gwyuk",
        "analytics_client_id": 574,
        "appraisals_client_id": 2344,
        "client_code": "gwyuk",
        "client_version": 1,
        "fleet_customer_id": 10420,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Gwynedd Forklifts Limited",
        "sales_client_code": "gwyuk",
        "sales_client_id": 10417,
        "sales_pipeline_type": "nightly",
    },
    "haml": {
        "analytics_client_code": "haml",
        "analytics_client_id": 262,
        "appraisals_client_id": 2491,
        "client_code": "haml",
        "client_version": 1,
        "fleet_customer_id": 10533,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Hamlin Equipment Rental",
        "sales_client_code": "haml",
        "sales_client_id": 10533,
        "sales_pipeline_type": "nightly",
    },
    "haug": {
        "analytics_client_code": "haug",
        "analytics_client_id": 608,
        "appraisals_client_id": 2211,
        "client_code": "haug",
        "client_version": 1,
        "fleet_customer_id": 10368,
        "ims_conversion_status": "ims",
        "name": "Haugland LLC",
        "sales_client_code": "haug",
        "sales_client_id": 10368,
        "sales_pipeline_type": "nightly",
    },
    "haw": {
        "analytics_client_code": "haw",
        "analytics_client_id": 77,
        "appraisals_client_id": 538,
        "client_code": "haw",
        "client_version": 1,
        "fleet_customer_id": 25,
        "ims_conversion_status": "ims",
        "name": "Hawthorne CAT",
        "sales_client_code": "haw",
        "sales_client_id": 77,
        "sales_pipeline_type": "nightly",
    },
    "hawk": {
        "analytics_client_code": "hawk",
        "analytics_client_id": 411,
        "appraisals_client_id": 2492,
        "client_code": "hawk",
        "client_version": 1,
        "fleet_customer_id": 10534,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Hawkins-Graves",
        "sales_client_code": "hawk",
        "sales_client_id": 10534,
        "sales_pipeline_type": "nightly",
    },
    "hbr": {
        "analytics_client_code": "hbr",
        "analytics_client_id": 137,
        "client_code": "hbr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Hull Brothers Rental",
        "sales_pipeline_type": "vod",
    },
    "hcm": {
        "analytics_client_code": "hcm",
        "analytics_client_id": -500,
        "client_code": "hcm",
        "client_version": 1,
        "fleet_customer_id": 90,
        "ims_conversion_status": "ims",
        "market_segment": "financial",
        "name": "Hitachi Construction Equipment Loaders",
        "primary_oem": "-",
        "sales_client_code": "hcm",
        "sales_client_id": 46,
        "sales_pipeline_type": "vod",
    },
    "hd": {
        "analytics_client_code": "hd",
        "analytics_client_id": 404,
        "appraisals_client_id": 2497,
        "client_code": "hd",
        "client_version": 1,
        "fleet_customer_id": 10539,
        "ims_conversion_status": "ims",
        "name": "Home Depot",
        "sales_client_code": "hd",
        "sales_client_id": 10539,
        "sales_pipeline_type": "nightly",
    },
    "hdc": {
        "analytics_client_code": "hdc",
        "analytics_client_id": 424,
        "client_code": "hdc",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Home Depot Canada",
        "sales_pipeline_type": "vod",
    },
    "hem": {
        "analytics_client_code": "he",
        "analytics_client_id": 4,
        "appraisals_client_id": 673,
        "client_code": "hem",
        "client_version": 1,
        "fleet_customer_id": 8,
        "ims_conversion_status": "ims",
        "name": "H \u0026 E",
        "sales_client_code": "he",
        "sales_client_id": 255,
        "sales_pipeline_type": "nightly",
    },
    "hg": {
        "analytics_client_code": "herc",
        "analytics_client_id": 198,
        "appraisals_client_id": 657,
        "client_code": "hg",
        "client_version": 1,
        "fleet_customer_id": 5,
        "ims_conversion_status": "ims",
        "market_segment": "National",
        "name": "Herc Equipment Rental Corp.",
        "sales_client_code": "herc",
        "sales_client_id": 5,
        "sales_pipeline_type": "nightly",
    },
    "hgi": {
        "analytics_client_code": "hgi",
        "analytics_client_id": 36,
        "appraisals_client_id": 815,
        "client_code": "hgi",
        "client_version": 1,
        "fleet_customer_id": 30,
        "ims_conversion_status": "ims",
        "name": "The Harnish Group, Inc.",
        "sales_client_code": "ncm",
        "sales_client_id": 36,
        "sales_pipeline_type": "nightly",
    },
    "hgrp": {
        "analytics_client_code": "hgrp",
        "analytics_client_id": 758,
        "appraisals_client_id": 2950,
        "client_code": "hgrp",
        "client_version": 1,
        "fleet_customer_id": 10749,
        "ims_conversion_status": "ims",
        "name": "Hall Group of Companies",
        "sales_client_code": "hgrp",
        "sales_client_id": 10749,
        "sales_pipeline_type": "nightly",
    },
    "hh": {
        "analytics_client_code": "hh",
        "analytics_client_id": 27,
        "appraisals_client_id": 286,
        "client_code": "hh",
        "client_version": 1,
        "fleet_customer_id": 163,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Hugg \u0026 Hall Equipment Company",
        "primary_oem": "volvo",
        "sales_client_code": "hh",
        "sales_client_id": 27,
        "sales_pipeline_type": "nightly",
    },
    "hhv": {
        "analytics_client_code": "hoby",
        "analytics_client_id": 438,
        "appraisals_client_id": 1356,
        "client_code": "hhv",
        "client_version": 1,
        "fleet_customer_id": 187,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Housby Heavy Equipment",
        "primary_oem": "Volvo",
        "sales_client_code": "hhv",
        "sales_client_id": 438,
        "sales_pipeline_type": "nightly",
    },
    "hill": {
        "analytics_client_code": "hill",
        "analytics_client_id": 320,
        "appraisals_client_id": 1125,
        "client_code": "hill",
        "client_version": 1,
        "fleet_customer_id": 75,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Hills Machinery",
        "primary_oem": "Case",
        "sales_client_code": "hill",
        "sales_client_id": 320,
        "sales_pipeline_type": "nightly",
    },
    "hito": {
        "analytics_client_code": "hito",
        "analytics_client_id": 342,
        "appraisals_client_id": 2495,
        "client_code": "hito",
        "client_version": 1,
        "fleet_customer_id": 10537,
        "ims_conversion_status": "ims",
        "name": "Hightower Equipment",
        "sales_client_code": "hito",
        "sales_client_id": 10537,
        "sales_pipeline_type": "nightly",
    },
    "hlc": {
        "analytics_client_code": "hlc",
        "analytics_client_id": 17,
        "appraisals_client_id": 349,
        "client_code": "hlc",
        "client_version": 1,
        "fleet_customer_id": 18,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Holt of California",
        "primary_oem": "caterpillar",
        "sales_client_code": "cat_hlc",
        "sales_client_id": 19,
        "sales_pipeline_type": "nightly",
    },
    "hlt": {
        "analytics_client_code": "hlt",
        "analytics_client_id": 30,
        "appraisals_client_id": 367,
        "client_code": "hlt",
        "client_version": 1,
        "fleet_customer_id": 26,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "HOLT Texas, Ltd",
        "primary_oem": "Caterpillar",
        "sales_client_code": "hlt",
        "sales_client_id": 30,
        "sales_pipeline_type": "nightly",
    },
    "hltc": {
        "analytics_client_code": "hltc",
        "analytics_client_id": 373,
        "appraisals_client_id": 2399,
        "client_code": "hltc",
        "client_version": 1,
        "fleet_customer_id": 10443,
        "ims_conversion_status": "ims",
        "name": "Holt Crane",
        "sales_client_code": "hltc",
        "sales_client_id": 10443,
        "sales_pipeline_type": "nightly",
    },
    "hmi": {
        "analytics_client_code": "hmi",
        "analytics_client_id": 90,
        "appraisals_client_id": 1495,
        "client_code": "hmi",
        "client_version": 1,
        "fleet_customer_id": 10105,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Heavy Machines Inc.",
        "sales_client_code": "hmi",
        "sales_client_id": 10105,
        "sales_pipeline_type": "nightly",
    },
    "hmla": {
        "analytics_client_code": "hmla",
        "analytics_client_id": 722,
        "appraisals_client_id": 2833,
        "client_code": "hmla",
        "client_version": 1,
        "fleet_customer_id": 10668,
        "ims_conversion_status": "ims",
        "name": "Hammer Equipment Services, LLC",
        "sales_client_code": "hmla",
        "sales_client_id": 10668,
        "sales_pipeline_type": "nightly",
    },
    "hndo": {
        "analytics_client_code": "hndo",
        "analytics_client_id": 479,
        "appraisals_client_id": 2193,
        "client_code": "hndo",
        "client_version": 1,
        "fleet_customer_id": 10359,
        "ims_conversion_status": "ims",
        "name": "Handy Rents",
        "sales_client_code": "hndo",
        "sales_client_id": 10359,
        "sales_pipeline_type": "nightly",
    },
    "hofe": {
        "analytics_client_code": "hofe",
        "analytics_client_id": 478,
        "appraisals_client_id": 2263,
        "client_code": "hofe",
        "client_version": 1,
        "fleet_customer_id": 10384,
        "ims_conversion_status": "ims",
        "name": "Hoffman Equipment Company",
        "sales_client_code": "hofe",
        "sales_client_id": 10384,
        "sales_pipeline_type": "nightly",
    },
    "holp": {
        "analytics_client_code": "holp",
        "analytics_client_id": 611,
        "appraisals_client_id": 2496,
        "client_code": "holp",
        "client_version": 1,
        "fleet_customer_id": 10538,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Holland Pump",
        "sales_client_code": "holp",
        "sales_client_id": 10538,
        "sales_pipeline_type": "nightly",
    },
    "honn": {
        "analytics_client_code": "honn",
        "analytics_client_id": 239,
        "appraisals_client_id": 838,
        "client_code": "honn",
        "client_version": 1,
        "fleet_customer_id": 189,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Honnen Equipment",
        "primary_oem": "john deere",
        "sales_client_code": "honn",
        "sales_client_id": 239,
        "sales_pipeline_type": "nightly",
    },
    "hop": {
        "analytics_client_code": "hop",
        "analytics_client_id": 104,
        "appraisals_client_id": 469,
        "client_code": "hop",
        "client_version": 1,
        "fleet_customer_id": 27,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "HO Penn Rents",
        "primary_oem": "Caterpillar",
        "sales_client_code": "hop",
        "sales_client_id": 104,
        "sales_pipeline_type": "nightly",
    },
    "hosk": {
        "analytics_client_code": "hosk",
        "analytics_client_id": 243,
        "client_code": "hosk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Hoskins Equipment",
        "sales_pipeline_type": "vod",
    },
    "howe": {
        "analytics_client_code": "howe",
        "analytics_client_id": 244,
        "appraisals_client_id": 2499,
        "client_code": "howe",
        "client_version": 1,
        "fleet_customer_id": 10541,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Howe Rental \u0026 Sales",
        "sales_client_code": "howe",
        "sales_client_id": 10541,
        "sales_pipeline_type": "nightly",
    },
    "hph": {
        "analytics_client_code": "hph",
        "appraisals_client_id": 1995,
        "client_code": "hph",
        "client_version": 1,
        "fleet_customer_id": 10282,
        "ims_conversion_status": "ims",
        "name": "Hirepro Holdings UK LTD",
        "sales_client_code": "hph",
        "sales_client_id": 10282,
        "sales_pipeline_type": "nightly",
    },
    "hrc": {
        "analytics_client_code": "hrc",
        "analytics_client_id": 82,
        "client_code": "hrc",
        "client_version": 1,
        "fleet_customer_id": 5,
        "ims_conversion_status": "legacy",
        "name": "Herc Rentals Canada",
        "sales_pipeline_type": "vod",
    },
    "hre": {
        "analytics_client_code": "hre",
        "analytics_client_id": 86,
        "appraisals_client_id": 1174,
        "client_code": "hre",
        "client_version": 1,
        "fleet_customer_id": 45,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "High Reach 2",
        "sales_client_code": "hre",
        "sales_client_id": 86,
        "sales_pipeline_type": "nightly",
    },
    "hre2": {
        "analytics_client_code": "hre2",
        "analytics_client_id": 389,
        "appraisals_client_id": 2494,
        "client_code": "hre2",
        "client_version": 1,
        "fleet_customer_id": 10536,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "High Reach 2 (HRE2)",
        "sales_client_code": "hre2",
        "sales_client_id": 10536,
        "sales_pipeline_type": "nightly",
    },
    "hri": {
        "analytics_client_code": "hri",
        "analytics_client_id": 198,
        "client_code": "hri",
        "client_version": 1,
        "fleet_customer_id": 5,
        "ims_conversion_status": "legacy",
        "name": "Herc Rentals",
        "sales_pipeline_type": "vod",
    },
    "hrtx": {
        "analytics_client_code": "hrtx",
        "analytics_client_id": 340,
        "appraisals_client_id": 1110,
        "client_code": "hrtx",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Heavy Equipment Rentals of Texas",
        "sales_pipeline_type": "vod",
    },
    "hsluk": {
        "analytics_client_code": "hsluk",
        "analytics_client_id": 623,
        "appraisals_client_id": 2057,
        "client_code": "hsluk",
        "client_version": 1,
        "fleet_customer_id": 10314,
        "ims_conversion_status": "ims",
        "name": "Hire \u0026 Supplies Ltd",
        "sales_client_code": "hsluk",
        "sales_client_id": 10314,
        "sales_pipeline_type": "nightly",
    },
    "hsr": {
        "analytics_client_code": "hsr",
        "appraisals_client_id": 1364,
        "client_code": "hsr",
        "client_version": 1,
        "fleet_customer_id": 10051,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "H\u0026S Rentals",
        "sales_client_code": "hsr",
        "sales_client_id": 440,
        "sales_pipeline_type": "vod",
    },
    "hssuk": {
        "analytics_client_code": "hssuk",
        "analytics_client_id": 572,
        "appraisals_client_id": 2500,
        "client_code": "hssuk",
        "client_version": 1,
        "fleet_customer_id": 10542,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "HSS ProService Limited",
        "sales_client_code": "hssuk",
        "sales_client_id": 10542,
        "sales_pipeline_type": "nightly",
    },
    "ht4hau": {
        "analytics_client_code": "ht4hau",
        "analytics_client_id": 700,
        "client_code": "ht4hau",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Height 4 Hire",
        "sales_pipeline_type": "vod",
    },
    "hub": {
        "analytics_client_code": "hub",
        "analytics_client_id": 160,
        "client_code": "hub",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Hub Equipment Company",
        "sales_pipeline_type": "vod",
    },
    "hubuk": {
        "analytics_client_code": "hubuk",
        "analytics_client_id": 697,
        "appraisals_client_id": 2362,
        "client_code": "hubuk",
        "client_version": 1,
        "fleet_customer_id": 10429,
        "ims_conversion_status": "ims",
        "name": "Hubbway Ltd",
        "sales_client_code": "hubuk",
        "sales_client_id": 10429,
        "sales_pipeline_type": "nightly",
    },
    "hudluk": {
        "analytics_client_code": "hudluk",
        "analytics_client_id": 594,
        "client_code": "hudluk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Hudson Lifting",
        "sales_pipeline_type": "vod",
    },
    "hul": {
        "analytics_client_code": "hul",
        "analytics_client_id": 276,
        "appraisals_client_id": 2493,
        "client_code": "hul",
        "client_version": 1,
        "fleet_customer_id": 10535,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Herc-U-Lift",
        "sales_client_code": "hul",
        "sales_client_id": 10535,
        "sales_pipeline_type": "nightly",
    },
    "hvy": {
        "analytics_client_code": "hvy",
        "analytics_client_id": -3,
        "client_code": "hvy",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "HVY Rentals",
        "sales_pipeline_type": "vod",
    },
    "hwy": {
        "analytics_client_code": "hwy",
        "analytics_client_id": 114,
        "appraisals_client_id": 812,
        "client_code": "hwy",
        "client_version": 1,
        "fleet_customer_id": 53,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Highway Equipment Co.",
        "primary_oem": "kobelco",
        "sales_client_code": "hwy",
        "sales_client_id": 114,
        "sales_pipeline_type": "nightly",
    },
    "icr": {
        "analytics_client_code": "icr",
        "analytics_client_id": 521,
        "appraisals_client_id": 1531,
        "client_code": "icr",
        "client_version": 1,
        "fleet_customer_id": 10121,
        "ims_conversion_status": "ims",
        "name": "Iron Capital Rentals (USA) Inc.",
        "sales_client_code": "icr",
        "sales_client_id": 10121,
        "sales_pipeline_type": "nightly",
    },
    "ift": {
        "analytics_client_code": "ift",
        "analytics_client_id": 759,
        "appraisals_client_id": 2930,
        "client_code": "ift",
        "client_version": 1,
        "fleet_customer_id": 10742,
        "ims_conversion_status": "ims",
        "name": "Integrated Financial Technologies",
        "sales_client_code": "ift",
        "sales_client_id": 10742,
        "sales_pipeline_type": "nightly",
    },
    "ihr": {
        "analytics_client_code": "ihr",
        "analytics_client_id": 193,
        "client_code": "ihr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Illini Hi-Reach",
        "sales_pipeline_type": "vod",
    },
    "ihs": {
        "analytics_client_code": "ihs",
        "analytics_client_id": 242,
        "client_code": "ihs",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "IHS Markit",
        "sales_pipeline_type": "vod",
    },
    "iii": {
        "analytics_client_code": "iii",
        "analytics_client_id": -500,
        "client_code": "iii",
        "client_version": 1,
        "fleet_customer_id": 121,
        "ims_conversion_status": "ims",
        "market_segment": "financial",
        "name": "ITOCHU International Inc.",
        "primary_oem": "-",
        "sales_client_code": "iii",
        "sales_client_id": 963,
        "sales_pipeline_type": "vod",
    },
    "instau": {
        "analytics_client_code": "instau",
        "analytics_client_id": 693,
        "client_code": "instau",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Instant Access",
        "sales_pipeline_type": "vod",
    },
    "ints": {
        "analytics_client_code": "ints",
        "analytics_client_id": 397,
        "appraisals_client_id": 2501,
        "client_code": "ints",
        "client_version": 1,
        "fleet_customer_id": 10543,
        "ims_conversion_status": "ims",
        "name": "Interstate Rentals",
        "sales_client_code": "ints",
        "sales_client_id": 10543,
        "sales_pipeline_type": "nightly",
    },
    "ircl": {
        "analytics_client_code": "ircl",
        "analytics_client_id": 727,
        "client_code": "ircl",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Ironclad Environmental Solutions",
        "sales_pipeline_type": "vod",
    },
    "iros": {
        "analytics_client_code": "iros",
        "analytics_client_id": 351,
        "client_code": "iros",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Iron Oak Services",
        "sales_pipeline_type": "vod",
    },
    "itc": {
        "analytics_client_code": "itc",
        "analytics_client_id": -500,
        "client_code": "itc",
        "client_version": 1,
        "fleet_customer_id": 119,
        "ims_conversion_status": "ims",
        "market_segment": "contractor",
        "name": "I-40 Trading",
        "primary_oem": "-",
        "sales_client_code": "itc",
        "sales_client_id": 941,
        "sales_pipeline_type": "vod",
    },
    "ite": {
        "analytics_client_code": "ite",
        "analytics_client_id": 161,
        "client_code": "ite",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Illinois Truck \u0026 Equipment",
        "sales_pipeline_type": "vod",
    },
    "jar": {
        "analytics_client_code": "jar",
        "analytics_client_id": 38,
        "appraisals_client_id": 473,
        "client_code": "jar",
        "client_version": 1,
        "fleet_customer_id": 16,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "J.A. Riggs Tractor Co.",
        "primary_oem": "caterpillar",
        "sales_client_code": "cat_jar",
        "sales_client_id": 18,
        "sales_pipeline_type": "nightly",
    },
    "jaruk": {
        "analytics_client_code": "jaruk",
        "analytics_client_id": 545,
        "appraisals_client_id": 2136,
        "client_code": "jaruk",
        "client_version": 1,
        "fleet_customer_id": 10340,
        "ims_conversion_status": "ims",
        "name": "Jarvie Plant",
        "sales_client_code": "jaruk",
        "sales_client_id": 10340,
        "sales_pipeline_type": "nightly",
    },
    "jbh": {
        "analytics_client_code": "jbh",
        "analytics_client_id": 760,
        "appraisals_client_id": 2952,
        "client_code": "jbh",
        "client_version": 1,
        "fleet_customer_id": 10750,
        "ims_conversion_status": "ims",
        "name": "JB Hunt",
        "sales_client_code": "jbh",
        "sales_client_id": 10750,
        "sales_pipeline_type": "nightly",
    },
    "jcb": {
        "analytics_client_code": "jcb",
        "analytics_client_id": -500,
        "client_code": "jcb",
        "client_version": 1,
        "fleet_customer_id": 110,
        "ims_conversion_status": "ims",
        "market_segment": "financial",
        "name": "JCB",
        "primary_oem": "jcb",
        "sales_client_code": "jcb",
        "sales_client_id": 912,
        "sales_pipeline_type": "vod",
    },
    "jck": {
        "analytics_client_code": "jck",
        "analytics_client_id": -500,
        "client_code": "jck",
        "client_version": 1,
        "fleet_customer_id": 99,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Jackson Equipment Sales",
        "primary_oem": "-",
        "sales_client_code": "jck",
        "sales_client_id": 304,
        "sales_pipeline_type": "vod",
    },
    "jdc": {
        "analytics_client_code": "jdc",
        "analytics_client_id": 513,
        "appraisals_client_id": 842,
        "client_code": "jdc",
        "client_version": 1,
        "fleet_customer_id": 10045,
        "ims_conversion_status": "ims",
        "market_segment": "financial",
        "name": "John Deere Construction \u0026 Forestry",
        "sales_client_code": "jdc",
        "sales_client_id": 309,
        "sales_pipeline_type": "nightly",
    },
    "jdf": {
        "analytics_client_code": "jdf",
        "analytics_client_id": -500,
        "client_code": "jdf",
        "client_version": 1,
        "fleet_customer_id": 122,
        "ims_conversion_status": "ims",
        "market_segment": "financial",
        "name": "John Deere Financial",
        "primary_oem": "-",
        "sales_client_code": "jdf",
        "sales_client_id": 965,
        "sales_pipeline_type": "vod",
    },
    "jes": {
        "analytics_client_code": "jes",
        "analytics_client_id": 150,
        "appraisals_client_id": 614,
        "client_code": "jes",
        "client_version": 1,
        "fleet_customer_id": 56,
        "ims_conversion_status": "ims",
        "name": "Jesco, Inc.",
        "sales_client_code": "jes",
        "sales_client_id": 150,
        "sales_pipeline_type": "nightly",
    },
    "jhl": {
        "analytics_client_code": "jhl",
        "analytics_client_id": 547,
        "appraisals_client_id": 1615,
        "client_code": "jhl",
        "client_version": 1,
        "fleet_customer_id": 10171,
        "ims_conversion_status": "ims",
        "name": "JHL Constructors Inc.",
        "sales_client_code": "jhl",
        "sales_client_id": 10171,
        "sales_pipeline_type": "nightly",
    },
    "jjc": {
        "analytics_client_code": "jjc",
        "analytics_client_id": 270,
        "appraisals_client_id": 2503,
        "client_code": "jjc",
        "client_version": 1,
        "fleet_customer_id": 10545,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "JJ Curran",
        "sales_client_code": "jjc",
        "sales_client_id": 10545,
        "sales_pipeline_type": "nightly",
    },
    "jldb": {
        "analytics_client_code": "jldb",
        "analytics_client_id": 543,
        "appraisals_client_id": 2504,
        "client_code": "jldb",
        "client_version": 1,
        "fleet_customer_id": 10546,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "JL Dobbs Crane",
        "sales_client_code": "jldb",
        "sales_client_id": 10546,
        "sales_pipeline_type": "nightly",
    },
    "jlg": {
        "analytics_client_code": "jlg",
        "analytics_client_id": 504,
        "appraisals_client_id": 971,
        "client_code": "jlg",
        "client_version": 1,
        "fleet_customer_id": 10061,
        "ims_conversion_status": "ims",
        "market_segment": "financial",
        "name": "JLG Industries, Inc.",
        "sales_client_code": "jlg",
        "sales_client_id": 961,
        "sales_pipeline_type": "nightly",
    },
    "jmc": {
        "analytics_client_code": "jmc",
        "analytics_client_id": 49,
        "client_code": "jmc",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Johnson CAT",
        "sales_pipeline_type": "vod",
    },
    "jmsuk": {
        "analytics_client_code": "jmsuk",
        "analytics_client_id": 434,
        "appraisals_client_id": 2382,
        "client_code": "jmsuk",
        "client_version": 1,
        "fleet_customer_id": 10438,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "JMS Powered Access",
        "sales_client_code": "jmsuk",
        "sales_client_id": 10438,
        "sales_pipeline_type": "nightly",
    },
    "jpruk": {
        "analytics_client_code": "jpruk",
        "analytics_client_id": 560,
        "client_code": "jpruk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "JP Vehicle Rentals",
        "sales_pipeline_type": "vod",
    },
    "jps": {
        "analytics_client_code": "jps",
        "analytics_client_id": 154,
        "client_code": "jps",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "JPS Equipment Rental LLC",
        "sales_pipeline_type": "vod",
    },
    "jpsuk": {
        "analytics_client_code": "jpsuk",
        "analytics_client_id": 482,
        "appraisals_client_id": 2027,
        "client_code": "jpsuk",
        "client_version": 1,
        "fleet_customer_id": 10295,
        "ims_conversion_status": "ims",
        "name": "JPS Platforms",
        "sales_client_code": "jpsuk",
        "sales_client_id": 10295,
        "sales_pipeline_type": "nightly",
    },
    "jpswuk": {
        "analytics_client_code": "jpswuk",
        "analytics_client_id": 598,
        "client_code": "jpswuk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "JPS Platforms - Welfare Cabins",
        "sales_pipeline_type": "vod",
    },
    "jre": {
        "analytics_client_code": "jre",
        "analytics_client_id": 135,
        "client_code": "jre",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "James River Equipment Company",
        "sales_pipeline_type": "vod",
    },
    "jre2": {
        "analytics_client_code": "jre2",
        "analytics_client_id": 520,
        "appraisals_client_id": 1771,
        "client_code": "jre2",
        "client_version": 1,
        "fleet_customer_id": 10179,
        "ims_conversion_status": "ims",
        "name": "James River Equipment",
        "sales_client_code": "jre2",
        "sales_client_id": 10179,
        "sales_pipeline_type": "nightly",
    },
    "jse": {
        "analytics_client_code": "jse",
        "analytics_client_id": -500,
        "client_code": "jse",
        "client_version": 1,
        "fleet_customer_id": 132,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Job Site Equipment Corp",
        "primary_oem": "-",
        "sales_client_code": "jse",
        "sales_client_id": 991,
        "sales_pipeline_type": "vod",
    },
    "jtbr": {
        "analytics_client_code": "jtbr",
        "analytics_client_id": 384,
        "appraisals_client_id": 2505,
        "client_code": "jtbr",
        "client_version": 1,
        "fleet_customer_id": 10547,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "JTB Rentals",
        "sales_client_code": "jtbr",
        "sales_client_id": 10547,
        "sales_pipeline_type": "nightly",
    },
    "kac": {
        "analytics_client_code": "kac",
        "analytics_client_id": 514,
        "appraisals_client_id": 677,
        "client_code": "kac",
        "client_version": 1,
        "fleet_customer_id": 10055,
        "ims_conversion_status": "ims",
        "market_segment": "financial",
        "name": "Komatsu America Corp",
        "sales_client_code": "kac",
        "sales_client_id": 847,
        "sales_pipeline_type": "nightly",
    },
    "kaige": {
        "analytics_client_code": "kaige",
        "analytics_client_id": 676,
        "client_code": "kaige",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Kaige Kubota",
        "sales_pipeline_type": "vod",
    },
    "kbs": {
        "analytics_client_code": "kbs",
        "analytics_client_id": 79,
        "appraisals_client_id": 602,
        "client_code": "kbs",
        "client_version": 1,
        "fleet_customer_id": 57,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Kirby Smith",
        "primary_oem": "komatsu",
        "sales_client_code": "kbs",
        "sales_client_id": 79,
        "sales_pipeline_type": "nightly",
    },
    "kec": {
        "analytics_client_code": "kec",
        "analytics_client_id": 97,
        "appraisals_client_id": 599,
        "client_code": "kec",
        "client_version": 1,
        "fleet_customer_id": 58,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Komatsu West",
        "primary_oem": "Komatsu",
        "sales_client_code": "kec_ksw",
        "sales_client_id": 97,
        "sales_pipeline_type": "nightly",
    },
    "key": {
        "analytics_client_code": "key",
        "analytics_client_id": 102,
        "client_code": "key",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Key Equipment",
        "sales_pipeline_type": "vod",
    },
    "kfu": {
        "client_code": "kfu",
        "client_version": 1,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "KOM - Komatsu Forklift U.S.A.",
        "primary_oem": "komatsu",
        "sales_pipeline_type": "vod",
    },
    "kie": {
        "analytics_client_code": "pks",
        "analytics_client_id": 356,
        "appraisals_client_id": 338,
        "client_code": "kie",
        "client_version": 1,
        "fleet_customer_id": 14,
        "ims_conversion_status": "ims",
        "name": "Kiewit",
        "sales_client_code": "pks",
        "sales_client_id": 14,
        "sales_pipeline_type": "nightly",
    },
    "kimp": {
        "analytics_client_code": "kimp",
        "analytics_client_id": 561,
        "appraisals_client_id": 2506,
        "client_code": "kimp",
        "client_version": 1,
        "fleet_customer_id": 10548,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Kimps Ace Hardware",
        "sales_client_code": "kimp",
        "sales_client_id": 10548,
        "sales_pipeline_type": "nightly",
    },
    "kmcf": {
        "analytics_client_code": "kmcf",
        "analytics_client_id": 338,
        "appraisals_client_id": 2507,
        "client_code": "kmcf",
        "client_version": 1,
        "fleet_customer_id": 10549,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "KMC Forklift",
        "sales_client_code": "kmcf",
        "sales_client_id": 10549,
        "sales_pipeline_type": "nightly",
    },
    "knmtjp": {
        "analytics_client_code": "knmtjp",
        "analytics_client_id": 673,
        "client_code": "knmtjp",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Kanamoto",
        "sales_pipeline_type": "vod",
    },
    "kobe": {
        "analytics_client_code": "kobe",
        "analytics_client_id": 450,
        "appraisals_client_id": 2377,
        "client_code": "kobe",
        "client_version": 1,
        "fleet_customer_id": 10436,
        "ims_conversion_status": "ims",
        "name": "Koberstein Rental and Sales Inc",
        "sales_client_code": "kobe",
        "sales_client_id": 10436,
        "sales_pipeline_type": "nightly",
    },
    "komf": {
        "analytics_client_code": "komf",
        "analytics_client_id": 391,
        "appraisals_client_id": 1281,
        "client_code": "komf",
        "client_version": 1,
        "fleet_customer_id": 156,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Komatsu Forklift",
        "primary_oem": "komatsu",
        "sales_client_code": "komf",
        "sales_client_id": 983,
        "sales_pipeline_type": "nightly",
    },
    "kphuk": {
        "analytics_client_code": "kphuk",
        "analytics_client_id": 603,
        "appraisals_client_id": 2508,
        "client_code": "kphuk",
        "client_version": 1,
        "fleet_customer_id": 10550,
        "ims_conversion_status": "ims",
        "name": "KPH Plant Hire",
        "sales_client_code": "kphuk",
        "sales_client_id": 10550,
        "sales_pipeline_type": "nightly",
    },
    "ktc": {
        "analytics_client_code": "ktc",
        "analytics_client_id": 115,
        "appraisals_client_id": 544,
        "client_code": "ktc",
        "client_version": 1,
        "fleet_customer_id": 76,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Kelly Tractor Co.",
        "primary_oem": "caterpillar",
        "sales_client_code": "ktc",
        "sales_client_id": 115,
        "sales_pipeline_type": "nightly",
    },
    "lalo": {
        "analytics_client_code": "lalo",
        "analytics_client_id": 241,
        "appraisals_client_id": 692,
        "client_code": "lalo",
        "client_version": 1,
        "fleet_customer_id": 74,
        "ims_conversion_status": "legacy",
        "market_segment": "Independent",
        "name": "LaLonde Equipment Rentals",
        "sales_client_code": "lalo",
        "sales_client_id": 241,
        "sales_pipeline_type": "nightly",
    },
    "lane": {
        "analytics_client_code": "lane",
        "analytics_client_id": -117,
        "appraisals_client_id": 2884,
        "client_code": "lane",
        "client_version": 1,
        "fleet_customer_id": 10703,
        "ims_conversion_status": "ims",
        "name": "Lane Construction Corp",
        "sales_client_code": "lane",
        "sales_client_id": 10703,
        "sales_pipeline_type": "nightly",
    },
    "lano": {
        "analytics_client_code": "lano",
        "analytics_client_id": 286,
        "client_code": "lano",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Lano Equipment",
        "sales_pipeline_type": "vod",
    },
    "late": {
        "analytics_client_code": "late",
        "analytics_client_id": 617,
        "appraisals_client_id": 2510,
        "client_code": "late",
        "client_version": 1,
        "fleet_customer_id": 10552,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "LAT Enterprises",
        "sales_client_code": "late",
        "sales_client_id": 10552,
        "sales_pipeline_type": "nightly",
    },
    "lawr": {
        "analytics_client_code": "lawr",
        "analytics_client_id": 426,
        "appraisals_client_id": 2264,
        "client_code": "lawr",
        "client_version": 1,
        "fleet_customer_id": 10385,
        "ims_conversion_status": "ims",
        "name": "Lawrence Equipment",
        "sales_client_code": "lawr",
        "sales_client_id": 10385,
        "sales_pipeline_type": "nightly",
    },
    "lcs": {
        "analytics_client_code": "lcs",
        "analytics_client_id": 277,
        "appraisals_client_id": 2118,
        "client_code": "lcs",
        "client_version": 1,
        "fleet_customer_id": 10332,
        "ims_conversion_status": "ims",
        "name": "Lincoln Supply",
        "sales_client_code": "lcs",
        "sales_client_id": 10332,
        "sales_pipeline_type": "nightly",
    },
    "ldgm": {
        "analytics_client_code": "ldgm",
        "analytics_client_id": 289,
        "client_code": "ldgm",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Location GM",
        "sales_pipeline_type": "vod",
    },
    "leg": {
        "analytics_client_code": "leg",
        "analytics_client_id": 199,
        "appraisals_client_id": 724,
        "client_code": "leg",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Legacy Equipment",
        "sales_pipeline_type": "vod",
    },
    "lep": {
        "analytics_client_code": "lep",
        "analytics_client_id": 380,
        "appraisals_client_id": 1292,
        "client_code": "lep",
        "client_version": 1,
        "fleet_customer_id": 160,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Leppo Group",
        "sales_client_code": "lep",
        "sales_client_id": 380,
        "sales_pipeline_type": "nightly",
    },
    "lepp": {
        "analytics_client_code": "lepp",
        "analytics_client_id": 294,
        "client_code": "lepp",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Leppo Group AX",
        "sales_pipeline_type": "vod",
    },
    "lew": {
        "analytics_client_code": "lew",
        "analytics_client_id": 191,
        "client_code": "lew",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Lewistown Rental",
        "sales_pipeline_type": "vod",
    },
    "lfatl": {
        "analytics_client_code": "lfatl",
        "analytics_client_id": 773,
        "client_code": "lfatl",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Lift Atlanta Inc",
        "sales_pipeline_type": "vod",
    },
    "lft1": {
        "analytics_client_code": "lft1",
        "analytics_client_id": 250,
        "appraisals_client_id": 2946,
        "client_code": "lft1",
        "client_version": 1,
        "fleet_customer_id": 10747,
        "ims_conversion_status": "ims",
        "name": "Lift One",
        "sales_client_code": "lft1",
        "sales_client_id": 10747,
        "sales_pipeline_type": "nightly",
    },
    "lgnd": {
        "analytics_client_code": "lgnd",
        "analytics_client_id": -500,
        "client_code": "lgnd",
        "client_version": 1,
        "fleet_customer_id": 124,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Legend Industries",
        "primary_oem": "-",
        "sales_client_code": "lgnd",
        "sales_client_id": 981,
        "sales_pipeline_type": "vod",
    },
    "lid": {
        "analytics_client_code": "lid",
        "analytics_client_id": 177,
        "client_code": "lid",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Location Ideal",
        "sales_pipeline_type": "vod",
    },
    "ligeuk": {
        "analytics_client_code": "ligeuk",
        "analytics_client_id": 709,
        "appraisals_client_id": 2847,
        "client_code": "ligeuk",
        "client_version": 1,
        "fleet_customer_id": 10678,
        "ims_conversion_status": "ims",
        "name": "Lifting Gear UK Ltd",
        "sales_client_code": "ligeuk",
        "sales_client_id": 10678,
        "sales_pipeline_type": "nightly",
    },
    "linc": {
        "analytics_client_code": "linc",
        "analytics_client_id": 312,
        "appraisals_client_id": 2511,
        "client_code": "linc",
        "client_version": 1,
        "fleet_customer_id": 10553,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Lift Inc",
        "sales_client_code": "linc",
        "sales_client_id": 10553,
        "sales_pipeline_type": "nightly",
    },
    "lle": {
        "analytics_client_code": "lle",
        "analytics_client_id": 226,
        "client_code": "lle",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Louisiana Lift and Equipment",
        "sales_pipeline_type": "vod",
    },
    "lmc": {
        "analytics_client_code": "lmc",
        "analytics_client_id": 64,
        "appraisals_client_id": 432,
        "client_code": "lmc",
        "client_version": 1,
        "fleet_customer_id": 28,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Louisiana CAT",
        "primary_oem": "Caterpillar",
        "sales_client_code": "lmc",
        "sales_client_id": 64,
        "sales_pipeline_type": "nightly",
    },
    "lme": {
        "analytics_client_code": "lme",
        "analytics_client_id": 236,
        "appraisals_client_id": 2513,
        "client_code": "lme",
        "client_version": 1,
        "fleet_customer_id": 10555,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Location Multi-Equipements",
        "sales_client_code": "lme",
        "sales_client_id": 10555,
        "sales_pipeline_type": "nightly",
    },
    "lndr": {
        "analytics_client_code": "lndr",
        "analytics_client_id": 113,
        "appraisals_client_id": 598,
        "client_code": "lndr",
        "client_version": 1,
        "fleet_customer_id": 218,
        "ims_conversion_status": "ims",
        "name": "Linder Industrial Machinery Company",
        "sales_client_code": "lndr",
        "sales_client_id": 113,
        "sales_pipeline_type": "nightly",
    },
    "lndr2": {
        "analytics_client_code": "lndr2",
        "analytics_client_id": 541,
        "appraisals_client_id": 2512,
        "client_code": "lndr2",
        "client_version": 1,
        "fleet_customer_id": 10554,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Linder - Kubota",
        "sales_client_code": "lndr2",
        "sales_client_id": 10554,
        "sales_pipeline_type": "nightly",
    },
    "lou": {
        "analytics_client_code": "lou",
        "analytics_client_id": 179,
        "appraisals_client_id": 796,
        "client_code": "lou",
        "client_version": 1,
        "fleet_customer_id": 164,
        "ims_conversion_status": "ims",
        "name": "LOU-TEC Group Inc",
        "sales_client_code": "lou",
        "sales_client_id": 179,
        "sales_pipeline_type": "nightly",
    },
    "lriuk": {
        "analytics_client_code": "lriuk",
        "analytics_client_id": 564,
        "client_code": "lriuk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Liftright Access",
        "sales_pipeline_type": "vod",
    },
    "lsfl": {
        "analytics_client_code": "lsfl",
        "analytics_client_id": 299,
        "client_code": "lsfl",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "LoneStar Forklift",
        "sales_pipeline_type": "vod",
    },
    "lti": {
        "analytics_client_code": "lti",
        "analytics_client_id": -500,
        "client_code": "lti",
        "client_version": 1,
        "fleet_customer_id": 126,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Lift Tech Inc.",
        "primary_oem": "-",
        "sales_client_code": "lti",
        "sales_client_id": 984,
        "sales_pipeline_type": "vod",
    },
    "ltr": {
        "analytics_client_code": "ltr",
        "analytics_client_id": 173,
        "client_code": "ltr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Location Trois-Rivieres",
        "sales_pipeline_type": "vod",
    },
    "ltr2": {
        "analytics_client_code": "ltr2",
        "analytics_client_id": 444,
        "appraisals_client_id": 2816,
        "client_code": "ltr2",
        "client_version": 1,
        "fleet_customer_id": 10651,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "LTR Location",
        "sales_client_code": "ltr2",
        "sales_client_id": 10651,
        "sales_pipeline_type": "nightly",
    },
    "lvm": {
        "analytics_client_code": "lvm",
        "analytics_client_id": 192,
        "appraisals_client_id": 1253,
        "client_code": "lvm",
        "client_version": 1,
        "fleet_customer_id": 72,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Leavitt Machinery USA",
        "sales_client_code": "lvm",
        "sales_client_id": 982,
        "sales_pipeline_type": "nightly",
    },
    "lvmc": {
        "analytics_client_code": "lvmc",
        "analytics_client_id": 196,
        "client_code": "lvmc",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Leavitt Machinery Canada",
        "sales_pipeline_type": "vod",
    },
    "lvtc": {
        "analytics_client_code": "lvtc",
        "analytics_client_id": 414,
        "appraisals_client_id": 2514,
        "client_code": "lvtc",
        "client_version": 1,
        "fleet_customer_id": 10556,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Longview Truck Center",
        "sales_client_code": "lvtc",
        "sales_client_id": 10556,
        "sales_pipeline_type": "nightly",
    },
    "lwi": {
        "analytics_client_code": "lwi",
        "analytics_client_id": 63,
        "client_code": "lwi",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Lift Works",
        "sales_pipeline_type": "vod",
    },
    "lyle": {
        "analytics_client_code": "lyle",
        "analytics_client_id": 795,
        "appraisals_client_id": 3053,
        "client_code": "lyle",
        "client_version": 1,
        "fleet_customer_id": 10837,
        "ims_conversion_status": "ims",
        "name": "Lyle Machinery",
        "sales_client_code": "lyle",
        "sales_client_id": 10837,
        "sales_pipeline_type": "nightly",
    },
    "lynuk": {
        "analytics_client_code": "lynuk",
        "analytics_client_id": 396,
        "appraisals_client_id": 2408,
        "client_code": "lynuk",
        "client_version": 1,
        "fleet_customer_id": 10449,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Lynch Plant Hire \u0026 Haulage",
        "sales_client_code": "lynuk",
        "sales_client_id": 10449,
        "sales_pipeline_type": "nightly",
    },
    "mac": {
        "analytics_client_code": "mac",
        "analytics_client_id": 14,
        "appraisals_client_id": 2521,
        "client_code": "mac",
        "client_version": 1,
        "fleet_customer_id": 10563,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "MFC Rental Services",
        "sales_client_code": "mac",
        "sales_client_id": 10563,
        "sales_pipeline_type": "nightly",
    },
    "mach": {
        "analytics_client_code": "mach",
        "analytics_client_id": 148,
        "appraisals_client_id": 2522,
        "client_code": "mach",
        "client_version": 1,
        "fleet_customer_id": 10564,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Michigan CAT",
        "sales_client_code": "mach",
        "sales_client_id": 10564,
        "sales_pipeline_type": "nightly",
    },
    "macl": {
        "analytics_client_code": "macl",
        "analytics_client_id": -500,
        "client_code": "macl",
        "client_version": 1,
        "fleet_customer_id": 129,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "MAC Leasing",
        "primary_oem": "-",
        "sales_client_code": "macl",
        "sales_client_id": 987,
        "sales_pipeline_type": "vod",
    },
    "madi": {
        "analytics_client_code": "madi",
        "analytics_client_id": 703,
        "appraisals_client_id": 2835,
        "client_code": "madi",
        "client_version": 1,
        "fleet_customer_id": 10670,
        "ims_conversion_status": "ims",
        "name": "Madison Supply",
        "sales_client_code": "madi",
        "sales_client_id": 10670,
        "sales_pipeline_type": "nightly",
    },
    "mart": {
        "analytics_client_code": "mart",
        "analytics_client_id": 364,
        "appraisals_client_id": 1104,
        "client_code": "mart",
        "client_version": 1,
        "fleet_customer_id": 201,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Martin Equipment",
        "primary_oem": "John Deere",
        "sales_client_code": "mart",
        "sales_client_id": 364,
        "sales_pipeline_type": "nightly",
    },
    "masr": {
        "analytics_client_code": "masr",
        "analytics_client_id": 362,
        "appraisals_client_id": 2518,
        "client_code": "masr",
        "client_version": 1,
        "fleet_customer_id": 10560,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Master Rental",
        "sales_client_code": "masr",
        "sales_client_id": 10560,
        "sales_pipeline_type": "nightly",
    },
    "matde": {
        "analytics_client_code": "matde",
        "analytics_client_id": 708,
        "client_code": "matde",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Mateco GmbH",
        "sales_pipeline_type": "vod",
    },
    "mawp": {
        "analytics_client_code": "mawp",
        "analytics_client_id": -500,
        "client_code": "mawp",
        "client_version": 1,
        "fleet_customer_id": 103,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "MEC Aerial Work Platforms",
        "primary_oem": "mec",
        "sales_client_code": "mawp",
        "sales_client_id": 559,
        "sales_pipeline_type": "vod",
    },
    "max": {
        "analytics_client_code": "max",
        "analytics_client_id": 219,
        "appraisals_client_id": 2273,
        "client_code": "max",
        "client_version": 1,
        "fleet_customer_id": 10388,
        "ims_conversion_status": "ims",
        "name": "Maxim",
        "sales_client_code": "max",
        "sales_client_id": 10388,
        "sales_pipeline_type": "nightly",
    },
    "mcan": {
        "analytics_client_code": "mcan",
        "analytics_client_id": 569,
        "appraisals_client_id": 1884,
        "client_code": "mcan",
        "client_version": 1,
        "fleet_customer_id": 10225,
        "ims_conversion_status": "ims",
        "name": "McCann Industries",
        "sales_client_code": "mcan",
        "sales_client_id": 10225,
        "sales_pipeline_type": "nightly",
    },
    "mccf": {
        "analytics_client_code": "mccf",
        "analytics_client_id": 425,
        "appraisals_client_id": 1905,
        "client_code": "mccf",
        "client_version": 1,
        "fleet_customer_id": 10238,
        "ims_conversion_status": "ims",
        "name": "McCoy Construction \u0026 Forestry",
        "sales_client_code": "mccf",
        "sales_client_id": 10238,
        "sales_pipeline_type": "nightly",
    },
    "mcgc": {
        "analytics_client_code": "mcgc",
        "analytics_client_id": 622,
        "appraisals_client_id": 2008,
        "client_code": "mcgc",
        "client_version": 1,
        "fleet_customer_id": 10288,
        "ims_conversion_status": "ims",
        "name": "MCG Civil",
        "sales_client_code": "mcgc",
        "sales_client_id": 10288,
        "sales_pipeline_type": "nightly",
    },
    "mcm": {
        "analytics_client_code": "mcm",
        "analytics_client_id": 217,
        "client_code": "mcm",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Mid Country Machinery",
        "sales_pipeline_type": "vod",
    },
    "mcm2": {
        "analytics_client_code": "mcm2",
        "analytics_client_id": 696,
        "appraisals_client_id": 3057,
        "client_code": "mcm2",
        "client_version": 1,
        "fleet_customer_id": 10841,
        "ims_conversion_status": "ims",
        "name": "Mid Country Machinery",
        "sales_client_code": "mcm2",
        "sales_client_id": 10841,
        "sales_pipeline_type": "nightly",
    },
    "mctc": {
        "analytics_client_code": "mctc",
        "analytics_client_id": -500,
        "client_code": "mctc",
        "client_version": 1,
        "fleet_customer_id": 102,
        "ims_conversion_status": "ims",
        "market_segment": "contractor",
        "name": "Mid Coast Transit Constructors",
        "primary_oem": "-",
        "sales_client_code": "mctc",
        "sales_client_id": 415,
        "sales_pipeline_type": "vod",
    },
    "mec": {
        "analytics_client_code": "mec",
        "analytics_client_id": 153,
        "appraisals_client_id": 633,
        "client_code": "mec",
        "client_version": 1,
        "fleet_customer_id": 4,
        "ims_conversion_status": "ims",
        "name": "Meade Equipment LLC",
        "sales_client_code": "mec",
        "sales_client_id": 153,
        "sales_pipeline_type": "nightly",
    },
    "megaau": {
        "analytics_client_code": "megaau",
        "analytics_client_id": 784,
        "client_code": "megaau",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Mega Hire",
        "sales_pipeline_type": "vod",
    },
    "mehuk": {
        "analytics_client_code": "mehuk",
        "analytics_client_id": 768,
        "appraisals_client_id": 2971,
        "client_code": "mehuk",
        "client_version": 1,
        "fleet_customer_id": 10761,
        "ims_conversion_status": "ims",
        "name": "M \u0026 R TOOL HIRE LTD",
        "sales_client_code": "mehuk",
        "sales_client_id": 10761,
        "sales_pipeline_type": "nightly",
    },
    "mer": {
        "analytics_client_code": "mer",
        "analytics_client_id": -500,
        "client_code": "mer",
        "client_version": 1,
        "fleet_customer_id": 97,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Magnolia Equipment Rental",
        "primary_oem": "-",
        "sales_client_code": "mer",
        "sales_client_id": 251,
        "sales_pipeline_type": "vod",
    },
    "mhefw": {
        "analytics_client_code": "mhefw",
        "analytics_client_id": 739,
        "appraisals_client_id": 2880,
        "client_code": "mhefw",
        "client_version": 1,
        "fleet_customer_id": 10700,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Fort Wayne Materials Handling",
        "sales_client_code": "mhefw",
        "sales_client_id": 10700,
        "sales_pipeline_type": "nightly",
    },
    "mhi": {
        "analytics_client_code": "mhi",
        "analytics_client_id": 544,
        "client_code": "mhi",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Material Handling Inc - Softbase",
        "sales_pipeline_type": "vod",
    },
    "mhi2": {
        "analytics_client_code": "mhi2",
        "analytics_client_id": 575,
        "appraisals_client_id": 2016,
        "client_code": "mhi2",
        "client_version": 1,
        "fleet_customer_id": 10292,
        "ims_conversion_status": "ims",
        "name": "Material Handling Inc",
        "sales_client_code": "mhi2",
        "sales_client_id": 10292,
        "sales_pipeline_type": "nightly",
    },
    "mhmuk": {
        "analytics_client_code": "mhmuk",
        "analytics_client_id": 734,
        "appraisals_client_id": 2869,
        "client_code": "mhmuk",
        "client_version": 1,
        "fleet_customer_id": 10686,
        "ims_conversion_status": "ims",
        "name": "MHM Plant Group Ltd",
        "sales_client_code": "mhmuk",
        "sales_client_id": 10686,
        "sales_pipeline_type": "nightly",
    },
    "mhr": {
        "analytics_client_code": "mhr",
        "analytics_client_id": 159,
        "client_code": "mhr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Midwest High Reach",
        "sales_pipeline_type": "vod",
    },
    "midc": {
        "analytics_client_code": "midc",
        "analytics_client_id": 485,
        "appraisals_client_id": 2523,
        "client_code": "midc",
        "client_version": 1,
        "fleet_customer_id": 10565,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Mid Columbia Forklift",
        "sales_client_code": "midc",
        "sales_client_id": 10565,
        "sales_pipeline_type": "nightly",
    },
    "milt": {
        "analytics_client_code": "milt",
        "analytics_client_id": 158,
        "appraisals_client_id": 751,
        "client_code": "milt",
        "client_version": 1,
        "fleet_customer_id": 2,
        "ims_conversion_status": "ims",
        "name": "Milton CAT",
        "sales_client_code": "milt",
        "sales_client_id": 158,
        "sales_pipeline_type": "nightly",
    },
    "mkhi": {
        "analytics_client_code": "mkhi",
        "analytics_client_id": 395,
        "appraisals_client_id": 2525,
        "client_code": "mkhi",
        "client_version": 1,
        "fleet_customer_id": 10567,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "MK Equipment",
        "sales_client_code": "mkhi",
        "sales_client_id": 10567,
        "sales_pipeline_type": "nightly",
    },
    "mlec": {
        "analytics_client_code": "mlec",
        "analytics_client_id": 208,
        "appraisals_client_id": 2520,
        "client_code": "mlec",
        "client_version": 1,
        "fleet_customer_id": 10562,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "McClung-Logan",
        "sales_client_code": "mlec",
        "sales_client_id": 10562,
        "sales_pipeline_type": "nightly",
    },
    "mltr": {
        "analytics_client_code": "mltr",
        "analytics_client_id": 314,
        "appraisals_client_id": 1959,
        "client_code": "mltr",
        "client_version": 1,
        "fleet_customer_id": 10266,
        "ims_conversion_status": "ims",
        "name": "Milton Rents",
        "sales_client_code": "mltr",
        "sales_client_id": 10266,
        "sales_pipeline_type": "nightly",
    },
    "mm": {
        "analytics_client_code": "mm",
        "analytics_client_id": 210,
        "appraisals_client_id": 803,
        "client_code": "mm",
        "client_version": 1,
        "fleet_customer_id": 199,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Modern Machinery",
        "primary_oem": "Komatsu",
        "sales_client_code": "mm",
        "sales_client_id": 210,
        "sales_pipeline_type": "nightly",
    },
    "mmc": {
        "analytics_client_code": "mmc",
        "analytics_client_id": 225,
        "appraisals_client_id": 2516,
        "client_code": "mmc",
        "client_version": 1,
        "fleet_customer_id": 10558,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "MacAllister Machinery - Indiana Cat",
        "sales_client_code": "mmc",
        "sales_client_id": 10558,
        "sales_pipeline_type": "nightly",
    },
    "mnluk": {
        "analytics_client_code": "mnluk",
        "analytics_client_id": 490,
        "client_code": "mnluk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Mainline",
        "sales_pipeline_type": "vod",
    },
    "mood": {
        "analytics_client_code": "mood",
        "analytics_client_id": 416,
        "appraisals_client_id": 2509,
        "client_code": "mood",
        "client_version": 1,
        "fleet_customer_id": 10551,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Lansdowne-Moody",
        "sales_client_code": "mood",
        "sales_client_id": 10551,
        "sales_pipeline_type": "nightly",
    },
    "mpg": {
        "analytics_client_code": "mpg",
        "analytics_client_id": -500,
        "client_code": "mpg",
        "client_version": 1,
        "fleet_customer_id": 113,
        "ims_conversion_status": "ims",
        "market_segment": "contractor",
        "name": "MPG Companies",
        "primary_oem": "-",
        "sales_client_code": "mpg",
        "sales_client_id": 916,
        "sales_pipeline_type": "vod",
    },
    "mphuk": {
        "analytics_client_code": "mphuk",
        "analytics_client_id": 487,
        "client_code": "mphuk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Mr Plant Hire",
        "sales_pipeline_type": "vod",
    },
    "mrcrn": {
        "analytics_client_code": "mrcrn",
        "analytics_client_id": 670,
        "appraisals_client_id": 2528,
        "client_code": "mrcrn",
        "client_version": 1,
        "fleet_customer_id": 10570,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Mr.Crane \u0026 Inquipco",
        "sales_client_code": "mrcrn",
        "sales_client_id": 10570,
        "sales_pipeline_type": "nightly",
    },
    "mrga": {
        "analytics_client_code": "mrga",
        "analytics_client_id": 718,
        "appraisals_client_id": 2818,
        "client_code": "mrga",
        "client_version": 1,
        "fleet_customer_id": 10653,
        "ims_conversion_status": "ims",
        "name": "M\u0026R Rental",
        "sales_client_code": "mrga",
        "sales_client_id": 10653,
        "sales_pipeline_type": "nightly",
    },
    "mro": {
        "analytics_client_code": "mro",
        "analytics_client_id": 8,
        "client_code": "mro",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Rental One",
        "sales_pipeline_type": "vod",
    },
    "mrs": {
        "analytics_client_code": "mrs",
        "analytics_client_id": 166,
        "appraisals_client_id": 2524,
        "client_code": "mrs",
        "client_version": 1,
        "fleet_customer_id": 10566,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Midway Rentals and Sales",
        "sales_client_code": "mrs",
        "sales_client_id": 166,
        "sales_pipeline_type": "nightly",
    },
    "mscsuk": {
        "analytics_client_code": "mscsuk",
        "analytics_client_id": -113,
        "appraisals_client_id": 3001,
        "client_code": "mscsuk",
        "client_version": 1,
        "fleet_customer_id": 10771,
        "ims_conversion_status": "ims",
        "name": "Mascus Test Client",
        "sales_client_code": "mscsuk",
        "sales_client_id": 10771,
        "sales_pipeline_type": "nightly",
    },
    "mson": {
        "analytics_client_code": "mson",
        "analytics_client_id": 666,
        "appraisals_client_id": 2376,
        "client_code": "mson",
        "client_version": 1,
        "fleet_customer_id": 10435,
        "ims_conversion_status": "ims",
        "name": "Morrison Industrial",
        "sales_client_code": "mson",
        "sales_client_id": 10435,
        "sales_pipeline_type": "nightly",
    },
    "msrnl": {
        "analytics_client_code": "msrnl",
        "analytics_client_id": 763,
        "client_code": "msrnl",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "MSR Verhuur",
        "sales_pipeline_type": "vod",
    },
    "mtc": {
        "appraisals_client_id": 1306,
        "client_code": "mtc",
        "client_version": 1,
        "fleet_customer_id": 10066,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "MTC Equipment",
        "sales_client_code": "mtc",
        "sales_client_id": 993,
        "sales_pipeline_type": "vod",
    },
    "mtcr": {
        "analytics_client_code": "mtcr",
        "analytics_client_id": 353,
        "appraisals_client_id": 2527,
        "client_code": "mtcr",
        "client_version": 1,
        "fleet_customer_id": 10569,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Mountain Crane Service",
        "sales_client_code": "mtcr",
        "sales_client_id": 10569,
        "sales_pipeline_type": "nightly",
    },
    "mtcy": {
        "analytics_client_code": "mtcy",
        "analytics_client_id": 752,
        "appraisals_client_id": 2922,
        "client_code": "mtcy",
        "client_version": 1,
        "fleet_customer_id": 10738,
        "ims_conversion_status": "ims",
        "name": "Motor City Rental \u0026 Sales",
        "sales_client_code": "mtcy",
        "sales_client_id": 10738,
        "sales_pipeline_type": "nightly",
    },
    "mtec": {
        "analytics_client_code": "mtec",
        "analytics_client_id": 264,
        "appraisals_client_id": 877,
        "client_code": "mtec",
        "client_version": 1,
        "fleet_customer_id": 202,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Murphy Tractor",
        "primary_oem": "John Deere",
        "sales_client_code": "mtec",
        "sales_client_id": 264,
        "sales_pipeline_type": "nightly",
    },
    "mtnnc": {
        "analytics_client_code": "mtnnc",
        "analytics_client_id": 778,
        "client_code": "mtnnc",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Mountain Rentals",
        "sales_pipeline_type": "vod",
    },
    "mumn": {
        "analytics_client_code": "mumn",
        "analytics_client_id": 349,
        "appraisals_client_id": 1455,
        "client_code": "mumn",
        "client_version": 1,
        "fleet_customer_id": 10028,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Minuteman Rentals",
        "sales_client_code": "mumn",
        "sales_client_id": 10028,
        "sales_pipeline_type": "nightly",
    },
    "mumn2": {
        "analytics_client_code": "mumn2",
        "analytics_client_id": 358,
        "client_code": "mumn2",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Minuteman Rentals 2",
        "sales_pipeline_type": "vod",
    },
    "mus": {
        "analytics_client_code": "mus",
        "analytics_client_id": 70,
        "appraisals_client_id": 732,
        "client_code": "mus",
        "client_version": 1,
        "fleet_customer_id": 29,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Mustang CAT",
        "primary_oem": "caterpillar",
        "sales_client_code": "mus",
        "sales_client_id": 70,
        "sales_pipeline_type": "nightly",
    },
    "mwtx": {
        "analytics_client_code": "mwtx",
        "analytics_client_id": 522,
        "appraisals_client_id": 1586,
        "client_code": "mwtx",
        "client_version": 1,
        "fleet_customer_id": 10152,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "MW Rentals",
        "sales_client_code": "mwtx",
        "sales_client_id": 10152,
        "sales_pipeline_type": "nightly",
    },
    "mzzt": {
        "analytics_client_code": "mzzt",
        "analytics_client_id": 590,
        "appraisals_client_id": 2519,
        "client_code": "mzzt",
        "client_version": 1,
        "fleet_customer_id": 10561,
        "ims_conversion_status": "ims",
        "name": "Mazzotta Rentals",
        "sales_client_code": "mzzt",
        "sales_client_id": 10561,
        "sales_pipeline_type": "nightly",
    },
    "nashau": {
        "analytics_client_code": "nashau",
        "analytics_client_id": 664,
        "client_code": "nashau",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Nash Hire",
        "sales_pipeline_type": "vod",
    },
    "nbt": {
        "analytics_client_code": "nbt",
        "analytics_client_id": 60,
        "client_code": "nbt",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Noble Iron Texas",
        "sales_pipeline_type": "vod",
    },
    "nclt": {
        "analytics_client_code": "nclt",
        "analytics_client_id": 691,
        "appraisals_client_id": 2371,
        "client_code": "nclt",
        "client_version": 1,
        "fleet_customer_id": 10434,
        "ims_conversion_status": "ims",
        "name": "North Coast Lift Truck",
        "sales_client_code": "nclt",
        "sales_client_id": 10434,
        "sales_pipeline_type": "nightly",
    },
    "ndt": {
        "analytics_client_code": "ndt",
        "analytics_client_id": 527,
        "appraisals_client_id": 1582,
        "client_code": "ndt",
        "client_version": 1,
        "fleet_customer_id": 10150,
        "ims_conversion_status": "ims",
        "market_segment": "other",
        "name": "NDT Japan",
        "sales_client_code": "ndt",
        "sales_client_id": 10150,
        "sales_pipeline_type": "nightly",
    },
    "ned": {
        "analytics_client_code": "ned",
        "analytics_client_id": 486,
        "appraisals_client_id": 1819,
        "client_code": "ned",
        "client_version": 1,
        "fleet_customer_id": 10205,
        "ims_conversion_status": "ims",
        "name": "National Equipment Dealers",
        "sales_client_code": "ned",
        "sales_client_id": 10205,
        "sales_pipeline_type": "nightly",
    },
    "nes": {
        "analytics_client_code": "nes",
        "analytics_client_id": 5,
        "client_code": "nes",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "NES",
        "sales_pipeline_type": "vod",
    },
    "newm": {
        "analytics_client_code": "newm",
        "analytics_client_id": 628,
        "appraisals_client_id": 2531,
        "client_code": "newm",
        "client_version": 1,
        "fleet_customer_id": 10573,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Newman Tractor",
        "sales_client_code": "newm",
        "sales_client_id": 10573,
        "sales_pipeline_type": "nightly",
    },
    "nexc": {
        "analytics_client_code": "nexc",
        "analytics_client_id": 618,
        "appraisals_client_id": 2532,
        "client_code": "nexc",
        "client_version": 1,
        "fleet_customer_id": 10574,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "NexGen Crane",
        "sales_client_code": "nexc",
        "sales_client_id": 10574,
        "sales_pipeline_type": "nightly",
    },
    "nhmh": {
        "analytics_client_code": "nhmh",
        "analytics_client_id": 317,
        "appraisals_client_id": 2530,
        "client_code": "nhmh",
        "client_version": 1,
        "fleet_customer_id": 10572,
        "ims_conversion_status": "ims",
        "name": "Naumann Hobbs",
        "sales_client_code": "nhmh",
        "sales_client_id": 10572,
        "sales_pipeline_type": "nightly",
    },
    "nmc": {
        "analytics_client_code": "nmc",
        "analytics_client_id": 85,
        "appraisals_client_id": 540,
        "client_code": "nmc",
        "client_version": 1,
        "fleet_customer_id": 10033,
        "ims_conversion_status": "ims",
        "name": "NMC Cat Rental",
        "sales_client_code": "nmc",
        "sales_client_id": 85,
        "sales_pipeline_type": "nightly",
    },
    "nmll": {
        "analytics_client_code": "nmll",
        "analytics_client_id": 619,
        "appraisals_client_id": 1985,
        "client_code": "nmll",
        "client_version": 1,
        "fleet_customer_id": 10276,
        "ims_conversion_status": "ims",
        "name": "North Mill Equipment Finance LLC",
        "sales_client_code": "nmll",
        "sales_client_id": 10276,
        "sales_pipeline_type": "nightly",
    },
    "nob": {
        "analytics_client_code": "nob",
        "analytics_client_id": 57,
        "client_code": "nob",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Noble Iron",
        "sales_pipeline_type": "vod",
    },
    "nor": {
        "analytics_client_code": "nor",
        "analytics_client_id": 35,
        "client_code": "nor",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Nortrax, Inc.",
        "sales_pipeline_type": "vod",
    },
    "norc": {
        "analytics_client_code": "norc",
        "analytics_client_id": 163,
        "client_code": "norc",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Nortrax Canada Inc.",
        "sales_pipeline_type": "vod",
    },
    "norv": {
        "analytics_client_code": "norv",
        "analytics_client_id": 524,
        "client_code": "norv",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "NorVal",
        "sales_pipeline_type": "vod",
    },
    "nr": {
        "analytics_client_code": "nr",
        "analytics_client_id": 3,
        "client_code": "nr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Neff Metrics",
        "sales_pipeline_type": "vod",
    },
    "nsci": {
        "analytics_client_code": "nsci",
        "analytics_client_id": 237,
        "appraisals_client_id": 806,
        "client_code": "nsci",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Norris Sales Company, Inc.",
        "sales_pipeline_type": "vod",
    },
    "nsknjp": {
        "analytics_client_code": "nsknjp",
        "analytics_client_id": 675,
        "client_code": "nsknjp",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Nishiken",
        "sales_pipeline_type": "vod",
    },
    "nstr": {
        "analytics_client_code": "nstr",
        "analytics_client_id": 653,
        "appraisals_client_id": 2534,
        "client_code": "nstr",
        "client_version": 1,
        "fleet_customer_id": 10576,
        "ims_conversion_status": "ims",
        "name": "Northside Tool Rental",
        "sales_client_code": "nstr",
        "sales_client_id": 10576,
        "sales_pipeline_type": "nightly",
    },
    "nts": {
        "analytics_client_code": "nts",
        "analytics_client_id": 433,
        "appraisals_client_id": 2529,
        "client_code": "nts",
        "client_version": 1,
        "fleet_customer_id": 10571,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "National Trench Safety",
        "sales_client_code": "nts",
        "sales_client_id": 10571,
        "sales_pipeline_type": "nightly",
    },
    "oak": {
        "analytics_client_code": "oak",
        "analytics_client_id": 321,
        "appraisals_client_id": 1579,
        "client_code": "oak",
        "client_version": 1,
        "fleet_customer_id": 10148,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Oaken Equipment",
        "sales_client_code": "oak",
        "sales_client_id": 10148,
        "sales_pipeline_type": "nightly",
    },
    "ocr": {
        "analytics_client_code": "ocr",
        "analytics_client_id": 127,
        "client_code": "ocr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "WesternOne OCR",
        "sales_pipeline_type": "vod",
    },
    "ohau": {
        "analytics_client_code": "ohau",
        "analytics_client_id": 261,
        "appraisals_client_id": 2538,
        "client_code": "ohau",
        "client_version": 1,
        "fleet_customer_id": 10580,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Orange Hire",
        "sales_client_code": "ohau",
        "sales_client_id": 10580,
        "sales_pipeline_type": "nightly",
    },
    "ohc": {
        "analytics_client_code": "ohc",
        "analytics_client_id": 42,
        "appraisals_client_id": 374,
        "client_code": "ohc",
        "client_version": 1,
        "fleet_customer_id": 31,
        "ims_conversion_status": "ims",
        "name": "Ohio Machinery Co.",
        "sales_client_code": "ohc",
        "sales_client_id": 42,
        "sales_pipeline_type": "nightly",
    },
    "ohrr": {
        "analytics_client_code": "ohrr",
        "analytics_client_id": 427,
        "appraisals_client_id": 2535,
        "client_code": "ohrr",
        "client_version": 1,
        "fleet_customer_id": 10577,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Ohio High Reach",
        "sales_client_code": "ohrr",
        "sales_client_id": 10577,
        "sales_pipeline_type": "nightly",
    },
    "okie": {
        "analytics_client_code": "okie",
        "analytics_client_id": 640,
        "appraisals_client_id": 2131,
        "client_code": "okie",
        "client_version": 1,
        "fleet_customer_id": 10338,
        "ims_conversion_status": "ims",
        "name": "Okie Rents",
        "sales_client_code": "okie",
        "sales_client_id": 10338,
        "sales_pipeline_type": "nightly",
    },
    "on1r": {
        "analytics_client_code": "on1r",
        "analytics_client_id": 466,
        "appraisals_client_id": 2536,
        "client_code": "on1r",
        "client_version": 1,
        "fleet_customer_id": 10578,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Only 1 Rentals",
        "sales_client_code": "on1r",
        "sales_client_id": 10578,
        "sales_pipeline_type": "nightly",
    },
    "one": {
        "analytics_client_code": "one",
        "analytics_client_id": 25,
        "client_code": "one",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "One Source",
        "sales_pipeline_type": "vod",
    },
    "opfx": {
        "analytics_client_code": "opfx",
        "analytics_client_id": 554,
        "appraisals_client_id": 2537,
        "client_code": "opfx",
        "client_version": 1,
        "fleet_customer_id": 10579,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Opifex",
        "sales_client_code": "opfx",
        "sales_client_id": 10579,
        "sales_pipeline_type": "nightly",
    },
    "orbiuk": {
        "analytics_client_code": "orbiuk",
        "analytics_client_id": 770,
        "appraisals_client_id": 2967,
        "client_code": "orbiuk",
        "client_version": 1,
        "fleet_customer_id": 10757,
        "ims_conversion_status": "ims",
        "name": "Orbital Equipment Ltd",
        "sales_client_code": "orbiuk",
        "sales_client_id": 10757,
        "sales_pipeline_type": "nightly",
    },
    "ore": {
        "analytics_client_code": "ore",
        "analytics_client_id": 211,
        "appraisals_client_id": -500,
        "client_code": "ore",
        "client_version": 1,
        "fleet_customer_id": 95,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "ORE Inc.",
        "primary_oem": "-",
        "sales_client_code": "ore",
        "sales_client_id": 211,
        "sales_pipeline_type": "nightly",
    },
    "ors": {
        "analytics_client_code": "ors",
        "analytics_client_id": 103,
        "client_code": "ors",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Ontario Rental and Supply",
        "sales_pipeline_type": "vod",
    },
    "ostpuk": {
        "analytics_client_code": "ostpuk",
        "analytics_client_id": 735,
        "appraisals_client_id": 2870,
        "client_code": "ostpuk",
        "client_version": 1,
        "fleet_customer_id": 10687,
        "ims_conversion_status": "ims",
        "name": "OneStop Access Scotland Ltd",
        "sales_client_code": "ostpuk",
        "sales_client_id": 10687,
        "sales_pipeline_type": "nightly",
    },
    "pat": {
        "analytics_client_code": "pat",
        "analytics_client_id": 31,
        "client_code": "pat",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Altorfer Industries, Inc.",
        "sales_pipeline_type": "vod",
    },
    "pdq": {
        "analytics_client_code": "pdq",
        "analytics_client_id": 45,
        "appraisals_client_id": 1447,
        "client_code": "pdq",
        "client_version": 1,
        "fleet_customer_id": 10017,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "PDQ Equipment Rentals, LLC",
        "sales_client_code": "pdq",
        "sales_client_id": 10017,
        "sales_pipeline_type": "nightly",
    },
    "peco": {
        "analytics_client_code": "peco",
        "analytics_client_id": 207,
        "appraisals_client_id": 1605,
        "client_code": "peco",
        "client_version": 1,
        "fleet_customer_id": 10167,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Power Equipment Company",
        "primary_oem": "Volvo",
        "sales_client_code": "peco",
        "sales_client_id": 10167,
        "sales_pipeline_type": "nightly",
    },
    "peqp": {
        "analytics_client_code": "peqp",
        "analytics_client_id": 719,
        "appraisals_client_id": 2817,
        "client_code": "peqp",
        "client_version": 1,
        "fleet_customer_id": 10652,
        "ims_conversion_status": "ims",
        "name": "Performance Equipment",
        "sales_client_code": "peqp",
        "sales_client_id": 10652,
        "sales_pipeline_type": "nightly",
    },
    "per": {
        "analytics_client_code": "per",
        "analytics_client_id": 134,
        "appraisals_client_id": 1115,
        "client_code": "per",
        "client_version": 1,
        "fleet_customer_id": 208,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Partner Rentals",
        "sales_client_code": "per",
        "sales_client_id": 134,
        "sales_pipeline_type": "nightly",
    },
    "perc": {
        "analytics_client_code": "perc",
        "analytics_client_id": 360,
        "appraisals_client_id": 2539,
        "client_code": "perc",
        "client_version": 1,
        "fleet_customer_id": 10581,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Perco Rentals",
        "sales_client_code": "perc",
        "sales_client_id": 10581,
        "sales_pipeline_type": "nightly",
    },
    "pet": {
        "analytics_client_code": "pet",
        "analytics_client_id": 69,
        "appraisals_client_id": 415,
        "client_code": "pet",
        "client_version": 1,
        "fleet_customer_id": 1,
        "ims_conversion_status": "ims",
        "name": "Peterson CAT",
        "sales_client_code": "cat_pet",
        "sales_client_id": 17,
        "sales_pipeline_type": "nightly",
    },
    "pfruk": {
        "analytics_client_code": "pfruk",
        "analytics_client_id": 538,
        "appraisals_client_id": 2540,
        "client_code": "pfruk",
        "client_version": 1,
        "fleet_customer_id": 10582,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Plantforce",
        "sales_client_code": "pfruk",
        "sales_client_id": 10582,
        "sales_pipeline_type": "nightly",
    },
    "pih": {
        "analytics_client_code": "pih",
        "analytics_client_id": 528,
        "appraisals_client_id": 1580,
        "client_code": "pih",
        "client_version": 1,
        "fleet_customer_id": 10149,
        "ims_conversion_status": "ims",
        "name": "Phillips Infrastructure Holdings",
        "sales_client_code": "pih",
        "sales_client_id": 10149,
        "sales_pipeline_type": "nightly",
    },
    "pkes": {
        "analytics_client_code": "pkes",
        "analytics_client_id": 592,
        "appraisals_client_id": 1926,
        "client_code": "pkes",
        "client_version": 1,
        "fleet_customer_id": 10253,
        "ims_conversion_status": "ims",
        "name": "Park East Sales",
        "sales_client_code": "pkes",
        "sales_client_id": 10253,
        "sales_pipeline_type": "nightly",
    },
    "pltouk": {
        "analytics_client_code": "pltouk",
        "analytics_client_id": 687,
        "appraisals_client_id": 2352,
        "client_code": "pltouk",
        "client_version": 1,
        "fleet_customer_id": 10423,
        "ims_conversion_status": "ims",
        "name": "Plantool Ltd",
        "sales_client_code": "pltouk",
        "sales_client_id": 10423,
        "sales_pipeline_type": "nightly",
    },
    "pm": {
        "analytics_client_code": "pm",
        "analytics_client_id": 13,
        "appraisals_client_id": 529,
        "client_code": "pm",
        "client_version": 1,
        "fleet_customer_id": 159,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Puckett Machinery, Inc.",
        "primary_oem": "caterpillar",
        "sales_client_code": "pm",
        "sales_client_id": 601,
        "sales_pipeline_type": "nightly",
    },
    "pmc": {
        "analytics_client_code": "pmc",
        "analytics_client_id": 78,
        "appraisals_client_id": 601,
        "client_code": "pmc",
        "client_version": 1,
        "fleet_customer_id": 59,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Power Motive Corp",
        "primary_oem": "komatsu",
        "sales_client_code": "pmc",
        "sales_client_id": 78,
        "sales_pipeline_type": "nightly",
    },
    "pmh": {
        "analytics_client_code": "pmh",
        "analytics_client_id": 258,
        "appraisals_client_id": 2542,
        "client_code": "pmh",
        "client_version": 1,
        "fleet_customer_id": 10584,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Puckett Heavy Machinery, Inc.",
        "sales_client_code": "pmh",
        "sales_client_id": 10584,
        "sales_pipeline_type": "nightly",
    },
    "pora": {
        "analytics_client_code": "pora",
        "analytics_client_id": 525,
        "appraisals_client_id": 2272,
        "client_code": "pora",
        "client_version": 1,
        "fleet_customer_id": 10356,
        "ims_conversion_status": "ims",
        "name": "Portland Rent-All",
        "sales_client_code": "pora",
        "sales_client_id": 10356,
        "sales_pipeline_type": "nightly",
    },
    "prch": {
        "analytics_client_code": "prch",
        "analytics_client_id": 629,
        "client_code": "prch",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Gear Up Rentals",
        "sales_pipeline_type": "vod",
    },
    "prer": {
        "analytics_client_code": "prer",
        "analytics_client_id": 271,
        "client_code": "prer",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Power Rents",
        "sales_pipeline_type": "vod",
    },
    "pri": {
        "analytics_client_code": "pri",
        "analytics_client_id": 73,
        "client_code": "pri",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Priority Equipment Rental",
        "sales_pipeline_type": "vod",
    },
    "primo": {
        "analytics_client_code": "primo",
        "analytics_client_id": 793,
        "appraisals_client_id": 3049,
        "client_code": "primo",
        "client_version": 1,
        "fleet_customer_id": 10835,
        "ims_conversion_status": "ims",
        "name": "Primoris Services Corporation",
        "sales_client_code": "primo",
        "sales_client_id": 10835,
        "sales_pipeline_type": "nightly",
    },
    "psm": {
        "analytics_client_code": "psm",
        "analytics_client_id": 249,
        "client_code": "psm",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Prosource Machinery",
        "sales_pipeline_type": "vod",
    },
    "pstr": {
        "analytics_client_code": "pstr",
        "analytics_client_id": 330,
        "appraisals_client_id": 2541,
        "client_code": "pstr",
        "client_version": 1,
        "fleet_customer_id": 10583,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Pro Star Rental",
        "sales_client_code": "pstr",
        "sales_client_id": 10583,
        "sales_pipeline_type": "nightly",
    },
    "qco": {
        "analytics_client_code": "qco",
        "analytics_client_id": 21,
        "appraisals_client_id": 678,
        "client_code": "qco",
        "client_version": 1,
        "fleet_customer_id": 32,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "The Quinn Group, Inc.",
        "primary_oem": "caterpillar",
        "sales_client_code": "qco",
        "sales_client_id": 21,
        "sales_pipeline_type": "nightly",
    },
    "qflau": {
        "analytics_client_code": "qflau",
        "analytics_client_id": 671,
        "appraisals_client_id": 2543,
        "client_code": "qflau",
        "client_version": 1,
        "fleet_customer_id": 10585,
        "ims_conversion_status": "ims",
        "name": "Queensland Forklifts",
        "sales_client_code": "qflau",
        "sales_client_id": 10585,
        "sales_pipeline_type": "nightly",
    },
    "qtum": {
        "analytics_client_code": "qtum",
        "analytics_client_id": 704,
        "client_code": "qtum",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Quantum Lift",
        "sales_pipeline_type": "vod",
    },
    "quaau": {
        "analytics_client_code": "quaau",
        "analytics_client_id": 652,
        "appraisals_client_id": 2544,
        "client_code": "quaau",
        "client_version": 1,
        "fleet_customer_id": 10586,
        "ims_conversion_status": "ims",
        "name": "Quick Access",
        "sales_client_code": "quaau",
        "sales_client_id": 10586,
        "sales_pipeline_type": "nightly",
    },
    "rab": {
        "analytics_client_code": "rab",
        "analytics_client_id": 186,
        "appraisals_client_id": 1885,
        "client_code": "rab",
        "client_version": 1,
        "fleet_customer_id": 10226,
        "ims_conversion_status": "ims",
        "name": "Rabern Rentals",
        "sales_client_code": "rab",
        "sales_client_id": 10226,
        "sales_pipeline_type": "nightly",
    },
    "raccuk": {
        "analytics_client_code": "raccuk",
        "analytics_client_id": 662,
        "appraisals_client_id": 2913,
        "client_code": "raccuk",
        "client_version": 1,
        "fleet_customer_id": 10732,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "React Access",
        "sales_client_code": "raccuk",
        "sales_client_id": 10732,
        "sales_pipeline_type": "nightly",
    },
    "racp": {
        "analytics_client_code": "racp",
        "analytics_client_id": -500,
        "client_code": "racp",
        "client_version": 1,
        "fleet_customer_id": 149,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Auxilior Capital Partners",
        "primary_oem": "-",
        "sales_client_code": "racp",
        "sales_client_id": 1018,
        "sales_pipeline_type": "vod",
    },
    "raef": {
        "analytics_client_code": "raef",
        "analytics_client_id": -500,
        "client_code": "raef",
        "client_version": 1,
        "fleet_customer_id": 134,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Amur Equipment Finance",
        "primary_oem": "-",
        "sales_client_code": "raef",
        "sales_client_id": 1001,
        "sales_pipeline_type": "vod",
    },
    "rafg": {
        "analytics_client_code": "rafg",
        "analytics_client_id": -500,
        "client_code": "rafg",
        "client_version": 1,
        "fleet_customer_id": 154,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Alliance Funding Group",
        "primary_oem": "-",
        "sales_client_code": "rafg",
        "sales_client_id": 1026,
        "sales_pipeline_type": "vod",
    },
    "raka": {
        "analytics_client_code": "raka",
        "analytics_client_id": 423,
        "appraisals_client_id": 1258,
        "client_code": "raka",
        "client_version": 1,
        "fleet_customer_id": 118,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "RAKA Rental",
        "primary_oem": "-",
        "sales_client_code": "raka",
        "sales_client_id": 922,
        "sales_pipeline_type": "nightly",
    },
    "ran": {
        "analytics_client_code": "ran",
        "analytics_client_id": 33,
        "client_code": "ran",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Ransome",
        "sales_pipeline_type": "vod",
    },
    "rapf": {
        "analytics_client_code": "rapf",
        "analytics_client_id": 516,
        "appraisals_client_id": 1160,
        "client_code": "rapf",
        "client_version": 1,
        "fleet_customer_id": 10071,
        "ims_conversion_status": "ims",
        "name": "AP Financing",
        "sales_client_code": "rapf",
        "sales_client_id": 1023,
        "sales_pipeline_type": "nightly",
    },
    "razr": {
        "analytics_client_code": "razr",
        "analytics_client_id": 339,
        "appraisals_client_id": 2545,
        "client_code": "razr",
        "client_version": 1,
        "fleet_customer_id": 10587,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Razor Rents",
        "sales_client_code": "razr",
        "sales_client_id": 10587,
        "sales_pipeline_type": "nightly",
    },
    "rbav": {
        "appraisals_client_id": 932,
        "client_code": "rbav",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Ritchie Bros - Auction Valuation Test",
        "sales_pipeline_type": "vod",
    },
    "rbfs": {
        "client_code": "rbfs",
        "client_version": 2,
        "ims_conversion_status": "legacy",
        "name": "Ritchie Bros - Financial Services - Do not use",
        "sales_pipeline_type": "vod",
    },
    "rbfsa": {
        "client_code": "rbfsa",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Ritchie Bros - Financial Services",
        "sales_pipeline_type": "nightly",
    },
    "rbims": {
        "analytics_client_code": "rbas",
        "analytics_client_id": -100,
        "appraisals_client_id": 972,
        "client_code": "rbims",
        "client_version": 1,
        "fleet_customer_id": 210,
        "ims_conversion_status": "legacy",
        "market_segment": "financial",
        "name": "Ritchie Bros - IMS",
        "sales_client_code": "rbims",
        "sales_client_id": 953,
        "sales_pipeline_type": "vod",
    },
    "rbme": {
        "analytics_client_code": "rbme",
        "analytics_client_id": 406,
        "appraisals_client_id": 910,
        "client_code": "rbme",
        "client_version": 1,
        "fleet_customer_id": 67,
        "ims_conversion_status": "ims",
        "market_segment": "Financial",
        "name": "Ritchie Bros - Marketplace-E",
        "sales_client_code": "rbme",
        "sales_client_id": 950,
        "sales_pipeline_type": "nightly",
    },
    "rbpps": {
        "analytics_client_id": 407,
        "appraisals_client_id": 1023,
        "client_code": "rbpps",
        "client_version": 1,
        "fleet_customer_id": 68,
        "ims_conversion_status": "ims",
        "market_segment": "Financial",
        "name": "Ritchie Bros - PPS",
        "sales_client_code": "rbpps",
        "sales_client_id": 954,
        "sales_pipeline_type": "nightly",
    },
    "rbt": {
        "analytics_client_code": "rbt",
        "analytics_client_id": -500,
        "client_code": "rbt",
        "client_version": 1,
        "fleet_customer_id": 135,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Bank of Texas",
        "primary_oem": "-",
        "sales_client_code": "rbt",
        "sales_client_id": 1002,
        "sales_pipeline_type": "vod",
    },
    "rbvd": {
        "analytics_client_code": "rbvd",
        "analytics_client_id": 517,
        "appraisals_client_id": 1144,
        "client_code": "rbvd",
        "client_version": 1,
        "fleet_customer_id": 231,
        "ims_conversion_status": "ims",
        "market_segment": "Residuals",
        "name": "BVD Group",
        "sales_client_code": "rbvd",
        "sales_client_id": 1024,
        "sales_pipeline_type": "nightly",
    },
    "rcb": {
        "analytics_client_code": "rcb",
        "analytics_client_id": -500,
        "client_code": "rcb",
        "client_version": 1,
        "fleet_customer_id": 136,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Citizens Bank",
        "primary_oem": "-",
        "sales_client_code": "rcb",
        "sales_client_id": 1003,
        "sales_pipeline_type": "vod",
    },
    "rcef": {
        "analytics_client_code": "rcef",
        "analytics_client_id": -500,
        "client_code": "rcef",
        "client_version": 1,
        "fleet_customer_id": 137,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Crestmark Equipment Finance",
        "primary_oem": "-",
        "sales_client_code": "rcef",
        "sales_client_id": 1004,
        "sales_pipeline_type": "vod",
    },
    "rcer": {
        "analytics_client_code": "rcer",
        "analytics_client_id": 305,
        "appraisals_client_id": 1129,
        "client_code": "rcer",
        "client_version": 1,
        "fleet_customer_id": 10044,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "River City Equipment",
        "sales_client_code": "rcer",
        "sales_client_id": 305,
        "sales_pipeline_type": "nightly",
    },
    "rckga": {
        "analytics_client_code": "rckga",
        "analytics_client_id": 764,
        "appraisals_client_id": 2958,
        "client_code": "rckga",
        "client_version": 1,
        "fleet_customer_id": 10754,
        "ims_conversion_status": "ims",
        "name": "Rick's Rental",
        "sales_client_code": "rckga",
        "sales_client_id": 10754,
        "sales_pipeline_type": "nightly",
    },
    "rcki": {
        "analytics_client_code": "rcki",
        "analytics_client_id": 607,
        "appraisals_client_id": 2553,
        "client_code": "rcki",
        "client_version": 1,
        "fleet_customer_id": 10595,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Rock Rental",
        "sales_client_code": "rcki",
        "sales_client_id": 10595,
        "sales_pipeline_type": "nightly",
    },
    "rcub": {
        "analytics_client_code": "rcub",
        "analytics_client_id": -500,
        "client_code": "rcub",
        "client_version": 1,
        "fleet_customer_id": 138,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Customers Bank",
        "primary_oem": "-",
        "sales_client_code": "rcub",
        "sales_client_id": 1005,
        "sales_pipeline_type": "vod",
    },
    "rda": {
        "analytics_client_code": "rda",
        "analytics_client_id": 101,
        "client_code": "rda",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Red-D-Arc",
        "sales_pipeline_type": "vod",
    },
    "rddg": {
        "analytics_client_code": "rddg",
        "analytics_client_id": 707,
        "appraisals_client_id": 2805,
        "client_code": "rddg",
        "client_version": 1,
        "fleet_customer_id": 10648,
        "ims_conversion_status": "ims",
        "name": "Readydig",
        "sales_client_code": "rddg",
        "sales_client_id": 10648,
        "sales_pipeline_type": "nightly",
    },
    "rdfg": {
        "analytics_client_code": "rdfg",
        "analytics_client_id": -500,
        "client_code": "rdfg",
        "client_version": 1,
        "fleet_customer_id": 140,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Delta Financial Group",
        "primary_oem": "-",
        "sales_client_code": "rdfg",
        "sales_client_id": 1007,
        "sales_pipeline_type": "vod",
    },
    "rdht": {
        "analytics_client_code": "rdht",
        "analytics_client_id": 600,
        "appraisals_client_id": 2300,
        "client_code": "rdht",
        "client_version": 2,
        "fleet_customer_id": 10400,
        "ims_conversion_status": "ims",
        "name": "Red Hat Rentals",
        "sales_client_code": "rdht",
        "sales_client_id": 10400,
        "sales_pipeline_type": "nightly",
    },
    "rdll": {
        "analytics_client_code": "rdll",
        "analytics_client_id": 518,
        "appraisals_client_id": 1027,
        "client_code": "rdll",
        "client_version": 1,
        "fleet_customer_id": 10068,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "DLL Portfolio",
        "sales_client_code": "rdll",
        "sales_client_id": 1008,
        "sales_pipeline_type": "nightly",
    },
    "rdo": {
        "analytics_client_code": "rdo",
        "analytics_client_id": 152,
        "appraisals_client_id": 615,
        "client_code": "rdo",
        "client_version": 1,
        "fleet_customer_id": 60,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "RDO Equipment Co.",
        "primary_oem": "John Deere",
        "sales_client_code": "rdo",
        "sales_client_id": 152,
        "sales_pipeline_type": "nightly",
    },
    "rdse": {
        "analytics_client_code": "rdse",
        "analytics_client_id": -500,
        "client_code": "rdse",
        "client_version": 1,
        "fleet_customer_id": 139,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "D Squared Equipment Appraisal",
        "primary_oem": "-",
        "sales_client_code": "rdse",
        "sales_client_id": 1006,
        "sales_pipeline_type": "vod",
    },
    "rdynh": {
        "analytics_client_code": "rdynh",
        "analytics_client_id": 615,
        "appraisals_client_id": 2291,
        "client_code": "rdynh",
        "client_version": 1,
        "fleet_customer_id": 10394,
        "ims_conversion_status": "ims",
        "name": "Ready Equipment",
        "sales_client_code": "rdynh",
        "sales_client_id": 10394,
        "sales_pipeline_type": "nightly",
    },
    "reacuk": {
        "analytics_client_code": "reacuk",
        "analytics_client_id": 626,
        "appraisals_client_id": 2031,
        "client_code": "reacuk",
        "client_version": 1,
        "fleet_customer_id": 10298,
        "ims_conversion_status": "ims",
        "name": "Reactive Rentals",
        "sales_client_code": "reacuk",
        "sales_client_id": 10298,
        "sales_pipeline_type": "nightly",
    },
    "rec": {
        "analytics_client_code": "rec",
        "analytics_client_id": 187,
        "client_code": "rec",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Rish",
        "sales_pipeline_type": "vod",
    },
    "recp": {
        "analytics_client_code": "recp",
        "analytics_client_id": -500,
        "client_code": "recp",
        "client_version": 1,
        "fleet_customer_id": 147,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Encina Capital Partners",
        "primary_oem": "-",
        "sales_client_code": "recp",
        "sales_client_id": 1016,
        "sales_pipeline_type": "vod",
    },
    "redo": {
        "analytics_client_code": "redo",
        "analytics_client_id": 431,
        "appraisals_client_id": 2548,
        "client_code": "redo",
        "client_version": 1,
        "fleet_customer_id": 10590,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Rental Depot",
        "sales_client_code": "redo",
        "sales_client_id": 10590,
        "sales_pipeline_type": "nightly",
    },
    "regr": {
        "analytics_client_code": "regr",
        "analytics_client_id": 441,
        "appraisals_client_id": 1436,
        "client_code": "regr",
        "client_version": 1,
        "fleet_customer_id": 10019,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Region Rents",
        "sales_client_code": "regr",
        "sales_client_id": 10019,
        "sales_pipeline_type": "nightly",
    },
    "reic": {
        "analytics_client_code": "reic",
        "analytics_client_id": 205,
        "appraisals_client_id": 901,
        "client_code": "reic",
        "client_version": 1,
        "fleet_customer_id": 9,
        "ims_conversion_status": "ims",
        "name": "Rental Equipment Investment Crop",
        "sales_client_code": "reic",
        "sales_client_id": 350,
        "sales_pipeline_type": "nightly",
    },
    "reng": {
        "analytics_client_code": "reng",
        "analytics_client_id": -500,
        "client_code": "reng",
        "client_version": 1,
        "fleet_customer_id": 153,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "ENGS Commercial Finance Co.",
        "primary_oem": "-",
        "sales_client_code": "reng",
        "sales_client_id": 1025,
        "sales_pipeline_type": "vod",
    },
    "rep": {
        "analytics_client_code": "rep",
        "analytics_client_id": 711,
        "client_code": "rep",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Rep Rents",
        "sales_pipeline_type": "vod",
    },
    "resd": {
        "analytics_client_code": "resd",
        "analytics_client_id": 725,
        "client_code": "resd",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Rental Equipment Solutions, Inc.",
        "sales_pipeline_type": "vod",
    },
    "reta": {
        "analytics_client_code": "reta",
        "analytics_client_id": 633,
        "appraisals_client_id": 2239,
        "client_code": "reta",
        "client_version": 1,
        "fleet_customer_id": 10379,
        "ims_conversion_status": "ims",
        "name": "Redtail Rental",
        "sales_client_code": "reta",
        "sales_client_id": 10379,
        "sales_pipeline_type": "nightly",
    },
    "rex": {
        "analytics_client_code": "rex",
        "analytics_client_id": 95,
        "appraisals_client_id": 696,
        "client_code": "rex",
        "client_version": 1,
        "fleet_customer_id": 223,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Rexco Equipment, Inc.",
        "primary_oem": "Bobcat",
        "sales_client_code": "rex",
        "sales_client_id": 95,
        "sales_pipeline_type": "nightly",
    },
    "rff": {
        "analytics_client_code": "rff",
        "analytics_client_id": -500,
        "client_code": "rff",
        "client_version": 1,
        "fleet_customer_id": 150,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "First Financial",
        "primary_oem": "-",
        "sales_client_code": "rff",
        "sales_client_id": 1019,
        "sales_pipeline_type": "vod",
    },
    "rfish": {
        "analytics_client_code": "rfish",
        "analytics_client_id": 756,
        "client_code": "rfish",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Redfish Rentals",
        "sales_pipeline_type": "vod",
    },
    "rfls": {
        "analytics_client_code": "rfls",
        "analytics_client_id": 372,
        "client_code": "rfls",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Reliable Forklift Sales",
        "sales_pipeline_type": "vod",
    },
    "rfr": {
        "analytics_client_code": "rfr",
        "analytics_client_id": 88,
        "client_code": "rfr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Rain For Rent",
        "sales_pipeline_type": "vod",
    },
    "rguy": {
        "analytics_client_code": "rguy",
        "analytics_client_id": 323,
        "appraisals_client_id": 729,
        "client_code": "rguy",
        "client_version": 1,
        "fleet_customer_id": 214,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Rental Guys",
        "sales_client_code": "rguy",
        "sales_client_id": 323,
        "sales_pipeline_type": "nightly",
    },
    "rgyc": {
        "analytics_client_code": "rgyc",
        "analytics_client_id": 601,
        "client_code": "rgyc",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "TheRentalGuysCA",
        "sales_pipeline_type": "vod",
    },
    "rhca": {
        "analytics_client_code": "rhca",
        "analytics_client_id": -500,
        "client_code": "rhca",
        "client_version": 1,
        "fleet_customer_id": 141,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Hitachi Capital America Vendor Services",
        "primary_oem": "-",
        "sales_client_code": "rhca",
        "sales_client_id": 1009,
        "sales_pipeline_type": "vod",
    },
    "rher": {
        "analytics_client_code": "rher",
        "analytics_client_id": 326,
        "appraisals_client_id": 1124,
        "client_code": "rher",
        "client_version": 1,
        "fleet_customer_id": 77,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Rocky Hill Equipment Rentals",
        "sales_client_code": "rher",
        "sales_client_id": 326,
        "sales_pipeline_type": "nightly",
    },
    "rib": {
        "analytics_client_code": "rib",
        "analytics_client_id": -500,
        "client_code": "rib",
        "client_version": 1,
        "fleet_customer_id": 142,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Investors Bank",
        "primary_oem": "-",
        "sales_client_code": "rib",
        "sales_client_id": 1010,
        "sales_pipeline_type": "vod",
    },
    "rjv": {
        "analytics_client_code": "rjv",
        "analytics_client_id": 370,
        "appraisals_client_id": 2551,
        "client_code": "rjv",
        "client_version": 1,
        "fleet_customer_id": 10593,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "RJV Equipment",
        "sales_client_code": "rjv",
        "sales_client_id": 10593,
        "sales_pipeline_type": "nightly",
    },
    "rlcs": {
        "analytics_client_code": "rlcs",
        "analytics_client_id": 392,
        "appraisals_client_id": 2547,
        "client_code": "rlcs",
        "client_version": 1,
        "fleet_customer_id": 10589,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Reliable Crane Service",
        "sales_client_code": "rlcs",
        "sales_client_id": 10589,
        "sales_pipeline_type": "nightly",
    },
    "rlex": {
        "analytics_client_code": "rlex",
        "analytics_client_id": 325,
        "appraisals_client_id": 2549,
        "client_code": "rlex",
        "client_version": 1,
        "fleet_customer_id": 10591,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Rentalex",
        "sales_client_code": "rlex",
        "sales_client_id": 10591,
        "sales_pipeline_type": "nightly",
    },
    "rline": {
        "analytics_client_code": "rline",
        "analytics_client_id": 627,
        "appraisals_client_id": 2546,
        "client_code": "rline",
        "client_version": 1,
        "fleet_customer_id": 10588,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Redline Rentals",
        "sales_client_code": "rline",
        "sales_client_id": 10588,
        "sales_pipeline_type": "nightly",
    },
    "rm": {
        "analytics_client_code": "rm",
        "analytics_client_id": 89,
        "appraisals_client_id": 600,
        "client_code": "rm",
        "client_version": 1,
        "fleet_customer_id": 190,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Road Machinery LLC",
        "primary_oem": "komatsu",
        "sales_client_code": "rm",
        "sales_client_id": 99,
        "sales_pipeline_type": "nightly",
    },
    "rmcs": {
        "analytics_client_code": "rmcs",
        "analytics_client_id": -500,
        "client_code": "rmcs",
        "client_version": 1,
        "fleet_customer_id": 151,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Marlin Capital Solutions",
        "primary_oem": "-",
        "sales_client_code": "rmcs",
        "sales_client_id": 1020,
        "sales_pipeline_type": "vod",
    },
    "rmef": {
        "analytics_client_code": "rmef",
        "analytics_client_id": -500,
        "client_code": "rmef",
        "client_version": 1,
        "fleet_customer_id": 143,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Midland Equipment Finance",
        "primary_oem": "-",
        "sales_client_code": "rmef",
        "sales_client_id": 1011,
        "sales_pipeline_type": "vod",
    },
    "rmen": {
        "analytics_client_code": "rmen",
        "analytics_client_id": 318,
        "appraisals_client_id": 2013,
        "client_code": "rmen",
        "client_version": 1,
        "fleet_customer_id": 10289,
        "ims_conversion_status": "ims",
        "name": "The Rental Men",
        "sales_client_code": "rmen",
        "sales_client_id": 10289,
        "sales_pipeline_type": "nightly",
    },
    "rmhc": {
        "analytics_client_code": "rmhc",
        "analytics_client_id": -500,
        "client_code": "rmhc",
        "client_version": 1,
        "fleet_customer_id": 155,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Mitsubishi HC Capital America",
        "primary_oem": "-",
        "sales_pipeline_type": "vod",
    },
    "rmoc": {
        "analytics_client_code": "rmoc",
        "analytics_client_id": -500,
        "client_code": "rmoc",
        "client_version": 1,
        "fleet_customer_id": 146,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Meridian OneCap",
        "primary_oem": "-",
        "sales_client_code": "rmoc",
        "sales_client_id": 1015,
        "sales_pipeline_type": "vod",
    },
    "rmr": {
        "analytics_client_code": "rmr",
        "analytics_client_id": 181,
        "client_code": "rmr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Red Mountain Rentals",
        "sales_pipeline_type": "vod",
    },
    "rms": {
        "analytics_client_code": "rms",
        "analytics_client_id": 165,
        "appraisals_client_id": 694,
        "client_code": "rms",
        "client_version": 1,
        "fleet_customer_id": 61,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Road Machinery \u0026 Supplies Co.",
        "primary_oem": "komatsu",
        "sales_client_code": "rms",
        "sales_client_id": 165,
        "sales_pipeline_type": "nightly",
    },
    "rnec": {
        "appraisals_client_id": 1071,
        "client_code": "rnec",
        "client_version": 1,
        "fleet_customer_id": 10070,
        "ims_conversion_status": "ims",
        "name": "NEC Financial Services",
        "sales_client_code": "rnec",
        "sales_client_id": 1021,
        "sales_pipeline_type": "nightly",
    },
    "rng": {
        "analytics_client_code": "rng",
        "analytics_client_id": 50,
        "appraisals_client_id": 413,
        "client_code": "rng",
        "client_version": 1,
        "fleet_customer_id": 33,
        "ims_conversion_status": "ims",
        "name": "Ring Power Corporation",
        "sales_client_code": "rng",
        "sales_client_id": 50,
        "sales_pipeline_type": "nightly",
    },
    "rnjp": {
        "analytics_client_code": "rnjp",
        "analytics_client_id": 284,
        "appraisals_client_id": 2533,
        "client_code": "rnjp",
        "client_version": 1,
        "fleet_customer_id": 10575,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Nikken",
        "sales_client_code": "rnjp",
        "sales_client_id": 10575,
        "sales_pipeline_type": "nightly",
    },
    "rnwk": {
        "analytics_client_code": "rnwk",
        "analytics_client_id": 297,
        "client_code": "rnwk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Rental Network",
        "sales_pipeline_type": "vod",
    },
    "rnyn": {
        "analytics_client_code": "rnyn",
        "analytics_client_id": 245,
        "client_code": "rnyn",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Runyon Rental",
        "sales_pipeline_type": "vod",
    },
    "rol": {
        "analytics_client_code": "rol",
        "analytics_client_id": 218,
        "appraisals_client_id": 885,
        "client_code": "rol",
        "client_version": 1,
        "fleet_customer_id": 82,
        "ims_conversion_status": "legacy",
        "market_segment": "OEM",
        "name": "Roland Machinery",
        "primary_oem": "Komatsu",
        "sales_client_code": "rol",
        "sales_client_id": 218,
        "sales_pipeline_type": "nightly",
    },
    "romco": {
        "analytics_client_code": "romco",
        "analytics_client_id": 665,
        "appraisals_client_id": 2554,
        "client_code": "romco",
        "client_version": 1,
        "fleet_customer_id": 10596,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "ROMCO Equipment",
        "sales_client_code": "romco",
        "sales_client_id": 10596,
        "sales_pipeline_type": "nightly",
    },
    "rora": {
        "analytics_client_code": "rora",
        "analytics_client_id": 363,
        "appraisals_client_id": 2552,
        "client_code": "rora",
        "client_version": 1,
        "fleet_customer_id": 10594,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Robertson Rent All",
        "sales_client_code": "rora",
        "sales_client_id": 10594,
        "sales_pipeline_type": "nightly",
    },
    "rpc": {
        "analytics_client_code": "rpc",
        "analytics_client_id": -500,
        "client_code": "rpc",
        "client_version": 1,
        "fleet_customer_id": 152,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Peapack Capital",
        "primary_oem": "-",
        "sales_client_code": "rpc",
        "sales_client_id": 1022,
        "sales_pipeline_type": "vod",
    },
    "rpm": {
        "analytics_client_code": "rpm",
        "analytics_client_id": 247,
        "appraisals_client_id": 1451,
        "client_code": "rpm",
        "client_version": 1,
        "fleet_customer_id": 10020,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "RPM Machinery",
        "sales_client_code": "rpm",
        "sales_client_id": 10020,
        "sales_pipeline_type": "nightly",
    },
    "rpwb": {
        "analytics_client_code": "rpwb",
        "analytics_client_id": -500,
        "client_code": "rpwb",
        "client_version": 1,
        "fleet_customer_id": 144,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Pacific Western Bank",
        "primary_oem": "-",
        "sales_client_code": "rpwb",
        "sales_client_id": 1012,
        "sales_pipeline_type": "vod",
    },
    "rrci": {
        "analytics_client_code": "rrci",
        "analytics_client_id": 609,
        "appraisals_client_id": 2550,
        "client_code": "rrci",
        "client_version": 1,
        "fleet_customer_id": 10592,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Resource Rental Center",
        "sales_client_code": "rrci",
        "sales_client_id": 10592,
        "sales_pipeline_type": "nightly",
    },
    "rredy": {
        "analytics_client_code": "rredy",
        "analytics_client_id": 772,
        "appraisals_client_id": 2968,
        "client_code": "rredy",
        "client_version": 1,
        "fleet_customer_id": 10758,
        "ims_conversion_status": "ims",
        "name": "Rent Ready Equipment \u0026 Sales",
        "sales_client_code": "rredy",
        "sales_client_id": 10758,
        "sales_pipeline_type": "nightly",
    },
    "rrr": {
        "analytics_client_code": "rrr",
        "analytics_client_id": 83,
        "client_code": "rrr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "R\u0026R Rentals",
        "sales_pipeline_type": "vod",
    },
    "rsc": {
        "analytics_client_code": "rsc",
        "analytics_client_id": -500,
        "client_code": "rsc",
        "client_version": 1,
        "fleet_customer_id": 145,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Surles Claims",
        "primary_oem": "-",
        "sales_client_code": "rsc",
        "sales_client_id": 1014,
        "sales_pipeline_type": "vod",
    },
    "rsmf": {
        "analytics_client_code": "rsmf",
        "analytics_client_id": 519,
        "appraisals_client_id": 854,
        "client_code": "rsmf",
        "client_version": 1,
        "fleet_customer_id": 10069,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Sumitomo Mitsui Finance and Leasing",
        "sales_client_code": "rsmf",
        "sales_client_id": 1013,
        "sales_pipeline_type": "nightly",
    },
    "rtc": {
        "analytics_client_code": "rtc",
        "analytics_client_id": -500,
        "client_code": "rtc",
        "client_version": 1,
        "fleet_customer_id": 148,
        "ims_conversion_status": "ims",
        "market_segment": "residuals",
        "name": "Travelers Capital",
        "primary_oem": "-",
        "sales_client_code": "rtc",
        "sales_client_id": 1017,
        "sales_pipeline_type": "vod",
    },
    "rtecau": {
        "analytics_client_code": "rtecau",
        "analytics_client_id": 682,
        "client_code": "rtecau",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Renteca",
        "sales_pipeline_type": "vod",
    },
    "rwgb": {
        "analytics_client_code": "rwgb",
        "analytics_client_id": 796,
        "appraisals_client_id": 3052,
        "client_code": "rwgb",
        "client_version": 1,
        "fleet_customer_id": 10836,
        "ims_conversion_status": "ims",
        "name": "Rental Works Greensboro",
        "sales_client_code": "rwgb",
        "sales_client_id": 10836,
        "sales_pipeline_type": "nightly",
    },
    "rwmd": {
        "analytics_client_code": "rwmd",
        "analytics_client_id": 220,
        "appraisals_client_id": 1763,
        "client_code": "rwmd",
        "client_version": 1,
        "fleet_customer_id": 10176,
        "ims_conversion_status": "ims",
        "name": "Rental Works Maryland",
        "sales_client_code": "rwmd",
        "sales_client_id": 10176,
        "sales_pipeline_type": "nightly",
    },
    "rwuk": {
        "analytics_client_code": "rwuk",
        "analytics_client_id": 214,
        "client_code": "rwuk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Riwal UK",
        "sales_pipeline_type": "vod",
    },
    "sab": {
        "analytics_client_code": "sab",
        "analytics_client_id": 224,
        "client_code": "sab",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Sabre Rentals Ltd.",
        "sales_pipeline_type": "vod",
    },
    "sagee": {
        "analytics_client_code": "sagee",
        "analytics_client_id": 777,
        "client_code": "sagee",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Sage Equipment LLC",
        "sales_pipeline_type": "vod",
    },
    "sagr": {
        "analytics_client_code": "sagr",
        "analytics_client_id": 462,
        "appraisals_client_id": 1444,
        "client_code": "sagr",
        "client_version": 1,
        "fleet_customer_id": 10014,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Sage Rental Services",
        "sales_client_code": "sagr",
        "sales_client_id": 10014,
        "sales_pipeline_type": "nightly",
    },
    "sba": {
        "analytics_client_code": "sba",
        "analytics_client_id": 591,
        "appraisals_client_id": 1908,
        "client_code": "sba",
        "client_version": 1,
        "fleet_customer_id": 10239,
        "ims_conversion_status": "ims",
        "name": "Sunbelt Asphalt",
        "sales_client_code": "sba",
        "sales_client_id": 10239,
        "sales_pipeline_type": "nightly",
    },
    "sbc": {
        "analytics_client_code": "sbc",
        "analytics_client_id": 132,
        "client_code": "sbc",
        "client_version": 1,
        "fleet_customer_id": 51,
        "ims_conversion_status": "legacy",
        "name": "Sunbelt Rentals CAN",
        "sales_pipeline_type": "vod",
    },
    "sbce": {
        "analytics_client_code": "sbce",
        "analytics_client_id": 296,
        "appraisals_client_id": 2559,
        "client_code": "sbce",
        "client_version": 1,
        "fleet_customer_id": 10601,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Smith Bros",
        "sales_client_code": "sbce",
        "sales_client_id": 10601,
        "sales_pipeline_type": "nightly",
    },
    "sbt": {
        "analytics_client_code": "sbt",
        "analytics_client_id": 131,
        "appraisals_client_id": 860,
        "client_code": "sbt",
        "client_version": 1,
        "fleet_customer_id": 51,
        "ims_conversion_status": "ims",
        "name": "Sunbelt",
        "sales_client_code": "sbt",
        "sales_client_id": 131,
        "sales_pipeline_type": "nightly",
    },
    "sbtie": {
        "analytics_client_code": "sbtie",
        "analytics_client_id": 728,
        "client_code": "sbtie",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Sunbelt Rentals Ireland",
        "sales_pipeline_type": "vod",
    },
    "sbtuk": {
        "analytics_client_code": "sbtuk",
        "analytics_client_id": 287,
        "appraisals_client_id": 2566,
        "client_code": "sbtuk",
        "client_version": 1,
        "fleet_customer_id": 10608,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Sunbelt Rentals UK",
        "sales_client_code": "sbtuk",
        "sales_client_id": 10608,
        "sales_pipeline_type": "nightly",
    },
    "sec": {
        "analytics_client_code": "sec",
        "analytics_client_id": 7,
        "appraisals_client_id": 726,
        "client_code": "sec",
        "client_version": 1,
        "fleet_customer_id": 3,
        "ims_conversion_status": "ims",
        "name": "Sunstate Equipment Co.",
        "sales_client_code": "sec",
        "sales_client_id": 602,
        "sales_pipeline_type": "nightly",
    },
    "seq": {
        "analytics_client_code": "seq",
        "analytics_client_id": 280,
        "client_code": "seq",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Southeastern Equipment",
        "sales_pipeline_type": "vod",
    },
    "serv": {
        "analytics_client_code": "serv",
        "analytics_client_id": 229,
        "appraisals_client_id": 1901,
        "client_code": "serv",
        "client_version": 1,
        "fleet_customer_id": 10234,
        "ims_conversion_status": "ims",
        "name": "Service Rentals and Supplies Inc",
        "sales_client_code": "serv",
        "sales_client_id": 10234,
        "sales_pipeline_type": "nightly",
    },
    "sfdg": {
        "analytics_client_code": "sfdg",
        "analytics_client_id": 686,
        "appraisals_client_id": 2280,
        "client_code": "sfdg",
        "client_version": 1,
        "fleet_customer_id": 10390,
        "ims_conversion_status": "ims",
        "name": "Summit Funding Group Inc.",
        "sales_client_code": "sfdg",
        "sales_client_id": 10390,
        "sales_pipeline_type": "nightly",
    },
    "sgec": {
        "analytics_client_code": "sgec",
        "analytics_client_id": 705,
        "appraisals_client_id": 2378,
        "client_code": "sgec",
        "client_version": 1,
        "fleet_customer_id": 10437,
        "ims_conversion_status": "ims",
        "name": "Siegmund Excavation \u0026 Construction",
        "sales_client_code": "sgec",
        "sales_client_id": 10437,
        "sales_pipeline_type": "nightly",
    },
    "shcuk": {
        "analytics_client_code": "shcuk",
        "analytics_client_id": 730,
        "appraisals_client_id": 2860,
        "client_code": "shcuk",
        "client_version": 1,
        "fleet_customer_id": 10682,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Skipton Hire Centre Ltd",
        "sales_client_code": "shcuk",
        "sales_client_id": 10682,
        "sales_pipeline_type": "nightly",
    },
    "shcuk2": {
        "analytics_client_code": "shcuk2",
        "analytics_client_id": 767,
        "client_code": "shcuk2",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Skipton Hire Centre Ltd",
        "sales_pipeline_type": "vod",
    },
    "shec": {
        "analytics_client_code": "shec",
        "analytics_client_id": 352,
        "client_code": "shec",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Shafer Equipment Rental",
        "sales_pipeline_type": "vod",
    },
    "shorau": {
        "analytics_client_code": "shorau",
        "analytics_client_id": 585,
        "appraisals_client_id": 2556,
        "client_code": "shorau",
        "client_version": 1,
        "fleet_customer_id": 10598,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Shore Hire",
        "sales_client_code": "shorau",
        "sales_client_id": 10598,
        "sales_pipeline_type": "nightly",
    },
    "shtx": {
        "analytics_client_code": "shtx",
        "analytics_client_id": 555,
        "client_code": "shtx",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Schaffer Equipment",
        "sales_pipeline_type": "vod",
    },
    "simp": {
        "analytics_client_code": "simp",
        "analytics_client_id": 167,
        "appraisals_client_id": 1928,
        "client_code": "simp",
        "client_version": 1,
        "fleet_customer_id": 10254,
        "ims_conversion_status": "ims",
        "name": "Simplex Equipment Rental",
        "sales_client_code": "simp",
        "sales_client_id": 10254,
        "sales_pipeline_type": "nightly",
    },
    "sims": {
        "analytics_client_code": "sims",
        "analytics_client_id": 310,
        "appraisals_client_id": 2557,
        "client_code": "sims",
        "client_version": 1,
        "fleet_customer_id": 10599,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "SimsCrane",
        "sales_client_code": "sims",
        "sales_client_id": 10599,
        "sales_pipeline_type": "nightly",
    },
    "sji": {
        "analytics_client_code": "sji",
        "analytics_client_id": 515,
        "appraisals_client_id": 402,
        "client_code": "sji",
        "client_version": 1,
        "fleet_customer_id": 229,
        "ims_conversion_status": "ims",
        "name": "Skyjack, Inc.",
        "sales_client_code": "sji",
        "sales_client_id": 960,
        "sales_pipeline_type": "nightly",
    },
    "ska": {
        "analytics_client_code": "ska",
        "analytics_client_id": -500,
        "client_code": "ska",
        "client_version": 1,
        "fleet_customer_id": 105,
        "ims_conversion_status": "ims",
        "market_segment": "contractor",
        "name": "Skanska USA Civil",
        "primary_oem": "-",
        "sales_client_code": "ska",
        "sales_client_id": 718,
        "sales_pipeline_type": "vod",
    },
    "sky": {
        "analytics_client_code": "sky",
        "analytics_client_id": 109,
        "appraisals_client_id": 2502,
        "client_code": "sky",
        "client_version": 1,
        "fleet_customer_id": 10544,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Skyworks of Virginia",
        "sales_client_code": "sky",
        "sales_client_id": 10544,
        "sales_pipeline_type": "nightly",
    },
    "slctc": {
        "analytics_client_code": "slctc",
        "analytics_client_id": 602,
        "client_code": "slctc",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Select Equipment Rentals",
        "sales_pipeline_type": "vod",
    },
    "slg": {
        "analytics_client_code": "slg",
        "analytics_client_id": 307,
        "appraisals_client_id": -500,
        "client_code": "slg",
        "client_version": 1,
        "fleet_customer_id": 100,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Sterling Crane USA",
        "primary_oem": "-",
        "sales_client_code": "slg",
        "sales_client_id": 100,
        "sales_pipeline_type": "nightly",
    },
    "slgc": {
        "analytics_client_code": "slgc",
        "analytics_client_id": 308,
        "appraisals_client_id": 2565,
        "client_code": "slgc",
        "client_version": 1,
        "fleet_customer_id": 10607,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Sterling Crane CAN",
        "sales_client_code": "slgc",
        "sales_client_id": 10607,
        "sales_pipeline_type": "nightly",
    },
    "smc": {
        "analytics_client_code": "smc",
        "analytics_client_id": -500,
        "client_code": "smc",
        "client_version": 1,
        "fleet_customer_id": 115,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Semcore 2 Rental Center",
        "primary_oem": "-",
        "sales_pipeline_type": "vod",
    },
    "smon": {
        "analytics_client_code": "smon",
        "analytics_client_id": 791,
        "client_code": "smon",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Simon Equipment Co",
        "sales_pipeline_type": "vod",
    },
    "sms": {
        "client_code": "sms",
        "client_version": 1,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "SMS Equipment (Do not use)",
        "primary_oem": "Komatsu",
        "sales_pipeline_type": "nightly",
    },
    "smse": {
        "analytics_client_code": "smse",
        "analytics_client_id": 432,
        "appraisals_client_id": 1332,
        "client_code": "smse",
        "client_version": 1,
        "fleet_customer_id": 197,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "SMS Equipment",
        "primary_oem": "komatsu",
        "sales_client_code": "smse",
        "sales_client_id": 995,
        "sales_pipeline_type": "nightly",
    },
    "smsn": {
        "analytics_client_code": "smsn",
        "analytics_client_id": 571,
        "appraisals_client_id": 1857,
        "client_code": "smsn",
        "client_version": 1,
        "fleet_customer_id": 10218,
        "ims_conversion_status": "ims",
        "name": "Simonson Equipment",
        "sales_client_code": "smsn",
        "sales_client_id": 10218,
        "sales_pipeline_type": "nightly",
    },
    "solpau": {
        "analytics_client_code": "solpau",
        "analytics_client_id": 701,
        "client_code": "solpau",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Solution Plant Hire",
        "sales_pipeline_type": "vod",
    },
    "sopw": {
        "analytics_client_code": "sopw",
        "analytics_client_id": 449,
        "appraisals_client_id": 1382,
        "client_code": "sopw",
        "client_version": 1,
        "fleet_customer_id": 10053,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "1 Source Power and Equipment",
        "sales_client_code": "sopw",
        "sales_client_id": 449,
        "sales_pipeline_type": "vod",
    },
    "spdi": {
        "analytics_client_code": "spdi",
        "analytics_client_id": -500,
        "client_code": "spdi",
        "client_version": 1,
        "fleet_customer_id": 107,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Superior Rentals",
        "primary_oem": "-",
        "sales_client_code": "spdi",
        "sales_client_id": 860,
        "sales_pipeline_type": "vod",
    },
    "spr": {
        "analytics_client_code": "spr",
        "analytics_client_id": -6,
        "client_code": "spr",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "SPR Rentals",
        "sales_pipeline_type": "vod",
    },
    "sray": {
        "analytics_client_code": "sray",
        "analytics_client_id": 454,
        "appraisals_client_id": 2360,
        "client_code": "sray",
        "client_version": 1,
        "fleet_customer_id": 10428,
        "ims_conversion_status": "ims",
        "name": "Sonsray Machinery",
        "sales_client_code": "sray",
        "sales_client_id": 10428,
        "sales_pipeline_type": "nightly",
    },
    "srci": {
        "analytics_client_code": "srci",
        "analytics_client_id": 282,
        "appraisals_client_id": 1127,
        "client_code": "srci",
        "client_version": 1,
        "fleet_customer_id": 80,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Sun Rental Center Ohio",
        "sales_client_code": "srci",
        "sales_client_id": 282,
        "sales_pipeline_type": "nightly",
    },
    "srs": {
        "analytics_client_code": "srs",
        "analytics_client_id": 108,
        "appraisals_client_id": 1872,
        "client_code": "srs",
        "client_version": 1,
        "fleet_customer_id": 10221,
        "ims_conversion_status": "ims",
        "name": "Stephensons Rental",
        "sales_client_code": "srs",
        "sales_client_id": 10221,
        "sales_pipeline_type": "nightly",
    },
    "staruk": {
        "analytics_client_code": "staruk",
        "analytics_client_id": 562,
        "appraisals_client_id": 2561,
        "client_code": "staruk",
        "client_version": 1,
        "fleet_customer_id": 10603,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Star Platforms",
        "sales_client_code": "staruk",
        "sales_client_id": 10603,
        "sales_pipeline_type": "nightly",
    },
    "stb": {
        "analytics_client_code": "stb",
        "analytics_client_id": 55,
        "appraisals_client_id": 616,
        "client_code": "stb",
        "client_version": 1,
        "fleet_customer_id": 34,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Stribling",
        "primary_oem": "john deere",
        "sales_client_code": "stb",
        "sales_client_id": 55,
        "sales_pipeline_type": "nightly",
    },
    "stec": {
        "analytics_client_code": "stec",
        "analytics_client_id": 539,
        "appraisals_client_id": 2558,
        "client_code": "stec",
        "client_version": 1,
        "fleet_customer_id": 10600,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "SkyTec",
        "sales_client_code": "stec",
        "sales_client_id": 10600,
        "sales_pipeline_type": "nightly",
    },
    "stene": {
        "analytics_client_code": "stene",
        "analytics_client_id": 635,
        "appraisals_client_id": 2564,
        "client_code": "stene",
        "client_version": 1,
        "fleet_customer_id": 10606,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Stephenson Equipment",
        "sales_client_code": "stene",
        "sales_client_id": 10606,
        "sales_pipeline_type": "nightly",
    },
    "stme": {
        "analytics_client_code": "stme",
        "analytics_client_id": 637,
        "appraisals_client_id": 2563,
        "client_code": "stme",
        "client_version": 1,
        "fleet_customer_id": 10605,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Stemar Equipment",
        "sales_client_code": "stme",
        "sales_client_id": 10605,
        "sales_pipeline_type": "nightly",
    },
    "stp": {
        "analytics_client_code": "stp",
        "analytics_client_id": 355,
        "appraisals_client_id": 1021,
        "client_code": "stp",
        "client_version": 1,
        "fleet_customer_id": 15,
        "ims_conversion_status": "ims",
        "name": "SitePro",
        "sales_client_code": "stp",
        "sales_client_id": 750,
        "sales_pipeline_type": "nightly",
    },
    "strt": {
        "analytics_client_code": "strt",
        "analytics_client_id": 474,
        "appraisals_client_id": 2562,
        "client_code": "strt",
        "client_version": 1,
        "fleet_customer_id": 10604,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Star Tractor",
        "sales_client_code": "strt",
        "sales_client_id": 10604,
        "sales_pipeline_type": "nightly",
    },
    "stw": {
        "analytics_client_code": "stw",
        "analytics_client_id": 26,
        "appraisals_client_id": 752,
        "client_code": "stw",
        "client_version": 1,
        "fleet_customer_id": 220,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Stowers Machinery Corporation",
        "primary_oem": "Caterpillar",
        "sales_client_code": "stw",
        "sales_client_id": 26,
        "sales_pipeline_type": "nightly",
    },
    "summ": {
        "analytics_client_code": "summ",
        "analytics_client_id": 548,
        "appraisals_client_id": 1623,
        "client_code": "summ",
        "client_version": 1,
        "fleet_customer_id": 10173,
        "ims_conversion_status": "ims",
        "name": "Summit Materials",
        "sales_client_code": "summ",
        "sales_client_id": 10173,
        "sales_pipeline_type": "nightly",
    },
    "sumo": {
        "analytics_client_code": "sumo",
        "analytics_client_id": 383,
        "appraisals_client_id": 2567,
        "client_code": "sumo",
        "client_version": 1,
        "fleet_customer_id": 10609,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Superior Rents",
        "sales_client_code": "sumo",
        "sales_client_id": 10609,
        "sales_pipeline_type": "nightly",
    },
    "sumr": {
        "analytics_client_code": "sumr",
        "analytics_client_id": 447,
        "appraisals_client_id": 1376,
        "client_code": "sumr",
        "client_version": 1,
        "fleet_customer_id": 191,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Summit Equipment Rentals",
        "sales_client_code": "sumr",
        "sales_client_id": 447,
        "sales_pipeline_type": "nightly",
    },
    "sun": {
        "analytics_client_code": "sun",
        "analytics_client_id": 87,
        "client_code": "sun",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Sunbelt",
        "sales_pipeline_type": "vod",
    },
    "sundt": {
        "analytics_client_code": "sundt",
        "analytics_client_id": 689,
        "appraisals_client_id": 2297,
        "client_code": "sundt",
        "client_version": 1,
        "fleet_customer_id": 10398,
        "ims_conversion_status": "ims",
        "name": "Sundt Construction Inc.",
        "sales_client_code": "sundt",
        "sales_client_id": 10398,
        "sales_pipeline_type": "nightly",
    },
    "sure": {
        "analytics_client_code": "sure",
        "analytics_client_id": 348,
        "appraisals_client_id": 2568,
        "client_code": "sure",
        "client_version": 1,
        "fleet_customer_id": 10610,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Superior Rigging and Erecting",
        "sales_client_code": "sure",
        "sales_client_id": 10610,
        "sales_pipeline_type": "nightly",
    },
    "suruk": {
        "analytics_client_code": "suruk",
        "analytics_client_id": 566,
        "client_code": "suruk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Surrey Hire",
        "sales_pipeline_type": "vod",
    },
    "swnkde": {
        "analytics_client_code": "swnkde",
        "analytics_client_id": 644,
        "client_code": "swnkde",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Schwenk Arbeitsbuehnen",
        "sales_pipeline_type": "vod",
    },
    "swtr": {
        "analytics_client_code": "swtr",
        "analytics_client_id": 493,
        "appraisals_client_id": 2560,
        "client_code": "swtr",
        "client_version": 1,
        "fleet_customer_id": 10602,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Southwest Tool Rental",
        "sales_client_code": "swtr",
        "sales_client_id": 10602,
        "sales_pipeline_type": "nightly",
    },
    "syn": {
        "analytics_client_code": "syn",
        "analytics_client_id": 182,
        "appraisals_client_id": 1814,
        "client_code": "syn",
        "client_version": 1,
        "fleet_customer_id": 10202,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Synergy Equipment",
        "sales_client_code": "syn",
        "sales_client_id": 10202,
        "sales_pipeline_type": "nightly",
    },
    "synruk": {
        "analytics_client_code": "synruk",
        "analytics_client_id": 632,
        "appraisals_client_id": 2170,
        "client_code": "synruk",
        "client_version": 1,
        "fleet_customer_id": 10355,
        "ims_conversion_status": "ims",
        "name": "Synergy Hire",
        "sales_client_code": "synruk",
        "sales_client_id": 10355,
        "sales_pipeline_type": "nightly",
    },
    "tagr": {
        "analytics_client_code": "tagr",
        "analytics_client_id": 439,
        "appraisals_client_id": 1362,
        "client_code": "tagr",
        "client_version": 1,
        "fleet_customer_id": 188,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Taylor Rental Greenville",
        "sales_client_code": "tagr",
        "sales_client_id": 439,
        "sales_pipeline_type": "nightly",
    },
    "tate": {
        "analytics_client_code": "tate",
        "analytics_client_id": 549,
        "appraisals_client_id": 2570,
        "client_code": "tate",
        "client_version": 1,
        "fleet_customer_id": 10612,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Tates Rents",
        "sales_client_code": "tate",
        "sales_client_id": 10612,
        "sales_pipeline_type": "nightly",
    },
    "tcan": {
        "analytics_client_code": "tcan",
        "analytics_client_id": 577,
        "appraisals_client_id": 2578,
        "client_code": "tcan",
        "client_version": 1,
        "fleet_customer_id": 10620,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Torcan Lift",
        "sales_client_code": "tcan",
        "sales_client_id": 10620,
        "sales_pipeline_type": "nightly",
    },
    "tec": {
        "analytics_client_code": "tec",
        "analytics_client_id": 133,
        "appraisals_client_id": 2581,
        "client_code": "tec",
        "client_version": 1,
        "fleet_customer_id": 10623,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Tractor \u0026 Equipment Company",
        "sales_client_code": "tec",
        "sales_client_id": 10623,
        "sales_pipeline_type": "nightly",
    },
    "teds": {
        "analytics_client_code": "teds",
        "analytics_client_id": 604,
        "appraisals_client_id": 2247,
        "client_code": "teds",
        "client_version": 1,
        "fleet_customer_id": 10380,
        "ims_conversion_status": "ims",
        "name": "Ted's Rental",
        "sales_client_code": "teds",
        "sales_client_id": 10380,
        "sales_pipeline_type": "nightly",
    },
    "tej": {
        "analytics_client_code": "tej",
        "analytics_client_id": 56,
        "appraisals_client_id": 1900,
        "client_code": "tej",
        "client_version": 1,
        "fleet_customer_id": 10233,
        "ims_conversion_status": "ims",
        "name": "Tejas Equipment Rental",
        "sales_client_code": "tej",
        "sales_client_id": 10233,
        "sales_pipeline_type": "nightly",
    },
    "ter": {
        "analytics_client_code": "ter",
        "analytics_client_id": 175,
        "client_code": "ter",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Trinity Equipment Rentals",
        "sales_pipeline_type": "vod",
    },
    "terc": {
        "analytics_client_code": "terc",
        "analytics_client_id": 468,
        "appraisals_client_id": 1418,
        "client_code": "terc",
        "client_version": 1,
        "fleet_customer_id": 205,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "TERC Equipment Rental",
        "sales_client_code": "terc",
        "sales_client_id": 468,
        "sales_pipeline_type": "nightly",
    },
    "tes": {
        "analytics_client_code": "tes",
        "analytics_client_id": 12,
        "client_code": "tes",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Trico Equipment",
        "sales_pipeline_type": "vod",
    },
    "tex": {
        "analytics_client_code": "tex",
        "analytics_client_id": 72,
        "appraisals_client_id": 536,
        "client_code": "tex",
        "client_version": 1,
        "fleet_customer_id": 193,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Texas First Rentals",
        "primary_oem": "Caterpillar",
        "sales_client_code": "tex",
        "sales_client_id": 72,
        "sales_pipeline_type": "nightly",
    },
    "tgtuk": {
        "analytics_client_code": "tgtuk",
        "analytics_client_id": 613,
        "appraisals_client_id": 1993,
        "client_code": "tgtuk",
        "client_version": 1,
        "fleet_customer_id": 10281,
        "ims_conversion_status": "ims",
        "name": "Target Plant Hire",
        "sales_client_code": "tgtuk",
        "sales_client_id": 10281,
        "sales_pipeline_type": "nightly",
    },
    "thguk": {
        "analytics_client_code": "thguk",
        "analytics_client_id": 586,
        "appraisals_client_id": 2586,
        "client_code": "thguk",
        "client_version": 1,
        "fleet_customer_id": 10628,
        "ims_conversion_status": "ims",
        "name": "UK Tool Hire",
        "sales_client_code": "thguk",
        "sales_client_id": 10628,
        "sales_pipeline_type": "nightly",
    },
    "tinc": {
        "analytics_client_code": "tinc",
        "analytics_client_id": 400,
        "appraisals_client_id": 2583,
        "client_code": "tinc",
        "client_version": 1,
        "fleet_customer_id": 10625,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Tri-Lift NC",
        "sales_client_code": "tinc",
        "sales_client_id": 10625,
        "sales_pipeline_type": "nightly",
    },
    "tjre": {
        "analytics_client_code": "tjre",
        "analytics_client_id": 634,
        "appraisals_client_id": 2571,
        "client_code": "tjre",
        "client_version": 1,
        "fleet_customer_id": 10613,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Tejas Rent Equip",
        "sales_client_code": "tjre",
        "sales_client_id": 10613,
        "sales_pipeline_type": "nightly",
    },
    "tmc": {
        "analytics_client_code": "tmc",
        "analytics_client_id": 66,
        "appraisals_client_id": 2574,
        "client_code": "tmc",
        "client_version": 1,
        "fleet_customer_id": 10616,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Titan Machinery Co.",
        "sales_client_code": "tmc",
        "sales_client_id": 10616,
        "sales_pipeline_type": "nightly",
    },
    "tmc2": {
        "analytics_client_code": "tmc2",
        "analytics_client_id": 398,
        "appraisals_client_id": 2575,
        "client_code": "tmc2",
        "client_version": 1,
        "fleet_customer_id": 10617,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Titan Machinery (e-Emphasys)",
        "sales_client_code": "tmc2",
        "sales_client_id": 10617,
        "sales_pipeline_type": "nightly",
    },
    "tmhs": {
        "analytics_client_code": "tmhs",
        "analytics_client_id": 402,
        "appraisals_client_id": 2580,
        "client_code": "tmhs",
        "client_version": 1,
        "fleet_customer_id": 10622,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Toyota Material Handling Systems",
        "sales_client_code": "tmhs",
        "sales_client_id": 10622,
        "sales_pipeline_type": "nightly",
    },
    "tmm": {
        "analytics_client_code": "tmm",
        "analytics_client_id": 76,
        "appraisals_client_id": 537,
        "client_code": "tmm",
        "client_version": 2,
        "fleet_customer_id": 35,
        "ims_conversion_status": "ims",
        "name": "Thompson Machinery",
        "sales_client_code": "tmm",
        "sales_client_id": 76,
        "sales_pipeline_type": "nightly",
    },
    "tntc": {
        "analytics_client_code": "tntc",
        "analytics_client_id": 309,
        "appraisals_client_id": 2577,
        "client_code": "tntc",
        "client_version": 1,
        "fleet_customer_id": 10619,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "TNTCrane",
        "sales_client_code": "tntc",
        "sales_client_id": 10619,
        "sales_pipeline_type": "nightly",
    },
    "tntcc": {
        "analytics_client_code": "tntcc",
        "analytics_client_id": 546,
        "appraisals_client_id": 2576,
        "client_code": "tntcc",
        "client_version": 1,
        "fleet_customer_id": 10618,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "TNT Crane Canada",
        "sales_client_code": "tntcc",
        "sales_client_id": 10618,
        "sales_pipeline_type": "nightly",
    },
    "tom": {
        "analytics_client_code": "tom",
        "analytics_client_id": 18,
        "appraisals_client_id": 531,
        "client_code": "tom",
        "client_version": 2,
        "fleet_customer_id": 167,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Thompson Tractor CAT",
        "primary_oem": "Caterpillar",
        "sales_client_code": "tom",
        "sales_client_id": 205,
        "sales_pipeline_type": "nightly",
    },
    "tomc": {
        "analytics_client_code": "tomc",
        "analytics_client_id": 437,
        "appraisals_client_id": 1343,
        "client_code": "tomc",
        "client_version": 1,
        "fleet_customer_id": 184,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Thompson Tractor CRS",
        "primary_oem": "Caterpillar",
        "sales_client_code": "tomc",
        "sales_client_id": 996,
        "sales_pipeline_type": "nightly",
    },
    "tomuk": {
        "analytics_client_code": "tomuk",
        "analytics_client_id": 542,
        "appraisals_client_id": 2572,
        "client_code": "tomuk",
        "client_version": 1,
        "fleet_customer_id": 10614,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Thomas Plant Hire",
        "sales_client_code": "tomuk",
        "sales_client_id": 10614,
        "sales_pipeline_type": "nightly",
    },
    "tor": {
        "analytics_client_code": "tor",
        "analytics_client_id": 140,
        "appraisals_client_id": 562,
        "client_code": "tor",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Toromont CAT",
        "sales_pipeline_type": "vod",
    },
    "torg": {
        "analytics_client_code": "torg",
        "analytics_client_id": 367,
        "appraisals_client_id": 2579,
        "client_code": "torg",
        "client_version": 1,
        "fleet_customer_id": 10621,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Torgersons",
        "sales_client_code": "torg",
        "sales_client_id": 10621,
        "sales_pipeline_type": "nightly",
    },
    "tpgn": {
        "analytics_client_code": "tpgn",
        "analytics_client_id": 663,
        "appraisals_client_id": 2228,
        "client_code": "tpgn",
        "client_version": 1,
        "fleet_customer_id": 10377,
        "ims_conversion_status": "ims",
        "name": "Top Gunn Equipment Rentals",
        "sales_client_code": "tpgn",
        "sales_client_id": 10377,
        "sales_pipeline_type": "nightly",
    },
    "tpi": {
        "analytics_client_code": "tpi",
        "analytics_client_id": 176,
        "client_code": "tpi",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Temp Power Inc",
        "sales_pipeline_type": "vod",
    },
    "trc": {
        "analytics_client_code": "trc",
        "analytics_client_id": 168,
        "client_code": "trc",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Timp Rental",
        "sales_pipeline_type": "vod",
    },
    "trg": {
        "client_code": "trg",
        "client_version": 1,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Taylor Rental Greenville (Not in Use)",
        "sales_pipeline_type": "vod",
    },
    "trgt": {
        "analytics_client_code": "trgt",
        "analytics_client_id": 204,
        "appraisals_client_id": 2569,
        "client_code": "trgt",
        "client_version": 1,
        "fleet_customer_id": 10611,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Target Rental",
        "sales_client_code": "trgt",
        "sales_client_id": 10611,
        "sales_pipeline_type": "nightly",
    },
    "tri": {
        "analytics_client_code": "tri",
        "analytics_client_id": 20,
        "client_code": "tri",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Trico Lift",
        "sales_pipeline_type": "vod",
    },
    "trmp": {
        "analytics_client_code": "trmp",
        "analytics_client_id": 365,
        "appraisals_client_id": 2584,
        "client_code": "trmp",
        "client_version": 1,
        "fleet_customer_id": 10626,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Triumph Modular",
        "sales_client_code": "trmp",
        "sales_client_id": 10626,
        "sales_pipeline_type": "nightly",
    },
    "trmt": {
        "analytics_client_code": "trmt",
        "analytics_client_id": 581,
        "appraisals_client_id": 2573,
        "client_code": "trmt",
        "client_version": 1,
        "fleet_customer_id": 10615,
        "ims_conversion_status": "ims",
        "name": "Time Rental",
        "sales_client_code": "trmt",
        "sales_client_id": 10615,
        "sales_pipeline_type": "nightly",
    },
    "trnr": {
        "analytics_client_code": "trnr",
        "analytics_client_id": 792,
        "appraisals_client_id": 3036,
        "client_code": "trnr",
        "client_version": 1,
        "fleet_customer_id": 10787,
        "ims_conversion_status": "ims",
        "name": "Turner Mining Group",
        "sales_client_code": "trnr",
        "sales_client_id": 10787,
        "sales_pipeline_type": "nightly",
    },
    "tru7uk": {
        "analytics_client_code": "tru7uk",
        "analytics_client_id": 580,
        "appraisals_client_id": 2030,
        "client_code": "tru7uk",
        "client_version": 1,
        "fleet_customer_id": 10297,
        "ims_conversion_status": "ims",
        "name": "Tru7",
        "sales_client_code": "tru7uk",
        "sales_client_id": 10297,
        "sales_pipeline_type": "nightly",
    },
    "tsa": {
        "analytics_client_code": "tsa",
        "analytics_client_id": 329,
        "appraisals_client_id": 1122,
        "client_code": "tsa",
        "client_version": 1,
        "fleet_customer_id": 217,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Time Savers Aerials",
        "sales_client_code": "tsa",
        "sales_client_id": 329,
        "sales_pipeline_type": "nightly",
    },
    "tsbo": {
        "analytics_client_code": "tsbo",
        "analytics_client_id": 377,
        "client_code": "tsbo",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Tri-State Bobcat",
        "sales_pipeline_type": "vod",
    },
    "tsbo2": {
        "analytics_client_code": "tsbo2",
        "analytics_client_id": 740,
        "appraisals_client_id": 2881,
        "client_code": "tsbo2",
        "client_version": 1,
        "fleet_customer_id": 10701,
        "ims_conversion_status": "ims",
        "name": "Tri-State Bobcat",
        "sales_client_code": "tsbo2",
        "sales_client_id": 10701,
        "sales_pipeline_type": "nightly",
    },
    "tsco": {
        "analytics_client_code": "tsco",
        "analytics_client_id": 420,
        "appraisals_client_id": 2582,
        "client_code": "tsco",
        "client_version": 1,
        "fleet_customer_id": 10624,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Trench Shoring Company",
        "sales_client_code": "tsco",
        "sales_client_id": 10624,
        "sales_pipeline_type": "nightly",
    },
    "tvrg": {
        "analytics_client_code": "tvrg",
        "analytics_client_id": 203,
        "appraisals_client_id": 2585,
        "client_code": "tvrg",
        "client_version": 1,
        "fleet_customer_id": 10627,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "True Value Rental of Greensboro",
        "sales_client_code": "tvrg",
        "sales_client_id": 10627,
        "sales_pipeline_type": "nightly",
    },
    "tyl": {
        "analytics_client_code": "tyl",
        "analytics_client_id": 125,
        "appraisals_client_id": 1047,
        "client_code": "tyl",
        "client_version": 1,
        "fleet_customer_id": 71,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Tyler Rental",
        "sales_client_code": "tyl",
        "sales_client_id": 125,
        "sales_pipeline_type": "nightly",
    },
    "u1": {
        "analytics_client_code": "u1",
        "analytics_client_id": 1,
        "appraisals_client_id": 651,
        "client_code": "u1",
        "client_version": 1,
        "fleet_customer_id": 6,
        "ims_conversion_status": "ims",
        "market_segment": "National",
        "name": "United Rentals, Inc.",
        "sales_client_code": "u1",
        "sales_client_id": 2,
        "sales_pipeline_type": "nightly",
    },
    "u1c": {
        "analytics_client_code": "u1c",
        "analytics_client_id": 110,
        "client_code": "u1c",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "URI Canada",
        "sales_pipeline_type": "vod",
    },
    "ucf": {
        "analytics_client_code": "uncf",
        "analytics_client_id": 464,
        "appraisals_client_id": 1757,
        "client_code": "ucf",
        "client_version": 1,
        "fleet_customer_id": 116,
        "ims_conversion_status": "ims",
        "market_segment": "-",
        "name": "United Construction \u0026 Forestry",
        "primary_oem": "-",
        "sales_client_code": "ucf",
        "sales_client_id": 920,
        "sales_pipeline_type": "nightly",
    },
    "ultuk": {
        "analytics_client_code": "ultuk",
        "analytics_client_id": 614,
        "appraisals_client_id": 2003,
        "client_code": "ultuk",
        "client_version": 1,
        "fleet_customer_id": 10285,
        "ims_conversion_status": "ims",
        "name": "Ultimate Access Hire",
        "sales_client_code": "ultuk",
        "sales_client_id": 10285,
        "sales_pipeline_type": "nightly",
    },
    "uncf": {
        "client_code": "uncf",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "United Construction \u0026 Forestry",
        "sales_pipeline_type": "vod",
    },
    "untejp": {
        "analytics_client_code": "untejp",
        "analytics_client_id": 674,
        "client_code": "untejp",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Unite",
        "sales_pipeline_type": "vod",
    },
    "uos": {
        "analytics_client_code": "uos",
        "analytics_client_id": 129,
        "appraisals_client_id": 871,
        "client_code": "uos",
        "client_version": 1,
        "fleet_customer_id": 81,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Custom Truck One Source",
        "sales_client_code": "uos",
        "sales_client_id": 129,
        "sales_pipeline_type": "nightly",
    },
    "upwuk": {
        "analytics_client_code": "upwuk",
        "analytics_client_id": 567,
        "client_code": "upwuk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Upward Access Hire",
        "sales_pipeline_type": "vod",
    },
    "ust": {
        "client_code": "ust",
        "client_version": 2,
        "ims_conversion_status": "legacy",
        "name": "US Trinity - Do not use",
        "sales_pipeline_type": "vod",
    },
    "usta": {
        "client_code": "usta",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "US Trinity",
        "sales_pipeline_type": "nightly",
    },
    "valy": {
        "analytics_client_code": "valy",
        "analytics_client_id": 501,
        "appraisals_client_id": 2587,
        "client_code": "valy",
        "client_version": 1,
        "fleet_customer_id": 10629,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Valley Equipment Rents",
        "sales_client_code": "valy",
        "sales_client_id": 10629,
        "sales_pipeline_type": "nightly",
    },
    "van": {
        "analytics_client_code": "van",
        "analytics_client_id": 178,
        "appraisals_client_id": 738,
        "client_code": "van",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Vantage Equipment LLC",
        "sales_pipeline_type": "vod",
    },
    "vces": {
        "analytics_client_code": "vces",
        "analytics_client_id": 227,
        "appraisals_client_id": 499,
        "client_code": "vces",
        "client_version": 1,
        "fleet_customer_id": 10050,
        "ims_conversion_status": "ims",
        "name": "Volvo Construction Equipment \u0026 Services of North America",
        "sales_client_code": "vces",
        "sales_client_id": 390,
        "sales_pipeline_type": "nightly",
    },
    "vcon": {
        "analytics_client_code": "vcon",
        "analytics_client_id": 765,
        "appraisals_client_id": 2959,
        "client_code": "vcon",
        "client_version": 1,
        "fleet_customer_id": 10755,
        "ims_conversion_status": "ims",
        "name": "VanCon",
        "sales_client_code": "vcon",
        "sales_client_id": 10755,
        "sales_pipeline_type": "nightly",
    },
    "ver": {
        "analytics_client_code": "ver",
        "analytics_client_id": 189,
        "appraisals_client_id": 1426,
        "client_code": "ver",
        "client_version": 1,
        "fleet_customer_id": 206,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Vandalia Rental",
        "primary_oem": "-",
        "sales_client_code": "ver",
        "sales_client_id": 189,
        "sales_pipeline_type": "nightly",
    },
    "vhr": {
        "analytics_client_code": "vhr",
        "analytics_client_id": 631,
        "appraisals_client_id": 2588,
        "client_code": "vhr",
        "client_version": 1,
        "fleet_customer_id": 10630,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "VHR Rental",
        "sales_client_code": "vhr",
        "sales_client_id": 10630,
        "sales_pipeline_type": "nightly",
    },
    "vol": {
        "analytics_client_code": "vol",
        "analytics_client_id": -500,
        "client_code": "vol",
        "client_version": 1,
        "fleet_customer_id": 108,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Volvo Construction Equipment",
        "primary_oem": "volvo",
        "sales_client_code": "vol",
        "sales_client_id": 901,
        "sales_pipeline_type": "vod",
    },
    "wag": {
        "analytics_client_code": "wag",
        "analytics_client_id": 29,
        "appraisals_client_id": 532,
        "client_code": "wag",
        "client_version": 1,
        "fleet_customer_id": 36,
        "ims_conversion_status": "ims",
        "name": "Wagner Equipment Co.",
        "sales_client_code": "wag",
        "sales_client_id": 29,
        "sales_pipeline_type": "nightly",
    },
    "wagh": {
        "analytics_client_code": "wagh",
        "analytics_client_id": 81,
        "client_code": "wagh",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Wagner Heavy Rents",
        "sales_pipeline_type": "vod",
    },
    "war": {
        "analytics_client_code": "war",
        "analytics_client_id": 39,
        "appraisals_client_id": 375,
        "client_code": "war",
        "client_version": 1,
        "fleet_customer_id": 37,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Warren Power \u0026 Machinery, Inc.",
        "primary_oem": "Caterpillar",
        "sales_client_code": "war",
        "sales_client_id": 405,
        "sales_pipeline_type": "nightly",
    },
    "ward": {
        "analytics_client_code": "ward",
        "analytics_client_id": 593,
        "appraisals_client_id": 2590,
        "client_code": "ward",
        "client_version": 1,
        "fleet_customer_id": 10632,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Wards Rental Center",
        "sales_client_code": "ward",
        "sales_client_id": 10632,
        "sales_pipeline_type": "nightly",
    },
    "wce": {
        "analytics_client_code": "wce",
        "analytics_client_id": 228,
        "appraisals_client_id": 763,
        "client_code": "wce",
        "client_version": 1,
        "fleet_customer_id": 69,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "West Coast Equipment",
        "sales_client_code": "wce",
        "sales_client_id": 228,
        "sales_pipeline_type": "nightly",
    },
    "we1": {
        "analytics_client_code": "we1",
        "analytics_client_id": 128,
        "client_code": "we1",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "WesternOne WE1",
        "sales_pipeline_type": "vod",
    },
    "weat": {
        "analytics_client_code": "weat",
        "analytics_client_id": 344,
        "appraisals_client_id": 2591,
        "client_code": "weat",
        "client_version": 1,
        "fleet_customer_id": 10633,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Weathers Rental Center",
        "sales_client_code": "weat",
        "sales_client_id": 10633,
        "sales_pipeline_type": "nightly",
    },
    "weav": {
        "analytics_client_code": "weav",
        "analytics_client_id": 452,
        "appraisals_client_id": 2592,
        "client_code": "weav",
        "client_version": 1,
        "fleet_customer_id": 10634,
        "ims_conversion_status": "ims",
        "name": "Weavers Rent All",
        "sales_client_code": "weav",
        "sales_client_id": 10634,
        "sales_pipeline_type": "nightly",
    },
    "wec": {
        "analytics_client_code": "wec",
        "analytics_client_id": 185,
        "appraisals_client_id": 2595,
        "client_code": "wec",
        "client_version": 1,
        "fleet_customer_id": 10637,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Wilson Equipment",
        "sales_client_code": "wec",
        "sales_client_id": 10637,
        "sales_pipeline_type": "nightly",
    },
    "weq": {
        "analytics_client_code": "weq",
        "analytics_client_id": 99,
        "client_code": "weq",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "WesternOne Equpiment Company",
        "sales_pipeline_type": "vod",
    },
    "wfhuk": {
        "analytics_client_code": "wfhuk",
        "analytics_client_id": 491,
        "client_code": "wfhuk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Welfare 4 Hire",
        "sales_pipeline_type": "vod",
    },
    "wha": {
        "analytics_client_code": "wha",
        "analytics_client_id": 61,
        "appraisals_client_id": 734,
        "client_code": "wha",
        "client_version": 1,
        "fleet_customer_id": 38,
        "ims_conversion_status": "ims",
        "name": "Whayne CAT",
        "sales_client_code": "wha",
        "sales_client_id": 61,
        "sales_pipeline_type": "nightly",
    },
    "whmh": {
        "analytics_client_code": "whmh",
        "analytics_client_id": 288,
        "appraisals_client_id": 2594,
        "client_code": "whmh",
        "client_version": 1,
        "fleet_customer_id": 10636,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Wheeler Material Handling",
        "sales_client_code": "whmh",
        "sales_client_id": 10636,
        "sales_pipeline_type": "nightly",
    },
    "wic": {
        "analytics_client_code": "wic",
        "analytics_client_id": 359,
        "appraisals_client_id": 2153,
        "client_code": "wic",
        "client_version": 1,
        "fleet_customer_id": 10345,
        "ims_conversion_status": "ims",
        "name": "WI Clark Company",
        "sales_client_code": "wic",
        "sales_client_id": 10345,
        "sales_pipeline_type": "nightly",
    },
    "wine": {
        "analytics_client_code": "wine",
        "analytics_client_id": 496,
        "appraisals_client_id": 2447,
        "client_code": "wine",
        "client_version": 1,
        "fleet_customer_id": 10489,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Brandywine Rentals",
        "sales_client_code": "wine",
        "sales_client_id": 10489,
        "sales_pipeline_type": "nightly",
    },
    "wire": {
        "analytics_client_code": "wire",
        "analytics_client_id": 751,
        "appraisals_client_id": 2916,
        "client_code": "wire",
        "client_version": 1,
        "fleet_customer_id": 10733,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Wiregrass Construction",
        "sales_client_code": "wire",
        "sales_client_id": 10733,
        "sales_pipeline_type": "nightly",
    },
    "wlrg": {
        "analytics_client_code": "wlrg",
        "analytics_client_id": 658,
        "appraisals_client_id": 2596,
        "client_code": "wlrg",
        "client_version": 1,
        "fleet_customer_id": 10638,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Wolter Inc",
        "sales_client_code": "wlrg",
        "sales_client_id": 10638,
        "sales_pipeline_type": "nightly",
    },
    "wmc": {
        "analytics_client_code": "wmc",
        "analytics_client_id": 112,
        "appraisals_client_id": 543,
        "client_code": "wmc",
        "client_version": 1,
        "fleet_customer_id": 221,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Wheeler Machinery Co.",
        "primary_oem": "Caterpillar",
        "sales_client_code": "wmc",
        "sales_client_id": 112,
        "sales_pipeline_type": "nightly",
    },
    "wogr": {
        "analytics_client_code": "wogr",
        "analytics_client_id": 269,
        "appraisals_client_id": 2589,
        "client_code": "wogr",
        "client_version": 1,
        "fleet_customer_id": 10631,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "W.O. Grubb",
        "sales_client_code": "wogr",
        "sales_client_id": 10631,
        "sales_pipeline_type": "nightly",
    },
    "wolf": {
        "analytics_client_code": "wolf",
        "analytics_client_id": 638,
        "appraisals_client_id": 2199,
        "client_code": "wolf",
        "client_version": 1,
        "fleet_customer_id": 10361,
        "ims_conversion_status": "ims",
        "name": "Greywolf Equipment",
        "sales_client_code": "wolf",
        "sales_client_id": 10361,
        "sales_pipeline_type": "nightly",
    },
    "wood": {
        "analytics_client_code": "wood",
        "analytics_client_id": -500,
        "client_code": "wood",
        "client_version": 1,
        "fleet_customer_id": 109,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Woodstock Equipment Company",
        "primary_oem": "-",
        "sales_client_code": "wood",
        "sales_client_id": 902,
        "sales_pipeline_type": "vod",
    },
    "woud": {
        "analytics_client_code": "woud",
        "analytics_client_id": 440,
        "appraisals_client_id": 2597,
        "client_code": "woud",
        "client_version": 1,
        "fleet_customer_id": 10639,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Woudenberg Enterprises",
        "sales_client_code": "woud",
        "sales_client_id": 10639,
        "sales_pipeline_type": "nightly",
    },
    "wri": {
        "analytics_client_code": "wri",
        "analytics_client_id": 143,
        "client_code": "wri",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "We Rent It",
        "sales_pipeline_type": "vod",
    },
    "wrk": {
        "analytics_client_code": "wrk",
        "analytics_client_id": 448,
        "appraisals_client_id": 1372,
        "client_code": "wrk",
        "client_version": 1,
        "fleet_customer_id": 198,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Works Equipment Rental",
        "sales_client_code": "wrk",
        "sales_client_id": 448,
        "sales_pipeline_type": "nightly",
    },
    "wse": {
        "analytics_client_code": "wse",
        "analytics_client_id": 52,
        "appraisals_client_id": 429,
        "client_code": "wse",
        "client_version": 1,
        "fleet_customer_id": 64,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Western States Equipment Co.",
        "primary_oem": "Caterpillar",
        "sales_client_code": "wse",
        "sales_client_id": 52,
        "sales_pipeline_type": "nightly",
    },
    "wslc": {
        "analytics_client_code": "wslc",
        "analytics_client_id": 388,
        "appraisals_client_id": 2593,
        "client_code": "wslc",
        "client_version": 1,
        "fleet_customer_id": 10635,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Western States Equipment",
        "sales_client_code": "wslc",
        "sales_client_id": 10635,
        "sales_pipeline_type": "nightly",
    },
    "wst": {
        "analytics_client_code": "wst",
        "analytics_client_id": 111,
        "appraisals_client_id": 617,
        "client_code": "wst",
        "client_version": 1,
        "fleet_customer_id": 62,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "West Side Tractor Sales Co.",
        "primary_oem": "john deere",
        "sales_client_code": "wst",
        "sales_client_id": 111,
        "sales_pipeline_type": "nightly",
    },
    "wwm": {
        "analytics_client_code": "wwm",
        "analytics_client_id": 328,
        "appraisals_client_id": 64,
        "client_code": "wwm",
        "client_version": 1,
        "fleet_customer_id": 10047,
        "ims_conversion_status": "ims",
        "market_segment": "Independent",
        "name": "Worldwide Machinery",
        "sales_client_code": "wwm",
        "sales_client_id": 328,
        "sales_pipeline_type": "nightly",
    },
    "wxla": {
        "analytics_client_code": "wxla",
        "analytics_client_id": 656,
        "client_code": "wxla",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Westrax Machinery",
        "sales_pipeline_type": "vod",
    },
    "wyc": {
        "analytics_client_code": "wyc",
        "analytics_client_id": 171,
        "appraisals_client_id": 1119,
        "client_code": "wyc",
        "client_version": 1,
        "fleet_customer_id": 43,
        "ims_conversion_status": "ims",
        "market_segment": "OEM",
        "name": "Wyoming CAT",
        "primary_oem": "Caterpillar",
        "sales_client_code": "wyc",
        "sales_client_id": 171,
        "sales_pipeline_type": "nightly",
    },
    "xau": {
        "analytics_client_code": "xau",
        "analytics_client_id": -9,
        "client_code": "xau",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "XAU Rentals",
        "sales_pipeline_type": "vod",
    },
    "xcmg": {
        "analytics_client_code": "xcmg",
        "analytics_client_id": -500,
        "client_code": "xcmg",
        "client_version": 1,
        "fleet_customer_id": 106,
        "ims_conversion_status": "ims",
        "market_segment": "financial",
        "name": "XCMG North America Corporation",
        "primary_oem": "-",
        "sales_client_code": "xcmg",
        "sales_client_id": 725,
        "sales_pipeline_type": "vod",
    },
    "xcn": {
        "analytics_client_code": "xcn",
        "analytics_client_id": -4,
        "client_code": "xcn",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "XCN Rentals",
        "sales_pipeline_type": "vod",
    },
    "xeu": {
        "analytics_client_code": "xeu",
        "analytics_client_id": -12,
        "client_code": "xeu",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "XEU Rentals",
        "sales_pipeline_type": "vod",
    },
    "xhd": {
        "analytics_client_code": "xhd",
        "analytics_client_id": -11,
        "client_code": "xhd",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "XHD Rentals",
        "sales_pipeline_type": "vod",
    },
    "xjp": {
        "analytics_client_code": "xjp",
        "analytics_client_id": -10,
        "client_code": "xjp",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "XJP Rentals",
        "sales_pipeline_type": "vod",
    },
    "xuk": {
        "analytics_client_code": "xuk",
        "analytics_client_id": -8,
        "client_code": "xuk",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "XUK Rentals",
        "sales_pipeline_type": "vod",
    },
    "xyz": {
        "analytics_client_code": "xyz",
        "analytics_client_id": -1,
        "appraisals_client_id": 462,
        "client_code": "xyz",
        "client_version": 1,
        "fleet_customer_id": 41,
        "ims_conversion_status": "ims",
        "name": "XYZ Company",
        "sales_client_code": "xyzs",
        "sales_client_id": 15,
        "sales_pipeline_type": "nightly",
    },
    "xyza": {
        "client_code": "xyza",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "XYZ Company Appraisals",
        "sales_pipeline_type": "vod",
    },
    "xyzef": {
        "analytics_client_code": "xyzef",
        "appraisals_client_id": 2034,
        "client_code": "xyzef",
        "client_version": 1,
        "fleet_customer_id": 10300,
        "ims_conversion_status": "ims",
        "name": "XYZ Equipment Finance",
        "sales_client_code": "xyzef",
        "sales_client_id": 10300,
        "sales_pipeline_type": "nightly",
    },
    "xyzeu": {
        "analytics_client_code": "xyzeu",
        "analytics_client_id": 596,
        "appraisals_client_id": 1949,
        "client_code": "xyzeu",
        "client_version": 1,
        "fleet_customer_id": 10264,
        "ims_conversion_status": "ims",
        "name": "XYZ Company - EU",
        "sales_client_code": "xyzeu",
        "sales_client_id": 10264,
        "sales_pipeline_type": "nightly",
    },
    "xyzuk": {
        "analytics_client_code": "xyzuk",
        "analytics_client_id": 595,
        "appraisals_client_id": 1948,
        "client_code": "xyzuk",
        "client_version": 1,
        "fleet_customer_id": 10263,
        "ims_conversion_status": "ims",
        "name": "XYZ Company - UK",
        "sales_client_code": "xyzuk",
        "sales_client_id": 10263,
        "sales_pipeline_type": "nightly",
    },
    "yan": {
        "analytics_client_code": "yan",
        "analytics_client_id": 92,
        "client_code": "yan",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "Yancey Rents",
        "sales_pipeline_type": "vod",
    },
    "ybo": {
        "analytics_client_code": "ybo",
        "analytics_client_id": 16,
        "appraisals_client_id": 482,
        "client_code": "ybo",
        "client_version": 1,
        "fleet_customer_id": 39,
        "ims_conversion_status": "ims",
        "name": "Yancy Bros. Co.",
        "sales_client_code": "ybo",
        "sales_client_id": 770,
        "sales_pipeline_type": "nightly",
    },
    "year": {
        "analytics_client_code": "year",
        "analytics_client_id": 723,
        "client_code": "year",
        "client_version": 1,
        "ims_conversion_status": "legacy",
        "name": "365 Equipment Supply",
        "sales_pipeline_type": "vod",
    },
    "ymc": {
        "analytics_client_code": "ymc",
        "analytics_client_id": 151,
        "appraisals_client_id": 632,
        "client_code": "ymc",
        "client_version": 1,
        "fleet_customer_id": 40,
        "ims_conversion_status": "ims",
        "market_segment": "oem",
        "name": "Yellowhouse Machinery Co.",
        "primary_oem": "john deere",
        "sales_client_code": "ymc",
        "sales_client_id": 151,
        "sales_pipeline_type": "nightly",
    },
    "zaci": {
        "analytics_client_code": "zaci",
        "analytics_client_id": 415,
        "appraisals_client_id": 1259,
        "client_code": "zaci",
        "client_version": 1,
        "fleet_customer_id": 130,
        "ims_conversion_status": "ims",
        "market_segment": "independent",
        "name": "Zachry Inc",
        "primary_oem": "-",
        "sales_client_code": "zaci",
        "sales_client_id": 988,
        "sales_pipeline_type": "nightly",
    },
    "zax": {
        "analytics_client_code": "zax",
        "analytics_client_id": 605,
        "appraisals_client_id": 1960,
        "client_code": "zax",
        "client_version": 1,
        "fleet_customer_id": 10267,
        "ims_conversion_status": "ims",
        "name": "Zaxis Financial Services Americas",
        "sales_client_code": "zax",
        "sales_client_id": 10267,
        "sales_pipeline_type": "nightly",
    },
    "zepde": {
        "analytics_client_code": "zepde",
        "analytics_client_id": 413,
        "appraisals_client_id": 2598,
        "client_code": "zepde",
        "client_version": 1,
        "fleet_customer_id": 10640,
        "ims_conversion_status": "ims",
        "market_segment": "rental",
        "name": "Zeppelin Rental",
        "sales_client_code": "zepde",
        "sales_client_id": 10640,
        "sales_pipeline_type": "nightly",
    },
    "zig": {
        "analytics_client_code": "zig",
        "analytics_client_id": 67,
        "appraisals_client_id": 535,
        "client_code": "zig",
        "client_version": 1,
        "fleet_customer_id": 222,
        "ims_conversion_status": "ims",
        "name": "Ziegler",
        "sales_client_code": "zig",
        "sales_client_id": 67,
        "sales_pipeline_type": "nightly",
    },
}
folder_client_mappings = {
    "AAARentAll": {"client_code": "aaa"},
    "ABToolRental": {"client_code": "abtr"},
    "AEEquipment": {"client_code": "aee"},
    "AFIUplift": {"client_code": "afiuk"},
    "AISConstructionEquipment": {"client_code": "ais"},
    "ASCO2": {"client_code": "asco"},
    "ASCOeEmphasys": {"client_code": "asco"},
    "ASCVolvo": {"client_code": "asc"},
    "AToolShedRentals": {"client_code": "atsr"},
    "AbleEquipment": {"client_code": "abl"},
    "AbleWynne": {"client_code": "abl"},
    "AcmeBobcat": {"client_code": "acmb"},
    "ActionRental": {"client_code": "act"},
    "Admar": {"client_code": "adm"},
    "AerialAccess": {"client_code": "aae"},
    "Aggreko": {"client_code": "agg"},
    "Ahern": {"client_code": "ar"},
    "Airgas": {"client_code": "air"},
    "AlbanCATIRS": {"client_code": "alb"},
    "AllStarEquipment": {"client_code": "ase"},
    "AllcottHire": {"client_code": "allau"},
    "Allstarrents": {"client_code": "asr"},
    "AltaEquipment": {"client_code": "alta"},
    "AltorferCAT": {"client_code": "pat"},
    "AmericanRentals": {"client_code": "duke"},
    "AmosMetzRental": {"client_code": "amos"},
    "AreaEquip": {"client_code": "are"},
    "ArrowLift": {"client_code": "arw"},
    "AshevilleHwy": {"client_code": "ash"},
    "AspenRentAll": {"client_code": "ara"},
    "AtlanticAerialsCo": {"client_code": "aai"},
    "AtlanticLiftSystems": {"client_code": "als"},
    "AtoZEquipmentRentalsandSales": {"client_code": "atoz"},
    "BATM": {"client_code": "batm"},
    "BATN": {"client_code": "batn"},
    "BATO": {"client_code": "bato"},
    "BATQ": {"client_code": "batq"},
    "BMEquipment": {"client_code": "bme"},
    "BeardEquip": {"client_code": "brd"},
    "BerryCompanies": {"client_code": "bci"},
    "BestLineEquipment": {"client_code": "best"},
    "BigOrangeRental": {"client_code": "bor"},
    "BinghamEquipment": {"client_code": "bec"},
    "BlackDiamondEquipmentRental": {"client_code": "bder"},
    "Blanchard": {"client_code": "bla"},
    "BlueLine": {"client_code": "blr"},
    "BlueLineCanada": {"client_code": "blc"},
    "BobcatStLouis": {"client_code": "bstl"},
    "BottomLine": {"client_code": "ble"},
    "Bramco": {"client_code": "bra"},
    "BriggsEquipment": {"client_code": "brg"},
    "BriggsEquipmentUK": {"client_code": "brguk"},
    "BroadlineEquipmentRentals": {"client_code": "ber"},
    "BulletRental": {"client_code": "bull"},
    "Butler": {"client_code": "but"},
    "ButlerCAT": {"client_code": "bmc"},
    "CERentals": {"client_code": "cer"},
    "CLBoyd": {"client_code": "boyd"},
    "CarolinaTractor": {"client_code": "car"},
    "CarterMachineryIRental": {"client_code": "ctr"},
    "CasaleRentAll": {"client_code": "cra"},
    "CashmanCAT": {"client_code": "csh"},
    "ChampRentals": {"client_code": "chr"},
    "ChaseCo": {"client_code": "chsc"},
    "ChetsRentAll": {"client_code": "chet"},
    "CiscoEquipment": {"client_code": "cis"},
    "CityRentals": {"client_code": "city"},
    "ClevelandCATIRS": {"client_code": "clb"},
    "CompanyWrench": {"client_code": "cwl"},
    "ContractorsBuildingSupply": {"client_code": "cbs"},
    "ContractorsEquipmentCenter": {"client_code": "cec"},
    "CooperEquipment": {"client_code": "coop"},
    "CooperRental_Direct": {"client_code": "coop"},
    "Cresco": {"client_code": "crs"},
    "CrownRental": {"client_code": "crwn"},
    "DandBRental": {"client_code": "dbrs"},
    "DeckerToolRental": {"client_code": "deck"},
    "DefatteEquipment": {"client_code": "defa"},
    "DeltaMaterialsHandling": {"client_code": "dmh"},
    "DiamondRental": {"client_code": "dmnd"},
    "DobbsEquipment": {"client_code": "dobb"},
    "Doggett": {"client_code": "dogg"},
    "DotsRentalsAndSales": {"client_code": "dots"},
    "DoubleRRentals": {"client_code": "drr"},
    "Durante": {"client_code": "dur"},
    "DuranteEquipment": {"client_code": "durf"},
    "DuranteRentals": {"client_code": "dur"},
    "EZERentItCentre": {"client_code": "eze"},
    "EastTennRentAlls": {"client_code": "etra"},
    "EmeryEquipment": {"client_code": "emry"},
    "EmpireIRS": {"client_code": "emp"},
    "EquipmentDepot": {"client_code": "eqd"},
    "ErbEquipment": {"client_code": "erb"},
    "ExploreTransport": {"client_code": "eptuk"},
    "FMEquipment": {"client_code": "fme"},
    "FabickCAT": {"client_code": "fab"},
    "FinningCAT": {"client_code": "fin"},
    "FiveStarEquipment": {"client_code": "fse"},
    "FlintEquipment": {"client_code": "fec"},
    "Foley": {"client_code": "fol"},
    "FoleyEquipment": {"client_code": "fks"},
    "FranklinEquipment": {"client_code": "fra"},
    "GMEquipment": {"client_code": "gmer"},
    "GTAEquipmentRentals": {"client_code": "gta"},
    "GWEquipment": {"client_code": "gwe"},
    "GardenStateBobcat": {"client_code": "gsb"},
    "GeneralEquipment": {"client_code": "ges"},
    "GeorgeToolRental": {"client_code": "gtr"},
    "GrandEquipment": {"client_code": "gec"},
    "GreatPlains": {"client_code": "gper"},
    "GreatWestEquipment": {"client_code": "gweq"},
    "GregoryPoole": {"client_code": "gpc"},
    "GroffTractor": {"client_code": "grof"},
    "HEMetrics": {"client_code": "hem"},
    "HOPenn": {"client_code": "hop"},
    "HamlinEquipmentRental": {"client_code": "haml"},
    "HarnishGroupIRS": {"client_code": "hgi"},
    "HawthorneCATIRS": {"client_code": "haw"},
    "HeavyMachines": {"client_code": "hmi"},
    "HercULift": {"client_code": "hul"},
    "HercUS": {"client_code": "hg"},
    "Hertz": {"client_code": "hg"},
    "HighReach": {"client_code": "hre"},
    "HighwayEquipment": {"client_code": "hwy"},
    "HoltCat": {"client_code": "hlt"},
    "HoltOfCA": {"client_code": "hlc"},
    "HonnenEquipment": {"client_code": "honn"},
    "HoskinsEquipment": {"client_code": "hosk"},
    "HoweRental\u0026Sales": {"client_code": "howe"},
    "HubEquipment": {"client_code": "hub"},
    "HuggHall": {"client_code": "hh"},
    "HullBrothers": {"client_code": "hbr"},
    "IllinoisTruckAndEquipment": {"client_code": "ite"},
    "JJCurran": {"client_code": "jjc"},
    "JPSEquipment": {"client_code": "jps"},
    "JamesRiverCDK": {"client_code": "jre"},
    "Jesco": {"client_code": "jes"},
    "JohnDeereConstruction": {"client_code": "jdc"},
    "KellyTractorCAT": {"client_code": "ktc"},
    "Kiewit": {"client_code": "kie"},
    "KirbySmithMachinery": {"client_code": "kbs"},
    "KomatsuEquipmentCo": {"client_code": "kec"},
    "LanoEquipment": {"client_code": "lano"},
    "LeavittMachinery": {"client_code": "lvm"},
    "LegacyEquipment": {"client_code": "leg"},
    "Leppo": {"client_code": "lepp"},
    "LewistownRental": {"client_code": "lew"},
    "LiftInc": {"client_code": "linc"},
    "LiftworksPoR": {"client_code": "lwi"},
    "LincolnSupply": {"client_code": "lcs"},
    "Linder": {"client_code": "lndr"},
    "LocationIdeal": {"client_code": "lid"},
    "LoneStar": {"client_code": "lsfl"},
    "LouTec": {"client_code": "lou"},
    "LouisianaCATIRS": {"client_code": "lmc"},
    "LouisianaLift": {"client_code": "lle"},
    "MacAllister": {"client_code": "mmc"},
    "Maxim": {"client_code": "max"},
    "MeadeEquipment": {"client_code": "mec"},
    "MidCountryMachinery": {"client_code": "mcm"},
    "MidwayRentalAndSales": {"client_code": "mrs"},
    "MiltonCAT": {"client_code": "milt"},
    "ModernMachinery": {"client_code": "mm"},
    "MurphyTractor": {"client_code": "mtec"},
    "MustangCAT": {"client_code": "mus"},
    "NMCCat": {"client_code": "nmc"},
    "Neff": {"client_code": "nr"},
    "NorrisSalesCompany": {"client_code": "nsci"},
    "Nortrax": {"client_code": "nor"},
    "NortraxCanada": {"client_code": "norc"},
    "NortraxCanadaJDBS": {"client_code": "norc"},
    "ORERentals": {"client_code": "ore"},
    "OhioCat": {"client_code": "ohc"},
    "OrangeHire": {"client_code": "ohau"},
    "PDQ": {"client_code": "pdq"},
    "Patten": {"client_code": "pat"},
    "PetersonCat": {"client_code": "pet"},
    "Power-Equip": {"client_code": "peco"},
    "PowerMotive": {"client_code": "pmc"},
    "PowerRents": {"client_code": "prer"},
    "PriorityEquipment": {"client_code": "pri"},
    "ProSourceMachinery": {"client_code": "psm"},
    "Puckett": {"client_code": "pm"},
    "PuckettHeavy": {"client_code": "pmh"},
    "QuinnCompany": {"client_code": "qco"},
    "RDOEquipment": {"client_code": "rdo"},
    "REICIntempo": {"client_code": "reic"},
    "RPMMachinery": {"client_code": "rpm"},
    "RRRentals": {"client_code": "rrr"},
    "Rabern": {"client_code": "rab"},
    "RedMountainRentals": {"client_code": "rmr"},
    "RentalNetwork": {"client_code": "rnwk"},
    "RentalOne": {"client_code": "mro"},
    "RexcoEquipment": {"client_code": "rex"},
    "Riggs": {"client_code": "jar"},
    "RingPower": {"client_code": "rng"},
    "RiverCityEquipment": {"client_code": "rcer"},
    "RiwalUK": {"client_code": "rwuk"},
    "RoadMachineryAndSupplyCo": {"client_code": "rms"},
    "RoadMachineryDTT": {"client_code": "rm"},
    "RoadMachinery_CrystalReport": {"client_code": "rm"},
    "RolandMachinery": {"client_code": "rol"},
    "Runyon2": {"client_code": "rnyn"},
    "SERV": {"client_code": "serv"},
    "SabreRentals": {"client_code": "sab"},
    "Simplex": {"client_code": "simp"},
    "SimsCraneAndEquipment": {"client_code": "sims"},
    "SmithBrosContractingEquipment": {"client_code": "sbce"},
    "SoutheasternEquipment": {"client_code": "seq"},
    "StephensonsRental": {"client_code": "srs"},
    "Stowers": {"client_code": "stw"},
    "Stribling": {"client_code": "stb"},
    "SunRentalCenterOhio": {"client_code": "srci"},
    "SunStateMetrics": {"client_code": "sec"},
    "Sunbelt": {"client_code": "sbt"},
    "SunbeltMetrics": {"client_code": "sbt"},
    "TEC": {"client_code": "tec"},
    "TargetRentals": {"client_code": "trgt"},
    "TejasPoR": {"client_code": "tej"},
    "TempPower": {"client_code": "tpi"},
    "TexasFirstRentals": {"client_code": "tex"},
    "TheRentalMen": {"client_code": "rmen"},
    "ThompsonAX": {"client_code": "tmm"},
    "ThompsonIRS": {"client_code": "tom"},
    "TitanRnt": {"client_code": "tmc"},
    "ToromontCAT": {"client_code": "tor"},
    "TrinityEquipment": {"client_code": "ter"},
    "TrueValueRentalGreensboro": {"client_code": "tvrg"},
    "UnitedRentals": {"client_code": "u1"},
    "Vandalia": {"client_code": "ver"},
    "VantageEquipment": {"client_code": "van"},
    "VolvoConstructionEquipment": {"client_code": "vces"},
    "WagnerHeavyIRS": {"client_code": "wag"},
    "WagnerHeavyInfor": {"client_code": "wag"},
    "WarrenCat": {"client_code": "war"},
    "WestCoastEquipment": {"client_code": "wce"},
    "WestSideTractor": {"client_code": "wst"},
    "WesternOneCGR": {"client_code": "cgr"},
    "WesternOneOCR": {"client_code": "ocr"},
    "WesternOneWE1": {"client_code": "we1"},
    "WesternOneWEQ": {"client_code": "weq"},
    "WesternStatesXAPT": {"client_code": "wse"},
    "Whayne": {"client_code": "wha"},
    "WhayneCAT": {"client_code": "wha"},
    "WheelerCAT": {"client_code": "wmc"},
    "WheelerMaterialHandling": {"client_code": "whmh"},
    "Wilson": {"client_code": "weq"},
    "XYZ": {"client_code": "xyz"},
    "YanceyIRental": {"client_code": "ybo"},
    "YanceyWynne": {"client_code": "yan"},
    "YellowHouse": {"client_code": "ymc"},
    "ZieglerCAT": {"client_code": "zig"},
    "ahernmetrics": {"client_code": "ar"},
    "neffmetrics": {"client_code": "nr"},
}
