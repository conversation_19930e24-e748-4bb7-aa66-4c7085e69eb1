firebase_projects = {
    "1upuk": {"client_project": "rs-client-1upuk-1", "name": "1 Up Access"},
    "a1id": {"client_project": "rs-client-a1id-1", "name": "A1 Rental Idaho"},
    "a1mo": {"client_project": "rs-client-a1mo-1", "name": "A1 Tool \u0026 Equipment"},
    "a1ri": {"client_project": "rs-client-a1ri-1", "name": "A1 Rent It"},
    "aaa": {"client_project": "rs-client-aaa-1", "name": "AAA Rent-All"},
    "aabg": {"client_project": "rs-client-aabg-1", "name": "Aabergs Equipment"},
    "aact": {"client_project": "rs-client-aact-1", "name": "AAction Rents"},
    "aada": {"client_project": "rs-client-aada-1", "name": "AA Rental of Dallas"},
    "aae": {"client_project": "rs-client-aae-1", "name": "Aerial Access"},
    "aai": {"client_project": "rs-client-aai-1", "name": "Atlantic Aerials Inc"},
    "abc": {"client_project": "rs-client-abc-1", "name": "ABC Rentals"},
    "abce": {"client_project": "rs-client-abce-1", "name": "ABC Equipment Rental"},
    "abcm": {
        "client_project": "rs-client-abcm-1",
        "name": "ABC Equipment Rental Maryland",
    },
    "abl": {"client_project": "rs-client-abl-1", "name": "Able Equipment"},
    "ablt": {
        "client_project": "rs-client-ablt-1",
        "name": "Able Tool \u0026 Equipment",
    },
    "abtr": {"client_project": "rs-client-abtr-1", "name": "AB Tool Rentals"},
    "acca": {"client_project": "rs-client-acca-1", "name": "Action Rental Calgary"},
    "achr": {"client_project": "rs-client-achr-1", "name": "All Choice Rentals"},
    "acmb": {"client_project": "rs-client-acmb-1", "name": "Acme Ops Bobcat"},
    "acme": {"client_project": "rs-client-acme-1", "name": "ACME Lift Co."},
    "acml": {"client_project": "rs-client-acml-1", "name": "Acme Lift"},
    "acmr": {"client_project": "rs-client-acmr-1", "name": "Acme Rents"},
    "acrs": {"client_project": "rs-client-acrs-1", "name": "Action Rentals and Sales"},
    "acsr": {
        "client_project": "rs-client-acsr-1",
        "name": "Acquired Clients for Sales Reporting",
    },
    "act": {
        "client_project": "rs-client-act-1",
        "name": "Action Rental (Pennsylvania)",
    },
    "actr": {"client_project": "rs-client-actr-1", "name": "ACT Research"},
    "adm": {"client_project": "rs-client-adm-1", "name": "Admar"},
    "advt": {"client_project": "rs-client-advt-1", "name": "AdvanceTrak Equipment"},
    "advuk": {
        "client_project": "rs-client-advuk-1",
        "name": "Advanced Access Platforms",
    },
    "aee": {"client_project": "rs-client-aee-1", "name": "AE Equipment"},
    "aer": {"client_project": "rs-client-aer-1", "name": "AER Rents (UK)"},
    "afiuk": {"client_project": "rs-client-afiuk-1", "name": "AFIUplift"},
    "aflau": {"client_project": "rs-client-aflau-1", "name": "All Lift Forklifts"},
    "agg": {"client_project": "rs-client-agg-1", "name": "Aggreko"},
    "aham": {"client_project": "rs-client-aham-1", "name": "Allingham"},
    "ainsuk": {"client_project": "rs-client-ainsuk-1", "name": "Ainscough Crane Hire"},
    "air": {"client_project": "rs-client-air-1", "name": "Airgas"},
    "ais": {
        "client_project": "rs-client-ais-1",
        "name": "AIS Construction Equipment Corp",
    },
    "alac": {"client_project": "rs-client-alac-1", "name": "All Access Rentals"},
    "alb": {"client_project": "rs-client-alb-1", "name": "Alban Cat"},
    "aleg": {"client_project": "rs-client-aleg-1", "name": "Allegiance Crane"},
    "aliduk": {
        "client_project": "rs-client-aliduk-1",
        "name": "Alide Plant Services Ltd",
    },
    "allau": {"client_project": "rs-client-allau-1", "name": "Allcott Hire"},
    "alll": {"client_project": "rs-client-alll-1", "name": "All Lift Service Company"},
    "almi": {"client_project": "rs-client-almi-1", "name": "Allied Rent-All"},
    "alpr": {"client_project": "rs-client-alpr-1", "name": "Alpine Equipment Rentals"},
    "alr": {
        "client_project": "rs-client-alr-1",
        "name": "All Roads Equipment - do not use",
    },
    "alro": {"client_project": "rs-client-alro-1", "name": "All Roads Equipment"},
    "als": {"client_project": "rs-client-als-1", "name": "Atlantic Lift Systems Inc"},
    "als2": {"client_project": "rs-client-als2-1", "name": "Atlantic Lift Systems"},
    "alstau": {"client_project": "rs-client-alstau-1", "name": "All Star Access Hire"},
    "alstc": {
        "client_project": "rs-client-alstc-1",
        "name": "All State Crane \u0026 Rigging",
    },
    "alt": {"client_project": "rs-client-alt-1", "name": "Altorfer Industries"},
    "alta": {"client_project": "rs-client-alta-1", "name": "Alta Equipment"},
    "altac": {"client_project": "rs-client-altac-1", "name": "Alta Canada"},
    "ame": {"client_project": "rs-client-ame-1", "name": "AMECO"},
    "ameco": {"client_project": "rs-client-ameco-1", "name": "AMECO"},
    "amecoc": {"client_project": "rs-client-amecoc-1", "name": "AMECO Canada"},
    "amer": {"client_project": "rs-client-amer-1", "name": "American Rentals"},
    "amil": {"client_project": "rs-client-amil-1", "name": "American Rental IL"},
    "amos": {"client_project": "rs-client-amos-1", "name": "Amos Metz Rentals"},
    "andm": {"client_project": "rs-client-andm-1", "name": "Anderson Machinery"},
    "angwa": {"client_project": "rs-client-angwa-1", "name": "Angeles Rentals"},
    "aphr": {"client_project": "rs-client-aphr-1", "name": "Apex Hesperia Rentals"},
    "api": {"client_project": "rs-client-api-1", "name": "APi Supply Lifts"},
    "aple": {"client_project": "rs-client-aple-1", "name": "Apple Bank for Savings"},
    "ar": {"client_project": "rs-client-ar-1", "name": "Ahern Rentals Inc."},
    "ara": {"client_project": "rs-client-ara-1", "name": "Aspen RentAll"},
    "arduk": {"client_project": "rs-client-arduk-1", "name": "Ardent Hire"},
    "are": {"client_project": "rs-client-are-1", "name": "Area Equip"},
    "arva": {"client_project": "rs-client-arva-1", "name": "Arvada Rent-Alls"},
    "arvh": {"client_project": "rs-client-arvh-1", "name": "10726"},
    "arw": {"client_project": "rs-client-arw-1", "name": "Arrow Lift"},
    "arw2": {"client_project": "rs-client-arw2-1", "name": "Arrow Lift Rentals"},
    "arwr": {"client_project": "rs-client-arwr-1", "name": "Arrow Rental"},
    "asc": {
        "client_project": "rs-client-asc-1",
        "name": "Ascendum Construction Equipment",
    },
    "asco": {"client_project": "rs-client-asco-1", "name": "ASCO Equipment"},
    "ase": {"client_project": "rs-client-ase-1", "name": "All Star Equipment"},
    "ash": {"client_project": "rs-client-ash-1", "name": "Asheville Hwy Rental"},
    "asl": {"client_project": "rs-client-asl-1", "name": "American Scissor Lift"},
    "asr": {"client_project": "rs-client-asr-1", "name": "Allstar Rents"},
    "asr2": {"client_project": "rs-client-asr2-1", "name": "All Star Rents"},
    "atoz": {
        "client_project": "rs-client-atoz-1",
        "name": "AtoZEquipmentRentalsandSales",
    },
    "atrau": {"client_project": "rs-client-atrau-1", "name": "Alltracks Plant Hire"},
    "atsr": {"client_project": "rs-client-atsr-1", "name": "A Tool Shed Rentals"},
    "aurr": {"client_project": "rs-client-aurr-1", "name": "Aurora Rents"},
    "aznc": {
        "client_project": "rs-client-aznc-1",
        "name": "A to Z Equipment Rentals of North Carolina",
    },
    "bacn": {"client_project": "rs-client-bacn-1", "name": "Bacon Universal"},
    "badr": {"client_project": "rs-client-badr-1", "name": "Badger Rental Services"},
    "bai": {"client_project": "rs-client-bai-1", "name": "The Bailey Company"},
    "balouk": {
        "client_project": "rs-client-balouk-1",
        "name": "Balloo Hire Centre Limited",
    },
    "batm": {"client_project": "rs-client-batm-1", "name": "Battlefield CAT Manitoba"},
    "batn": {
        "client_project": "rs-client-batn-1",
        "name": "Battlefield CAT Newfoundland",
    },
    "bato": {"client_project": "rs-client-bato-1", "name": "Battlefield CAT Ontario"},
    "batq": {"client_project": "rs-client-batq-1", "name": "Battlefield CAT Quebec"},
    "bbef": {
        "client_project": "rs-client-bbef-1",
        "name": "Bell Bank Equipment Finance",
    },
    "bci": {"client_project": "rs-client-bci-1", "name": "Berry Companies"},
    "bcon": {"client_project": "rs-client-bcon-1", "name": "Bercon Rentals"},
    "bcvt": {"client_project": "rs-client-bcvt-1", "name": "EOC Holdings"},
    "bder": {
        "client_project": "rs-client-bder-1",
        "name": "Black Diamond Equipment Rental",
    },
    "bec": {"client_project": "rs-client-bec-1", "name": "Bingham Equipment"},
    "bee": {"client_project": "rs-client-bee-1", "name": "Bee Equipment Sales"},
    "beer": {"client_project": "rs-client-beer-1", "name": "Blue Eagle Rentals"},
    "beps": {"client_project": "rs-client-beps-1", "name": "Buckeye Power Sales"},
    "ber": {
        "client_project": "rs-client-ber-1",
        "name": "Broadline Equipment Rentals LTD",
    },
    "best": {"client_project": "rs-client-best-1", "name": "Best Line Equipment"},
    "bfl": {"client_project": "rs-client-bfl-1", "name": "DJM Equipment"},
    "bigge": {"client_project": "rs-client-bigge-1", "name": "Bigge"},
    "bis": {"client_project": "rs-client-bis-1", "name": "Black Iron Sales"},
    "bla": {"client_project": "rs-client-bla-1", "name": "Blanchard Machinery Co."},
    "blc": {"client_project": "rs-client-blc-1", "name": "BlueLine Rental Canada"},
    "ble": {"client_project": "rs-client-ble-1", "name": "Bottom Line Equipment LLC"},
    "blr": {"client_project": "rs-client-blr-1", "name": "BlueLine Rental"},
    "bltz": {"client_project": "rs-client-bltz-1", "name": "Sales January 2022 Blitz"},
    "bmc": {"client_project": "rs-client-bmc-1", "name": "Butler Machinery Company"},
    "bme": {
        "client_project": "rs-client-bme-1",
        "name": "B\u0026M Equipment Rental \u0026 Sales",
    },
    "bntt": {
        "client_project": "rs-client-bntt-1",
        "name": "Bennett Equipment \u0026 Supply",
    },
    "boco": {"client_project": "rs-client-boco-1", "name": "BoomCo Rental"},
    "boon": {
        "client_project": "rs-client-boon-1",
        "name": "Boone Rent-All \u0026 Parties Too",
    },
    "bor": {"client_project": "rs-client-bor-1", "name": "Big Orange Rental"},
    "borc": {"client_project": "rs-client-borc-1", "name": "Borsheim Crane Service"},
    "boyd": {"client_project": "rs-client-boyd-1", "name": "CL Boyd Company"},
    "bphuk": {
        "client_project": "rs-client-bphuk-1",
        "name": "BPH Construction Equipment Ltd",
    },
    "bra": {"client_project": "rs-client-bra-1", "name": "Bramco, Inc."},
    "brco": {"client_project": "rs-client-brco-1", "name": "Best Rental"},
    "brd": {"client_project": "rs-client-brd-1", "name": "Beard Equipment"},
    "brg": {"client_project": "rs-client-brg-1", "name": "Briggs Equipment Company"},
    "brguk": {"client_project": "rs-client-brguk-1", "name": "Briggs Equipment UK"},
    "brguk2": {"client_project": "rs-client-brguk2-1", "name": "Briggs Equipment UK"},
    "bros": {"client_project": "rs-client-bros-1", "name": "Equipments Brossard"},
    "brtvw": {
        "client_project": "rs-client-brtvw-1",
        "name": "BrightView Landscapes LLC",
    },
    "brunk": {
        "client_project": "rs-client-brunk-1",
        "name": "Brunswick Lift Rentals LTD",
    },
    "bser": {"client_project": "rs-client-bser-1", "name": "BSE Rents"},
    "bsp": {"client_project": "rs-client-bsp-1", "name": "Business Special Projects"},
    "bstl": {"client_project": "rs-client-bstl-1", "name": "Bobcat of St Louis"},
    "buhsuk": {
        "client_project": "rs-client-buhsuk-1",
        "name": "Buckhurst Plant Hire Ltd",
    },
    "bull": {"client_project": "rs-client-bull-1", "name": "Bullet Rental"},
    "but": {"client_project": "rs-client-but-1", "name": "Butler Rental"},
    "cab": {"client_project": "rs-client-cab-1", "name": "C\u0026B Material Handling"},
    "calf": {"client_project": "rs-client-calf-1", "name": "CanLift"},
    "calw": {"client_project": "rs-client-calw-1", "name": "Cal West Rentals"},
    "canl": {"client_project": "rs-client-canl-1", "name": "CanLift Equipment Ltd."},
    "cannuk": {"client_project": "rs-client-cannuk-1", "name": "Cannon Access"},
    "cap": {"client_project": "rs-client-cap-1", "name": "Capital Rentals"},
    "car": {
        "client_project": "rs-client-car-1",
        "name": "Carolina Tractor \u0026 Equipment Company",
    },
    "cbro": {"client_project": "rs-client-cbro-1", "name": "Cianbro Equipment"},
    "cbs": {"client_project": "rs-client-cbs-1", "name": "Contractors Building Supply"},
    "cc": {"client_project": "rs-client-cc-1", "name": "Conserv Capital"},
    "cclt": {"client_project": "rs-client-cclt-1", "name": "C\u0026C Lift Truck"},
    "cea": {
        "client_project": "rs-client-cea-1",
        "name": "Certified Equipment Appraisals",
    },
    "cec": {
        "client_project": "rs-client-cec-1",
        "name": "Contractors Equipment Center",
    },
    "ceda": {"client_project": "rs-client-ceda-1", "name": "Cedar Equipment Rental"},
    "cedr": {"client_project": "rs-client-cedr-1", "name": "Cedar Equipment Rental"},
    "cer": {"client_project": "rs-client-cer-1", "name": "C\u0026E Rentals"},
    "cesp": {"client_project": "rs-client-cesp-1", "name": "CES Power"},
    "cgr": {"client_project": "rs-client-cgr-1", "name": "WesternOne CGR"},
    "chet": {"client_project": "rs-client-chet-1", "name": "Chet's Rent-All"},
    "chet2": {"client_project": "rs-client-chet2-1", "name": "Chet's Rent-All"},
    "chi": {"client_project": "rs-client-chi-1", "name": "Construction Heaters Inc"},
    "chpuk": {"client_project": "rs-client-chpuk-1", "name": "Chippindale"},
    "chr": {"client_project": "rs-client-chr-1", "name": "Champion Rentals"},
    "chris": {
        "client_project": "rs-client-chris-1",
        "name": "Christopher Equipment Inc.",
    },
    "chsc": {"client_project": "rs-client-chsc-1", "name": "ChaseCo"},
    "cis": {
        "client_project": "rs-client-cis-1",
        "name": "Cisco Equipment Rentals, LLC",
    },
    "city": {"client_project": "rs-client-city-1", "name": "City Rentals"},
    "clb": {
        "client_project": "rs-client-clb-1",
        "name": "Cleveland Brothers Equipment Co., Inc.",
    },
    "clenau": {"client_project": "rs-client-clenau-1", "name": "Clennett Hire"},
    "clk": {
        "client_project": "rs-client-clk-1",
        "name": "Clark Equipment Rental \u0026 Sales",
    },
    "clm": {"client_project": "rs-client-clm-1", "name": "CLM Equipment"},
    "cmpe": {"client_project": "rs-client-cmpe-1", "name": "Complete Equipment"},
    "cnh": {
        "client_project": "rs-client-cnh-1",
        "name": "CNH Industrial Capital America LLC",
    },
    "cnw": {"client_project": "rs-client-cnw-1", "name": "CN Wood"},
    "coatau": {"client_project": "rs-client-coatau-1", "name": "Coates Hire"},
    "coca": {
        "client_project": "rs-client-coca-1",
        "name": "Commercial Capital Company",
    },
    "coke": {"client_project": "rs-client-coke-1", "name": "Coker Rental Company"},
    "col": {"client_project": "rs-client-col-1", "name": "Columbus Equipment"},
    "compau": {
        "client_project": "rs-client-compau-1",
        "name": "Complete Hire Equipment",
    },
    "cont": {"client_project": "rs-client-cont-1", "name": "Contractors Rental Supply"},
    "contuk": {"client_project": "rs-client-contuk-1", "name": "Contract Plant Hire"},
    "coop": {"client_project": "rs-client-coop-1", "name": "Cooper Equipment Rentals"},
    "cow": {"client_project": "rs-client-cow-1", "name": "Cowin"},
    "cra": {"client_project": "rs-client-cra-1", "name": "Casale Rent-All"},
    "crguy": {"client_project": "rs-client-crguy-1", "name": "Crane Guys"},
    "crham": {
        "client_project": "rs-client-crham-1",
        "name": "CRH Americas Materials, Inc.",
    },
    "croc": {"client_project": "rs-client-croc-1", "name": "Crane Rental Service (CA)"},
    "crr": {"client_project": "rs-client-crr-1", "name": "CRR Rentals"},
    "crs": {"client_project": "rs-client-crs-1", "name": "Cresco"},
    "crwn": {"client_project": "rs-client-crwn-1", "name": "Crown Equipment"},
    "csh": {"client_project": "rs-client-csh-1", "name": "Cashman Equipment"},
    "csnuk": {
        "client_project": "rs-client-csnuk-1",
        "name": "2 Cousins Powered Access",
    },
    "cstc": {"client_project": "rs-client-cstc-1", "name": "Coast Capital"},
    "ctr": {
        "client_project": "rs-client-ctr-1",
        "name": "Carter Machinery Company, Inc.",
    },
    "cts": {"client_project": "rs-client-cts-1", "name": "Chappell Tractor Sales"},
    "cwb": {"client_project": "rs-client-cwb-1", "name": "CWB National Leasing"},
    "cwl": {"client_project": "rs-client-cwl-1", "name": "Company Wrench Ltd."},
    "cwx": {"client_project": "rs-client-cwx-1", "name": "Craneworks"},
    "daba": {"client_project": "rs-client-daba-1", "name": "Aba Daba"},
    "dahl": {"client_project": "rs-client-dahl-1", "name": "Dahl's Equipment Rentals"},
    "darr": {"client_project": "rs-client-darr-1", "name": "Darr Equipment"},
    "dbrs": {"client_project": "rs-client-dbrs-1", "name": "D\u0026B Rental"},
    "deck": {"client_project": "rs-client-deck-1", "name": "Decker Tool Rental"},
    "defa": {"client_project": "rs-client-defa-1", "name": "Defatte Equipment"},
    "dilt": {"client_project": "rs-client-dilt-1", "name": "Dillon Toyota Lift"},
    "dlusa": {
        "client_project": "rs-client-dlusa-1",
        "name": "Deutsche Leasing USA Inc",
    },
    "dmh": {
        "client_project": "rs-client-dmh-1",
        "name": "Delta Materials Handling, Inc.",
    },
    "dmnd": {"client_project": "rs-client-dmnd-1", "name": "Diamond Rental"},
    "dobb": {"client_project": "rs-client-dobb-1", "name": "Dobbs Equipment"},
    "dogg": {
        "client_project": "rs-client-dogg-1",
        "name": "Doggett Equipment Services",
    },
    "dogt": {"client_project": "rs-client-dogt-1", "name": "Doggett Toyota"},
    "dons": {"client_project": "rs-client-dons-1", "name": "Don's Rental"},
    "dots": {"client_project": "rs-client-dots-1", "name": "Dot's Rentals"},
    "doz": {"client_project": "rs-client-doz-1", "name": "Dozers.Com"},
    "dpn": {"client_project": "rs-client-dpn-1", "name": "D P Nicoli"},
    "dpp": {"client_project": "rs-client-dpp-1", "name": "Doosan Portable Power"},
    "drogie": {
        "client_project": "rs-client-drogie-1",
        "name": "Drogheda Hire and Sales",
    },
    "drr": {"client_project": "rs-client-drr-1", "name": "Double R Rentals Ltd."},
    "dsm": {"client_project": "rs-client-dsm-1", "name": "Diesel Machinery"},
    "dtst": {
        "client_project": "rs-client-dtst-1",
        "name": "Doggett Toyota Lift of South Texas",
    },
    "duit": {
        "client_project": "rs-client-duit-1",
        "name": "Duit Construction Co., Inc.",
    },
    "duke": {"client_project": "rs-client-duke-1", "name": "The Duke Company"},
    "dur": {"client_project": "rs-client-dur-1", "name": "Durante Rentals, LLC"},
    "durf": {"client_project": "rs-client-durf-1", "name": "Durante Florida"},
    "dynr": {"client_project": "rs-client-dynr-1", "name": "Dynamic Equipment"},
    "eabo": {"client_project": "rs-client-eabo-1", "name": "Earthborne Inc"},
    "ecc": {"client_project": "rs-client-ecc-1", "name": "Ecco Equipment"},
    "ecco": {"client_project": "rs-client-ecco-1", "name": "ECCO III Enterprises"},
    "ecfl": {"client_project": "rs-client-ecfl-1", "name": "Eco Rentals"},
    "efin": {"client_project": "rs-client-efin-1", "name": "Equipment Finders"},
    "ehe": {"client_project": "rs-client-ehe-1", "name": "Expert Heavy Equipment"},
    "elcl": {"client_project": "rs-client-elcl-1", "name": "El Cheapo Lifts"},
    "elite": {"client_project": "rs-client-elite-1", "name": "Elite Material Handling"},
    "eltnuk": {"client_project": "rs-client-eltnuk-1", "name": "Elavation Ltd"},
    "emp": {"client_project": "rs-client-emp-1", "name": "Empire CAT"},
    "emrg": {"client_project": "rs-client-emrg-1", "name": "Empower Rental Group"},
    "emry": {"client_project": "rs-client-emry-1", "name": "Emery Equipment"},
    "epc": {"client_project": "rs-client-epc-1", "name": "Equipco"},
    "eptuk": {"client_project": "rs-client-eptuk-1", "name": "Explore Transport UK"},
    "eqd": {"client_project": "rs-client-eqd-1", "name": "Equipment Depot"},
    "eqinc": {"client_project": "rs-client-eqinc-1", "name": "Equipment Inc."},
    "eqptnz": {"client_project": "rs-client-eqptnz-1", "name": "Equipt Plant Hire"},
    "erb": {"client_project": "rs-client-erb-1", "name": "Erb Equipment"},
    "ergs": {
        "client_project": "rs-client-ergs-1",
        "name": "Empower Group Supplemental",
    },
    "ers": {"client_project": "rs-client-ers-1", "name": "Excavator Rental"},
    "es": {"client_project": "rs-client-es-1", "name": "EquipmentShare"},
    "ess": {"client_project": "rs-client-ess-1", "name": "Emery Sapp \u0026 Sons"},
    "et2": {"client_project": "rs-client-et2-1", "name": "Engineering Test Client 2"},
    "etc": {"client_project": "rs-client-etc-1", "name": "Engineering Test Client"},
    "etca01": {"client_project": "rs-client-etca01-1", "name": "ETCA01 Rentals"},
    "etcalloy": {
        "client_project": "rs-client-etcalloy-1",
        "name": "Engineering Test Client - Alloy DB",
    },
    "etcbad": {
        "client_project": "rs-client-etcbad-1",
        "name": "Engineering Test Client - Bad Data",
    },
    "etcd": {
        "client_project": "rs-client-etcd-1",
        "name": "Engineering Test Client - Shard",
    },
    "etcinsi": {
        "client_project": "rs-client-etcinsi-1",
        "name": "Engineering Test Client - ClientInsights",
    },
    "etcint": {
        "client_project": "rs-client-etcint-1",
        "name": "Engineering Test Client - Integrations",
    },
    "etcl": {
        "client_project": "rs-client-etcl-1",
        "name": "Engineering Test Client - Large",
    },
    "etcm": {
        "client_project": "rs-client-etcm-1",
        "name": "Engineering Test Client - Medium",
    },
    "etcquarter": {
        "client_project": "rs-client-etcquarter-1",
        "name": "Engineering Test Client - Quarter",
    },
    "etcr": {
        "client_project": "rs-client-etcr-1",
        "name": "Engineering Test Client - Regression",
    },
    "etcri": {"client_project": "rs-client-etcri-1", "name": "ETCRI Rental Insights"},
    "etcrxl": {
        "client_project": "rs-client-etcrxl-1",
        "name": "Engineering Test Client - Regression XLarge",
    },
    "etcserv": {
        "client_project": "rs-client-etcserv-1",
        "name": "Engineering Test Client - Services",
    },
    "etcuk": {
        "client_project": "rs-client-etcuk-1",
        "name": "Engineering Test Client - UK",
    },
    "etcxl": {
        "client_project": "rs-client-etcxl-1",
        "name": "Engineering Test Client - XLarge",
    },
    "etcxlalloy": {
        "client_project": "rs-client-etcxlalloy-1",
        "name": "Engineering Test Client - XLarge Alloy DB",
    },
    "etra": {"client_project": "rs-client-etra-1", "name": "East Tenn Rent-Alls"},
    "eut": {"client_project": "rs-client-eut-1", "name": "Eutaw Construction Co Inc"},
    "ewal": {"client_project": "rs-client-ewal-1", "name": "Ewald Kubota"},
    "expd": {"client_project": "rs-client-expd-1", "name": "Expedition Rentals"},
    "eze": {"client_project": "rs-client-eze-1", "name": "EZE Rent-It Centre Ltd"},
    "ezyau": {"client_project": "rs-client-ezyau-1", "name": "Ezy Up Hire"},
    "fab": {"client_project": "rs-client-fab-1", "name": "John Fabick Tractor Company"},
    "fari": {"client_project": "rs-client-fari-1", "name": "Faris Machinery"},
    "fbr": {"client_project": "rs-client-fbr-1", "name": "F\u0026B Rentals"},
    "fec": {"client_project": "rs-client-fec-1", "name": "Flint Equipment Company"},
    "fin": {"client_project": "rs-client-fin-1", "name": "Finning Cat"},
    "fks": {"client_project": "rs-client-fks-1", "name": "Foley Equipment Company"},
    "flexau": {"client_project": "rs-client-flexau-1", "name": "Flexihire"},
    "flr": {"client_project": "rs-client-flr-1", "name": "FLR Rentals"},
    "flwy": {"client_project": "rs-client-flwy-1", "name": "Fleetway Capital Corp"},
    "fme": {"client_project": "rs-client-fme-1", "name": "Komatsu East"},
    "fol": {"client_project": "rs-client-fol-1", "name": "Foley, Inc."},
    "folu": {"client_project": "rs-client-folu-1", "name": "Foley Used"},
    "fra": {"client_project": "rs-client-fra-1", "name": "Franklin Equipment LLC"},
    "fraz": {"client_project": "rs-client-fraz-1", "name": "Fraza Group"},
    "frcd": {"client_project": "rs-client-frcd-1", "name": "Fairchild Equipment"},
    "frd": {"client_project": "rs-client-frd-1", "name": "Furukawa Rock Drill"},
    "friv": {"client_project": "rs-client-friv-1", "name": "4 Rivers Equipment"},
    "frse": {"client_project": "rs-client-frse-1", "name": "Fresno Equipment"},
    "fse": {"client_project": "rs-client-fse-1", "name": "Five Star Equipment"},
    "fser": {"client_project": "rs-client-fser-1", "name": "FirstSource"},
    "gccs": {"client_project": "rs-client-gccs-1", "name": "Gulf Coast Crane"},
    "gec": {"client_project": "rs-client-gec-1", "name": "Grand Equipment Company"},
    "gec2": {"client_project": "rs-client-gec2-1", "name": "Grand Equipment (GEC2)"},
    "gee": {"client_project": "rs-client-gee-1", "name": "Gee Heavy Equipment"},
    "gera": {"client_project": "rs-client-gera-1", "name": "General RentAll"},
    "ges": {"client_project": "rs-client-ges-1", "name": "General Equpment"},
    "gfl": {
        "client_project": "rs-client-gfl-1",
        "name": "GFL Environmental Services Inc",
    },
    "gfr": {"client_project": "rs-client-gfr-1", "name": "Giffin Rentals"},
    "gmer": {"client_project": "rs-client-gmer-1", "name": "GM Equipment"},
    "gnie": {"client_project": "rs-client-gnie-1", "name": "Genie Lift"},
    "gnj": {"client_project": "rs-client-gnj-1", "name": "G \u0026 J Equipment Rental"},
    "gonz": {"client_project": "rs-client-gonz-1", "name": "Gonzalez Trading"},
    "goruk": {"client_project": "rs-client-goruk-1", "name": "Gordon Bow"},
    "gouk": {"client_project": "rs-client-gouk-1", "name": "GoHire (Hull) Ltd"},
    "gpc": {
        "client_project": "rs-client-gpc-1",
        "name": "Gregory Poole Equipment Company",
    },
    "gper": {"client_project": "rs-client-gper-1", "name": "Great Plains Rentals"},
    "grof": {
        "client_project": "rs-client-grof-1",
        "name": "Groff Tractor and Equipment LLC",
    },
    "grr": {"client_project": "rs-client-grr-1", "name": "Giffin Rentals"},
    "gsb": {"client_project": "rs-client-gsb-1", "name": "Garden State Bobcat Group"},
    "gscr": {"client_project": "rs-client-gscr-1", "name": "General Steel Crane"},
    "gsec": {"client_project": "rs-client-gsec-1", "name": "Great Southern"},
    "gta": {"client_project": "rs-client-gta-1", "name": "GTA Equipment Rentals"},
    "gtauk": {"client_project": "rs-client-gtauk-1", "name": "GT Access"},
    "gtmuk": {"client_project": "rs-client-gtmuk-1", "name": "GTM Heavy Rentals"},
    "gtr": {"client_project": "rs-client-gtr-1", "name": "George Tool Rental"},
    "gwe": {"client_project": "rs-client-gwe-1", "name": "G\u0026W Equipment"},
    "gweq": {"client_project": "rs-client-gweq-1", "name": "Great West Equipment"},
    "gwyuk": {
        "client_project": "rs-client-gwyuk-1",
        "name": "Gwynedd Forklifts Limited",
    },
    "haml": {"client_project": "rs-client-haml-1", "name": "Hamlin Equipment Rental"},
    "haug": {"client_project": "rs-client-haug-1", "name": "Haugland LLC"},
    "haw": {"client_project": "rs-client-haw-1", "name": "Hawthorne CAT"},
    "hawk": {"client_project": "rs-client-hawk-1", "name": "Hawkins-Graves"},
    "hbr": {"client_project": "rs-client-hbr-1", "name": "Hull Brothers Rental"},
    "hcm": {
        "client_project": "rs-client-hcm-1",
        "name": "Hitachi Construction Equipment Loaders",
    },
    "hd": {"client_project": "rs-client-hd-1", "name": "Home Depot"},
    "hdc": {"client_project": "rs-client-hdc-1", "name": "Home Depot Canada"},
    "hem": {"client_project": "rs-client-hem-1", "name": "H \u0026 E"},
    "hg": {"client_project": "rs-client-hg-1", "name": "Herc Equipment Rental Corp."},
    "hgi": {"client_project": "rs-client-hgi-1", "name": "The Harnish Group, Inc."},
    "hgrp": {"client_project": "rs-client-hgrp-1", "name": "Hall Group of Companies"},
    "hh": {
        "client_project": "rs-client-hh-1",
        "name": "Hugg \u0026 Hall Equipment Company",
    },
    "hhv": {"client_project": "rs-client-hhv-1", "name": "Housby Heavy Equipment"},
    "hill": {"client_project": "rs-client-hill-1", "name": "Hills Machinery"},
    "hito": {"client_project": "rs-client-hito-1", "name": "Hightower Equipment"},
    "hlc": {"client_project": "rs-client-hlc-1", "name": "Holt of California"},
    "hlt": {"client_project": "rs-client-hlt-1", "name": "HOLT Texas, Ltd"},
    "hltc": {"client_project": "rs-client-hltc-1", "name": "Holt Crane"},
    "hmi": {"client_project": "rs-client-hmi-1", "name": "Heavy Machines Inc."},
    "hmla": {
        "client_project": "rs-client-hmla-1",
        "name": "Hammer Equipment Services, LLC",
    },
    "hndo": {"client_project": "rs-client-hndo-1", "name": "Handy Rents"},
    "hofe": {"client_project": "rs-client-hofe-1", "name": "Hoffman Equipment Company"},
    "holp": {"client_project": "rs-client-holp-1", "name": "Holland Pump"},
    "honn": {"client_project": "rs-client-honn-1", "name": "Honnen Equipment"},
    "hop": {"client_project": "rs-client-hop-1", "name": "HO Penn Rents"},
    "hosk": {"client_project": "rs-client-hosk-1", "name": "Hoskins Equipment"},
    "howe": {"client_project": "rs-client-howe-1", "name": "Howe Rental \u0026 Sales"},
    "hph": {"client_project": "rs-client-hph-1", "name": "Hirepro Holdings UK LTD"},
    "hrc": {"client_project": "rs-client-hrc-1", "name": "Herc Rentals Canada"},
    "hre": {"client_project": "rs-client-hre-1", "name": "High Reach 2"},
    "hre2": {"client_project": "rs-client-hre2-1", "name": "High Reach 2 (HRE2)"},
    "hri": {"client_project": "rs-client-hri-1", "name": "Herc Rentals"},
    "hrtx": {
        "client_project": "rs-client-hrtx-1",
        "name": "Heavy Equipment Rentals of Texas",
    },
    "hsluk": {
        "client_project": "rs-client-hsluk-1",
        "name": "Hire \u0026 Supplies Ltd",
    },
    "hsr": {"client_project": "rs-client-hsr-1", "name": "H\u0026S Rentals"},
    "hssuk": {"client_project": "rs-client-hssuk-1", "name": "HSS ProService Limited"},
    "ht4hau": {"client_project": "rs-client-ht4hau-1", "name": "Height 4 Hire"},
    "hub": {"client_project": "rs-client-hub-1", "name": "Hub Equipment Company"},
    "hubuk": {"client_project": "rs-client-hubuk-1", "name": "Hubbway Ltd"},
    "hudluk": {"client_project": "rs-client-hudluk-1", "name": "Hudson Lifting"},
    "hul": {"client_project": "rs-client-hul-1", "name": "Herc-U-Lift"},
    "hvy": {"client_project": "rs-client-hvy-1", "name": "HVY Rentals"},
    "hwy": {"client_project": "rs-client-hwy-1", "name": "Highway Equipment Co."},
    "icr": {
        "client_project": "rs-client-icr-1",
        "name": "Iron Capital Rentals (USA) Inc.",
    },
    "ift": {
        "client_project": "rs-client-ift-1",
        "name": "Integrated Financial Technologies",
    },
    "ihr": {"client_project": "rs-client-ihr-1", "name": "Illini Hi-Reach"},
    "ihs": {"client_project": "rs-client-ihs-1", "name": "IHS Markit"},
    "iii": {"client_project": "rs-client-iii-1", "name": "ITOCHU International Inc."},
    "instau": {"client_project": "rs-client-instau-1", "name": "Instant Access"},
    "ints": {"client_project": "rs-client-ints-1", "name": "Interstate Rentals"},
    "ircl": {
        "client_project": "rs-client-ircl-1",
        "name": "Ironclad Environmental Solutions",
    },
    "iros": {"client_project": "rs-client-iros-1", "name": "Iron Oak Services"},
    "itc": {"client_project": "rs-client-itc-1", "name": "I-40 Trading"},
    "ite": {
        "client_project": "rs-client-ite-1",
        "name": "Illinois Truck \u0026 Equipment",
    },
    "jar": {"client_project": "rs-client-jar-1", "name": "J.A. Riggs Tractor Co."},
    "jaruk": {"client_project": "rs-client-jaruk-1", "name": "Jarvie Plant"},
    "jbh": {"client_project": "rs-client-jbh-1", "name": "JB Hunt"},
    "jcb": {"client_project": "rs-client-jcb-1", "name": "JCB"},
    "jck": {"client_project": "rs-client-jck-1", "name": "Jackson Equipment Sales"},
    "jdc": {
        "client_project": "rs-client-jdc-1",
        "name": "John Deere Construction \u0026 Forestry",
    },
    "jdf": {"client_project": "rs-client-jdf-1", "name": "John Deere Financial"},
    "jes": {"client_project": "rs-client-jes-1", "name": "Jesco, Inc."},
    "jhl": {"client_project": "rs-client-jhl-1", "name": "JHL Constructors Inc."},
    "jjc": {"client_project": "rs-client-jjc-1", "name": "JJ Curran"},
    "jldb": {"client_project": "rs-client-jldb-1", "name": "JL Dobbs Crane"},
    "jlg": {"client_project": "rs-client-jlg-1", "name": "JLG Industries, Inc."},
    "jmc": {"client_project": "rs-client-jmc-1", "name": "Johnson CAT"},
    "jmsuk": {"client_project": "rs-client-jmsuk-1", "name": "JMS Powered Access"},
    "jpruk": {"client_project": "rs-client-jpruk-1", "name": "JP Vehicle Rentals"},
    "jps": {"client_project": "rs-client-jps-1", "name": "JPS Equipment Rental LLC"},
    "jpsuk": {"client_project": "rs-client-jpsuk-1", "name": "JPS Platforms"},
    "jpswuk": {
        "client_project": "rs-client-jpswuk-1",
        "name": "JPS Platforms - Welfare Cabins",
    },
    "jre": {
        "client_project": "rs-client-jre-1",
        "name": "James River Equipment Company",
    },
    "jre2": {"client_project": "rs-client-jre2-1", "name": "James River Equipment"},
    "jse": {"client_project": "rs-client-jse-1", "name": "Job Site Equipment Corp"},
    "jtbr": {"client_project": "rs-client-jtbr-1", "name": "JTB Rentals"},
    "kac": {"client_project": "rs-client-kac-1", "name": "Komatsu America Corp"},
    "kaige": {"client_project": "rs-client-kaige-1", "name": "Kaige Kubota"},
    "kbs": {"client_project": "rs-client-kbs-1", "name": "Kirby Smith"},
    "kec": {"client_project": "rs-client-kec-1", "name": "Komatsu West"},
    "key": {"client_project": "rs-client-key-1", "name": "Key Equipment"},
    "kfu": {
        "client_project": "rs-client-kfu-1",
        "name": "KOM - Komatsu Forklift U.S.A.",
    },
    "kie": {"client_project": "rs-client-kie-1", "name": "Kiewit"},
    "kimp": {"client_project": "rs-client-kimp-1", "name": "Kimps Ace Hardware"},
    "kmcf": {"client_project": "rs-client-kmcf-1", "name": "KMC Forklift"},
    "knmtjp": {"client_project": "rs-client-knmtjp-1", "name": "Kanamoto"},
    "kobe": {
        "client_project": "rs-client-kobe-1",
        "name": "Koberstein Rental and Sales Inc",
    },
    "komf": {"client_project": "rs-client-komf-1", "name": "Komatsu Forklift"},
    "kphuk": {"client_project": "rs-client-kphuk-1", "name": "KPH Plant Hire"},
    "ktc": {"client_project": "rs-client-ktc-1", "name": "Kelly Tractor Co."},
    "lalo": {"client_project": "rs-client-lalo-1", "name": "LaLonde Equipment Rentals"},
    "lane": {"client_project": "rs-client-lane-1", "name": "Lane Construction Corp"},
    "lano": {"client_project": "rs-client-lano-1", "name": "Lano Equipment"},
    "late": {"client_project": "rs-client-late-1", "name": "LAT Enterprises"},
    "lawr": {"client_project": "rs-client-lawr-1", "name": "Lawrence Equipment"},
    "lcs": {"client_project": "rs-client-lcs-1", "name": "Lincoln Supply"},
    "ldgm": {"client_project": "rs-client-ldgm-1", "name": "Location GM"},
    "leg": {"client_project": "rs-client-leg-1", "name": "Legacy Equipment"},
    "lep": {"client_project": "rs-client-lep-1", "name": "Leppo Group"},
    "lepp": {"client_project": "rs-client-lepp-1", "name": "Leppo Group AX"},
    "lew": {"client_project": "rs-client-lew-1", "name": "Lewistown Rental"},
    "lfatl": {"client_project": "rs-client-lfatl-1", "name": "Lift Atlanta Inc"},
    "lft1": {"client_project": "rs-client-lft1-1", "name": "Lift One"},
    "lgnd": {"client_project": "rs-client-lgnd-1", "name": "Legend Industries"},
    "lid": {"client_project": "rs-client-lid-1", "name": "Location Ideal"},
    "ligeuk": {"client_project": "rs-client-ligeuk-1", "name": "Lifting Gear UK Ltd"},
    "linc": {"client_project": "rs-client-linc-1", "name": "Lift Inc"},
    "lle": {
        "client_project": "rs-client-lle-1",
        "name": "Louisiana Lift and Equipment",
    },
    "lmc": {"client_project": "rs-client-lmc-1", "name": "Louisiana CAT"},
    "lme": {"client_project": "rs-client-lme-1", "name": "Location Multi-Equipements"},
    "lndr": {
        "client_project": "rs-client-lndr-1",
        "name": "Linder Industrial Machinery Company",
    },
    "lndr2": {"client_project": "rs-client-lndr2-1", "name": "Linder - Kubota"},
    "lou": {"client_project": "rs-client-lou-1", "name": "LOU-TEC Group Inc"},
    "lriuk": {"client_project": "rs-client-lriuk-1", "name": "Liftright Access"},
    "lsfl": {"client_project": "rs-client-lsfl-1", "name": "LoneStar Forklift"},
    "lti": {"client_project": "rs-client-lti-1", "name": "Lift Tech Inc."},
    "ltr": {"client_project": "rs-client-ltr-1", "name": "Location Trois-Rivieres"},
    "ltr2": {"client_project": "rs-client-ltr2-1", "name": "LTR Location"},
    "lvm": {"client_project": "rs-client-lvm-1", "name": "Leavitt Machinery USA"},
    "lvmc": {"client_project": "rs-client-lvmc-1", "name": "Leavitt Machinery Canada"},
    "lvtc": {"client_project": "rs-client-lvtc-1", "name": "Longview Truck Center"},
    "lwi": {"client_project": "rs-client-lwi-1", "name": "Lift Works"},
    "lyle": {"client_project": "rs-client-lyle-1", "name": "Lyle Machinery"},
    "lynuk": {
        "client_project": "rs-client-lynuk-1",
        "name": "Lynch Plant Hire \u0026 Haulage",
    },
    "mac": {"client_project": "rs-client-mac-1", "name": "MFC Rental Services"},
    "mach": {"client_project": "rs-client-mach-1", "name": "Michigan CAT"},
    "macl": {"client_project": "rs-client-macl-1", "name": "MAC Leasing"},
    "madi": {"client_project": "rs-client-madi-1", "name": "Madison Supply"},
    "mart": {"client_project": "rs-client-mart-1", "name": "Martin Equipment"},
    "masr": {"client_project": "rs-client-masr-1", "name": "Master Rental"},
    "matde": {"client_project": "rs-client-matde-1", "name": "Mateco GmbH"},
    "mawp": {"client_project": "rs-client-mawp-1", "name": "MEC Aerial Work Platforms"},
    "max": {"client_project": "rs-client-max-1", "name": "Maxim"},
    "mcan": {"client_project": "rs-client-mcan-1", "name": "McCann Industries"},
    "mccf": {
        "client_project": "rs-client-mccf-1",
        "name": "McCoy Construction \u0026 Forestry",
    },
    "mcgc": {"client_project": "rs-client-mcgc-1", "name": "MCG Civil"},
    "mcm": {"client_project": "rs-client-mcm-1", "name": "Mid Country Machinery"},
    "mcm2": {"client_project": "rs-client-mcm2-1", "name": "Mid Country Machinery"},
    "mctc": {
        "client_project": "rs-client-mctc-1",
        "name": "Mid Coast Transit Constructors",
    },
    "mec": {"client_project": "rs-client-mec-1", "name": "Meade Equipment LLC"},
    "megaau": {"client_project": "rs-client-megaau-1", "name": "Mega Hire"},
    "mehuk": {
        "client_project": "rs-client-mehuk-1",
        "name": "M \u0026 R TOOL HIRE LTD",
    },
    "mer": {"client_project": "rs-client-mer-1", "name": "Magnolia Equipment Rental"},
    "mhefw": {
        "client_project": "rs-client-mhefw-1",
        "name": "Fort Wayne Materials Handling",
    },
    "mhi": {
        "client_project": "rs-client-mhi-1",
        "name": "Material Handling Inc - Softbase",
    },
    "mhi2": {"client_project": "rs-client-mhi2-1", "name": "Material Handling Inc"},
    "mhmuk": {"client_project": "rs-client-mhmuk-1", "name": "MHM Plant Group Ltd"},
    "mhr": {"client_project": "rs-client-mhr-1", "name": "Midwest High Reach"},
    "midc": {"client_project": "rs-client-midc-1", "name": "Mid Columbia Forklift"},
    "milt": {"client_project": "rs-client-milt-1", "name": "Milton CAT"},
    "mkhi": {"client_project": "rs-client-mkhi-1", "name": "MK Equipment"},
    "mlec": {"client_project": "rs-client-mlec-1", "name": "McClung-Logan"},
    "mltr": {"client_project": "rs-client-mltr-1", "name": "Milton Rents"},
    "mm": {"client_project": "rs-client-mm-1", "name": "Modern Machinery"},
    "mmc": {
        "client_project": "rs-client-mmc-1",
        "name": "MacAllister Machinery - Indiana Cat",
    },
    "mnluk": {"client_project": "rs-client-mnluk-1", "name": "Mainline"},
    "mood": {"client_project": "rs-client-mood-1", "name": "Lansdowne-Moody"},
    "mpg": {"client_project": "rs-client-mpg-1", "name": "MPG Companies"},
    "mphuk": {"client_project": "rs-client-mphuk-1", "name": "Mr Plant Hire"},
    "mrcrn": {
        "client_project": "rs-client-mrcrn-1",
        "name": "Mr.Crane \u0026 Inquipco",
    },
    "mrga": {"client_project": "rs-client-mrga-1", "name": "M\u0026R Rental"},
    "mro": {"client_project": "rs-client-mro-1", "name": "Rental One"},
    "mrs": {"client_project": "rs-client-mrs-1", "name": "Midway Rentals and Sales"},
    "mscsuk": {"client_project": "rs-client-mscsuk-1", "name": "Mascus Test Client"},
    "mson": {"client_project": "rs-client-mson-1", "name": "Morrison Industrial"},
    "msrnl": {"client_project": "rs-client-msrnl-1", "name": "MSR Verhuur"},
    "mtc": {"client_project": "rs-client-mtc-1", "name": "MTC Equipment"},
    "mtcr": {"client_project": "rs-client-mtcr-1", "name": "Mountain Crane Service"},
    "mtcy": {
        "client_project": "rs-client-mtcy-1",
        "name": "Motor City Rental \u0026 Sales",
    },
    "mtec": {"client_project": "rs-client-mtec-1", "name": "Murphy Tractor"},
    "mtnnc": {"client_project": "rs-client-mtnnc-1", "name": "Mountain Rentals"},
    "mumn": {"client_project": "rs-client-mumn-1", "name": "Minuteman Rentals"},
    "mumn2": {"client_project": "rs-client-mumn2-1", "name": "Minuteman Rentals 2"},
    "mus": {"client_project": "rs-client-mus-1", "name": "Mustang CAT"},
    "mwtx": {"client_project": "rs-client-mwtx-1", "name": "MW Rentals"},
    "mzzt": {"client_project": "rs-client-mzzt-1", "name": "Mazzotta Rentals"},
    "nashau": {"client_project": "rs-client-nashau-1", "name": "Nash Hire"},
    "nbt": {"client_project": "rs-client-nbt-1", "name": "Noble Iron Texas"},
    "nclt": {"client_project": "rs-client-nclt-1", "name": "North Coast Lift Truck"},
    "ndt": {"client_project": "rs-client-ndt-1", "name": "NDT Japan"},
    "ned": {"client_project": "rs-client-ned-1", "name": "National Equipment Dealers"},
    "nes": {"client_project": "rs-client-nes-1", "name": "NES"},
    "newm": {"client_project": "rs-client-newm-1", "name": "Newman Tractor"},
    "nexc": {"client_project": "rs-client-nexc-1", "name": "NexGen Crane"},
    "nhmh": {"client_project": "rs-client-nhmh-1", "name": "Naumann Hobbs"},
    "nmc": {"client_project": "rs-client-nmc-1", "name": "NMC Cat Rental"},
    "nmll": {
        "client_project": "rs-client-nmll-1",
        "name": "North Mill Equipment Finance LLC",
    },
    "nob": {"client_project": "rs-client-nob-1", "name": "Noble Iron"},
    "nor": {"client_project": "rs-client-nor-1", "name": "Nortrax, Inc."},
    "norc": {"client_project": "rs-client-norc-1", "name": "Nortrax Canada Inc."},
    "norv": {"client_project": "rs-client-norv-1", "name": "NorVal"},
    "nr": {"client_project": "rs-client-nr-1", "name": "Neff Metrics"},
    "nsci": {
        "client_project": "rs-client-nsci-1",
        "name": "Norris Sales Company, Inc.",
    },
    "nsknjp": {"client_project": "rs-client-nsknjp-1", "name": "Nishiken"},
    "nstr": {"client_project": "rs-client-nstr-1", "name": "Northside Tool Rental"},
    "nts": {"client_project": "rs-client-nts-1", "name": "National Trench Safety"},
    "oak": {"client_project": "rs-client-oak-1", "name": "Oaken Equipment"},
    "ocr": {"client_project": "rs-client-ocr-1", "name": "WesternOne OCR"},
    "ohau": {"client_project": "rs-client-ohau-1", "name": "Orange Hire"},
    "ohc": {"client_project": "rs-client-ohc-1", "name": "Ohio Machinery Co."},
    "ohrr": {"client_project": "rs-client-ohrr-1", "name": "Ohio High Reach"},
    "okie": {"client_project": "rs-client-okie-1", "name": "Okie Rents"},
    "on1r": {"client_project": "rs-client-on1r-1", "name": "Only 1 Rentals"},
    "one": {"client_project": "rs-client-one-1", "name": "One Source"},
    "opfx": {"client_project": "rs-client-opfx-1", "name": "Opifex"},
    "orbiuk": {"client_project": "rs-client-orbiuk-1", "name": "Orbital Equipment Ltd"},
    "ore": {"client_project": "rs-client-ore-1", "name": "ORE Inc."},
    "ors": {"client_project": "rs-client-ors-1", "name": "Ontario Rental and Supply"},
    "ostpuk": {
        "client_project": "rs-client-ostpuk-1",
        "name": "OneStop Access Scotland Ltd",
    },
    "pat": {"client_project": "rs-client-pat-1", "name": "Altorfer Industries, Inc."},
    "pdq": {"client_project": "rs-client-pdq-1", "name": "PDQ Equipment Rentals, LLC"},
    "peco": {"client_project": "rs-client-peco-1", "name": "Power Equipment Company"},
    "peqp": {"client_project": "rs-client-peqp-1", "name": "Performance Equipment"},
    "per": {"client_project": "rs-client-per-1", "name": "Partner Rentals"},
    "perc": {"client_project": "rs-client-perc-1", "name": "Perco Rentals"},
    "pet": {"client_project": "rs-client-pet-1", "name": "Peterson CAT"},
    "pfruk": {"client_project": "rs-client-pfruk-1", "name": "Plantforce"},
    "pih": {
        "client_project": "rs-client-pih-1",
        "name": "Phillips Infrastructure Holdings",
    },
    "pkes": {"client_project": "rs-client-pkes-1", "name": "Park East Sales"},
    "pltouk": {"client_project": "rs-client-pltouk-1", "name": "Plantool Ltd"},
    "pm": {"client_project": "rs-client-pm-1", "name": "Puckett Machinery, Inc."},
    "pmc": {"client_project": "rs-client-pmc-1", "name": "Power Motive Corp"},
    "pmh": {
        "client_project": "rs-client-pmh-1",
        "name": "Puckett Heavy Machinery, Inc.",
    },
    "pora": {"client_project": "rs-client-pora-1", "name": "Portland Rent-All"},
    "prch": {"client_project": "rs-client-prch-1", "name": "Gear Up Rentals"},
    "prer": {"client_project": "rs-client-prer-1", "name": "Power Rents"},
    "pri": {"client_project": "rs-client-pri-1", "name": "Priority Equipment Rental"},
    "primo": {
        "client_project": "rs-client-primo-1",
        "name": "Primoris Services Corporation",
    },
    "psm": {"client_project": "rs-client-psm-1", "name": "Prosource Machinery"},
    "pstr": {"client_project": "rs-client-pstr-1", "name": "Pro Star Rental"},
    "qco": {"client_project": "rs-client-qco-1", "name": "The Quinn Group, Inc."},
    "qflau": {"client_project": "rs-client-qflau-1", "name": "Queensland Forklifts"},
    "qtum": {"client_project": "rs-client-qtum-1", "name": "Quantum Lift"},
    "quaau": {"client_project": "rs-client-quaau-1", "name": "Quick Access"},
    "rab": {"client_project": "rs-client-rab-1", "name": "Rabern Rentals"},
    "raccuk": {"client_project": "rs-client-raccuk-1", "name": "React Access"},
    "racp": {"client_project": "rs-client-racp-1", "name": "Auxilior Capital Partners"},
    "raef": {"client_project": "rs-client-raef-1", "name": "Amur Equipment Finance"},
    "rafg": {"client_project": "rs-client-rafg-1", "name": "Alliance Funding Group"},
    "raka": {"client_project": "rs-client-raka-1", "name": "RAKA Rental"},
    "ran": {"client_project": "rs-client-ran-1", "name": "Ransome"},
    "rapf": {"client_project": "rs-client-rapf-1", "name": "AP Financing"},
    "razr": {"client_project": "rs-client-razr-1", "name": "Razor Rents"},
    "rbav": {
        "client_project": "rs-client-rbav-1",
        "name": "Ritchie Bros - Auction Valuation Test",
    },
    "rbfs": {
        "client_project": "rs-client-rbfs-2",
        "name": "Ritchie Bros - Financial Services - Do not use",
    },
    "rbfsa": {
        "client_project": "rs-client-rbfsa-1",
        "name": "Ritchie Bros - Financial Services",
    },
    "rbims": {"client_project": "rs-client-rbims-1", "name": "Ritchie Bros - IMS"},
    "rbme": {
        "client_project": "rs-client-rbme-1",
        "name": "Ritchie Bros - Marketplace-E",
    },
    "rbpps": {"client_project": "rs-client-rbpps-1", "name": "Ritchie Bros - PPS"},
    "rbt": {"client_project": "rs-client-rbt-1", "name": "Bank of Texas"},
    "rbvd": {"client_project": "rs-client-rbvd-1", "name": "BVD Group"},
    "rcb": {"client_project": "rs-client-rcb-1", "name": "Citizens Bank"},
    "rcef": {
        "client_project": "rs-client-rcef-1",
        "name": "Crestmark Equipment Finance",
    },
    "rcer": {"client_project": "rs-client-rcer-1", "name": "River City Equipment"},
    "rckga": {"client_project": "rs-client-rckga-1", "name": "Rick's Rental"},
    "rcki": {"client_project": "rs-client-rcki-1", "name": "Rock Rental"},
    "rcub": {"client_project": "rs-client-rcub-1", "name": "Customers Bank"},
    "rda": {"client_project": "rs-client-rda-1", "name": "Red-D-Arc"},
    "rddg": {"client_project": "rs-client-rddg-1", "name": "Readydig"},
    "rdfg": {"client_project": "rs-client-rdfg-1", "name": "Delta Financial Group"},
    "rdht": {"client_project": "rs-client-rdht-2", "name": "Red Hat Rentals"},
    "rdll": {"client_project": "rs-client-rdll-1", "name": "DLL Portfolio"},
    "rdo": {"client_project": "rs-client-rdo-1", "name": "RDO Equipment Co."},
    "rdse": {
        "client_project": "rs-client-rdse-1",
        "name": "D Squared Equipment Appraisal",
    },
    "rdynh": {"client_project": "rs-client-rdynh-1", "name": "Ready Equipment"},
    "reacuk": {"client_project": "rs-client-reacuk-1", "name": "Reactive Rentals"},
    "rec": {"client_project": "rs-client-rec-1", "name": "Rish"},
    "recp": {"client_project": "rs-client-recp-1", "name": "Encina Capital Partners"},
    "redo": {"client_project": "rs-client-redo-1", "name": "Rental Depot"},
    "regr": {"client_project": "rs-client-regr-1", "name": "Region Rents"},
    "reic": {
        "client_project": "rs-client-reic-1",
        "name": "Rental Equipment Investment Crop",
    },
    "reng": {
        "client_project": "rs-client-reng-1",
        "name": "ENGS Commercial Finance Co.",
    },
    "rep": {"client_project": "rs-client-rep-1", "name": "Rep Rents"},
    "resd": {
        "client_project": "rs-client-resd-1",
        "name": "Rental Equipment Solutions, Inc.",
    },
    "reta": {"client_project": "rs-client-reta-1", "name": "Redtail Rental"},
    "rex": {"client_project": "rs-client-rex-1", "name": "Rexco Equipment, Inc."},
    "rff": {"client_project": "rs-client-rff-1", "name": "First Financial"},
    "rfish": {"client_project": "rs-client-rfish-1", "name": "Redfish Rentals"},
    "rfls": {"client_project": "rs-client-rfls-1", "name": "Reliable Forklift Sales"},
    "rfr": {"client_project": "rs-client-rfr-1", "name": "Rain For Rent"},
    "rguy": {"client_project": "rs-client-rguy-1", "name": "Rental Guys"},
    "rgyc": {"client_project": "rs-client-rgyc-1", "name": "TheRentalGuysCA"},
    "rhca": {
        "client_project": "rs-client-rhca-1",
        "name": "Hitachi Capital America Vendor Services",
    },
    "rher": {
        "client_project": "rs-client-rher-1",
        "name": "Rocky Hill Equipment Rentals",
    },
    "rib": {"client_project": "rs-client-rib-1", "name": "Investors Bank"},
    "rjv": {"client_project": "rs-client-rjv-1", "name": "RJV Equipment"},
    "rlcs": {"client_project": "rs-client-rlcs-1", "name": "Reliable Crane Service"},
    "rlex": {"client_project": "rs-client-rlex-1", "name": "Rentalex"},
    "rline": {"client_project": "rs-client-rline-1", "name": "Redline Rentals"},
    "rm": {"client_project": "rs-client-rm-1", "name": "Road Machinery LLC"},
    "rmcs": {"client_project": "rs-client-rmcs-1", "name": "Marlin Capital Solutions"},
    "rmef": {"client_project": "rs-client-rmef-1", "name": "Midland Equipment Finance"},
    "rmen": {"client_project": "rs-client-rmen-1", "name": "The Rental Men"},
    "rmhc": {
        "client_project": "rs-client-rmhc-1",
        "name": "Mitsubishi HC Capital America",
    },
    "rmoc": {"client_project": "rs-client-rmoc-1", "name": "Meridian OneCap"},
    "rmr": {"client_project": "rs-client-rmr-1", "name": "Red Mountain Rentals"},
    "rms": {
        "client_project": "rs-client-rms-1",
        "name": "Road Machinery \u0026 Supplies Co.",
    },
    "rnec": {"client_project": "rs-client-rnec-1", "name": "NEC Financial Services"},
    "rng": {"client_project": "rs-client-rng-1", "name": "Ring Power Corporation"},
    "rnjp": {"client_project": "rs-client-rnjp-1", "name": "Nikken"},
    "rnwk": {"client_project": "rs-client-rnwk-1", "name": "Rental Network"},
    "rnyn": {"client_project": "rs-client-rnyn-1", "name": "Runyon Rental"},
    "rol": {"client_project": "rs-client-rol-1", "name": "Roland Machinery"},
    "romco": {"client_project": "rs-client-romco-1", "name": "ROMCO Equipment"},
    "rora": {"client_project": "rs-client-rora-1", "name": "Robertson Rent All"},
    "rpc": {"client_project": "rs-client-rpc-1", "name": "Peapack Capital"},
    "rpm": {"client_project": "rs-client-rpm-1", "name": "RPM Machinery"},
    "rpwb": {"client_project": "rs-client-rpwb-1", "name": "Pacific Western Bank"},
    "rrci": {"client_project": "rs-client-rrci-1", "name": "Resource Rental Center"},
    "rredy": {
        "client_project": "rs-client-rredy-1",
        "name": "Rent Ready Equipment \u0026 Sales",
    },
    "rrr": {"client_project": "rs-client-rrr-1", "name": "R\u0026R Rentals"},
    "rsc": {"client_project": "rs-client-rsc-1", "name": "Surles Claims"},
    "rsmf": {
        "client_project": "rs-client-rsmf-1",
        "name": "Sumitomo Mitsui Finance and Leasing",
    },
    "rtc": {"client_project": "rs-client-rtc-1", "name": "Travelers Capital"},
    "rtecau": {"client_project": "rs-client-rtecau-1", "name": "Renteca"},
    "rwgb": {"client_project": "rs-client-rwgb-1", "name": "Rental Works Greensboro"},
    "rwmd": {"client_project": "rs-client-rwmd-1", "name": "Rental Works Maryland"},
    "rwuk": {"client_project": "rs-client-rwuk-1", "name": "Riwal UK"},
    "sab": {"client_project": "rs-client-sab-1", "name": "Sabre Rentals Ltd."},
    "sagee": {"client_project": "rs-client-sagee-1", "name": "Sage Equipment LLC"},
    "sagr": {"client_project": "rs-client-sagr-1", "name": "Sage Rental Services"},
    "sba": {"client_project": "rs-client-sba-1", "name": "Sunbelt Asphalt"},
    "sbc": {"client_project": "rs-client-sbc-1", "name": "Sunbelt Rentals CAN"},
    "sbce": {"client_project": "rs-client-sbce-1", "name": "Smith Bros"},
    "sbt": {"client_project": "rs-client-sbt-1", "name": "Sunbelt"},
    "sbtie": {"client_project": "rs-client-sbtie-1", "name": "Sunbelt Rentals Ireland"},
    "sbtuk": {"client_project": "rs-client-sbtuk-1", "name": "Sunbelt Rentals UK"},
    "sec": {"client_project": "rs-client-sec-1", "name": "Sunstate Equipment Co."},
    "seq": {"client_project": "rs-client-seq-1", "name": "Southeastern Equipment"},
    "serv": {
        "client_project": "rs-client-serv-1",
        "name": "Service Rentals and Supplies Inc",
    },
    "sfdg": {"client_project": "rs-client-sfdg-1", "name": "Summit Funding Group Inc."},
    "sgec": {
        "client_project": "rs-client-sgec-1",
        "name": "Siegmund Excavation \u0026 Construction",
    },
    "shcuk": {"client_project": "rs-client-shcuk-1", "name": "Skipton Hire Centre Ltd"},
    "shcuk2": {
        "client_project": "rs-client-shcuk2-1",
        "name": "Skipton Hire Centre Ltd",
    },
    "shec": {"client_project": "rs-client-shec-1", "name": "Shafer Equipment Rental"},
    "shorau": {"client_project": "rs-client-shorau-1", "name": "Shore Hire"},
    "shtx": {"client_project": "rs-client-shtx-1", "name": "Schaffer Equipment"},
    "simp": {"client_project": "rs-client-simp-1", "name": "Simplex Equipment Rental"},
    "sims": {"client_project": "rs-client-sims-1", "name": "SimsCrane"},
    "sji": {"client_project": "rs-client-sji-1", "name": "Skyjack, Inc."},
    "ska": {"client_project": "rs-client-ska-1", "name": "Skanska USA Civil"},
    "sky": {"client_project": "rs-client-sky-1", "name": "Skyworks of Virginia"},
    "slctc": {
        "client_project": "rs-client-slctc-1",
        "name": "Select Equipment Rentals",
    },
    "slg": {"client_project": "rs-client-slg-1", "name": "Sterling Crane USA"},
    "slgc": {"client_project": "rs-client-slgc-1", "name": "Sterling Crane CAN"},
    "smc": {"client_project": "rs-client-smc-1", "name": "Semcore 2 Rental Center"},
    "smon": {"client_project": "rs-client-smon-1", "name": "Simon Equipment Co"},
    "sms": {"client_project": "rs-client-sms-1", "name": "SMS Equipment (Do not use)"},
    "smse": {"client_project": "rs-client-smse-1", "name": "SMS Equipment"},
    "smsn": {"client_project": "rs-client-smsn-1", "name": "Simonson Equipment"},
    "solpau": {"client_project": "rs-client-solpau-1", "name": "Solution Plant Hire"},
    "sopw": {
        "client_project": "rs-client-sopw-1",
        "name": "1 Source Power and Equipment",
    },
    "spdi": {"client_project": "rs-client-spdi-1", "name": "Superior Rentals"},
    "spr": {"client_project": "rs-client-spr-1", "name": "SPR Rentals"},
    "sray": {"client_project": "rs-client-sray-1", "name": "Sonsray Machinery"},
    "srci": {"client_project": "rs-client-srci-1", "name": "Sun Rental Center Ohio"},
    "srs": {"client_project": "rs-client-srs-1", "name": "Stephensons Rental"},
    "staruk": {"client_project": "rs-client-staruk-1", "name": "Star Platforms"},
    "stb": {"client_project": "rs-client-stb-1", "name": "Stribling"},
    "stec": {"client_project": "rs-client-stec-1", "name": "SkyTec"},
    "stene": {"client_project": "rs-client-stene-1", "name": "Stephenson Equipment"},
    "stme": {"client_project": "rs-client-stme-1", "name": "Stemar Equipment"},
    "stp": {"client_project": "rs-client-stp-1", "name": "SitePro"},
    "strt": {"client_project": "rs-client-strt-1", "name": "Star Tractor"},
    "stw": {
        "client_project": "rs-client-stw-1",
        "name": "Stowers Machinery Corporation",
    },
    "summ": {"client_project": "rs-client-summ-1", "name": "Summit Materials"},
    "sumo": {"client_project": "rs-client-sumo-1", "name": "Superior Rents"},
    "sumr": {"client_project": "rs-client-sumr-1", "name": "Summit Equipment Rentals"},
    "sun": {"client_project": "rs-client-sun-1", "name": "Sunbelt"},
    "sundt": {"client_project": "rs-client-sundt-1", "name": "Sundt Construction Inc."},
    "sure": {
        "client_project": "rs-client-sure-1",
        "name": "Superior Rigging and Erecting",
    },
    "suruk": {"client_project": "rs-client-suruk-1", "name": "Surrey Hire"},
    "swnkde": {
        "client_project": "rs-client-swnkde-1",
        "name": "Schwenk Arbeitsbuehnen",
    },
    "swtr": {"client_project": "rs-client-swtr-1", "name": "Southwest Tool Rental"},
    "syn": {"client_project": "rs-client-syn-1", "name": "Synergy Equipment"},
    "synruk": {"client_project": "rs-client-synruk-1", "name": "Synergy Hire"},
    "tagr": {"client_project": "rs-client-tagr-1", "name": "Taylor Rental Greenville"},
    "tate": {"client_project": "rs-client-tate-1", "name": "Tates Rents"},
    "tcan": {"client_project": "rs-client-tcan-1", "name": "Torcan Lift"},
    "tec": {
        "client_project": "rs-client-tec-1",
        "name": "Tractor \u0026 Equipment Company",
    },
    "teds": {"client_project": "rs-client-teds-1", "name": "Ted's Rental"},
    "tej": {"client_project": "rs-client-tej-1", "name": "Tejas Equipment Rental"},
    "ter": {"client_project": "rs-client-ter-1", "name": "Trinity Equipment Rentals"},
    "terc": {"client_project": "rs-client-terc-1", "name": "TERC Equipment Rental"},
    "tes": {"client_project": "rs-client-tes-1", "name": "Trico Equipment"},
    "tex": {"client_project": "rs-client-tex-1", "name": "Texas First Rentals"},
    "tgtuk": {"client_project": "rs-client-tgtuk-1", "name": "Target Plant Hire"},
    "thguk": {"client_project": "rs-client-thguk-1", "name": "UK Tool Hire"},
    "tinc": {"client_project": "rs-client-tinc-1", "name": "Tri-Lift NC"},
    "tjre": {"client_project": "rs-client-tjre-1", "name": "Tejas Rent Equip"},
    "tmc": {"client_project": "rs-client-tmc-1", "name": "Titan Machinery Co."},
    "tmc2": {
        "client_project": "rs-client-tmc2-1",
        "name": "Titan Machinery (e-Emphasys)",
    },
    "tmhs": {
        "client_project": "rs-client-tmhs-1",
        "name": "Toyota Material Handling Systems",
    },
    "tmm": {"client_project": "rs-client-tmm-2", "name": "Thompson Machinery"},
    "tntc": {"client_project": "rs-client-tntc-1", "name": "TNTCrane"},
    "tntcc": {"client_project": "rs-client-tntcc-1", "name": "TNT Crane Canada"},
    "tom": {"client_project": "rs-client-tom-2", "name": "Thompson Tractor CAT"},
    "tomc": {"client_project": "rs-client-tomc-1", "name": "Thompson Tractor CRS"},
    "tomuk": {"client_project": "rs-client-tomuk-1", "name": "Thomas Plant Hire"},
    "tor": {"client_project": "rs-client-tor-1", "name": "Toromont CAT"},
    "torg": {"client_project": "rs-client-torg-1", "name": "Torgersons"},
    "tpgn": {
        "client_project": "rs-client-tpgn-1",
        "name": "Top Gunn Equipment Rentals",
    },
    "tpi": {"client_project": "rs-client-tpi-1", "name": "Temp Power Inc"},
    "trc": {"client_project": "rs-client-trc-1", "name": "Timp Rental"},
    "trg": {
        "client_project": "rs-client-trg-1",
        "name": "Taylor Rental Greenville (Not in Use)",
    },
    "trgt": {"client_project": "rs-client-trgt-1", "name": "Target Rental"},
    "tri": {"client_project": "rs-client-tri-1", "name": "Trico Lift"},
    "trmp": {"client_project": "rs-client-trmp-1", "name": "Triumph Modular"},
    "trmt": {"client_project": "rs-client-trmt-1", "name": "Time Rental"},
    "trnr": {"client_project": "rs-client-trnr-1", "name": "Turner Mining Group"},
    "tru7uk": {"client_project": "rs-client-tru7uk-1", "name": "Tru7"},
    "tsa": {"client_project": "rs-client-tsa-1", "name": "Time Savers Aerials"},
    "tsbo": {"client_project": "rs-client-tsbo-1", "name": "Tri-State Bobcat"},
    "tsbo2": {"client_project": "rs-client-tsbo2-1", "name": "Tri-State Bobcat"},
    "tsco": {"client_project": "rs-client-tsco-1", "name": "Trench Shoring Company"},
    "tvrg": {
        "client_project": "rs-client-tvrg-1",
        "name": "True Value Rental of Greensboro",
    },
    "tyl": {"client_project": "rs-client-tyl-1", "name": "Tyler Rental"},
    "u1": {"client_project": "rs-client-u1-1", "name": "United Rentals, Inc."},
    "u1c": {"client_project": "rs-client-u1c-1", "name": "URI Canada"},
    "ucf": {
        "client_project": "rs-client-ucf-1",
        "name": "United Construction \u0026 Forestry",
    },
    "ultuk": {"client_project": "rs-client-ultuk-1", "name": "Ultimate Access Hire"},
    "uncf": {
        "client_project": "rs-client-uncf-1",
        "name": "United Construction \u0026 Forestry",
    },
    "untejp": {"client_project": "rs-client-untejp-1", "name": "Unite"},
    "uos": {"client_project": "rs-client-uos-1", "name": "Custom Truck One Source"},
    "upwuk": {"client_project": "rs-client-upwuk-1", "name": "Upward Access Hire"},
    "ust": {"client_project": "rs-client-ust-2", "name": "US Trinity - Do not use"},
    "usta": {"client_project": "rs-client-usta-1", "name": "US Trinity"},
    "valy": {"client_project": "rs-client-valy-1", "name": "Valley Equipment Rents"},
    "van": {"client_project": "rs-client-van-1", "name": "Vantage Equipment LLC"},
    "vces": {
        "client_project": "rs-client-vces-1",
        "name": "Volvo Construction Equipment \u0026 Services of North America",
    },
    "vcon": {"client_project": "rs-client-vcon-1", "name": "VanCon"},
    "ver": {"client_project": "rs-client-ver-1", "name": "Vandalia Rental"},
    "vhr": {"client_project": "rs-client-vhr-1", "name": "VHR Rental"},
    "vol": {
        "client_project": "rs-client-vol-1",
        "name": "Volvo Construction Equipment",
    },
    "wag": {"client_project": "rs-client-wag-1", "name": "Wagner Equipment Co."},
    "wagh": {"client_project": "rs-client-wagh-1", "name": "Wagner Heavy Rents"},
    "war": {
        "client_project": "rs-client-war-1",
        "name": "Warren Power \u0026 Machinery, Inc.",
    },
    "ward": {"client_project": "rs-client-ward-1", "name": "Wards Rental Center"},
    "wce": {"client_project": "rs-client-wce-1", "name": "West Coast Equipment"},
    "we1": {"client_project": "rs-client-we1-1", "name": "WesternOne WE1"},
    "weat": {"client_project": "rs-client-weat-1", "name": "Weathers Rental Center"},
    "weav": {"client_project": "rs-client-weav-1", "name": "Weavers Rent All"},
    "wec": {"client_project": "rs-client-wec-1", "name": "Wilson Equipment"},
    "weq": {
        "client_project": "rs-client-weq-1",
        "name": "WesternOne Equpiment Company",
    },
    "wfhuk": {"client_project": "rs-client-wfhuk-1", "name": "Welfare 4 Hire"},
    "wha": {"client_project": "rs-client-wha-1", "name": "Whayne CAT"},
    "whmh": {"client_project": "rs-client-whmh-1", "name": "Wheeler Material Handling"},
    "wic": {"client_project": "rs-client-wic-1", "name": "WI Clark Company"},
    "wine": {"client_project": "rs-client-wine-1", "name": "Brandywine Rentals"},
    "wire": {"client_project": "rs-client-wire-1", "name": "Wiregrass Construction"},
    "wlrg": {"client_project": "rs-client-wlrg-1", "name": "Wolter Inc"},
    "wmc": {"client_project": "rs-client-wmc-1", "name": "Wheeler Machinery Co."},
    "wogr": {"client_project": "rs-client-wogr-1", "name": "W.O. Grubb"},
    "wolf": {"client_project": "rs-client-wolf-1", "name": "Greywolf Equipment"},
    "wood": {
        "client_project": "rs-client-wood-1",
        "name": "Woodstock Equipment Company",
    },
    "woud": {"client_project": "rs-client-woud-1", "name": "Woudenberg Enterprises"},
    "wri": {"client_project": "rs-client-wri-1", "name": "We Rent It"},
    "wrk": {"client_project": "rs-client-wrk-1", "name": "Works Equipment Rental"},
    "wse": {
        "client_project": "rs-client-wse-1",
        "name": "Western States Equipment Co.",
    },
    "wslc": {"client_project": "rs-client-wslc-1", "name": "Western States Equipment"},
    "wst": {"client_project": "rs-client-wst-1", "name": "West Side Tractor Sales Co."},
    "wwm": {"client_project": "rs-client-wwm-1", "name": "Worldwide Machinery"},
    "wxla": {"client_project": "rs-client-wxla-1", "name": "Westrax Machinery"},
    "wyc": {"client_project": "rs-client-wyc-1", "name": "Wyoming CAT"},
    "xau": {"client_project": "rs-client-xau-1", "name": "XAU Rentals"},
    "xcmg": {
        "client_project": "rs-client-xcmg-1",
        "name": "XCMG North America Corporation",
    },
    "xcn": {"client_project": "rs-client-xcn-1", "name": "XCN Rentals"},
    "xeu": {"client_project": "rs-client-xeu-1", "name": "XEU Rentals"},
    "xhd": {"client_project": "rs-client-xhd-1", "name": "XHD Rentals"},
    "xjp": {"client_project": "rs-client-xjp-1", "name": "XJP Rentals"},
    "xuk": {"client_project": "rs-client-xuk-1", "name": "XUK Rentals"},
    "xyz": {"client_project": "rs-client-xyz-1", "name": "XYZ Company"},
    "xyza": {"client_project": "rs-client-xyza-1", "name": "XYZ Company Appraisals"},
    "xyzef": {"client_project": "rs-client-xyzef-1", "name": "XYZ Equipment Finance"},
    "xyzeu": {"client_project": "rs-client-xyzeu-1", "name": "XYZ Company - EU"},
    "xyzuk": {"client_project": "rs-client-xyzuk-1", "name": "XYZ Company - UK"},
    "yan": {"client_project": "rs-client-yan-1", "name": "Yancey Rents"},
    "ybo": {"client_project": "rs-client-ybo-1", "name": "Yancy Bros. Co."},
    "year": {"client_project": "rs-client-year-1", "name": "365 Equipment Supply"},
    "ymc": {"client_project": "rs-client-ymc-1", "name": "Yellowhouse Machinery Co."},
    "zaci": {"client_project": "rs-client-zaci-1", "name": "Zachry Inc"},
    "zax": {
        "client_project": "rs-client-zax-1",
        "name": "Zaxis Financial Services Americas",
    },
    "zepde": {"client_project": "rs-client-zepde-1", "name": "Zeppelin Rental"},
    "zig": {"client_project": "rs-client-zig-1", "name": "Ziegler"},
}
