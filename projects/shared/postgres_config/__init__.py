pg_db_servers = {
    "alloydb": {
        "server": {"local": "************", "dev": "************", "prod": ""},
        "server_replica": {"local": "************", "dev": "************", "prod": ""},
        "server_multi_tenant": {
            "local": "************",
            "dev": "************",
            "prod": "",
        },
        "server_multi_tenant_replica": {
            "local": "************",
            "dev": "************",
            "prod": "",
        },
        "platform_server": {"local": "************", "dev": "************", "prod": ""},
        "use_certificates": False,
    },
    "pg_bouncer": {
        "server_multi_tenant": {
            "local": "rfm03-pool.develop.rouseservices.com",
            "dev": "rfm03-pool.develop.rouseservices.com",
            "prod": "rfm03-pool.rouseservices.com",
        },
        "server_multi_tenant_replica": {
            "local": "rfm03r0-pool.develop.rouseservices.com",
            "dev": "rfm03r0-pool.develop.rouseservices.com",
            "prod": "rfm03r0-pool.rouseservices.com",
        },
        "platform_server": {
            "local": "platform01-pool.develop.rouseservices.com",
            "dev": "platform01-pool.develop.rouseservices.com",
            "prod": "platform01-pool.rouseservices.com",
        },
        "rfm_services01": {
            "local": "rfm-services01-pool.develop.rouseservices.com",
            "dev": "rfm-services01-pool.develop.rouseservices.com",
            "prod": "rfm-services01-pool.rouseservices.com",
        },
        "rfm01_ppe": {
            "local": None,
            "dev": None,
            "prod": "rfm01-ppe-pool.rouseservices.com",
        },
        "use_certificates": True,
    },
}

pg_db_shard_servers = {
    "rfm02": {
        "main": {
            "local": None,  # server retired
            "dev": None,  # server retired
            "prod": None,  # server retired
        },
        "replica": {
            "local": None,  # server retired
            "dev": None,  # server retired
            "prod": None,  # server retired
        },
        "use_certificates": True,
        "use_rfm_global": True,
        "use_fleet_manager": True,
        "is_multi_tenant": False,
        "db_name": "rfm",
        "db_name_allow_prepared_statement": {"main": "rfm_pipeline", "replica": "rfm"},
    },
    "rfm03": {
        "main": {
            "local": "rfm03-pool.develop.rouseservices.com",
            "dev": "rfm03-pool.develop.rouseservices.com",
            "prod": "rfm03-pool.rouseservices.com",
        },
        "replica": {
            "local": "rfm03r0-pool.develop.rouseservices.com",
            "dev": "rfm03r0-pool.develop.rouseservices.com",
            "prod": "rfm03r0-pool.rouseservices.com",
        },
        "use_certificates": True,
        "use_rfm_global": True,
        "use_fleet_manager": True,
        "is_multi_tenant": True,
        "db_name": "rfm__mt",
        "db_name_allow_prepared_statement": {"main": "rfm__mt", "replica": "rfm__mt"},
    },
    "rfm04": {
        "main": {
            "local": "rfm04-pool.develop.rouseservices.com",
            "dev": "rfm04-pool.develop.rouseservices.com",
            "prod": "rfm04-pool.rouseservices.com",
        },
        "replica": {
            "local": "rfm04r0-pool.develop.rouseservices.com",
            "dev": "rfm04r0-pool.develop.rouseservices.com",
            "prod": "rfm04r0-pool.rouseservices.com",
        },
        "use_certificates": True,
        "use_rfm_global": True,
        "use_fleet_manager": True,
        "is_multi_tenant": False,
        "db_name": "rfm",
        "db_name_allow_prepared_statement": {"main": "rfm_pipeline", "replica": "rfm"},
    },
    "rfm05": {
        "main": {
            "local": None,  # not configured in dev
            "dev": None,  # not configured in dev
            "prod": "rfm05-pool.rouseservices.com",
        },
        "replica": {
            "local": None,  # not configured in dev
            "dev": None,  # not configured in dev
            "prod": "rfm05r0-pool.rouseservices.com",
        },
        "use_certificates": True,
        "use_rfm_global": True,
        "use_fleet_manager": True,
        "is_multi_tenant": False,
        "db_name": "rfm",
        "db_name_allow_prepared_statement": {"main": "rfm_pipeline", "replica": "rfm"},
    },
    "rfm06": {
        "main": {
            "local": None,  # not configured in dev
            "dev": None,  # not configured in dev
            "prod": "rfm06-pool.rouseservices.com",
        },
        "replica": {
            "local": None,  # not configured in dev
            "dev": None,  # not configured in dev
            "prod": "rfm06r0-pool.rouseservices.com",
        },
        "use_certificates": True,
        "use_rfm_global": True,
        "use_fleet_manager": True,
        "is_multi_tenant": False,
        "db_name": "rfm",
        "db_name_allow_prepared_statement": {"main": "rfm_pipeline", "replica": "rfm"},
    },
    "rfm07": {
        "main": {
            "local": "rfm07-pool.develop.rouseservices.com",
            "dev": "rfm07-pool.develop.rouseservices.com",
            "prod": "rfm07-pool.rouseservices.com",
        },
        "replica": {
            "local": "rfm07r0-pool.develop.rouseservices.com",
            "dev": "rfm07r0-pool.develop.rouseservices.com",
            "prod": "rfm07r0-pool.rouseservices.com",
        },
        "use_certificates": True,
        "use_rfm_global": True,
        "use_fleet_manager": True,
        "is_multi_tenant": False,
        "db_name": "rfm",
        "db_name_allow_prepared_statement": {"main": "rfm_pipeline", "replica": "rfm"},
    },
    "rfm08": {
        "main": {
            "local": None,  # not configured in dev
            "dev": None,  # not configured in dev
            "prod": "rfm08-pool.rouseservices.com",
        },
        "replica": {
            "local": None,  # not configured in dev
            "dev": None,  # not configured in dev
            "prod": "rfm08r0-pool.rouseservices.com",
        },
        "use_certificates": True,
        "use_rfm_global": True,
        "use_fleet_manager": True,
        "is_multi_tenant": False,
        "db_name": "rfm",
        "db_name_allow_prepared_statement": {"main": "rfm_pipeline", "replica": "rfm"},
    },
    "rfm1001": {
        "main": {
            "local": "rfm1001-pool.develop.rouseservices.com",
            "dev": "rfm1001-pool.develop.rouseservices.com",
            "prod": "rfm1001-pool.rouseservices.com",
        },
        "replica": {
            "local": "rfm1001rp0-pool.develop.rouseservices.com",
            "dev": "rfm1001rp0-pool.develop.rouseservices.com",
            "prod": "rfm1001rp0-pool.rouseservices.com",
        },
        "use_certificates": True,
        "use_rfm_global": True,
        "use_fleet_manager": True,
        "is_multi_tenant": False,
        "db_name": "rfm",
        "db_name_allow_prepared_statement": {"main": "rfm_pipeline", "replica": "rfm"},
        "alloydb_columnar_engine": [
            {
                "cluster_id": {
                    "local": "rfm1001-dev",
                    "dev": "rfm1001-dev",
                    "prod": "rfm1001-prod",
                },
                "instance_id": {
                    "local": "rfm1001rp1-dev",
                    "dev": "rfm1001rp1-dev",
                    "prod": "rfm1001rp1-prod",
                },
                "region": {
                    "local": "us-central1",
                    "dev": "us-central1",
                    "prod": "us-central1",
                },
            }
        ],
    },
    "rfm09": {
        "main": {
            "local": None,  # not configured in dev
            "dev": None,  # not configured in dev
            "prod": "rfm09-pool.rouseservices.com",
        },
        "replica": {
            "local": None,  # not configured in dev
            "dev": None,  # not configured in dev
            "prod": "rfm09-pool.rouseservices.com",  # No replica server has been created, use the same as main
        },
        "use_certificates": True,
        "use_rfm_global": True,
        "use_fleet_manager": True,
        "is_multi_tenant": False,
        "db_name": "rfm",
        "db_name_allow_prepared_statement": {"main": "rfm_pipeline", "replica": "rfm"},
    },
    "rfm_services01": {
        "main": {
            "local": "rfm-services01-pool.develop.rouseservices.com",
            "dev": "rfm-services01-pool.develop.rouseservices.com",
            "prod": "rfm-services01-pool.rouseservices.com",
        },
        "replica": {
            "local": "rfm-services01r0-pool.develop.rouseservices.com",
            "dev": "rfm-services01r0-pool.develop.rouseservices.com",
            "prod": "rfm-services01r0-pool.rouseservices.com",
        },
        "use_certificates": True,
        "use_rfm_global": True,
        "use_fleet_manager": False,
        "is_multi_tenant": False,
        "db_name": "rfm_globals",
    },
    "rfm01_ppe": {
        "main": {
            "local": None,
            "dev": None,
            "prod": "rfm01-ppe-pool.rouseservices.com",
        },
        "replica": {
            "local": None,
            "dev": None,
            "prod": "rfm01-ppe-pool.rouseservices.com",
        },
        "use_certificates": True,
        "use_rfm_global": True,
        "use_fleet_manager": False,
        "is_multi_tenant": False,
        "is_pre_prod": True,
        "db_name": "rfm",
        "db_name_allow_prepared_statement": {"main": "rfm_pipeline", "replica": "rfm"},
    },
    "alloy-rfm08": {
        "main": {
            "local": "pgbouncer-alloy-rfm08.develop.rouseservices.com",
            "dev": "pgbouncer-alloy-rfm08.develop.rouseservices.com",
            "prod": None,  # not configured in prod
        },
        "replica": {
            "local": "pgbouncer-alloy-rfm08.develop.rouseservices.com",
            "dev": "pgbouncer-alloy-rfm08.develop.rouseservices.com",
            "prod": None,  # not configured in prod
        },
        "use_certificates": False,
        "use_rfm_global": False,
        "use_rfm_global_alloy": True,
        "use_fleet_manager": False,  # AlloyDB does not generate data for fleet_manager
        "is_multi_tenant": False,
        "db_name": "rfm",
        "db_name_allow_prepared_statement": {"main": "rfm", "replica": "rfm"},
        "cluster_id": {
            "local": "alloydb-cluster",
            "dev": "alloydb-cluster",
            "prod": None,  # not configured in prod
        },
        "instance_id": {
            "local": "alloydb-rfm08-dev",
            "dev": "alloydb-rfm08-dev",
            "prod": None,  # not configured in prod
        },
    },
    "rfm101": {
        "main": {
            "local": None,  # not configured in dev
            "dev": None,  # not configured in dev
            "prod": "rfm101-pool.rouseservices.com",
        },
        "replica": {
            "local": None,  # not configured in dev
            "dev": None,  # not configured in dev
            "prod": "rfm101-pool.rouseservices.com",
        },
        "use_certificates": False,
        "use_rfm_global": False,
        "use_rfm_global_alloy": True,
        "use_fleet_manager": False,  # AlloyDB does not generate data for fleet_manager
        "is_multi_tenant": False,
        "db_name": "rfm",
        "db_name_allow_prepared_statement": {"main": "rfm", "replica": "rfm"},
        "cluster_id": {
            "local": None,  # not configured in dev
            "dev": None,  # not configured in dev
            "prod": "rfm101-prod",
        },
        "instance_id": {
            "local": None,  # not configured in dev
            "dev": None,  # not configured in dev
            "prod": "rfm101-prod",
        },
    },
    "rfm101-etcxl": {
        "main": {
            "local": "rfm101-etcxl-proxy-pool.develop.rouseservices.com",
            "dev": "rfm101-etcxl-proxy-pool.develop.rouseservices.com",
            "prod": "rfm101-etcxl-proxy-pool.rouseservices.com",
        },
        "replica": {
            "local": "rfm101-etcxl-proxy-pool.develop.rouseservices.com",
            "dev": "rfm101-etcxl-proxy-pool.develop.rouseservices.com",
            "prod": "rfm101-etcxl-proxy-pool.rouseservices.com",
        },
        "use_certificates": False,
        "use_rfm_global": False,
        "use_rfm_global_alloy": False,  # Same server as rfm101
        "use_fleet_manager": False,  # AlloyDB does not generate data for fleet_manager
        "is_multi_tenant": False,
        "db_name": "rfm",
        "db_name_allow_prepared_statement": {"main": "rfm", "replica": "rfm"},
        "cluster_id": {
            "local": "alloydb-cluster",
            "dev": "alloydb-cluster",
            "prod": "rfm101-prod",
        },
        "instance_id": {
            "local": "alloydb-rfm08-dev",
            "dev": "alloydb-rfm08-dev",
            "prod": "rfm101-prod",
        },
    },
    "rfm101-sbt": {
        "main": {
            "local": "rfm101-sbt-proxy-pool.develop.rouseservices.com",
            "dev": "rfm101-sbt-proxy-pool.develop.rouseservices.com",
            "prod": "rfm101-sbt-proxy-pool.rouseservices.com",
        },
        "replica": {
            "local": "rfm101-sbt-proxy-pool.develop.rouseservices.com",
            "dev": "rfm101-sbt-proxy-pool.develop.rouseservices.com",
            "prod": "rfm101-sbt-proxy-pool.rouseservices.com",
        },
        "use_certificates": False,
        "use_rfm_global": False,
        "use_rfm_global_alloy": False,  # Same server as rfm101
        "use_fleet_manager": False,  # AlloyDB does not generate data for fleet_manager
        "is_multi_tenant": False,
        "db_name": "rfm",
        "db_name_allow_prepared_statement": {"main": "rfm", "replica": "rfm"},
        "cluster_id": {
            "local": "alloydb-cluster",
            "dev": "alloydb-cluster",
            "prod": "rfm101-prod",
        },
        "instance_id": {
            "local": "alloydb-rfm08-dev",
            "dev": "alloydb-rfm08-dev",
            "prod": "rfm101-prod",
        },
    },
    "rfm101-u1": {
        "main": {
            "local": "rfm101-u1-proxy-pool.develop.rouseservices.com",
            "dev": "rfm101-u1-proxy-pool.develop.rouseservices.com",
            "prod": "rfm101-u1-proxy-pool.rouseservices.com",
        },
        "replica": {
            "local": "rfm101-u1-proxy-pool.develop.rouseservices.com",
            "dev": "rfm101-u1-proxy-pool.develop.rouseservices.com",
            "prod": "rfm101-u1-proxy-pool.rouseservices.com",
        },
        "use_certificates": False,
        "use_rfm_global": False,
        "use_rfm_global_alloy": False,  # Same server as rfm101
        "use_fleet_manager": False,  # AlloyDB does not generate data for fleet_manager
        "is_multi_tenant": False,
        "db_name": "rfm",
        "db_name_allow_prepared_statement": {"main": "rfm", "replica": "rfm"},
        "cluster_id": {
            "local": "alloydb-cluster",
            "dev": "alloydb-cluster",
            "prod": "rfm101-prod",
        },
        "instance_id": {
            "local": "alloydb-rfm08-dev",
            "dev": "alloydb-rfm08-dev",
            "prod": "rfm101-prod",
        },
    },
    "rfm101-hg": {
        "main": {
            "local": "rfm101-hg-proxy-pool.develop.rouseservices.com",
            "dev": "rfm101-hg-proxy-pool.develop.rouseservices.com",
            "prod": "rfm101-hg-proxy-pool.rouseservices.com",
        },
        "replica": {
            "local": "rfm101-hg-proxy-pool.develop.rouseservices.com",
            "dev": "rfm101-hg-proxy-pool.develop.rouseservices.com",
            "prod": "rfm101-hg-proxy-pool.rouseservices.com",
        },
        "use_certificates": False,
        "use_rfm_global": False,
        "use_rfm_global_alloy": False,  # Same server as rfm101
        "use_fleet_manager": False,  # AlloyDB does not generate data for fleet_manager
        "is_multi_tenant": False,
        "db_name": "rfm",
        "db_name_allow_prepared_statement": {"main": "rfm", "replica": "rfm"},
        "cluster_id": {
            "local": "alloydb-cluster",
            "dev": "alloydb-cluster",
            "prod": "rfm101-prod",
        },
        "instance_id": {
            "local": "alloydb-rfm08-dev",
            "dev": "alloydb-rfm08-dev",
            "prod": "rfm101-prod",
        },
    },
    "alloydb": {
        "main": {
            "local": "************",
            "dev": "************",
            "prod": "",
        },
        "replica": {
            "local": "************",
            "dev": "************",
            "prod": "",
        },
        "use_certificates": False,
        "use_rfm_global": False,
        "use_fleet_manager": False,
        "is_multi_tenant": False,
        "db_name": "rfm",
        "db_name_allow_prepared_statement": {"main": "rfm_pipeline", "replica": "rfm"},
    },
}
