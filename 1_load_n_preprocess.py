#!/usr/bin/env python

arg_doc = """
Preprocess raw data, compute lookback windows and save to local or GCP
"""


import random
from typing import Tuple

import numpy as np
from common.ds_cache import LocalObjectHandler, GCPObjectHandler
from common.load import Loader
from common.lookback import LookBack
from common.preprocessing import Preprocessor
from common.utils import get_enviroment_name, input_sanity_check, preprocessed_sanity_check, take_sample
from common.utils import protect_production_env, compute_sample_sizes_debug_cache, compute_sold_price_debug_cache

from common.sample_size_cache import SampleSizeCache
from config import create_algo_config, AlgoConfig


import datetime as dt
import pandas as pd
from arg_parser import get_parser, postprocess_args
from loguru import logger
import os
import sys
from time import perf_counter

_EMPTY_STR = ''

# NOTE: export LOG_LEVEL=DEBUG  <== for debug messages  (default is INFO)
log_level = os.getenv("LOG_LEVEL", "INFO")

# Set up the logger
logger.remove()  # Remove any previously added sinks
logger.add(sys.stdout, level=log_level)

def _do_preprocess_incrementally(preprocessed_start: str,preprocessor: Preprocessor, lookback: LookBack, config: AlgoConfig,
                                 raw_data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:

    algo = config.name

    #assert validation_date is not None, 'validation_date is required for incremental processing'
    preprocess_incremental = perf_counter()

    logger.warning(f'starting preprocessing from data from {preprocessed_start}')
    logger.warning(f'NOT SAMPLING DATA. Ignoring sampling in the case the arg --sample is passed.')

    # add download old raw data and lookback data code
    start_env_handler = GCPObjectHandler(root_path=preprocessed_start) if preprocessed_start != 'local' else LocalObjectHandler()

    # old_lookback_data=joblib.load('preprocessed/lookback_data.pkl')#add the proper code to download and read old lookback data
    old_lookback_data = start_env_handler.load(config.lookback_data_path)

    assert 'auc_end_date' in old_lookback_data.columns, 'auc_end_date is missing'

    logger.info(f"old_lookback_data last date: {old_lookback_data.auc_end_date.max()}")
    logger.info(f"old_lookback_data shape: {old_lookback_data.shape}")


    #NOTE: substracting ndays (overlap will be reprocessed). The data engineering team can add new data with 45 days of delay 
    # with retroactive publish dates. (lag issue in truck tractors retail data)
    old_cutoff_date = old_lookback_data.auc_end_date.max() - dt.timedelta(days=81)
    old_lookback_data = old_lookback_data[old_lookback_data.auc_end_date <= old_cutoff_date]

    raw_data_incremental = raw_data[ (raw_data.auc_end_date > old_cutoff_date)]

    msg = (
            f'raw_data_incremental is empty. Check the dates. validation date should be greater '
            f'than {old_lookback_data.auc_end_date.max()}'
    )
    #assert old_lookback_data.auc_end_date.max() < validation_date, msg
    assert raw_data_incremental.shape[0] > 0, msg

    logger.info(f"Preprocessing raw_data_incremental shape: {raw_data_incremental.shape}")
    preprocessed_data_incremental = preprocessor.fit_transform(raw_data_incremental)
    preprocessed_sanity_check(preprocessed_data_incremental)

    # this preprocessed_data will be sent to the lookback so that sums data uses this 
    # instead of the incremental subset
    preprocessed_data = pd.concat([old_lookback_data[preprocessed_data_incremental.columns.tolist()],
                                   preprocessed_data_incremental])

    preprocess_incremental_end = perf_counter()
    _msg = (
        f" ===> (INSIDE PREPROCESSING) Preprocess incremental " 
        f"took {(preprocess_incremental_end - preprocess_incremental)/60.0:.2f} minutes"
    )
    logger.info(_msg)   

    sorted_data = lookback.preprocess_and_sort(preprocessed_data)

    sort_inc_end = perf_counter()
    logger.info(f" ===> (INSIDE PREPROCESSING) Sort took {(sort_inc_end - preprocess_incremental_end)/60.0:.2f} minutes")

    # NOTE: this code add look back groups to the incremental data. However we also run this inside 
    # preprocess_and_sort. So it is running twice for incremental data.
    # TODO: refactor this code to avoid running this twice 
    preprocessed_data_incremental_with_groups = lookback._make_loock_back_groups(preprocessed_data_incremental)
    new_lookback_data = lookback.fit_transform(preprocessed_data_incremental_with_groups,sorted_data)

    lookback_inc_end = perf_counter()
    logger.info(f" ===> (INSIDE PREPROCESSING) Lookback incremental took {(lookback_inc_end - sort_inc_end)/60.0:.2f} minutes")

    # concat incremental with the old data to get the full lookback
    lookback_data = pd.concat([old_lookback_data,new_lookback_data])

    logger.info(f"old_lookback_data shape: {old_lookback_data.shape}")
    logger.info(f'new_lookback_data shape: {new_lookback_data.shape}')
    logger.info(f'lookback_data shape: {lookback_data.shape}')

    return preprocessed_data,lookback_data, sorted_data

def _do_preprocess_non_incrementally(preprocessor: Preprocessor, lookback: LookBack,config: AlgoConfig,
                                     raw_data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:


    preprocess_non_incremental_start = perf_counter()

    preprocessed_data = preprocessor.fit_transform(raw_data)
    preprocessed_sanity_check(preprocessed_data)

    preprocess_non_incremental_end = perf_counter()
    logger.info(f" ===> (INSIDE PREPROCESSING) Preprocess non incremental took {(preprocess_non_incremental_end - preprocess_non_incremental_start)/60.0:.2f} minutes")

    sorted_data = lookback.preprocess_and_sort(preprocessed_data)

    sort_non_inc_end = perf_counter()
    logger.info(f" ===> (INSIDE PREPROCESSING) Sort non incremental took {(sort_non_inc_end - preprocess_non_incremental_end)/60.0:.2f} minutes")

    lookback_data = lookback.fit_transform(preprocessed_data,sorted_data)

    lookback_non_inc_end = perf_counter()
    logger.info(f" ===> (INSIDE PREPROCESSING)  Lookback non incremental took {(lookback_non_inc_end - sort_non_inc_end)/60.0:.2f} minutes") 

    return preprocessed_data,lookback_data, sorted_data

# NOTE: Needs to use default str empty space because of vertexai yml files.
# I belive vertexai read the args passed as str: '--start-env=None'. So None is str
def main(env: str, algo: str, sample: int, validation_date: dt.date = _EMPTY_STR, 
         preprocess_start: str = _EMPTY_STR):

    # Set seeds for reproducibility
    seed_value = 42  
    os.environ['PYTHONHASHSEED'] = str(seed_value)  
    random.seed(seed_value)         # Python random seed
    np.random.seed(seed_value)      # Numpy random seed

    protect_production_env(env)

    logger.info(f"===> Preprocess data main(): env: {env}; algo: {algo}; sample size: {sample}; validation date: {validation_date} ...")

    config = create_algo_config()
    loader = Loader()
    raw_data = loader.load_data()

    # NOTE: shufle the data to avoid any bias in the training
    # We are using tmp fix that orders the data: 1st non-duplcated Mascus data, 
    # then the rest of the Mascus and non Mascus data.
    #raw_data = raw_data.sample(frac=1, random_state=seed_value).reset_index(drop=True)

    # NOTE: random sample and incremental processing expect this column
    raw_data['auc_end_date'] = pd.to_datetime(raw_data.auc_end_time, errors = 'coerce').dt.tz_localize(None).dt.date

    logger.info(f"raw data size: {raw_data.shape[0]}")

    # NOTE: load last 15 days of currency data. We cache the mean of this period.
    _n_days_ago = validation_date - dt.timedelta(days=15)
    currency_data = loader.load_currency_cache(start_date = _n_days_ago, end_date=validation_date)
    logger.info(f"loaded currency data shape: {currency_data.shape}")

    # TODO: Review in simplify. env == 'prod' is a bug and we probably do not need it
    # NOTE: Get random sample for debugging and testing
    logger.info(f"original raw data shape: {raw_data.shape}")
    if sample > 0 and preprocess_start == _EMPTY_STR and get_enviroment_name(env) != 'prod':

        logger.debug(f"type validation date: {type(validation_date)}")
        raw_data = take_sample(raw_data, sample, validation_date)
       
    logger.info(f"Used raw sample (train + test) size: {raw_data.shape}")
    input_sanity_check(raw_data)

    preprocess_lookback_start = perf_counter()
    preprocessor = Preprocessor({})
    lookback = LookBack()

    if preprocess_start != _EMPTY_STR:
        preprocessed_data, lookback_data, sorted_data = _do_preprocess_incrementally(preprocess_start,preprocessor, 
                                                                                    lookback, 
                                                                                    config, raw_data)
    else:
        preprocessed_data, lookback_data, sorted_data = _do_preprocess_non_incrementally(preprocessor, lookback,
                                                                            config, raw_data)
    
    # NOTE: We only want this check in training time. In prediction time, we don't want to check this
    _today = dt.datetime.today().date()
    assert (preprocessed_data['auc_end_date'] < _today).all(), f"All dates must be before today"
    
    logger.info(f"lookback_data shape: {lookback_data.shape}")

    assert 'currency_code' in lookback_data.columns, 'currency_code is missing'
    assert 'item_country' in lookback_data.columns, 'item_country is missing'

    assert 'currency_code' in sorted_data.columns, 'currency_code is missing'
    assert 'item_country' in sorted_data.columns, 'item_country is missing'

    cache_start = perf_counter()
    # NOTE: It is required to cache before do lookback.transform(). Otherwise, the lookback cache will be empty
    logger.info("caching lookback data")
    lookback.cache_look_back_window_prediction(sorted_data, currency_data)

    # NOTE: free memory (maybe this is why we have memory issues or you use more memory)
    del sorted_data
    cache_end = perf_counter()
    duration_in_minutes = (cache_end - cache_start)/60.0
    logger.info(f" ===> (INSIDE PREPROCESSING) cache lookback took {duration_in_minutes:.2f} minutes")

    logger.info(f" ===> (INSIDE PREPROCESSING) Total Preprocess  took {(cache_end - preprocess_lookback_start)/60.0:.2f} minutes")

    handler = GCPObjectHandler(root_path=env) if get_enviroment_name(env) != 'local' else LocalObjectHandler()

    logger.info(f"Saving preprocessed data to {config.preprocessed_data_path}...")
    handler.save(preprocessed_data, config.preprocessed_data_path)

    logger.info(f"Saving lookback data to {config.lookback_data_path}...")
    handler.save(lookback_data, config.lookback_data_path)

    logger.info(f"Saving lookback model to {config.lookback_model_path}...")
    handler.save(lookback, config.lookback_model_path)

    # NOTE: Debug
    debug_start = perf_counter()
    logger.info(f"Saving data for debugging.")

    input_sanity_check(raw_data)

    handler.save(raw_data, 'debug/raw_data.csv')
    handler.save(preprocessed_data, 'debug/preprocessed_data.csv')

    lookback_data_cache = lookback.convert_lookback_cache_to_tabular_data()
    handler.save(lookback_data_cache, 'debug/lookback_cache.csv')
    handler.save(lookback._currency_cache, 'debug/currency_cache.csv')

    logger.info("Computing sold prices debug cache...")
    sold_prices = compute_sold_price_debug_cache(preprocessed_data)
    fname = 'debug/sold_prices.csv'
    logger.info(f"Saving sold prices to {fname}...")
    handler.save(sold_prices, fname)

    logger.info("Computing sample sizes debug cache...")
    sample_sizes = compute_sample_sizes_debug_cache(preprocessed_data)
    fname = 'debug/sample_sizes.csv'
    logger.info(f"Saving sample sizes to {fname}...")
    handler.save(sample_sizes, fname)
    
    logger.info("Computing sample size age bin cache...")
    ss_cacche = SampleSizeCache()
    ss_cacche.fit(preprocessed_data)
    handler.save(ss_cacche, 'algolite/sample_size_cache.pkl')



    debug_end = perf_counter()
    duration_in_minutes = (debug_end - debug_start)/60.0
    logger.warning(f" ===> saving preprocessed files for debug took {duration_in_minutes:.2f} minutes")

if __name__ == '__main__':

    parser = get_parser(arg_doc, has_sample=True, has_inc_preprocess=True)
    args = parser.parse_args()

    main_args = postprocess_args(args)
    main(**main_args)
