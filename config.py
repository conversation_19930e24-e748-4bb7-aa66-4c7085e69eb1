
from dataclasses import dataclass, field

@dataclass
class AlgoConfig():

    name: str = 'lite'
    version: str = ''
    validation_date: str = ''
    env: str = ''
    sample: int = 0

    # NOTE: dataclasses needs to use default_factory to create default values for mutables values
    lookback_data_path: str = field(default_factory=lambda: 'preprocessed/lookback_data.pkl')
    lookback_model_path: str = field(default_factory=lambda: 'preprocessed/lookback.pkl')
    preprocessed_data_path: str = field(default_factory=lambda: 'preprocessed/preprocessed_data.pkl')

    embedder_weights_path: str = field(default_factory=lambda: 'algolite/best_keras_model_weights.hdf5')
    embedder_model_path: str = field(default_factory=lambda: 'algolite/embedder.pkl') 

    regressor_model_path: str = field(default_factory=lambda: 'algolite/regressor.pkl')

    confidence_model_path: str = field(default_factory=lambda: 'confidence/confidence_regressor.pkl')

    # TODO: remove this later
    # NOTE: caches
    sample_size_cache_path_for_debug: str = field(default_factory=lambda: 'preprocessed/sample_size_cache.pkl')


def create_algo_config(algo: str = 'lite', env: str= '', version: str= '', validation_date: str = '', sample: int = 0) -> AlgoConfig:

    return AlgoConfig(name=algo, env=env, version=version, validation_date=validation_date, sample=sample)
