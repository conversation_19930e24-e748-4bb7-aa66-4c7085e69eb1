# Use the latest 2.1 version of CircleCI pipeline process engine.
# See: https://circleci.com/docs/2.0/configuration-reference
version: 2.1
orbs:
  slack: circleci/slack@4.12.5

jobs:
  notify-pipeline-start:
    docker:
      - image: cimg/base:stable
    steps:
      - checkout
      - run:
          name: Set branch-specific Slack channel
          command: |
            case "${CIRCLE_BRANCH}" in
              master|main)
                echo "export SLACK_CHANNEL='valuations-deployments'" >> $BASH_ENV
                echo "export ENVIRONMENT='Production'" >> $BASH_ENV
                ;;
              develop|dev)
                echo "export SLACK_CHANNEL='valuations-ds-cicd-pipelines'" >> $BASH_ENV
                echo "export ENVIRONMENT='Development'" >> $BASH_ENV
                ;;
              *)
                echo "export SLACK_CHANNEL='valuations-ds-cicd-pipelines'" >> $BASH_ENV
                echo "export ENVIRONMENT='Feature'" >> $BASH_ENV
                ;;
            esac
            source $BASH_ENV
            echo "Branch: ${CIRCLE_BRANCH}"
            echo "Environment: ${ENVIRONMENT}"
            echo "Slack Channel: ${SLACK_CHANNEL}"
      - slack/notify:
          channel: '${SLACK_CHANNEL}'
          custom: |
            {
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":rocket: *Algo-Pricing Pipeline Start*\n\n*Environment:* ${ENVIRONMENT}\n*Branch:* \\`${CIRCLE_BRANCH}\\`\n*Commit:* \\`${CIRCLE_SHA1:0:7}\\`\n*Author:* ${CIRCLE_USERNAME}\n*Build:* <${CIRCLE_BUILD_URL}|#${CIRCLE_BUILD_NUM}>"
                  }
                }
              ]
            }

workflows:
  algo_pricing_pipeline:
    jobs:
      - notify-pipeline-start:
          context:
            - slack-secrets
