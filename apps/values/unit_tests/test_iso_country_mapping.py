from apps.values.controller import get_values_for_asset

from unittest.mock import patch
from datetime import datetime
from json import dumps

import pytest


def mock_request(
        category_id=15,
        subcategory_id=2797,
        make_id=31,
        model_id=18905,
        model_year=2021,
        country='USA',
        use_locality=False,
        effective_date="2025-01-31"
        ):
    return {
        "fleet_asset_id": 1,
        "country": country,
        "effective_date": effective_date,
        "category_id": category_id,
        "subcategory_id": subcategory_id,
        "make_id": make_id,
        "model_id": model_id,
        "model_year": model_year,
        "include_algo": False,
        "include_ml4r": True,
        "use_locality": use_locality,
        "use_fallback_to_in_progress": True,
        "version": 2
    }


@pytest.mark.parametrize("f_country, s_country", [
    ('USA', 'US'),
    ('CAN', 'CA'),
    ('GBR', 'UK'),
    ('FRA', 'FR'),
    ('DEU', 'DE'),
    ('ESP', 'ES'),
    ('ITA', 'IT'),
])

def test_valuation_country(f_country, s_country):
    f_request = mock_request(country=f_country)
    f_valuation = get_values_for_asset(dumps(f_request))

    s_request = mock_request(country=s_country)
    s_valuation = get_values_for_asset(dumps(s_request))

    assert f_valuation['metadata']['valuation_country'] == s_valuation['metadata']['valuation_country']
    assert f_valuation['metadata']['valuation_country'] == s_valuation['metadata']['valuation_country']
    assert f_valuation['data']['adjusted_value']['fmv'] == s_valuation['data']['adjusted_value']['fmv']
    assert f_valuation['data']['adjusted_value']['flv'] == s_valuation['data']['adjusted_value']['flv']
    assert f_valuation['data']['adjusted_value']['wlv'] == s_valuation['data']['adjusted_value']['wlv']
    assert f_valuation['data']['adjusted_value']['olv'] == s_valuation['data']['adjusted_value']['olv']
