import datetime
import json
import logging
import os
import string
import traceback
import requests

sess = requests.Session()
adapter = requests.adapters.HTTPAdapter(max_retries=200)
sess.mount('http://', adapter)
from typing import Optional, Tuple, Union
from decimal import Decimal
from dataclasses import asdict
import jsonpickle
from django.http import HttpResponse
from random import choices
# from django.core.cache import cache

import beeline
from rouse_values_library.dtos import AllowNegativeValue
from sentry_sdk import set_tag

from rouse_values_library import (
    AssetDetail,
    ValuationConfiguration,
    CostOption,
    AlgoValuationContext,
    ValuationResult,
    Value
)
from rouse_values_library_extras import get_prevailing_date, get_market_code_for_country_code, cpi_country_map, \
    get_markets, get_current_prevailing_date, get_iso_country_code

from valuation_service import settings
from .requests import ValuateAssetRequest, ValuateAssetsRequest
from .values_service import valuation_service_api, \
    region_service_api, \
    mpe_service, \
    effective_date_to_model_id_map, \
    effective_date_to_model_group_map, \
    build_residual_curve
from .algo_service import algo_service
from valuation_service.settings import MODEL_GROUP_URLS
from concurrent.futures import ProcessPoolExecutor

VALUATION_MODEL_IN_PROGRESS_ID = -1
logger = logging.getLogger(__name__)
logger.setLevel("DEBUG")


class FailedValuation:
    id = None
    valuation_model_id = None
    valuation_model_date = None
    reason = None

    @staticmethod
    def create(valuation_request_id, valuation_model_id, valuation_model_date, reason):
        result = FailedValuation()
        result.id = valuation_request_id
        result.valuation_model_id = valuation_model_id
        result.valuation_model_date = valuation_model_date
        result.reason = reason
        result.success = False
        return result


# @beeline.traced(name='controller.get_ab_cost_for_asset')
def get_ab_cost_for_asset(json_obj):
    """
    Get asset AB Cost from json payload
    @param json_obj: json data that represents the asset
    """
    valuate_asset_request = ValuateAssetRequest.from_json(parse_request(json_obj))
    model_year = valuate_asset_request.model_year
    original = asdict(valuate_asset_request)
    original["effective_date"] = f"{'2017' if int(model_year) < 2017 else model_year}-06-30"
    valuate_asset_request = ValuateAssetRequest.from_json(json.dumps(original))

    ab_cost = get_ab_cost_for_asset_request(valuate_asset_request)
    if ab_cost in MODEL_GROUP_URLS.keys():
        # here we will send a request to another pod
        url = MODEL_GROUP_URLS[ab_cost]
        url = url.replace("get_values", "get_ab_cost")
        json_obj = json.loads(json_obj)
        json_obj["recursion_detected"] = True
        try:
            response = sess.post(url, json=json_obj)
            if response.status_code == 200:
                ab_cost = response.json().get("ab_cost", None)
            else:
                return json.loads(jsonpickle.encode(FailedValuation.create(
                    valuate_asset_request.id, None, None, "Unable to get ab cost")))
        except Exception as ex:
            logging.exception(ex)
            return json.loads(jsonpickle.encode(FailedValuation.create(
                valuate_asset_request.id, None, None, f"Unable to get ab cost: {ex}")))

    return json.loads(jsonpickle.encode({"ab_cost": ab_cost}, unpicklable=False, make_refs=True))


# @beeline.traced(name='controller.get_ab_cost_for_asset_request')
def get_ab_cost_for_asset_request(valuate_asset_request: ValuateAssetRequest):
    asset, market = _build_asset(valuate_asset_request)
    config, error = _build_config(valuate_asset_request, market)
    if error is not None:
        return FailedValuation.create(valuate_asset_request.id, None, None, f"{error}")
    # if unable to figure out the market, don't even call the service
    if market is None:
        return FailedValuation.create(valuate_asset_request.id, None, None, "Unable to build determine market")
    if asset is None:
        return FailedValuation.create(valuate_asset_request.id, None, None, "Unable to build asset detail")

    valuation_model_group, error = _get_valuation_model_group(valuate_asset_request, market)
    if valuation_model_group == 'beta':
        valuation_model_group = 'bravo'
    elif valuation_model_group == 'gamma':
        valuation_model_group = 'charlie'

    local_model_group = settings.LOCAL_MODEL_GROUP
    if local_model_group != 'zulu' and local_model_group != valuation_model_group:
        return valuation_model_group

    try:
        original_context = valuation_service_api.create_valuation_execution_context(config, asset)

        if (original_context
                and original_context.has_ab_cost and "Using In Progress valuation model" not in original_context.log):
            return original_context.valuation_model.get("valuation_cost", None)
    except Exception:
        return None


# @beeline.traced(name='controller._get_valuation_response')
def _get_valuation_response(request, batch_id=None):
    valuation = get_values_for_asset_request(request)
    if valuation in MODEL_GROUP_URLS.keys():
        # here we will send a request to another pod
        url = MODEL_GROUP_URLS[valuation]
        json_obj = json.loads(request.to_json())
        json_obj["recursion_detected"] = True

        response = sess.post(url, json=json_obj)
        if response.status_code == 200:
            valuation = response.json()
        else:
            valuation = FailedValuation.create(request.id, None, None, f"Unable to reach alternate pod")
    if batch_id is not None and hasattr(valuation, 'metadata'):
        valuation.metadata.batch_id = batch_id

    return valuation


# @beeline.traced(name='controller.get_values_for_assets')
def get_values_for_assets(json_obj):
    """
    Valuate assets from json payload
    @param json_obj: json data that represents the asset to valuate
    """
    _requests = ValuateAssetsRequest.from_json(parse_assets_request(json_obj))
    batch_id = _requests.batch_id
    if batch_id is None:
        batch_id = f"{''.join(choices(string.ascii_letters + string.digits, k=8))}-" \
                   f"{''.join(choices(string.ascii_letters + string.digits, k=4))}-" \
                   f"{''.join(choices(string.ascii_letters + string.digits, k=4))}-" \
                   f"{''.join(choices(string.ascii_letters + string.digits, k=12))}"
    ids = [batch_id for _ in _requests.batch]
    with ProcessPoolExecutor(max_workers=16) as executor:
        valuations = executor.map(_get_valuation_response, _requests.batch, ids)

    return json.loads(jsonpickle.encode(list(valuations), unpicklable=False, make_refs=True))


# @beeline.traced(name='controller.get_values_for_asset')
def get_values_for_asset(json_obj, ml4r_override=None):
    """
    Valuate asset from json payload
    @param json_obj: json data that represents the asset to valuate
    """
    valuate_asset_request = ValuateAssetRequest.from_json(parse_request(json_obj))
    valuation = get_values_for_asset_request(valuate_asset_request, ml4r_override)
    if valuation in MODEL_GROUP_URLS.keys():
        # here we will send a request to another pod
        url = MODEL_GROUP_URLS[valuation]
        json_obj = json.loads(json_obj)
        json_obj["recursion_detected"] = True
        try:
            response = sess.post(url, json=json_obj)
            if response.status_code == 200:
                valuation = response.json()

            else:
                return response
        except Exception as ex:
            logging.exception(ex)
            valuation = json.loads(jsonpickle.encode(
                FailedValuation.create(valuate_asset_request.id, None, None, f"Unable to valuate: {ex}")))

    return json.loads(jsonpickle.encode(valuation, unpicklable=False, make_refs=True))


# @beeline.traced(name='controller.get_values_for_asset_request')
def get_values_for_asset_request(valuate_asset_request: ValuateAssetRequest, ml4r_override=None) -> Union[FailedValuation, ValuationResult]:
    if invalid_valuation := _request_is_valid(valuate_asset_request):
        return invalid_valuation

    asset, market = _build_asset(valuate_asset_request)
    cache_behavior = valuate_asset_request.cache_behavior
    include_locality_comps = valuate_asset_request.include_locality_comps

    if cache_behavior not in ['Normal', 'No_Cache', 'Delete_Key', 'Ignore_AT', 'Ignore_Reg']:
        cache_behavior = 'Normal'

    # if unable to figure out the market, don't even call the service
    if market is None:
        return FailedValuation.create(valuate_asset_request.id, None, None, "Unable to build determine market")

    if asset is None:
        return FailedValuation.create(valuate_asset_request.id, None, None, "Unable to build asset detail")

    config, error = _build_config(valuate_asset_request, market, ml4r_override)
    if error is not None:
        return FailedValuation.create(valuate_asset_request.id, None, None, f"{error}")

    valuation_model_group, error = _get_valuation_model_group(valuate_asset_request, market)
    if valuation_model_group == 'beta':
        valuation_model_group = 'bravo'
    elif valuation_model_group == 'gamma':
        valuation_model_group = 'charlie'

    local_model_group = settings.LOCAL_MODEL_GROUP

    if local_model_group != 'zulu' and local_model_group != valuation_model_group:
        # if valuate_asset_request.recursion_detected is True:
        #     return FailedValuation.create(valuate_asset_request.id, None, None, "Recursive model group detected")
        return valuation_model_group

    valuation, error = _get_valuation(config, asset, market, include_locality_comps, cache_behavior,
                                      valuate_asset_request)
    if valuation is not None:
        return valuation

    # This should not happen, it means that the library has a bug or there is some reason so it can't valuate
    # the asset, I'll try again just once.
    logger.warning(f"Trying to valuate asset again...")
    valuation, error = _get_valuation(config, asset, market, include_locality_comps, cache_behavior,
                                      valuate_asset_request)
    # valuation.log['model_group'] = local_model_group
    if valuation is not None:
        return valuation

    return FailedValuation.create(valuate_asset_request.id, None, None, f"Unable to valuate asset {error}")


# @beeline.traced(name='controller._request_is_valid')
def _request_is_valid(valuate_asset_request: ValuateAssetRequest) -> Optional[FailedValuation]:
    """
    Valuate asset from json payload
    @param json_obj: json data that represents the asset to valuate
    """
    if valuate_asset_request.fleet_asset_id is None:
        return FailedValuation.create(valuate_asset_request.id, None, None, "Fleet Asset id is required")

    if valuate_asset_request.fleet_asset_id < 1:
        return FailedValuation.create(valuate_asset_request.id, None, None,
                                      f"Fleet Asset id is invalid {valuate_asset_request.fleet_asset_id}")

    if valuate_asset_request.category_id is None:
        return FailedValuation.create(valuate_asset_request.id, None, None, "Asset category id is required")

    if valuate_asset_request.category_id < 1:
        return FailedValuation.create(valuate_asset_request.id, None, None,
                                      f"Asset category id is invalid {valuate_asset_request.category_id}")

    if valuate_asset_request.subcategory_id is None:
        return FailedValuation.create(valuate_asset_request.id, None, None, "Asset subcategory id is required")

    if valuate_asset_request.subcategory_id < 1:
        return FailedValuation.create(valuate_asset_request.id,
                                      None, None,
                                      f"Asset subcategory id is invalid {valuate_asset_request.subcategory_id}"
                                      )

    if valuate_asset_request.make_id is None:
        return FailedValuation.create(valuate_asset_request.id, None, None, "Asset make id is required")

    if valuate_asset_request.make_id < 1:
        return FailedValuation.create(valuate_asset_request.id, None, None,
                                      f"Asset make id is invalid {valuate_asset_request.make_id}")

    if valuate_asset_request.model_id is None:
        return FailedValuation.create(valuate_asset_request.id, None, None, "Asset model id is required")

        # -1 currently means InProgress
    if valuate_asset_request.model_id < 1:
        return FailedValuation.create(valuate_asset_request.id, None, None,
                                      f"Asset model id is invalid {valuate_asset_request.model_id}")

    if valuate_asset_request.model_year is None:
        return FailedValuation.create(valuate_asset_request.id, None, None, "Asset model year id is required")

    if valuate_asset_request.model_year < 1:
        return FailedValuation.create(valuate_asset_request.id, None, None,
                                      f"Asset model year is invalid {valuate_asset_request.model_year}")

    if valuate_asset_request.country is None:
        return FailedValuation.create(valuate_asset_request.id, None, None, "Country is required")

    if len(valuate_asset_request.country) < 1:
        return FailedValuation.create(valuate_asset_request.id, None, None,
                                      f"Country is invalid {valuate_asset_request.country}")

    if valuate_asset_request.effective_date is None:
        return FailedValuation.create(valuate_asset_request.id, None, None, "Effective date is required")

    return None


# @beeline.traced(name='controller._get_valuation')
def _get_valuation(
        config: ValuationConfiguration,
        asset: AssetDetail,
        market: str,
        include_locality_comps: bool,
        cache_behavior='Normal',
        valuate_asset_request: ValuateAssetRequest = None
) -> Tuple[Optional[ValuationResult], Optional[str]]:
    try:
        valuation = valuation_service_api.valuate(config, asset)

        # valuation.log.performance.algo_call_time = 0.0
        if _add_algo(config, asset, valuation, cache_behavior):
            _recalculate_mpe(valuation, asset.market)

            _recalculate_algo(valuation)
        if str(valuation.version) != "2":
            success = valuation.success
            model_year = valuation.model_year
        else:
            success = valuation.metadata.success
            model_year = valuation.metadata.model_year

        if success and include_locality_comps:
            regions = _get_regions(config, asset)
            if regions:
                locality_comps = build_locality_comps(valuation, regions)
                if hasattr(valuation, "data"):
                    valuation.data.locality_comps = locality_comps
                else:
                    valuation.locality_comps = locality_comps

        has_ml4r_values = (hasattr(valuation, "data")
                           and hasattr(valuation.data, "ml4r") and valuation.data.ml4r is not None
                           and ("fmv" in valuation.data.ml4r and "flv" in valuation.data.ml4r))
        if valuate_asset_request.include_residual_curve and (success or has_ml4r_values):
            set_residual_curve_in_valuation(
                success, has_ml4r_values, valuation, valuate_asset_request, model_year, asset)
        valuation.log.performance.model_group = settings.LOCAL_MODEL_GROUP
        clean_valuation_log(valuation)

        return valuation, None
    except Exception as e:
        logger.exception(f"Unable to valuate asset {e}. {traceback.format_exc()}")
        return None, str(e)


def _recalculate_algo(valuation):
    if valuation.data.flv_multipliers.mpe_flv_multiplier is not None and\
            valuation.data.algo_values.get("algo_flv") is not None:
        valuation.data.algo_values["algo_mpe"] = int(valuation.data.flv_multipliers.mpe_flv_multiplier *
                                                     valuation.data.algo_values.get("algo_flv"))


# @beeline.traced(name='controller.clean_valuation_log')
def clean_valuation_log(valuation):
    del valuation.log.scale_factor_residual_data
    del valuation.log.scale_factor_historical_data
    del valuation.log.residual_curve_forecast_schedules
    if hasattr(valuation, "data") and hasattr(valuation.data, "residual_curve_residual_forecast"):
        del valuation.data.residual_curve_residual_forecast


# @beeline.traced(name='controller.set_residual_curve_in_valuation')
def set_residual_curve_in_valuation(success, has_ml4r_values, valuation, valuate_asset_request, model_year, asset):
    if ("Unable to find the asset in either book" in valuation.log.trace
            or "Asset cost is invalid for the requested valuation model" in valuation.log.trace
            and not has_ml4r_values):
        return
    if model_year < 2011:
        valuation.log.trace.append(f"Unable to calculate residual curve. Asset is too old.")
        return
    final_residual_curve = None
    replacement_cost = valuation.log.replacement_cost
    res_curve_schedules = valuation.log.residual_curve_schedules
    factors_totals = valuation.log.scale_factor_totals
    forecast_schedules = valuation.log.residual_curve_forecast_schedules
    scale_factor_residual_data = valuation.log.scale_factor_residual_data
    scale_factor_historical_data = valuation.log.scale_factor_historical_data

    if str(valuation.version) != "2":
        current_year = valuation.valuation_model_date.year
        try:
            residual_forecasts = dict(fmv=valuation.residual_curve_residual_forecast.fmv,
                                      flv=valuation.residual_curve_residual_forecast.flv)
        except:
            residual_forecasts = None
        country = asset.market.country.code
        current_age = valuation.asset_age
    else:
        current_year = valuation.metadata.valuation_model_date.year
        current_age = valuation.metadata.asset_age
        try:
            residual_forecasts = dict(fmv=valuation.data.residual_curve_residual_forecast.fmv,
                                      flv=valuation.data.residual_curve_residual_forecast.flv)
        except:
            residual_forecasts = None
        country = valuation.metadata.valuation_country

    if model_year < current_year-12:
        valuation.log.trace.append(f"Unable to calculate residual curve. Asset is too old.")
        return

    window = {"current_year": current_year, "min_year": current_year-12}

    if success and hasattr(valuation, "data") and hasattr(valuation.data, "residual_forecast"):
        original_ab_cost = _get_original_ab_cost(valuate_asset_request, model_year)
        final_residual_curve = build_residual_curve(
            replacement_cost, original_ab_cost, res_curve_schedules,
            model_year, current_age, factors_totals, False, country, window,
            residual_forecasts, forecast_schedules, scale_factor_residual_data, scale_factor_historical_data)

    elif ((success or has_ml4r_values)
          and hasattr(valuation, "data") and hasattr(valuation.data, "ml4r") and valuation.data.ml4r
          and ("fmv" in valuation.data.ml4r and "flv" in valuation.data.ml4r)):
        ml4r_values = dict(fmv=valuation.data.ml4r["fmv"], flv=valuation.data.ml4r["flv"])
        final_residual_curve = build_residual_curve(
            None, ml4r_values, res_curve_schedules, model_year, current_age, factors_totals, True, country, window,
            residual_forecasts, forecast_schedules, scale_factor_residual_data, scale_factor_historical_data)

    if (final_residual_curve and
            (final_residual_curve.get("fmv_residual_curve", []) or final_residual_curve.get("flv_residual_curve", []))):
        if hasattr(valuation, "data"):
            valuation.data.residual_curve = final_residual_curve
        else:
            valuation.residual_curve = final_residual_curve
    else:
        no_ml4r_data = (not has_ml4r_values 
                        and (valuate_asset_request.country.lower() in settings.ML4R_COUNTRIES
                            or valuate_asset_request.country.lower() not in settings.CALCULATED_COUNTRIES))
        valuation.log.trace.append(get_residual_curve_trace(model_year, no_ml4r_data))
        del valuation.log.residual_curve_schedules


# @beeline.traced(name='controller.get_residual_curve_trace')
def get_residual_curve_trace(model_year, no_ml4r_data):
    reason = ""
    if model_year > datetime.datetime.now().year:
        reason = "Asset model year is higher than current year."
    elif no_ml4r_data:
        reason = "Unable to retrieve ML4R data."
    return f"Unable to calculate residual curve. {reason}"


# @beeline.traced(name='controller._get_original_ab_cost')
def _get_original_ab_cost(valuate_asset_request, model_year):
    if model_year >= 2017:
        original_ab_cost = get_ab_cost_for_asset(json.dumps(asdict(valuate_asset_request)))
        original_ab_cost = (original_ab_cost.get("ab_cost", None)
                            if original_ab_cost and "ab_cost" in original_ab_cost
                            else None)
        return original_ab_cost
    return None


# @beeline.traced(name='controller._get_regions')
def _get_regions(config: ValuationConfiguration, asset: AssetDetail):
    """ Get Regions from cache, otherwise search in DB through RVL """
    context = valuation_service_api.create_valuation_execution_context(config, asset)
    country = asset.market.country.code
    regions = None

    if not regions:
        regions = region_service_api.get_regions(context, country)

    return sorted(regions, key=lambda r: r[list(r.keys())[0]]['name'])


def build_locality_comps(valuation: ValuationResult, regions: list):
    locality_comps = []
    base_value_decimal = valuation.log.decimal_values
    adjusted_value_decimal = valuation.log.adjusted_decimal_values

    base_multiplier = 1
    for scale_factor in valuation.log.scale_factor_totals:
        if "region" not in scale_factor.type:
            base_multiplier *= Decimal(scale_factor.total_multiplier)

    for region in regions:
        region_code = list(region.keys())[0]
        region_multiplier = region[region_code]["multiplier"]
        final_multiplier = Decimal(base_multiplier) * Decimal(region_multiplier)
        flv = None
        fmv = None
        wlv = None
        adjusted_flv = None
        adjusted_fmv = None
        adjusted_wlv = None
        if base_value_decimal.flv is not None:
            flv = base_value_decimal.flv * final_multiplier
            adjusted_flv = int(flv)
        if base_value_decimal.fmv is not None:
            fmv = base_value_decimal.fmv * final_multiplier
            adjusted_fmv = int(fmv)
        if base_value_decimal.wlv is not None:
            wlv = base_value_decimal.wlv * final_multiplier
            adjusted_wlv = int(wlv)

        adjusted_value = Value(flv=adjusted_flv, fmv=adjusted_fmv, wlv=adjusted_wlv)
        flv_varience = None
        fmv_varience = None
        wlv_varience = None
        if flv is not None:
            flv_varience = round(flv - adjusted_value_decimal.flv)
        if fmv is not None:
            fmv_varience = round(fmv - adjusted_value_decimal.fmv)
        if wlv is not None:
            wlv_varience = round(wlv - adjusted_value_decimal.wlv)

        variance = AllowNegativeValue(flv=flv_varience,
                         fmv=fmv_varience,
                         wlv=wlv_varience)

        locality_comps.append({
            "code": region_code,
            "name": region[region_code]["name"],
            "multiplier": region_multiplier,
            "adjusted_value": adjusted_value,
            "variance": variance
        })

    return locality_comps


def _add_algo(
        config: ValuationConfiguration,
        asset: AssetDetail,
        valuation: ValuationResult,
        cache_behavior='Normal'
) -> bool:
    tag = 'Include Algo' if config.include_algo else 'No Algo'
    set_tag("include_algo", tag)
    valuation_version = str(valuation.version)
    if valuation_version == "2":
        has_algo = hasattr(valuation.data, "algo_values")
    else:
        has_algo = hasattr(valuation, "algo_values")
    if (
            not config.include_algo or
            not has_algo
    ):
        return False
    else:
        algo_service.add_values_to_valuation(asset, valuation, cache_behavior)
        return True


# @beeline.traced(name='controller._build_asset')
def _build_asset(valuate_asset_request: ValuateAssetRequest) -> Tuple[Optional[AssetDetail], Optional[str]]:
    usage = {
        "meter_miles": float(
            valuate_asset_request.meter_miles) if valuate_asset_request.meter_miles is not None else None,
        "meter_hours": float(
            valuate_asset_request.meter_hours) if valuate_asset_request.meter_hours is not None else None,
        "meter_code": valuate_asset_request.meter_code,
        "expected_usage_per_year": valuate_asset_request.expected_usage_per_year,
    }

    try:
        # VALS-225: 2023-06-14 Sunil wants to default all markets to USNA
        #   so that we can serve Latin American countries
        market_code = get_market_code_for_country_code(valuate_asset_request.country, 'USNA')
    except Exception:
        return None, None

    market_code = market_code.lower()
    market = {
        "code": market_code,
        "name": valuate_asset_request.country,
        "country": {"code": valuate_asset_request.country},
    }
    if valuate_asset_request.locality:
        market["country"]["region"] = {"code": valuate_asset_request.locality}

    asset_args = {
        "condition": valuate_asset_request.equipment_status,
        "recondition_date": valuate_asset_request.get_recondition_date(),
        "equipment_options": valuate_asset_request.equipment_options,
        "market": market,
        "usage": usage,
        "cost": float(valuate_asset_request.asset_cost) if valuate_asset_request.asset_cost else None,
    }

    asset = AssetDetail.from_csmm(
        valuate_asset_request.fleet_asset_id,
        valuate_asset_request.category_id,
        valuate_asset_request.subcategory_id,
        valuate_asset_request.make_id,
        valuate_asset_request.model_id,
        valuate_asset_request.model_year,
        **asset_args,
    )
    return asset, market_code


# @beeline.traced(name='controller._build_config')
def _build_config(
        valuate_asset_request: ValuateAssetRequest,
        market: str,
        ml4r_override: bool = None
) -> Tuple[Optional[ValuationConfiguration], Optional[str]]:
    config = {
        "adjustment_config": {
            "use_schedule": True,
            "use_meter": valuate_asset_request.use_meter,
            "use_locality": valuate_asset_request.use_locality,
            "use_condition": valuate_asset_request.use_condition,
            "use_recondition": valuate_asset_request.use_reconditioned_date,
            "use_equipment_options": valuate_asset_request.use_equipment_options,
        },
        "cost_option": CostOption.ASSET.name if valuate_asset_request.use_asset_cost else CostOption.AB_COST.name,
        "meter_forecast": valuate_asset_request.generate_residual_values,
        "residual_forecast_config": {
            "include_flv": valuate_asset_request.generate_residual_values,
            "include_fmv": valuate_asset_request.generate_residual_values,
            "include_olv": valuate_asset_request.generate_residual_values,
            "include_mpe": valuate_asset_request.generate_residual_values,
            "include_wlv": (valuate_asset_request.generate_residual_values and valuate_asset_request.generate_wlv),
        },
        "valuation_config": {
            "include_replacement_cost": True,
            "include_base_value": True,
            "include_adjusted_value": True,
            "include_adjustments": True,
            "include_flv": True,
            "include_fmv": True,
            "include_olv": True,
            "include_mpe": True,
            "include_wlv": valuate_asset_request.generate_wlv,
        },
        "valuation_method": "standard",
        "fallback_to_in_progress": valuate_asset_request.use_fallback_to_in_progress,
        "fallback_to_asset_cost": valuate_asset_request.use_fallback_to_asset_cost,
        "include_algo": valuate_asset_request.include_algo,
        "include_eu_locality_comps": valuate_asset_request.include_eu_locality_comps,
        "forecast_usage_model": valuate_asset_request.forecast_usage_model,
        "valuation_version": valuate_asset_request.valuation_version,
        "include_ml4r": valuate_asset_request.include_ml4r,
        "include_forecast_results": valuate_asset_request.include_forecast_results,
        "include_residual_curve": valuate_asset_request.include_residual_curve,
    }
    if ml4r_override is not None:
        config['allow_ml4r'] = ml4r_override
    else:
        config['allow_ml4r'] = (valuate_asset_request.country.lower() in settings.ML4R_COUNTRIES) or \
                               (valuate_asset_request.country.lower() not in settings.CALCULATED_COUNTRIES)

    valuation_model_id, error = _get_valuation_model_id(valuate_asset_request, market)
    valuation_model_id_usna, error = _get_valuation_model_id(valuate_asset_request, "USNA")
    if error:
        return None, error

    conf = ValuationConfiguration(valuation_model_id, valuation_model_id_usna, **config)
    if valuate_asset_request.id:
        # Override the id
        conf.id = valuate_asset_request.id
    return conf, None


def _get_valuation_model_id(
        valuate_asset_request: ValuateAssetRequest,
        market: str
) -> Tuple[Optional[int], Optional[str]]:
    if valuate_asset_request.use_in_progress_model:
        return VALUATION_MODEL_IN_PROGRESS_ID, None

    if valuate_asset_request.use_prevailing_model:
        effective_date = get_prevailing_date(datetime.datetime.now(), market.upper())
    else:
        effective_date = valuate_asset_request.get_effective_date()

    effective_date = effective_date.strftime("%Y-%m")
    try:
        return effective_date_to_model_id_map[market.upper()][effective_date], None
    except KeyError:
        error = f"Requested to valuate using an unsupported date {effective_date} for market {market.upper()}"
        logger.debug(error)
        return None, error


def _recalculate_mpe(valuation: ValuationResult, market: str) -> None:
    if not _can_calculate_base_mpe(valuation):
        return
    try:
        valuation_version = str(valuation.version)
        context = AlgoValuationContext()
        context.set_algo_requested(True)
        context.set_mpe_requested(True)
        context.set_is_mpe_eligible(True)
        context.market_code = market
        if valuation_version == "2":
            context.set_algo_base_price(valuation.data.algo_values['algo_base'])
            context.set_algo_adjusted_price(valuation.data.algo_values['algo_flv'])
            context.base_value = valuation.data.base_value
            context.adjusted_value = valuation.data.adjusted_value
            context.flv_multipliers = valuation.data.flv_multipliers

        else:
            context.set_algo_base_price(valuation.algo_values['algo_base'])
            context.set_algo_adjusted_price(valuation.algo_values['algo_flv'])
            context.base_value = valuation.base_value
            context.adjusted_value = valuation.adjusted_value
            context.flv_multipliers = valuation.flv_multipliers

        context.market_code = market.code
        context.log = valuation.log.trace
        context.rule_data = valuation.log.rule_data
        mpe_service.add_mpe(context)

        if hasattr(valuation.log, "rule_data"):
            if valuation.log.base_value is not None:
                if valuation_version == "2":
                    valuation.log.base_value.mpe = valuation.data.base_value.mpe
                else:
                    valuation.log.base_value.mpe = valuation.base_value.mpe

            mpe_data = valuation.log.rule_data.get('MPE')
            if mpe_data and mpe_data.get('algo_value'):
                mpe_data["reason"] = ""
                mpe_data["is_valid"] = True
                mpe_data["code"] = 0
            _force_base_adjusted_value(valuation, valuation.log.rule_data.get('MPE').get('algo_value'))
    except Exception:
        logger.exception("Unable to calculate mpe from algo values")


def _force_base_adjusted_value(valuation: ValuationResult, mpe):
    valuation_version = str(valuation.version)
    if valuation_version == "2":
        if valuation.data.base_value is None:
            valuation.data.base_value = Value()
            valuation.data.base_value.mpe = mpe

        if valuation.data.adjusted_value is None:
            valuation.data.adjusted_value = Value()
            valuation.data.adjusted_value.mpe = mpe
    else:
        if valuation.base_value is None:
            valuation.base_value = Value()
            valuation.base_value.mpe = mpe

        if valuation.adjusted_value is None:
            valuation.adjusted_value = Value()
            valuation.adjusted_value.mpe = mpe


def _can_calculate_base_mpe(valuation: ValuationResult) -> bool:
    valuation_version = str(valuation.version)
    if valuation_version == "2":
        algo_values = valuation.data.algo_values
    else:
        algo_values = valuation.algo_values
    if (
            algo_values['algo_base'] is None
            or algo_values['algo_fmv'] is None
            or algo_values['algo_flv'] is None
    ):
        return False

    algo_error_code = algo_values['error_code']
    if algo_error_code is None or ((algo_error_code != 0) and (algo_error_code != -9)):
        return False

    return True


def trigger_dag(dag_name):
    gcp_project = settings.GCP_PROJECT_MANAGEMENT
    url = f'https://us-central1-{gcp_project}.cloudfunctions.net/trigger-airflow-dag'
    data = {"dag_name": dag_name}

    response = sess.post(url, json=data)
    return response


def _get_valuation_model_group(
        valuate_asset_request: ValuateAssetRequest,
        market: str
) -> Tuple[Optional[int], Optional[str]]:
    if valuate_asset_request.use_in_progress_model:
        return 'alpha', None
    if valuate_asset_request.use_prevailing_model:
        effective_date = get_prevailing_date(datetime.datetime.now(), market.upper())
    else:
        effective_date = valuate_asset_request.get_effective_date()

    effective_date = effective_date.strftime("%Y-%m")
    try:
        return effective_date_to_model_group_map[market.upper()][effective_date], None
    except KeyError:
        error = f"Requested to valuate using an unsupported date {effective_date} for market {market.upper()}"
        logger.debug(error)
        return None, error


def get_target_server(valuation_request):
    valuate_asset_request = ValuateAssetRequest.from_json(valuation_request)
    valuation_type = get_values_for_asset_request(valuate_asset_request)
    val_server_type = "alpha"

    if valuation_type in MODEL_GROUP_URLS.keys():
        val_server_type = valuation_type

    return MODEL_GROUP_URLS, val_server_type


def get_sqlite_metadata(locality):
    market = get_market_code_for_country_code(locality)
    model_locations = valuation_service_api.get_model_locations(market)

    keys = list(model_locations.keys())
    keys.sort()

    return {
        key: {
            "Date": f'{model_locations[key][0]["date"]} <-> {model_locations[key][-1]["date"]}',
            "Files": f'{model_locations[key][0]["id"]} - {model_locations[key][-1]["id"]}'
        }
        for key in keys
    }


def get_valuation_model_freshness():
    markets = get_markets()
    book_dict = {}
    for market in markets:
        book_dict[market] = False
        valuation_models = valuation_service_api.get_effective_date_to_valuation_model().get(market, None)
        if valuation_models is None:
            continue
        effective_date = get_current_prevailing_date(market)
        book_dict[market] = _get_valuation_model_freshness(effective_date, valuation_models)
    return book_dict


def _get_valuation_model_freshness(effective_date: datetime.date, valuation_models: dict):
    if type(effective_date) is not datetime.date:
        return False
    if type(valuation_models) is not dict:
        return False

    try:
        effective_date_str = effective_date.strftime("%Y-%m")
        valuation_model_for_effective_date = valuation_models.get(effective_date_str, None)
        if valuation_model_for_effective_date is not None:
            return True
        return False
    except Exception as e:
        logger.error(f"Error checking Valuation Model freshness: {e}")
        return False


def get_cpi_freshness():
    years = list(cpi_country_map.keys())
    current_date = datetime.date.today()
    return _check_cpi_current(current_date, years)


def _check_cpi_current(current_date: datetime.date, available_cpi_years: list):
    if type(current_date) is not datetime.date:
        return False
    if type(available_cpi_years) is not list:
        return False
    try:
        if (current_date.month == 1) and (current_date.day < 15):
            expected_cpi_year = str(current_date.year - 2)
        else:
            expected_cpi_year = str(current_date.year - 1)
        if expected_cpi_year in available_cpi_years:
            return True
        return False
    except Exception as e:
        logger.error(f"Error checking CPI freshness: {e}")
        return False

def parse_request(json_str: str) -> str:
    dict_data = json.loads(json_str)
    if "country" in dict_data:
        dict_data["country"] = _map_country_code(dict_data["country"])
    return json.dumps(dict_data)


def parse_assets_request(json_str: str) -> str:
    dict_data = json.loads(json_str)
    for item in dict_data.get('batch', []):
        if "country" in item:
            item["country"] = _map_country_code(item["country"])
    return json.dumps(dict_data)


def _map_country_code(country) -> str:
    iso_country = get_iso_country_code(country)
    if iso_country != country:
        logger.warning(f"Country {country} is not ISO compliant, using {iso_country} instead.")
    return iso_country if iso_country else country