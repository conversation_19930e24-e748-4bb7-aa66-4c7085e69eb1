
import os
import sys
from datetime import datetime
import datetime as dt

from google.cloud import bigquery
import pandas as pd
from loguru import logger

# NOTE: export LOG_LEVEL=DEBUG  <== for debug messages  (default is INFO)
log_level = os.getenv("LOG_LEVEL", "INFO")

# Set up the logger
logger.remove()  # Remove any previously added sinks
logger.add(sys.stdout, level=log_level)

PROJECT_ID = 'appraisals-data-prod-707493'

# NOTE: TMP FIX purposed by <PERSON> https://rouseservices.slack.com/archives/C08UEE3CVNE/p1748643251571879
# for removing duplicated Mascus data
QUERY = """
-- Sunil suggestions
-- 1) make_id NOT IN (58137, 78)   can you use the modelIDs. Because rare cases
--    there is make_id but not model_id. They believe the might have this in case in future
-- 2) AND NOT (item type = 1 AND saletype IN (export, etc…) )
--    Concern to have sales type under public data in the future 
SELECT

    -- val_compare, rouse_flv, rouse_fmv, rouse_wlv,  -- <= debug purpose n sanity check
    -- values are USD for USA and CAN and country mapped. For instance GBP for GBR (Based on Hugo: https://rouseservices.slack.com/archives/C0843BGH7UZ/p1733442617134569)
    CASE val_compare
      WHEN 'FLV' THEN rouse_flv   -- auctions
      WHEN 'FMV' THEN rouse_fmv   -- free market value (retail)
      WHEN 'WLV' THEN rouse_wlv   -- whosale
      ELSE NULL 
    END AS rouse_estimation,

    -- schedule rates 
    --fmv_sched_pct as schedule_rate_auction,
    --flv_sched_pct as schedule_rate_retail,
    
    -- ids 
    _version_id,        -- <= Hugo (Data Engineer) needs this to track the version of the data (mlops)
    item_type_id,
    seller,             -- what Sunill call clients. I client is a data provider 
    -- serial_number,
    -- category_id,
    equipment_type_id,
    rouse_unit_id as item_id,           -- change to standardize names 

    -- sales 
    sale_type, 
    sale_date as auc_end_time,

    -- taxonomy 
    category_name as category,
    subcategory_name as type,
    make_name as make,
    model_name as model,

    -- usage 
    model_year as mfg_year,
    meter_for_training as meter_reading,
    -- meter_expected, -- avg meter per subcategroy and year. Needs to doble check with Sunil
    meter_code_for_training as meter_units,
    
    -- location 
    continent as continent,
    sub_continent as subcontinent,
    region as item_region,          -- region and state will not be used. leaving for debbug or in the case we want to use it
    country_code as item_country,   -- Not used 
    state as item_state, 

    -- prices and currencies 
    -- FX in LOCAL_CURRENCY/USD. Ex: 0.78 GBP/USD => 1 USD = 0.78 GBP or 1 USD buys 0.78 GBP
    --                           Ex: 5.60 BRL/USD => 1 USD = 5.60 BRL or 1 USD buys 5.60 BRL
    rolling_exchange_rate as conversion_rate,   
    currency_code as currency_code,
    sale_price, -- price in local currency (for debugging)
    sale_price/rolling_exchange_rate as high_bid_usd, -- price in USD (converts local currency to USD)

    -- features
    source_classification as model_desc,
    source_description as item_features,    --  change to standardize names
    
    -- abcost 
    -- cost,
    -- cost_currency_code,
    -- acquisition_date,
    is_purchased_used,
    -- new_used,
    ab_cost_usna as abcost

FROM 
    `appraisals-data-prod-707493.data_science_models.transactions`

WHERE 
    category_id  NOT IN (220, 21, 31)   -- exclude Not Appraised (NO taxonomy n ABCost), Miscellaneous and Other Equipment
    AND make_id NOT IN (58137, 78)      -- exclude Not Attributed and Miscellaneous
    AND sale_type NOT IN ('Export','Rental Purchase Option','Other','Auction')      -- Keep Retail, Dealer=Whosale and Unreserved Auction
    AND (sale_date >='2013-01-01')
    AND sale_date < CURRENT_DATE()  -- Add this condition to prevent loading future dates (Bad data)
    -- FIX: removing only for Mascus for this 3 categories
    AND ( seller like '%Mascus%' AND category_name IN ('Articulating Boom Lifts', 'Scissor Lifts', 'Vertical Mast Lifts', 'Telescopic Boom Lifts'))

-- FIX: remove duplicated Mascus data
QUALIFY ROW_NUMBER() OVER (PARTITION BY country_code, seller, category_id,subcategory_id,make_id,model_id,model_year,sale_price,meter ORDER BY modified_date DESC) = 1

-- NOTE: All other sellers and categories, no de-duplication
UNION ALL

SELECT

    -- val_compare, rouse_flv, rouse_fmv, rouse_wlv,  -- <= debug purpose n sanity check
    -- values are USD for USA and CAN and country mapped. For instance GBP for GBR (Based on Hugo: https://rouseservices.slack.com/archives/C0843BGH7UZ/p1733442617134569)
    CASE val_compare
      WHEN 'FLV' THEN rouse_flv   -- auctions
      WHEN 'FMV' THEN rouse_fmv   -- free market value (retail)
      WHEN 'WLV' THEN rouse_wlv   -- whosale
      ELSE NULL 
    END AS rouse_estimation,

    -- schedule rates 
    --fmv_sched_pct as schedule_rate_auction,
    --flv_sched_pct as schedule_rate_retail,
    
    -- ids 
    _version_id,        -- <= Hugo (Data Engineer) needs this to track the version of the data (mlops)
    item_type_id,
    seller,             -- what Sunill call clients. I client is a data provider 
    -- serial_number,
    -- category_id,
    equipment_type_id,
    rouse_unit_id as item_id,           -- change to standardize names 

    -- sales 
    sale_type, 
    sale_date as auc_end_time,

    -- taxonomy 
    category_name as category,
    subcategory_name as type,
    make_name as make,
    model_name as model,

    -- usage 
    model_year as mfg_year,
    meter_for_training as meter_reading,
    -- meter_expected, -- avg meter per subcategroy and year. Needs to doble check with Sunil
    meter_code_for_training as meter_units,
    
    -- location 
    continent as continent,
    sub_continent as subcontinent,
    region as item_region,          -- region and state will not be used. leaving for debbug or in the case we want to use it
    country_code as item_country,   -- Not used 
    state as item_state, 

    -- prices and currencies 
    -- FX in LOCAL_CURRENCY/USD. Ex: 0.78 GBP/USD => 1 USD = 0.78 GBP or 1 USD buys 0.78 GBP
    --                           Ex: 5.60 BRL/USD => 1 USD = 5.60 BRL or 1 USD buys 5.60 BRL
    rolling_exchange_rate as conversion_rate,   
    currency_code as currency_code,
    sale_price, -- price in local currency (for debugging)
    sale_price/rolling_exchange_rate as high_bid_usd, -- price in USD (converts local currency to USD)

    -- features
    source_classification as model_desc,
    source_description as item_features,    --  change to standardize names
    
    -- abcost 
    -- cost,
    -- cost_currency_code,
    -- acquisition_date,
    is_purchased_used,
    -- new_used,
    ab_cost_usna as abcost

FROM 
    `appraisals-data-prod-707493.data_science_models.transactions`

WHERE 
    category_id  NOT IN (220, 21, 31)   -- exclude Not Appraised (NO taxonomy n ABCost), Miscellaneous and Other Equipment
    AND make_id NOT IN (58137, 78)      -- exclude Not Attributed and Miscellaneous
    AND sale_type NOT IN ('Export','Rental Purchase Option','Other','Auction')      -- Keep Retail, Dealer=Whosale and Unreserved Auction
    AND (sale_date >='2013-01-01')
    AND sale_date < CURRENT_DATE()  -- Add this condition to prevent loading future dates (Bad data)
    AND not ( seller like '%Mascus%' AND category_name IN ('Articulating Boom Lifts', 'Scissor Lifts', 'Vertical Mast Lifts', 'Telescopic Boom Lifts'))
"""

class Loader():

    def __init__(self):
         pass

    def load_data(self) -> pd.DataFrame:

        bigquery_client = bigquery.Client(project=PROJECT_ID)

        t1 = datetime.now()
        today_str = t1.strftime("%Y-%m-%d %H:%M:%S")

        logger.info(f"start loading raw data: {today_str}")

        raw_data = bigquery_client.query(QUERY).to_dataframe()
        t2 = datetime.now()
        today_str = t2.strftime("%Y-%m-%d %H:%M:%S")
        logger.info(f"finish loading: {today_str}")

        delta = t2 - t1
        minutes = delta.total_seconds() / 60
        logger.info(f"loading took: {minutes:.2f} minutes")

        if raw_data.shape[0] < 1_000_000:
            logger.warning("loaded data is small. Did you limit the query for dev or debug ? ==========")

        return raw_data

    def load_currency_cache(self,start_date:  dt.date, end_date: dt.date = None ) -> pd.DataFrame:

        assert type(start_date) == dt.date, "start_date must be a date object"
 
        assert type(end_date) == dt.date, "end_date must be a date object"

        currency_query = f"""
        SELECT 
            exchange_rate_date as auc_end_date,
            exchange_rate as conversion_rate,
            currency_code as currency_code
        FROM appraisals-data-prod-707493.exchange_rates.exchange_rates
        WHERE 
            exchange_rate_date < '{end_date}' -- to be safe and prevent bad data (date in future)
            AND exchange_rate_date > '{start_date}'
        ORDER BY currency_code DESC;
        """

        bigquery_client = bigquery.Client(project=PROJECT_ID)

        t1 = datetime.now()
        today_str = t1.strftime("%Y-%m-%d %H:%M:%S")

        logger.info(f"start loading currency cache: {today_str}")

        currency_cache = bigquery_client.query(currency_query).to_dataframe()
        t2 = datetime.now()
        today_str = t2.strftime("%Y-%m-%d %H:%M:%S")
        logger.info(f"finish loading: {today_str}")

        delta = t2 - t1
        minutes = delta.total_seconds() / 60
        logger.info(f"loading took: {minutes:.2f} minutes")

        logger.info(f"currency_cache cols: {currency_cache.columns.tolist()}")
        
        return currency_cache