#FIX: keras and tensorflow upgrade
import random
from typing import List, Any
import pandas as pd
import numpy as np

from sklearn.preprocessing import StandardScaler

import tensorflow as tf
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping
from tensorflow.keras.layers import Embedding, Dense, Input, Concatenate, Dropout, Activation, Reshape
from tensorflow.keras.models import Model, model_from_json
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.initializers import HeNorm<PERSON>
from tensorflow.keras.metrics import RootMeanSquaredError

from datetime import datetime as dt
from pathlib import Path
import time

from dataclasses import dataclass, field

from common.ds_cache import LocalObjectHandler, GCPObjectHandler
from common.encoder import Encoder
from common.lookback import LookBack

from loguru import logger
import os
import sys

# NOTE: export LOG_LEVEL=DEBUG  <== for debug messages  (default is INFO)
log_level = os.getenv("LOG_LEVEL", "INFO")

# Set up the logger
logger.remove()  # Remove any previously added sinks
logger.add(sys.stdout, level=log_level)

NUMERICAL_MISSING_VALUE = -1
_VERBOSE_LEVEL = 0

def make_default_encoded_categorical_vars():

    # TODO: Add 'encoded_currency_code' when start with multiple countries
    encoded_categorical_vars = ['encoded_category', 'encoded_sale_type', 
            'encoded_type', 'encoded_make', 
            'encoded_item_country', #'encoded_continent', 'encoded_subcontinent', # 'encoded_item_state', 
            'encoded_Item_ModelIndex0', 'encoded_Item_ModelIndex2', 'encoded_Item_ModelIndex4', 'encoded_Item_ModelIndex6', 
            'encoded_make_cat', 'encoded_make_type', 'encoded_currency_code',
            
            ]

    return encoded_categorical_vars

def make_default_numerical_vars():

    _numerical_vars = ['abcost', 'days_since_auc',
            'Item_ModelIndex1', 'Item_ModelIndex3', 'Item_ModelIndex5',
            'auc_end_year', 'auc_end_month', 'item_age_yrs', 'mfg_year', 'age_bin',
            'conversion_rate',
            'miles', 'hours']

    lookback = LookBack()
    _lookback_cols = lookback.lookback_cols
    
    return _numerical_vars + _lookback_cols

@dataclass
class EmbedderConfig():
    
    # NOTE: dataclasses needs to use default_factory to create default values for mutables values
    # Defaults values are used for Algolite only
    # TODO: rename this after training 1.14.2-conf-exp and 1.14.1-conf-exp encoded_categorical_cols
    encoded_categorical_vars: List[str] = field(default_factory=make_default_encoded_categorical_vars)
    numerical_vars: List[str] = field(default_factory=make_default_numerical_vars)

    path_to_model_weights: str = 'algolite/best_keras_model_weights.hdf5'

    epoch: int = 40
    batch_size: int = 128

    # NOTE: Embedder will fill these fields before saving
    scaler: Any = field(init=False,default=None)
    encoder: Any = field(init=False,default=None)
    model_as_json: str = field(init=False,default=None)  # NOTE: <= Embedder fill this field before saving. It is okay to be empty when loading

    categorical_vars: List[str] = field(init=False,default=None) # TODO: rename this after training 1.14.2-conf-exp and 1.14.1-conf-exp to numerical_cols 
    training_features: List[str] = field(init=False,default=None) # TODO: rename this after training 1.14.2-conf-exp and 1.14.1-conf-exp numerical_cols

    # NOTE: NOT in the input (init). Embedder will fill these fields during fitting
    embedded_cat_cols: List[str] = field(init=False,default=None) # TODO: rename this after training 1.14.2-conf-exp and 1.14.1-conf-exp embedded_cols

    def __post_init__(self):
        # NOTE: initialize the categorical_vars. needs to be done this way because dataclass do NOT acept to refer
        # a previous field. 
        
        self.categorical_vars = [col.replace('encoded_', '') for col in self.encoded_categorical_vars]

        # TODO: Review this latter to avoid dependent of the order of the columns
        # NOTE: The oder of the columns matters, because of self.preproc method. (<= NOT sure anymore)
        self.training_features =  self.encoded_categorical_vars + self.numerical_vars

class Embedder():

    def __init__(self, config: EmbedderConfig = None):
        
        self.config = config

        self.model = None
        self.extractor = None

        # Set seeds for reproducibility
        seed_value = 42  
        os.environ['PYTHONHASHSEED'] = str(seed_value)  
        random.seed(seed_value)         # Python random seed
        np.random.seed(seed_value)      # Numpy random seed
        tf.random.set_seed(seed_value)  # TensorFlow random seed
        tf.config.experimental.enable_op_determinism()  # Optional but helps with reproducibility

    def save(self, path_to_config: str, handler: LocalObjectHandler = LocalObjectHandler()) -> None:
        # NOTE: Cannot use joblib to save keras+tensorflow model

        assert self.config is not None, "Config is not set yet"
        assert self.model is not None, "Model is not fitted yet"
        assert self.extractor is not None, "Model is not fitted yet"

        assert self.config.categorical_vars is not None, "Config.categorical_vars is not set yet. Did you fit the model?"

        self.config.model_as_json = self.model.to_json()

        handler.save(self.config, path_to_config)

        if type(handler) == GCPObjectHandler:
            logger.info(f"Upload best model weight to {self.config.path_to_model_weights}")
            handler.upload(self.config.path_to_model_weights, self.config.path_to_model_weights)
        else:
            logger.info(f"fit saves best weights to {self.config.path_to_model_weights}")
            logger.debug(f"save: self.config.path_to_model_weights: {self.config.path_to_model_weights}")

    def load_config(self, path_to_config: str, handler: LocalObjectHandler = LocalObjectHandler()) -> None:

        assert self.config is None, "Config is already set. Do not overwrite it"
        assert self.model is None, "Model is already fitted. Do not overwrite it"
        assert self.extractor is None, "Model is already fitted. Do not overwrite it"

        config = handler.load(path_to_config)

        return config

    def load_dnn(self, model_as_json: str,  path_to_weights: str, handler: LocalObjectHandler = LocalObjectHandler()) -> None:

        # NOTE: Load dnn architecture from json file
        self.model = model_from_json(model_as_json)

        if type(handler) == GCPObjectHandler:
            # NOTE: Download model weights to local first
            
            Path('.cache').mkdir(parents=True, exist_ok=True)
            destination_weights = Path('.cache') / Path(path_to_weights).name

            # logger.warning(f"load_dnn: path_to_weights: {path_to_weights}")
            # logger.warning(f"load_dnn: destination_weights: {destination_weights}")

            Path('.cache').mkdir(parents=True, exist_ok=True)
            handler.download(path_to_weights, destination_weights)

            # NOTE: copy form cache to path_to_weights
            handler.copy(destination_weights, path_to_weights)

        # NOTE: load weights of the dnn
        self.model.load_weights(path_to_weights)
        logger.info("Loaded model from disk")

        # NOTE: Load embedding extractor
        self.extractor = self._get_extractor()

    def load(self, path_to_config: str, handler: LocalObjectHandler = LocalObjectHandler()) -> None:

        self.config = self.load_config(path_to_config, handler)

        logger.debug(f"load: self.config.path_to_model_weights: {self.config.path_to_model_weights}")

        self.load_dnn(self.config.model_as_json, self.config.path_to_model_weights, handler)

    # TODO: Rename this method later. Becareful for not conflic with preprocessing method
    # Maybe consider to rename the preprocess method instead
    def prepare_input_for_embedder_layer(self,X) -> List[np.ndarray]:

        X = X.astype('float32')
        
        input_list_train = []
        
        # NOTE: Pay attention. The order fo the categorical columns matter. 
        # Should be the same in training and prediciton time
        for i in range(len(self.config.encoded_categorical_vars)):
            ind = X.values[..., [i]]
            input_list_train.append(ind)
        
        # the rest of the columns
        if len(X.columns.tolist()) > len(self.config.encoded_categorical_vars):
            other_cols = [x for x in X.columns.tolist() if x not in self.config.encoded_categorical_vars]
            input_list_train.append(X[other_cols].values)
        
        logger.debug(f'len: {len(input_list_train)}')
        return input_list_train

    def build_embedding_network(self,cat_dict: dict,other_cols: List[str]) -> Model:

        # TODO: Change the DNN archictecturer to use he_initialzer
        # _initializer = HeNormal()
        _initializer = 'normal'

        # Build embedding layers
        model_out = []
        model_in  = []
        for key in cat_dict:

            input_dim = Input(shape=(1,), dtype='int64')
            no_of_unique_cat  = cat_dict[key]
            embedding_size = min(np.ceil((no_of_unique_cat)/2), 50 )
            embedding_size = int(embedding_size)
            embed_dim = Embedding(no_of_unique_cat, embedding_size, input_length=1)(input_dim)

            embed_dim = Reshape(target_shape=(embedding_size,))(embed_dim)
            model_out.append(embed_dim)
            model_in.append(input_dim)

        # Build the rest of the input layers
        main_input = Input(shape=(len(other_cols),), name='main_input')

        # Merge all embedding layers output
        cat_output = Concatenate()(model_out)

        # Merge embbeding output with numerical input layer
        outputs = Concatenate(axis=1)([cat_output, main_input])

        # Build the rest of layers that process both embedding and numerical input
        outputs = Dense(1000, kernel_initializer=_initializer)(outputs)
        outputs = Activation('relu')(outputs)
        outputs = Dropout(.25)(outputs)

        outputs = Dense(500,kernel_initializer=_initializer)(outputs)
        outputs = Activation('relu')(outputs)
        outputs = Dropout(.15)(outputs)

        outputs = Dense(128,kernel_initializer=_initializer)(outputs)
        outputs = Activation('relu')(outputs)
        outputs = (Dropout(.15))(outputs)
        outputs = Dense(64,kernel_initializer=_initializer)(outputs)
        outputs = Activation('relu')(outputs)
        outputs = (Dropout(.15))(outputs)

        outputs = Dense(1)(outputs)
        outputs = Activation('linear')(outputs)

        model = Model([*model_in, main_input], outputs)

        model.compile(
                loss="mean_absolute_error" , # "mean_squared_error" which one is better?
                optimizer="adam",#Adam(lr=0.001),
                metrics=[RootMeanSquaredError(),'mae','mape']
            )
        return model

    def _get_extractor(self):
        """returns a model that extracts the embeddings from the trained model"""
        # NOTE: Embeddings is the first concatenation layer
        merge_idx = [idx for idx, layer in enumerate(self.model.layers)
                         if 'Concatenate' in str(layer)][0]

        extractor = Model(inputs=self.model.input, outputs=self.model.layers[merge_idx].output)

        return extractor

    def _get_embedded_cols(self,X: pd.DataFrame) -> List[str]:

        embedded_cat_cols = []
        for col in self.config.encoded_categorical_vars:
            no_of_unique_cat = X[col].nunique()
            embedding_size = int(min(np.ceil((no_of_unique_cat) / 2), 50))

            col_renamed = col.replace('encoded_', 'ee_')
            col_list = [col_renamed + '_' + str(i) for i in range(int(embedding_size))]
            
            embedded_cat_cols.extend(col_list)

        return embedded_cat_cols

    # TODO: refactor this. I prefer fit(X,y)
    def fit(self,train_data: pd.DataFrame):
           
        assert 'encoded_sale_type' in self.config.encoded_categorical_vars, "Expect this col to train EE"
        assert 'encoded_category' in self.config.encoded_categorical_vars, "Expect this col to train EE"
        assert 'encoded_item_state' not in self.config.encoded_categorical_vars, "We decided to remove state and region from the model"
        assert 'encoded_region' not in self.config.encoded_categorical_vars, "We decided to remove state and region from the model"
        assert 'abcost' in self.config.numerical_vars, "Expect this col to train EE"

        #self.config.categorical_vars = [f"{col.replace('encoded_','')}" for col in self.config.encoded_categorical_vars]

        # NOTE: Make sure all categorical vars are in the dataframe
        assert all(c in train_data.columns for c in self.config.categorical_vars)

        # TODO: Review this latter to avoid dependent of the order of the columns
        # NOTE: The oder of the columns matters, because of self.preproc method.
        # self.config.training_features =  self.config.encoded_categorical_vars + self.config.numerical_vars

        self.config.numerical_vars = [col for col in self.config.training_features if col not in self.config.encoded_categorical_vars]

        # NOTE: The code assume all categorical vars are passed to the Embedding. 
        # There is no categorical var that is not passed to the Embedding ans later is used in the regressor 
        self.config.encoder = Encoder(categorical_cols=self.config.categorical_vars)

        # NOTE: making a copy because scaler overwrite the original columns.
        # And one of the requirements is to save the original columns for debug purpose
        encoded_data = self.config.encoder.fit_transform(train_data.copy())

        #encoded_data = self._preprocess(encoded_data)

        X = encoded_data[self.config.training_features]
        y = encoded_data['high_bid_usd'].to_numpy()

        # NOTE: encoder and EE layer are coupled by the code below
        self.config.embedded_cat_cols = self._get_embedded_cols(X)

        logger.info(f" ==== X.shape {X.shape}  ==== ")
        # TODO: check if standardized_cols are other vars are the same later.
        self.config.scaler = StandardScaler()
        X[self.config.numerical_vars] = self.config.scaler.fit_transform(X[self.config.numerical_vars])

        X_preproc = self.prepare_input_for_embedder_layer(X)

        cat_dict = {col:len(X[col].unique()) for col in self.config.encoded_categorical_vars}   
        dnn_model = self.build_embedding_network(cat_dict, self.config.numerical_vars)

        #print(dnn_model.summary())

        # NOTE: Save best model weights. It is responsible to save best_keras_model_weights.hdf5
        checkpoint = ModelCheckpoint(self.config.path_to_model_weights,
                                monitor='val_loss', 
                                verbose=_VERBOSE_LEVEL, save_best_only=True)
    
        es = EarlyStopping(monitor='val_loss', patience=20)
        callbacks_list = [checkpoint, es]

        # Start training DNN
        history = dnn_model.fit(X_preproc, y, 
                        epochs=self.config.epoch, batch_size=self.config.batch_size, 
                        verbose=_VERBOSE_LEVEL, callbacks=callbacks_list, validation_split=0.20, shuffle=True)
    
        self.model = dnn_model
        self.extractor = self._get_extractor()

        logger.info(f"Fit completed: {dt.now()}")

        return history.history

    def predict(self,X: pd.DataFrame) -> np.ndarray:

        # NOTE: This copy should be fine. predict is called in small input data. Batch predictions do not need to be fast
        X_encoded = self.config.encoder.transform(X.copy())

        # NOTE: filling missing values with NUMERICAL_MISSING_VALUE in other vars only
        #X_encoded = self._preprocess(X_encoded)

        X_encoded[self.config.numerical_vars] = self.config.scaler.transform(X_encoded[self.config.numerical_vars])
        X_preproc = self.prepare_input_for_embedder_layer(X_encoded[self.config.training_features])

        return self.model.predict(X_preproc)

    def predict_embedding(self,X: pd.DataFrame) -> pd.DataFrame:
       
        start=time.time()

        # NOTE: This copy should be fine. predict is called in small input data. Batch predictions do not need to be fast
        X_encoded = self.config.encoder.transform(X.copy())

        # NOTE: filling missing values with NUMERICAL_MISSING_VALUE in other vars only
        #X_encoded = self._preprocess(X_encoded)

        X_encoded[self.config.numerical_vars] = self.config.scaler.transform(X_encoded[self.config.numerical_vars])
        X_preproc = self.prepare_input_for_embedder_layer(X_encoded[self.config.training_features])
        logger.debug('got preprocessed data')

        embedding_layer_output = pd.DataFrame(self.extractor.predict(X_preproc, verbose=0))

        embedding_layer_output.columns = self.config.embedded_cat_cols

        # NOTE: Add entity embedding encoded_vars + numerical vars to get all features necessary for training regressors 
        embedding = pd.concat([embedding_layer_output.reset_index(drop=True),X[self.config.numerical_vars].reset_index(drop=True)],axis=1)
        end=time.time()
        logger.info(f'time taken to get embedding: {(end - start)/60.0:.2f} minutes')

        return embedding
    
    # TODO: Remove this later
    def encode_transform(self,X: pd.DataFrame) -> pd.DataFrame:
        """returns entity embbedings for each row in X"""
        start=time.time()

        # NOTE: This copy should be fine. predict is called in small input data. Batch predictions do not need to be fast
        X_encoded = self.config.encoder.transform(X.copy())

        # NOTE: filling missing values with NUMERICAL_MISSING_VALUE in other vars only
        X_encoded = self._preprocess(X_encoded)

        return X_encoded

