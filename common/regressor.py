import random
from typing import List, Any
import numpy as np
import pandas as pd
from sklearn.compose import TransformedTargetRegressor
from sklearn.model_selection import train_test_split
import lightgbm as lgb

from loguru import logger
import os
import sys

# NOTE: export LOG_LEVEL=DEBUG  <== for debug messages  (default is INFO)
log_level = os.getenv("LOG_LEVEL", "INFO")

# Set up the logger
logger.remove()  # Remove any previously added sinks
logger.add(sys.stdout, level=log_level)

_CONTINENT_TO_ENCODE = {
    'asia': 1,
    'oceania': 2,
    'europe': 5,
    'southamerica': 8,
    'northamerica': 9
}

_SUBCONTINENT_TO_ENCODE = {
    'southasia': 1,
    'oceania': 3,
    'europeft': 9,
    'eastasia': 2,
    'middleeast': 4,
    'southamerica': 16,
    'usacan': 14,
    'latinamerica': 15,
    'israel': 5,
    'russia': 11,
    'balkan': 10,
    'unitedkingdom': 8
}

def encode_continent(subcontinent, continent):
    return (
        _SUBCONTINENT_TO_ENCODE.get(subcontinent, NUMERICAL_MISSING_VALUE),
        _CONTINENT_TO_ENCODE.get(continent, NUMERICAL_MISSING_VALUE)
    )
vectorized_encode = np.vectorize(encode_continent)

NUMERICAL_MISSING_VALUE = -1
TEST_PCT = 0.05
_VERBOSE_LEVEL = -1

def create_regressor(algo: str, embedding_cols: List[str]):

    # Assumes all cols are used
    algolite_regressor = Regressor(embedding_cols)

    return algolite_regressor

class Regressor():

    def __init__(self, ab_training_features: List[str] = None):

        self.regressor_ab = None
        self.recover_regressor = None
        
        # NOTE: Values tuned by Vanessa on RB data. Assuming that is going to work for Rouse data
        self.params  = {"n_estimators":2000,
                        "random_state":1,
                        "subsample":1.0,
                        'max_depth': 8, 
                        'num_leaves': 40, 
                        'learning_rate': 0.090,
                        'min_child_weight':50, 
                        'colsample_bytree': 0.500,
                        'n_jobs':-1,
                        'determenistic': True,  # NOTE: added to make the model reproducible when trained same data
                        'verbose': _VERBOSE_LEVEL
                        }

        # NOTE: fill during fit. Now used only for debugging
        self.ab_training_features = ab_training_features # NOTE: Not used yet, assumed all cols in the input are used
        self.recover_training_features = []

        self._recover_categories = ['truck tractors', 'excavators']

        # NOTE: learning curves
        self.recovery_learning_curve = {}  
        self.ab_learning_curve_log = {}

        # Set seeds for reproducibility
        seed_value = 42  
        os.environ['PYTHONHASHSEED'] = str(seed_value)  
        random.seed(seed_value)         # Python random seed
        np.random.seed(seed_value)      # Numpy random seed

    def preprocess(self, X_ee: pd.DataFrame, lookback_data: pd.DataFrame) -> pd.DataFrame:

        assert X_ee.shape[0] == lookback_data.shape[0], "Make sure X_ee and lookback_data have the same number of rows"

        X_ee.reset_index(inplace=True)
        lookback_data.reset_index(inplace=True)

        # NOTE: replace label encoder by harcode encoder for continent and subcontinent
        # X_ee['encoded_continent'] = lookback_data['continent'].map(lambda s: _CONTINENT_TO_ENCODE.get(s,NUMERICAL_MISSING_VALUE))
        # X_ee['encoded_subcontinent'] = lookback_data['subcontinent'].map(lambda s: _SUBCONTINENT_TO_ENCODE.get(s,NUMERICAL_MISSING_VALUE))
        # NOTE: Still testing
        X_ee[['encoded_subcontinent', 'encoded_continent']] = np.array(
                                        vectorized_encode(lookback_data['subcontinent'], lookback_data['continent'])
                                    ).T 
        
        
        ab_training_features = X_ee.columns.tolist()

        return X_ee[ab_training_features] # X_ee[self.ab_training_features]

    def fit_ab(self, X_with_ee_train: pd.DataFrame, ab_y_train: pd.Series) -> None:

        assert 'abcost' in X_with_ee_train.columns, "Make sure abcost is in X_with_ee_train"    

        self.regressor_ab = lgb.LGBMRegressor(**self.params)

        logger.warning(f"Spliting data train and test for the ab regressor: {TEST_PCT:.2%}")
        ab_y_train_log = np.log1p(ab_y_train)
        X_train2, X_val, y_train_log, y_val_log = train_test_split(X_with_ee_train, ab_y_train_log, test_size=TEST_PCT, 
                                                            random_state=2024)


        self.regressor_ab.fit(
            X_train2, y_train_log, 
            eval_set=[(X_train2, y_train_log), (X_val, y_val_log)], 
            eval_metric='mae', # rmse
            callbacks=[lgb.record_evaluation(self.ab_learning_curve_log)] 
        )


    def fit_recover(self, X_with_ee_train: pd.DataFrame, recover_y_train: pd.Series) -> None:
        
        assert 'abcost' not in X_with_ee_train.columns, "Make sure abcost is not in X_with_ee_train"
        assert X_with_ee_train.shape[0] == recover_y_train.shape[0], "Make sure X_with_ee_train and recover_train have the same number of rows"
        
        self.recover_regressor = lgb.LGBMRegressor(**self.params)


        logger.warning(f"Spliting data train and test for the recover regressor: {TEST_PCT:.2%}")
        X_train2, X_val, recover_y_train2, recover_y_val = train_test_split(X_with_ee_train, recover_y_train, test_size=TEST_PCT, 
                                                            random_state=2025)
        
        self.recover_regressor.fit(
            X_train2, recover_y_train2, 
            eval_set=[(X_train2, recover_y_train2), (X_val, recover_y_val)],  
            eval_metric='mae',   # rmse
            callbacks=[lgb.record_evaluation(self.recovery_learning_curve)]
        )

    def fit(self, X_with_ee_train: pd.DataFrame, y_train: pd.Series) -> None:

        assert 'encoded_continent' in X_with_ee_train.columns, 'encoded continent is missing.Did you call self.preprocess?'
        assert 'encoded_subcontinent' in X_with_ee_train.columns, 'encoded subcontinent is missing.Did you call self.preprocess?'

        logger.info('fitting abcost')
        logger.info(f"==============> min abcost in training data: {X_with_ee_train.abcost.min()}")
        self.ab_training_features = X_with_ee_train.columns.tolist()
        self.fit_ab(X_with_ee_train, y_train)

        logger.info('fitting recover')
        X_with_ee_train_with_abcost = X_with_ee_train[X_with_ee_train.abcost > 0]
        y_train_with_abcost = y_train[X_with_ee_train.abcost > 0]

        logger.info(f"==============> min abcost in training data: {X_with_ee_train_with_abcost.abcost.min()}")
        recover_train = y_train_with_abcost/X_with_ee_train_with_abcost.abcost

        self.recover_training_features = [c for c in X_with_ee_train_with_abcost.columns if c not in ['abcost']]
        self.fit_recover(X_with_ee_train_with_abcost[self.recover_training_features], recover_train)

    def predict_ab(self, X_with_ee_test: pd.DataFrame) -> pd.Series:

        assert self.regressor_ab is not None, "Regressor is not trained"

        #X_with_ee_test.to_csv('X_with_ee_test.csv')

        y_log_pred = self.regressor_ab.predict(X_with_ee_test[self.ab_training_features])

        return np.expm1(y_log_pred)

    def predict_recover(self, X_with_ee_test: pd.DataFrame) -> pd.Series:
        
        assert self.recover_regressor is not None, "Regressor is not trained"
        assert 'abcost' not in self.recover_training_features, "Make sure abcost is not in X_with_ee_test"

        recover_pred = self.recover_regressor.predict(X_with_ee_test[self.recover_training_features])
        
        # NOTE: return recover = y/abcost and not sold price (y)
        return recover_pred

    def postprocessing(self, y_pred_ab: pd.Series, y_pred_recover_rate: pd.Series, **kwargs) -> pd.Series:

        abcost = kwargs.get('abcost', None)
        age_bin = kwargs.get('age_bin', None)
        category = kwargs.get('category', None)
        _type = kwargs.get('type', None)
        
        assert category is not None, "Make sure category is passed to posprocessing"
        assert _type is not None, "Make sure _type is passed to posprocessing"
        assert abcost is not None, "Make sure abcost is passed to posprocessing"
        assert age_bin is not None, "Make sure age_bin is passed to posprocessing"

        assert _type.shape[0] == category.shape[0] == abcost.shape[0] == age_bin.shape[0], "Make sure all inputs have the same number of rows"
        
        # NOTE: returning price predictions
        y_pred_recover = y_pred_recover_rate*abcost

        # TODO: Refactor the rules below later to make it more readable. can be simpler
        # rule7: modified rule6. 
        # rule7a_criteria = ((np.isin(category,['truck tractors']) & (abcost > 100_000)) | (abcost > 500_000)) & \
        #                     (y_pred_recover > 0.0)
        rule7a_criteria = (np.isin(category,['truck tractors']) & (abcost > 100_000)) | (abcost > 500_000)
        y_pred = np.where(rule7a_criteria, y_pred_recover, y_pred_ab)

        # make sure all mini excavators use ab model
        rule7b_criteria = (_type.str.contains('mini')) & (category == 'excavators')
        y_pred = np.where(rule7b_criteria, y_pred_ab, y_pred)

        # make sure all tractors and abcost missing to use ab cost 
        y_pred = np.where((np.isin(category,['tractors']) | (abcost < 0) ), y_pred_ab, y_pred)
        
        return y_pred

    def predict(self, X_with_ee_test: pd.DataFrame, category: pd.Series, type: pd.Series) -> pd.Series:

        assert X_with_ee_test.shape[0] == category.shape[0], "Make sure X_with_ee_test and category have the same number of rows"
        assert 'abcost' in X_with_ee_test.columns, "Make sure abcost is in X_with_ee_test"
        assert 'age_bin' in X_with_ee_test.columns, "Make sure age_bin is in X_with_ee_test" 
    

        y_pred_ab = self.predict_ab(X_with_ee_test)
        y_pred_recover = self.predict_recover(X_with_ee_test)

        y_pred = self.postprocessing(y_pred_ab, y_pred_recover,
                                    category=category,
                                    type=type,
                                    abcost=X_with_ee_test.abcost, 
                                    age_bin=X_with_ee_test.age_bin)

        return y_pred
