from typing import List, <PERSON><PERSON>
import numpy as np
import datetime as dt
import calendar

import pandas as pd

from loguru import logger
import os
import sys
import re
import semantic_version

# NOTE: export LOG_LEVEL=DEBUG  <== for debug messages  (default is INFO)
log_level = os.getenv("LOG_LEVEL", "INFO")

# Set up the logger
logger.remove()  # Remove any previously added sinks
logger.add(sys.stdout, level=log_level)

def get_enviroment_name(env: str) -> str:

    if env.startswith("prod/"):
        return "prod"
    elif env.startswith("dev"):
        return "dev"
    elif env.startswith("exp/"):
        return "exp"
    else:
        return "local"

def get_published_date() -> str:

    current_date = dt.datetime.now()
    first_day_of_current_month = current_date.replace(day=1)

    # Subtract one day from the first day of the current month
    last_day_of_previous_month = first_day_of_current_month - dt.timedelta(days=1)

    # Get the year and month for the last day of the previous month
    year = last_day_of_previous_month.year
    month = last_day_of_previous_month.month

    # Get the last day (number) of the previous month
    last_day = calendar.monthrange(year, month)[1]

    auc_end_time = f"{year}-{month:02d}-{last_day}"

    return auc_end_time

def check_env_compliance(env: str) -> None:
    """
    Check env compliance based on the given environment.

    :param env: The algorithm environment (e.g., "prod/", "1.0.0-dev", "1.0.0-exp")
    """

    if env.startswith("local"):
        return
    
    if env.startswith("exp/"):
        # Check if the experiment has 3 levels of subfolders
        # Ex: exp/ds_user_name/<experiment_name>
        subfolders = env.split("/")

        if len(subfolders) < 3:

            logger.info(f"subfolders: {subfolders}")
            logger.error(f"Invalid exp env: {env}. Valid exp env should match the pattern: exp/ds_user_name/<experiment_name>")

            return False

    # Check if env is in the right format
    if env.startswith("prod/"):

        # Ensure the env (which is TAG in this context) follows the prod/x.y.z pattern
        msg =  f"{env}\nError: When starting with 'prod/', TAG must follow the pattern prod/x.y.z"
        assert re.match(r'prod/\d+\.\d+\.\d+', env), msg
    
    else:

        # Ensure the env (which is TAG in this context) contains 'dev' or 'exp' when it's not 'prod'
        msg = f"{env}\nError: TAG must contain 'local' or 'dev' or 'exp' when env is not 'prod'. For example: local or 1.0.0-dev or 1.0.0-exp"
        assert "dev" in env or "exp" in env, msg

def check_tag_compliance_and_failed_if_not(env: str, tag: str) -> None:

    if env.startswith("prod/"):

        try:
            # TODO: test this later t make sure is working
            parsed_tag = semantic_version.Version(tag)
            logger.info(f"parsed_tag: {parsed_tag}")

        except ValueError:
            raise ValueError(f"Tag '{tag}' is not a valid semantic version")
    
        # Extract version numbers from 'env' and 'tag'
        env_version = env.split('/')[-1]
        assert env_version == tag, f"The version in 'env' ({env_version}) does not match the tag ({tag})"

    if env.startswith("dev"):

        if 'dev' not in tag:
            logger.error(f"Invalid tag pattern for environment {env}. Tag must contain 'dev'.Ex; 9.1.3-dev")
            sys.exit(1)
    
    if env.startswith("exp/"):

        if 'exp' not in tag:
            logger.error(f"Invalid tag pattern for environment {env}. Tag must contain 'exp'.Ex; 9.1.3-exp")
            sys.exit(1)

def protect_production_env(env: str) -> None:
    
    check_env_compliance(env)

    # Obtain the current hostname
    hostname = os.uname().nodename

    # Check if env contains "prod" and hostname is different from ds-vm-leandro2
    # msg = f"Environment {env} is protected and can't be accessed from hostname {hostname}"

    if env.startswith("prod/"):
        logger.warning(f"NOT IMPLEMENTED YET: Environment {env} is protected and can't be accessed from hostname {hostname}")
        # handler = GCPObjectHandler(root_path=env)
        # _check = handler.check_blob_exists('README.txt') 
        # assert _check == False, f"Environment (model): {env} alread exist. DO NOT OVERWRITE. Manually delete it first"

def sanity_check_data_is_sorted(df, column_name: str ='auc_end_date') -> None:

    is_sorted = df[column_name].is_monotonic_increasing

    assert is_sorted, "DataFrame is sorted in ascending order by 'auc_end_date'."

def input_sanity_check(raw_data: pd.DataFrame) -> None:

    assert 'abcost' in raw_data.columns
    assert 'conversion_rate' in raw_data.columns, 'conversion_rate is missing'
    assert 'currency_code' in raw_data.columns, 'currency_code is missing'
    assert 'item_country' in raw_data.columns, 'item_country is missing'
    assert 'continent' in raw_data.columns, 'continent is missing'
    assert 'subcontinent' in raw_data.columns, 'subcontinent is missing'
    assert '_version_id' in raw_data.columns, '_version_id is missing. Required of circle/ci (mlops)'

def preprocessed_sanity_check(preprocessed_data: pd.DataFrame) -> None:
        
    assert 'condition_rating' not in preprocessed_data.columns, 'condition_rating cannot be in preprocessed_data'
    assert 'continent' in preprocessed_data.columns, 'continent is missing'
    assert 'subcontinent' in preprocessed_data.columns, 'subcontinent is missing'
    assert '_version_id' not in preprocessed_data.columns, '_version_id is onlys used for circle/ci (mlops)'

    assert 'miles' in preprocessed_data.columns and 'hours' in preprocessed_data.columns, 'miles or hours is missing in preprocessed_data'	
    
    assert preprocessed_data['conversion_rate'].dtype == np.float64, "conversion_rate must be float"
    assert 'age_bin' in preprocessed_data.columns
    assert preprocessed_data['auc_end_date'].dtype == dt.date, f"auc_end_date must be date. Got: {preprocessed_data['auc_end_date'].dtype}"
        
def lookback_sanity_check(lookback_data) -> None:

    assert 'abcost' in lookback_data.columns
    assert 'miles' in lookback_data.columns or 'hours' in lookback_data.columns
    assert 'age_bin' in lookback_data.columns
    assert 'sale_type' in lookback_data.columns
    assert 'continent' in lookback_data.columns, 'continent is missing'
    assert '_version_id' not in lookback_data.columns, '_version_id is onlys used for circle/ci (mlops)'

    assert 'currency_code' in lookback_data.columns, 'currency_code is missing'
    assert 'item_region' not in lookback_data.columns, 'business decision to remove ths column'

    # ref: https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.arrays.IntegerArray.html#pandas-arrays-integerarray
    # https://stackoverflow.com/questions/68901882/pandas-cast-int64-capitalised-to-int64
    # They aren't the same type. an Int64 is a nullable array and is implemented with a shadow column that tells you whether a given cell 
    # should be pandas.NA. The problem with int64 is that if you have NaN values, the column type can change to float. If you don't have NaN,
    #  then int64 is the better choice
    # NOTE: In the api one of the units might be missing, so we need to check for both
    if 'hours' in lookback_data.columns:
        assert lookback_data['hours'].dtype == np.int64 or lookback_data['hours'].dtype == np.int32, f"hours must be int. Got: {lookback_data['hours'].dtype}"
    
    if 'miles' in lookback_data.columns:
        assert lookback_data['miles'].dtype == np.int64 or lookback_data['miles'].dtype == np.int32, f"miles must be int. Got: {lookback_data['miles'].dtype}"

    assert lookback_data['mfg_year'].dtype == np.int64 or lookback_data['mfg_year'].dtype == np.int32

    assert lookback_data['auc_end_date'].dtype == dt.date,  f"auc_end_date must be date. Got: {lookback_data['auc_end_date'].dtype}"

def embedder_sanity_check(embedder)-> None:

    assert 'miles' in embedder.config.numerical_vars, "We need this col"
    assert 'hours' in embedder.config.numerical_vars, "We need this col"
    assert 'abcost' in embedder.config.numerical_vars, "We need this col"
    assert 'age_bin' in embedder.config.numerical_vars, "We need this col"

    # NOTE: This features are hardcoded in the regressor preprocessing
    assert 'continent' not in embedder.config.categorical_vars  , "continent is required"
    assert 'subcontinent' not in embedder.config.categorical_vars  , "subcontinent is required"

    assert 'high_bid_usd' not in embedder.config.numerical_vars, "Prevent target leakage"
    assert 'sale_price' not in embedder.config.numerical_vars, "Prevent target leakage"
    assert 'rouse_estimation' not in embedder.config.numerical_vars, "Prevent target leakage"

    assert 'high_bid_usd' not in embedder.config.categorical_vars, "Prevent target leakage"
    assert 'rouse_estimation' not in embedder.config.categorical_vars, "Prevent target leakage"
    assert 'sale_price' not in embedder.config.categorical_vars, "Prevent target leakage"

    assert 'item_region' not in embedder.config.categorical_vars, 'business decision to remove this column'
    assert 'seller' not in embedder.config.encoded_categorical_vars , 'business decision to remove this column'

    # check for encoded_seller_0, encoded_seller_1, ....
    has_seller_col = any('seller' in col for col in embedder.config.encoded_categorical_vars)
    assert not has_seller_col, "No entity embedding seller was found in DataFrame"

def regressor_sanity_check(X_with_entity_embedding: pd.DataFrame) -> None:
        
        # NOTE: This features are hardcoded in the regressor preprocessing
        assert 'continent' not in X_with_entity_embedding.columns  , "continent is required"
        assert 'subcontinent' not in X_with_entity_embedding.columns  , "subcontinent is required"

        assert 'high_bid_usd' not in X_with_entity_embedding.columns, "Prevent target leakage"
        assert 'sale_price' not in X_with_entity_embedding.columns, "Prevent target leakage"
        assert 'rouse_estimation' not in X_with_entity_embedding.columns, "Prevent target leakage"

        assert 'item_region' not in X_with_entity_embedding.columns, 'business decision to remove ths column'
        
        assert 'high_bid_usd' not in X_with_entity_embedding.columns, "Prevent target leakage"

        # check for encoded_seller_0, encoded_seller_1, ....
        has_seller_col = any('seller' in col for col in X_with_entity_embedding.columns)
        assert not has_seller_col, "No entity embedding seller was found in DataFrame"

        # check for cols ids
        has_id_col = any('_id' in col for col in X_with_entity_embedding.columns)
        assert not has_id_col, "No +id column is allowed in DataFrame"

def base_and_quant_sanity_check(preprocessed_data: pd.DataFrame,algo: str = 'lite') -> None:
    assert 'auc_end_date' in preprocessed_data.columns, "auction end date is required"
    assert 'high_bid_usd' in preprocessed_data.columns, "required to base regressor"

    assert 'meter_reading' in preprocessed_data.columns, "fail preprocessing"
    assert 'make_type_mean_90_0' in preprocessed_data.columns, "lookback cols missing"

def confidence_sanity_check(postprocess_data: pd.DataFrame,algo: str = 'lite') -> None:
    assert 'predicted_lower' in postprocess_data.columns, "quantile regression failed"
    assert 'confidence_ratio' in postprocess_data.columns, "get features failed"
    assert 'score' in postprocess_data.columns, "score missing, no target for confidence regressor"
    
    assert 'type_age_hours' in postprocess_data.columns, "average hours is missing"
    assert 'type_mean_90_0' in postprocess_data.columns, "lookback cols is missing"
    assert 'abcost_exist' in postprocess_data.columns, "abcost_exist is missing"

def post_training_sanity_check(regressor, algo: str = 'lite') -> None:   
    assert 'high_bid_usd' not in regressor.ab_training_features, "Prevent target leakage"
    assert 'sale_price' not in regressor.ab_training_features, "Prevent target leakage"
    assert 'rouse_estimation' not in regressor.ab_training_features, "Prevent target leakage"

    assert 'abcost' in regressor.ab_training_features, "abcost is required"

    assert 'high_bid_usd' not in regressor.recover_training_features, "Prevent target leakage"
    assert 'sale_price' not in regressor.recover_training_features, "Prevent target leakage"
    assert 'rouse_estimation' not in regressor.recover_training_features, "Prevent target leakage"

    assert 'abcost' not in regressor.recover_training_features, "abcost is not allowed in this regressor"

def split_train_test_based_on_date(data: pd.DataFrame, validation_date: dt.date = None) -> Tuple[pd.DataFrame, pd.DataFrame]:

    assert 'auc_end_date' in data.columns, 'auc_end_date is missing'
    assert validation_date is not None, 'validation_date is missing'

    logger.debug(f"validation_date: {validation_date}")

    # NOTE: Split train and test
    train_period = data[data.auc_end_date <= validation_date]

    # NOTE: Prevent future date (bad data)
    # Get yesterday's date
    yesterday = (pd.Timestamp.now().normalize() - pd.Timedelta(days=1)).date()
    _validation_date = pd.Timestamp(validation_date).date()
    test_period = data[(data.auc_end_date > _validation_date) & (data.auc_end_date <= yesterday)]


    if test_period.shape[0] <= 1000:
        logger.warning(f"Small test set: {test_period.shape[0]}")

    return train_period, test_period

def take_sample(data: pd.DataFrame, _sample: int,  validation_date: dt.date = None ) -> pd.DataFrame:
    """
    Take a sample of the data
    validation_date: date to split train and test. If None, it will be 6 months ago 
    """

    _RANDOM_STATE = 11
    assert _sample <= data.shape[0], f"Sample size {_sample} is bigger than data size {data.shape[0]}"
    train_data, test_data = split_train_test_based_on_date(data, validation_date)

    _THR = 25
    if test_data.shape[0] < _THR:

        logger.warning(f"Small test set: {test_data.shape[0]} < {_THR}. Sampling only traininig data")
        sample_data = train_data.sample(_sample, replace=False,random_state=_RANDOM_STATE)
        logger.warning(f"Used raw sample (train: {sample_data.shape[0]} + test: 0) size: {sample_data.shape}")

        return sample_data


    if _sample > 0:

        # NOTE: 80% train and 20% test
        test_sample = int(_sample*0.2)
        train_sample = _sample - test_sample

        if test_data.shape[0] > 0 and test_sample/test_data.shape[0] > 1.25:
            logger.warning(f"test_sample: {test_sample} is bigger than test_data: {test_data.shape[0]}")
            
        # NOTE: sampling test with replacement to avoid taking a sample large than the original test set
        test_data = test_data.sample(test_sample,replace=True, random_state=_RANDOM_STATE)
        train_data = train_data.sample(train_sample, replace=False,random_state=_RANDOM_STATE)
    
    sample_data = pd.concat([train_data, test_data], axis=0)

    logger.warning(f"Used raw sample (train: {train_data.shape[0]} + test: {test_data.shape[0]}) size: {sample_data.shape}")

    return sample_data

def keep_core_assets(data: pd.DataFrame, cols_to_check: List[str] = ['category', 'type']) -> pd.DataFrame:
    
    core_data = data

    for c in cols_to_check:

        core_data = core_data[~(core_data[c].str.lower().str.contains('attachment|parts|tools|misc|accessor', na=False))]

    return core_data

def compute_sample_sizes_debug_cache(preprocessed_data: pd.DataFrame) -> pd.DataFrame:

    _group_cols = ['sale_type', 'category', 'type', 'make', 'model','mfg_year', 'item_country' ]
    for _c in _group_cols:
        assert _c in preprocessed_data.columns, f"col {_c} not in the input"
    
    assert 'auc_end_date' in preprocessed_data.columns, "auc_end_date not in the input"
    assert preprocessed_data['auc_end_date'].apply(lambda x: isinstance(x, dt.date)).all(), \
        "The 'auc_end_date' column should contain datetime.date objects"

    assert preprocessed_data['mfg_year'].apply(lambda x: isinstance(x, int)).all(), \
    "The 'mfg_year' column should contain integers only"

    # NOTE: all data
    sample_sizes_all = preprocessed_data.groupby(_group_cols).size().reset_index(name='ss_all')

    # NOTE: past 2 years
    last_2_years_date = preprocessed_data['auc_end_date'].max() - dt.timedelta(days=365*2)
    last_2year_data = preprocessed_data[preprocessed_data['auc_end_date'] >= last_2_years_date]
    sample_sizes_last_2_years = last_2year_data.groupby(_group_cols).size().reset_index(name='ss_2y')

    # NOTE: past 365 days
    last_365_days_date = preprocessed_data['auc_end_date'].max() - dt.timedelta(days=365)
    last_365day_data = preprocessed_data[preprocessed_data['auc_end_date'] >= last_365_days_date]
    sample_sizes_last_365 = last_365day_data.groupby(_group_cols).size().reset_index(name='ss_365')

    # NOTE: past 90 days
    last_90_days_date = preprocessed_data['auc_end_date'].max() - dt.timedelta(days=90)
    last_90day_data = preprocessed_data[preprocessed_data['auc_end_date'] >= last_90_days_date]
    sample_sizes_last_90 = last_90day_data.groupby(_group_cols).size().reset_index(name='ss_90')

    # NOTE: past 30 days
    last_30_days_date = preprocessed_data['auc_end_date'].max() - dt.timedelta(days=30)
    last_30day_data = preprocessed_data[preprocessed_data['auc_end_date'] >= last_30_days_date]
    sample_sizes_last_30 = last_30day_data.groupby(_group_cols).size().reset_index(name='ss_30')


    # NOTE: merge all data
    # sample_sizes = pd.merge(sample_sizes_all, sample_sizes_last_90, on=_group_cols, how='outer')
    sample_sizes = pd.merge(sample_sizes_all, sample_sizes_last_2_years, on=_group_cols, how='outer')
    sample_sizes = pd.merge(sample_sizes, sample_sizes_last_365, on=_group_cols, how='outer')
    sample_sizes = pd.merge(sample_sizes, sample_sizes_last_90, on=_group_cols, how='outer')
    sample_sizes = pd.merge(sample_sizes, sample_sizes_last_30, on=_group_cols, how='outer')
    sample_sizes = sample_sizes.fillna(0).astype({'ss_all': int, 
                                                  'ss_2y': int,
                                                  'ss_365': int,
                                                  'ss_90': int,
                                                  'ss_30': int})

    return sample_sizes

def compute_sold_price_debug_cache(preprocessed_data: pd.DataFrame) -> pd.DataFrame:
    
    _group_cols = ['item_country','sale_type', 'category', 'type', 'make', 'model', 'age_bin' ]

    for _c in _group_cols:
        assert _c in preprocessed_data.columns, f"col {_c} not in the input"

    assert 'auc_end_date' in preprocessed_data.columns, "auc_end_date not in the input"
    assert preprocessed_data['auc_end_date'].dtype == dt.date, f"auc_end_date must be date. Got: {preprocessed_data['auc_end_date'].dtype}"

    _2years_ago_date = pd.Timestamp.now().date() - pd.Timedelta(days=730)
    _365_days_ago_date = pd.Timestamp.now().date() - pd.Timedelta(days=365)
    _90_days_ago_date = pd.Timestamp.now().date() - pd.Timedelta(days=90)

    criteria = (preprocessed_data['auc_end_date'] >= _2years_ago_date) & (preprocessed_data['auc_end_date'] < _365_days_ago_date)
    data_1y_to_2y =  preprocessed_data[criteria].copy()
    criteria = (preprocessed_data['auc_end_date'] >= _365_days_ago_date) &  (preprocessed_data['auc_end_date'] < _90_days_ago_date)
    data_90d_to_1y = preprocessed_data[criteria].copy()
    data_past_90d = preprocessed_data[preprocessed_data['auc_end_date'] >= _90_days_ago_date].copy()

    sample_sizes_1y_to_2y = data_1y_to_2y.groupby(_group_cols).size().reset_index(name='sample_size_1y_to_2y')
    sample_sizes_90d_to_1y = data_90d_to_1y.groupby(_group_cols).size().reset_index(name='sample_size_90d_to_1y')
    sample_sizes_90d = data_past_90d.groupby(_group_cols).size().reset_index(name='sample_size_90d')

    avg_sold_price_1y_to_2y = data_1y_to_2y.groupby(_group_cols).agg(
            avg_sold_price_730=('high_bid_usd', 'mean')).reset_index()

    avg_sold_prices_90d_1y = data_90d_to_1y.groupby(_group_cols).agg(
            avg_sold_price_365=('high_bid_usd', 'mean')).reset_index()
    
    avg_sold_prices_90d = data_past_90d.groupby(_group_cols).agg(
            avg_sold_price_90=('high_bid_usd', 'mean')).reset_index()

    # NOTE: Merge all dataframes
    merged_data = sample_sizes_1y_to_2y.merge(
        sample_sizes_90d_to_1y,
        on=_group_cols,
        how='outer'
    ).merge(
        sample_sizes_90d,
        on=_group_cols,
        how='outer'
    ).merge(
        avg_sold_price_1y_to_2y,
        on=_group_cols,
        how='outer'
    ).merge(
        avg_sold_prices_90d_1y,
        on=_group_cols,
        how='outer'
    ).merge(
        avg_sold_prices_90d,
        on=_group_cols,
        how='outer'
    )
    
    # NOTE: Fill missing sample sizes with 0
    merged_data = merged_data.fillna(0).astype({'sample_size_1y_to_2y': int,
                                                'sample_size_90d_to_1y': int, 
                                                'sample_size_90d': int})
    
    merged_data = merged_data[_group_cols + ['sample_size_1y_to_2y','sample_size_90d_to_1y', 'sample_size_90d',
                                              'avg_sold_price_730','avg_sold_price_365', 'avg_sold_price_90']]

    return merged_data

# TODO: remove this later
def compute_sample_size_age_bin_cache(preprocessed_data: pd.DataFrame) -> pd.DataFrame:
    
    _group_cols = ['type', 'make', 'subcontinent', 'Item_ModelIndex0', 'Item_ModelIndex1', 'Item_ModelIndex2' ]

    for _c in _group_cols:
        assert _c in preprocessed_data.columns, f"col {_c} not in the input"

    assert 'age_bin' in preprocessed_data.columns, "age_bin not in the input"
    assert 'auc_end_date' in preprocessed_data.columns, "auc_end_date not in the input"
    assert preprocessed_data['auc_end_date'].dtype == dt.date, f"auc_end_date must be date. Got: {preprocessed_data['auc_end_date'].dtype}"

    last_365_days_date = pd.Timestamp.now().date() - pd.Timedelta(days=365)

    data_from_past_365_days = preprocessed_data[preprocessed_data['auc_end_date'] >= last_365_days_date].copy()

    sample_sizes_age_bin = data_from_past_365_days.groupby(_group_cols + ['age_bin']).size().reset_index(name='sample_size_age_bin')

    sample_sizes_age_bin_lower =  sample_sizes_age_bin.copy()
    sample_sizes_age_bin_lower['age_bin'] = sample_sizes_age_bin_lower['age_bin'].apply(lambda bin: bin + 1).astype(int)
    sample_sizes_age_bin_lower.rename(columns={'sample_size_age_bin': 'sample_size_age_bin_lower'}, inplace=True)
    sample_sizes_age_bin_lower.drop(columns=['sample_size_age_bin'], errors='ignore', inplace=True)

    sample_sizes_age_bin_upper = sample_sizes_age_bin.copy()
    sample_sizes_age_bin_upper['age_bin'] = sample_sizes_age_bin_upper['age_bin'].apply(lambda bin: bin - 1).astype(int)
    sample_sizes_age_bin_upper.rename(columns={'sample_size_age_bin': 'sample_size_age_bin_upper'}, inplace=True)
    sample_sizes_age_bin_upper.drop(columns=['sample_size_age_bin'], errors='ignore', inplace=True)

    # NOTE: merged in one dataframe
    _cols_lower = _group_cols + ['age_bin', 'sample_size_age_bin_lower']
    _cols_upper = _group_cols + ['age_bin', 'sample_size_age_bin_upper']
    merged_sample_sizes_cache = sample_sizes_age_bin.merge(
        sample_sizes_age_bin_lower[_cols_lower],
        on=_group_cols + ['age_bin'],
        how='left'
    ).merge(
        sample_sizes_age_bin_upper[_cols_upper],
        on=_group_cols + ['age_bin'],
        how='left'
    )

    # Fill missing sample sizes with 0
    merged_sample_sizes_cache = merged_sample_sizes_cache.fillna(0).astype({'sample_size_age_bin_lower': int, 
                                                                            'sample_size_age_bin_upper': int})

    # NOTE:  Ensure sample_size_age_bin_lower and sample_size_age_bin_upper are 0 for age_bin = -1
    merged_sample_sizes_cache.loc[merged_sample_sizes_cache['age_bin'] == -1, ['sample_size_age_bin_upper']] = 0
    merged_sample_sizes_cache.loc[merged_sample_sizes_cache['age_bin'] == 0, ['sample_size_age_bin_lower']] = 0

    del data_from_past_365_days
    del sample_sizes_age_bin_upper
    del sample_sizes_age_bin_lower

    # NOTE: concat the group columns to create a new column
    merged_sample_sizes_cache['group_key'] = merged_sample_sizes_cache[_group_cols + ['age_bin']].fillna('').astype(str).agg('_'.join, axis=1)

    # NOTE: re order cols
    merged_sample_sizes_cache = merged_sample_sizes_cache[['group_key'] + _group_cols + ['age_bin', 'sample_size_age_bin_lower', 'sample_size_age_bin', 'sample_size_age_bin_upper']]

    return merged_sample_sizes_cache