import os
import random
import sys

import re
from typing import List
import numpy as np
import pandas as pd
import datetime as dt
import warnings

warnings.filterwarnings("ignore", message="DataFrame is highly fragmented.")

from pandarallel import pandarallel
pandarallel.initialize()

from loguru import logger

from common.utils import keep_core_assets

# NOTE: export LOG_LEVEL=DEBUG  <== for debug messages  (default is INFO)
log_level = os.getenv("LOG_LEVEL", "INFO")

# Set up the logger
logger.remove()  # Remove any previously added sinks
logger.add(sys.stdout, level=log_level)

NUMERICAL_MISSING_VALUE = -1
STR_UNKNOWN_VALUE = 'unknown'  # misisng value for categorical variables

pd.options.mode.chained_assignment = None  # default='warn' 

# NOTE: Select apply functions in parralell or not. Some OS might not support pandarallel
class Applier():

    def __init__(self, config = 'parallel'):
        self.config = config

    def apply_function(self, series: pd.Series,fn) -> pd.Series:

        fn_lambda = lambda x: fn(x)

        if self.config == 'parallel':

            return series.parallel_apply(fn_lambda)
        else:
            return series.apply(fn_lambda)


def _replace_multiple(mainString, toBeReplaces, newString):

    # TODO: refactor. probably avoid the for loop and use regex args might be faster. But a much less line of code.
    # Iterate over the strings to be replaced
    for elem in toBeReplaces:
        # Check if string is in the main string
        if elem in mainString:
            # Replace the string
            mainString = mainString.replace(elem, newString)

    return mainString

class Preprocessor():

    def __init__(self, config):
        
        self.config = config
        self._usage_vars = ['miles', 'hours']

        self.numerical_cols =  ['mfg_year', 'conversion_rate', 'meter_reading','abcost'] 
        self.text_cols = ['sale_type', 'make', 'model', 'category', 'type', 'item_country']

        # Set seeds for reproducibility
        seed_value = 42  
        os.environ['PYTHONHASHSEED'] = str(seed_value)  
        random.seed(seed_value)         # Python random seed
        np.random.seed(seed_value)      # Numpy random seed
    
    def _normalize_unknown(self, data: pd.DataFrame) -> pd.DataFrame:

        unknown_equiv = ['Unk',' ','','Not Available','See Report','Not Applicable'
        'Unknown Manufacturer','Unknown Asset Model','Unknown Asset Primary Industry',
        'Unknown Asset Category Group','Unknown Asset Group Name']
        unknown_equiv = unknown_equiv + [x.lower() for x in unknown_equiv] + [x.upper() for x in unknown_equiv]
        
        other_equiv = ['Other','Misc. Other']
        other_equiv = other_equiv + [x.lower() for x in other_equiv] + [x.upper() for x in other_equiv]
        
        for col_ in data.columns[(data.dtypes == object)]:
            
            data.loc[data[col_].isin(unknown_equiv), col_] = STR_UNKNOWN_VALUE
            data.loc[data[col_].isin(other_equiv), col_] = 'Other'
        
        return data

    def _convert_to_numeric_and_fill_missing(self, data: pd.DataFrame, numerical_cols: list) -> pd.DataFrame:
        
        for _col in numerical_cols:
            data[_col] = pd.to_numeric(data[_col], errors='coerce').fillna(NUMERICAL_MISSING_VALUE).astype(float)

        return data

    def _prepare_usage_cols(self, data: pd.DataFrame, usage_vars: list) -> pd.DataFrame:

        logger.info("prepare rouse data usage cols")

        # Check if meter cols are numerical (float or int)
        assert pd.api.types.is_numeric_dtype(data['meter_reading']), "meter_reading is not numerical. See _convert_to_numeric_and_fill_missing?"

        _unique_meter_units = data.meter_units.unique()
        assert 'K' not in _unique_meter_units, "K should have been removed already in meter for training"
        assert 'M' in _unique_meter_units or 'H' in _unique_meter_units, "M or H should be present in meter for training"

        # TODO: Do not convert K, M, H and missing. It is rouse standart. This remove preprocessing steps and line of codes
        # Also we can enforce (assert) to pass this values during the prediction time
        # TODO: Do it later. need to replace all references to miles and hours in mulitple files
        mu_mapping = {'K': 'kms', 'M': 'miles', 'H': 'hours',
             'kms': 'kms', 'miles': 'miles', 'hours': 'hours' ## <== in the case you ran twice in a row 
             }

        data['meter_units'] = data.meter_units.map(lambda mu: mu_mapping.get(mu,STR_UNKNOWN_VALUE))

        data.meter_reading.fillna(NUMERICAL_MISSING_VALUE,inplace=True)

        # NOTE: prediciton time meter reading can be negative. Find this values on public cache. We need to fill with -1
        # They said they are fixing but it is better make sure in your code as well.
        data['meter_reading']  = np.where(data['meter_reading'] > -0.01, data['meter_reading'], NUMERICAL_MISSING_VALUE)

        # usa_vars = ['miles',  'hours']      
        for col in usage_vars:
            data[col] = np.where(data['meter_units'] == col, data['meter_reading'], NUMERICAL_MISSING_VALUE)
  
        data[['miles', 'hours']] = data[['miles', 'hours']].astype('int')

        return data

    def _prepare_time_cols(self, data: pd.DataFrame, curr_date: pd.to_datetime) -> pd.DataFrame:

        data.auc_end_time=pd.to_datetime(data.auc_end_time, errors = 'coerce')
        # ignore timezones
        data['auc_end_time'] = data['auc_end_time'].dt.tz_localize(None)
        data['auc_end_date'] = pd.to_datetime(data.auc_end_time, errors = 'coerce').dt.date
        
        # NOTE: Prediction time is not necessary to set to zero because in prod we freeze public date (auc_end_time)
        data['days_since_auc'] = (curr_date - data.auc_end_time).dt.days

        # Date cols processing
        data['auc_end_year'] = pd.DatetimeIndex(data['auc_end_date']).year
        data['auc_end_month'] = pd.DatetimeIndex(data['auc_end_date']).month
        data['auc_end_quarter'] = pd.DatetimeIndex(data['auc_end_date']).quarter

        # TODO: review. Potential refactor to save cpu time. Why create auc_year and drop it later? Why not map also fills missings?
        # We just need _get_item_age_years()
        # fixing item_age_years
        curr_year = curr_date.year
        # NOTE: we do not have auc_end_year in future data. We need to use curr_year.
        # TODO: This should be safe to remove it. 
        data['auc_year'] = data.auc_end_year.map(lambda x: min(x, curr_year))

        data['mfg_year'] = data['mfg_year'].fillna(NUMERICAL_MISSING_VALUE).astype('int')

        _n_mfg_year_missings = data[data['mfg_year'] <= 0].shape[0]
        if _n_mfg_year_missings > 0:
            logger.warning(f"Removing sample with missing on model year: {_n_mfg_year_missings}({_n_mfg_year_missings/data.shape[0]:.2%}). Prediction assumes no missing in mfg_year") 
            data = data[data['mfg_year'] > 0]

        assert (data['mfg_year'] <= 0).sum() == 0 and data['mfg_year'].isna().sum() == 0, "Code expect no missing in mfg_year"
        assert (data['auc_end_year'] <= 0).sum() == 0 and data['auc_end_year'].isna().sum() == 0, "Code expect no missing in auc_end_year"

        # NOTE: define base year for item_age_yrs to handle models 2023/2024.
        data['auc_year'] = data.apply(lambda r: max(r['auc_end_year'], r['mfg_year']),axis=1)
        data['auc_year'] = data['auc_year'].astype('int')

        # TODO: Review this code. Since there is no missing in mfg_year, we do not need to use np.where
        # The code might be lanbeling item_age_yrs with -1 for cur_year + 1 and due to missings in mfg_year
        # options: a) age + 1, b) age missings is -2 c) do nothing there are not missings in mfg_year
        # d) curr_year + 1 and curr_year  age = 0 (no differnece btw curr_year and auc_year)
        data['item_age_yrs'] = data.auc_year - data['mfg_year']

        data = data.drop(columns=['auc_year'])
        
        data.loc[data['mfg_year'] <= 1900,'item_age_yrs'] = NUMERICAL_MISSING_VALUE 
        data.loc[data['mfg_year'] <= 1900,'mfg_year'] = NUMERICAL_MISSING_VALUE

        # NOTE: auc_year NAN generates NAN in item_age_yrs. the line below fix it
        data['item_age_yrs'] = data['item_age_yrs'].fillna(NUMERICAL_MISSING_VALUE)

        _max_age_thr = 120
        assert data.item_age_yrs.max() < _max_age_thr, f"item_age_yrs max value is too high: {data.item_age_yrs.max()}" 
        
        too_old = data[data['item_age_yrs'] > _max_age_thr].shape[0]
        if too_old>0:
            logger.warning(f'There are very old items: {too_old}({too_old/data.shape[0]:.2%}) suspicious data')
            #data[data['item_age_yrs'] > _max_age_thr].to_csv('bad_age.csv', index=False)
        
        def _get_age_bin(x):
            if x <= 3: # NOTE: cases -1,0,1,2,3
                return x
            if x <= 5:
                return 4
            if x <= 8:
                return 5
            if x <= 12:
                return 6
            if x <= 16:
                return 7
            if x <= 20:
                return 8
            return 9 # <= cases older than 20 years and np.nan 
                
        data['age_bin'] = data.item_age_yrs.map(lambda x: _get_age_bin(x))

        return data

    def _prepare_model_cols(self, data: pd.DataFrame, applier: Applier = Applier()) -> pd.DataFrame:
        
        logger.info("EXECUTING parallel_apply to clean model (_replace_multiple)")

        set_replace_multiple = lambda x: _replace_multiple(str(x), [' ', '-', '/', '(', ')', '.'], '')
        data['model'] = applier.apply_function(data['model'], set_replace_multiple)

        logger.info('split model field')
        data['Item_ModelIndex0'] = data['model'].apply(lambda x: self.get_sub_string_model(x, 0))
        data['Item_ModelIndex0'] = data['Item_ModelIndex0'].apply(lambda x: 'NOT' if x == '' else x)
        
        data['Item_ModelIndex1'] = data['model'].apply(lambda x: self.get_sub_string_model(x, 1))
        data['Item_ModelIndex1'] = data['Item_ModelIndex1'].apply(lambda x: '0' if x == '' else x)
        data['Item_ModelIndex1'] = pd.to_numeric(data['Item_ModelIndex1'] ,errors='coerce')

        data['Item_ModelIndex2'] = data['model'].apply(lambda x: self.get_sub_string_model(x, 2))
        data['Item_ModelIndex2'] = data['Item_ModelIndex2'].apply(lambda x: 'NOT' if x == '' else x)

        data['Item_ModelIndex3'] = data['model'].apply(lambda x: self.get_sub_string_model(x, 3))
        data['Item_ModelIndex3'] = data['Item_ModelIndex3'].apply(lambda x: '0' if x == '' else x)
        data['Item_ModelIndex3'] = pd.to_numeric(data['Item_ModelIndex3'],errors='coerce')

        data['Item_ModelIndex4'] = data['model'].apply(lambda x: self.get_sub_string_model(x, 4))
        data['Item_ModelIndex4'] = data['Item_ModelIndex4'].apply(lambda x: 'NOT' if x == '' else x)

        data['Item_ModelIndex5'] = data['model'].apply(lambda x: self.get_sub_string_model(x, 5))
        data['Item_ModelIndex5'] = data['Item_ModelIndex5'].apply(lambda x: '0' if x == '' else x)
        data['Item_ModelIndex5'] = pd.to_numeric(data['Item_ModelIndex5'],errors='coerce')

        data['Item_ModelIndex6'] = data['model'].apply(lambda x: self.get_sub_string_model(x, 6))
        data['Item_ModelIndex6'] = data['Item_ModelIndex6'].apply(lambda x: 'NOT' if x == '' else x)
        logger.info('model split is ready')

        return data

    def _remove_unknow_taxonomy(self, data: pd.DataFrame, tax_cols: str) -> pd.DataFrame:

        n_orig = data.shape[0]

        for col in tax_cols:

            if col in data.columns.tolist() and STR_UNKNOWN_VALUE in data[col].unique():
            
                n_unknown = data[data[col] == STR_UNKNOWN_VALUE].shape[0]
                logger.warning(f"Removing {n_unknown}({n_unknown/n_orig:.2%}) unknown values in {col}")
                data = data[data[col] != STR_UNKNOWN_VALUE]

        return data

    def _prepare_taxonomy_cols(self, data: pd.DataFrame, applier: Applier = Applier()) -> pd.DataFrame:

        assert 'age_bin' in data.columns, 'age_bin column is not present in the data'
        assert 'make' in data.columns, 'make column is not present in the data'
        assert 'model' in data.columns, 'model column is not present in the data'

        _tax_cols_with_no_allowed_missings = ['item_country','category','type', 'sale_type']
        data = self._remove_unknow_taxonomy(data, _tax_cols_with_no_allowed_missings)

        #adding logic to change make and model to unknown if it contains unknown 
        # or null or empty string
        data['make'] = data['make'].replace(to_replace='', value=STR_UNKNOWN_VALUE)
        data['make'] = data['make'].fillna(STR_UNKNOWN_VALUE)
        data['make'] = data['make'].str.lower()
        data['make'] =np.where(data['make'].str.contains(STR_UNKNOWN_VALUE),STR_UNKNOWN_VALUE,data['make'])
        data['model'] = data['model'].replace(to_replace='', value=STR_UNKNOWN_VALUE)
        data['model'] = data['model'].fillna(STR_UNKNOWN_VALUE)
        data['model'] = data['model'].str.lower()
        data['model'] = np.where(data['model'].str.contains(STR_UNKNOWN_VALUE), STR_UNKNOWN_VALUE, data['model'])
        
        data = self._prepare_model_cols(data,applier)
        assert 'Item_ModelIndex0' in data.columns, 'Item_ModelIndex0 column is not present in the data'

        # TODO: Review this. Might not make sense for rouse data
        # NOTE: labeling unknowns models (missing values) as 1 and known models as 0
        set_unknowns = lambda x: STR_UNKNOWN_VALUE if str(x) == '' or str(x) == 'None' else x
        data['model_unknown'] = applier.apply_function(data['model'], set_unknowns)
        data['model_unknown'] = np.where(data.model_unknown.str.contains(STR_UNKNOWN_VALUE), 1, 0)

        return data

    def _normalize_text_cols(self, data: pd.DataFrame, text_cols: List[str]) -> pd.DataFrame:

        text_cols_lower = data[text_cols].apply(lambda x: x.str.lower())
        text_cols_stripped = text_cols_lower.apply(lambda x: x.str.strip())
        data[text_cols] = text_cols_stripped

        return data

    def _remove_suspicious_truck_tractors(self, data: pd.DataFrame) -> pd.DataFrame:
        
        truck_tractors = data[data['category'] == 'truck tractors']
        n_truck_tractors = truck_tractors.shape[0]

        recover = np.where(data['abcost'] > 0.0, data['high_bid_usd']/data['abcost'],1.00)

        recover_threshold = 0.03
        is_suspicious_truc_tractors = (recover < recover_threshold) & (data['category'] == 'truck tractors')
        _n_remmoved_truck_tractors = data[is_suspicious_truc_tractors].shape[0]
        logger.warning(f"Remove suspicious truck tractors: {_n_remmoved_truck_tractors} ({_n_remmoved_truck_tractors/n_truck_tractors:.2%}  of truck tractors)")
        data = data[recover > recover_threshold]

        return data

    def _prepare_abcost_cols(self, data: pd.DataFrame, is_training = True) -> pd.DataFrame:

        n_orig = data.shape[0]

        # NOTE: suspicious abcost values. Since we are filtering sold prices > 1k
        _suspicious_thr = 800
        n_suspicious = data[data['abcost'] < _suspicious_thr].shape[0]
        if n_suspicious > 0:
            logger.warning(f"Labelling suspsicious abcost values as missings (< {_suspicious_thr}) as missing: {n_suspicious}({n_suspicious/n_orig:.2%})")
            data['abcost'] = np.where(data['abcost'] < _suspicious_thr,NUMERICAL_MISSING_VALUE , data.abcost)
    
        if is_training:
            #recover = data.apply(lambda r: r['high_bid_usd']/r['abcost'] if r['abcost'] > 0.0 else -1, axis=1)
            recover = np.where(data['abcost'] > 0.0, data['high_bid_usd']/data['abcost'],NUMERICAL_MISSING_VALUE)
            
            # NOTE: be careful. this can remove a lot of parts and tools.
            recover_threshold = 1.8
            _n_removed = data[recover > recover_threshold].shape[0]
            logger.warning(f"Removing large recover values (suspicious abcost values > {recover_threshold}): {_n_removed} ({_n_removed/n_orig:.2%})")
            data = data[recover <= recover_threshold]

        return data

    def _prepare_location_cols(self, data: pd.DataFrame) -> pd.DataFrame:

        # NOTE: https://rouseservices.atlassian.net/browse/VALS-321
        assert 'continent' in data.columns, 'continent column is required'
        assert 'subcontinent' in data.columns, 'subcontinent column is required'
        data['continent'] = data['continent'].str.lower().str.strip()
        data['subcontinent'] = data['subcontinent'].str.lower().str.strip()

        # drop region and state
        if 'item_region' in data.columns:
            data = data.drop(['item_region'], axis=1)

        # NOTE: Sunil requested to test models without state
        if 'item_state' in data.columns:
            data = data.drop(['item_state'], axis=1)

        return data

    def get_sub_string_model(self,assetModel: str, subStringIndex: int):
        match = re.match(r"([a-z]*)([0-9]*)([a-z]*)([0-9]*)([a-z]*)([0-9]*)([a-z]*)", assetModel, re.I)
        
        if match:
            items = match.groups()
            return items[subStringIndex]

        return assetModel

    def keep_core_assets(self,data: pd.DataFrame, cols_to_check: List[str] = ['category', 'type']) -> pd.DataFrame:
        
        logger.info('keeping core assets only ...')
        core_data = keep_core_assets(data, cols_to_check=cols_to_check)
        return core_data

    def preprocess(self, raw_data: pd.DataFrame,applier: Applier = Applier()) -> pd.DataFrame:
        
        logger.info('Preprocessing fitting ... ')
        
        assert 'miles' not in raw_data.columns, "Expect not have this cols. Will duplicated cols with same name"
        assert 'hours' not in raw_data.columns, "Expect not have this cols. Will duplicated cols with same name"

        # NOTE: Drop the '_version_id' column from the preprocessed_data DataFrame view.
        # To avoid make a copy of large data raw_data and keep the original col _version_id on raw_data
        preprocessed_data = raw_data.loc[:, ~raw_data.columns.isin(['_version_id'])]

        _numerical_cols = self.numerical_cols + ['high_bid_usd']
        preprocessed_data = self._convert_to_numeric_and_fill_missing(preprocessed_data, numerical_cols=_numerical_cols)

        # NOTE: lower cases and trim spaces at the beginning and end of the string
        _text_cols = self.text_cols 
        preprocessed_data = self._normalize_text_cols(preprocessed_data, text_cols=_text_cols)

        # NOTE: remove cheaper assets and high_bid_usd missing
        # This is exclude a lot of bad data, suspicious sold prices with recovery < 1% or recovery > 2.00
        min_sold_price = 1_500

        n_orig = preprocessed_data.shape[0]
        n_cheap_assets = preprocessed_data[preprocessed_data['high_bid_usd'] < min_sold_price].shape[0]
        logger.warning(f"Removing low value assets (< {min_sold_price}): {n_cheap_assets}({n_cheap_assets/n_orig:.2%})") 
        preprocessed_data = preprocessed_data[preprocessed_data['high_bid_usd'] > min_sold_price]

        preprocessed_data = self._prepare_abcost_cols(preprocessed_data)

        preprocessed_data = self._remove_suspicious_truck_tractors(preprocessed_data)

        # NOTE: ignore timezones
        curr_date = pd.to_datetime('today').tz_localize(None)
        preprocessed_data = self._prepare_time_cols(preprocessed_data, curr_date=curr_date)

        assert 'age_bin' in preprocessed_data.columns, "age_bin column cannot be missing"

        # NOTE: Work with ~10 years of data to reduce memory usage.
        # TODO: work with the last 11 years * 12 months = 132 months
        cut_date = dt.date(2012,1,1)
        logger.warning(f"Removing {preprocessed_data[preprocessed_data['auc_end_date'] < cut_date].shape[0]} older records than {cut_date.year}")
        logger.warning(f"New raw data size {preprocessed_data[preprocessed_data['auc_end_date'] >= cut_date].shape[0]}")
        preprocessed_data = preprocessed_data[preprocessed_data['auc_end_date'] >= cut_date]

        preprocessed_data = self._normalize_unknown(preprocessed_data)

        preprocessed_data = self._prepare_usage_cols(preprocessed_data, usage_vars=self._usage_vars)

        assert 'miles' in preprocessed_data.columns or  'hours' in preprocessed_data.columns, "miles or hours column canot be missing"
        assert 'kms' not in preprocessed_data.columns, "kms column should have removed already"

        preprocessed_data = self._prepare_taxonomy_cols(preprocessed_data,applier)
        preprocessed_data = self._prepare_location_cols(preprocessed_data)
        
        logger.warning(f"training data size: {preprocessed_data.shape[0]}")

        return preprocessed_data

    def fit_transform(self, raw_data: pd.DataFrame,applier: Applier = Applier()) -> pd.DataFrame:

        logger.info("fitting and transforming ... ")
        preprocessed_data = self.preprocess(raw_data,applier)

        return preprocessed_data
    
    def transform(self, raw_data: pd.DataFrame, days_sinc_auc: int = 0, applier: Applier = Applier()) -> pd.DataFrame:

        logger.info('Preprocessing transforming ... ')
        
        assert 'miles' not in raw_data.columns, "Expect not have this cols. Will duplicated cols with same name"
        assert 'hours' not in raw_data.columns, "Expect not have this cols. Will duplicated cols with same name"
        
        raw_data = self._convert_to_numeric_and_fill_missing(raw_data, numerical_cols=self.numerical_cols)
        assert raw_data[raw_data['mfg_year'] <= 0].shape[0] == 0, "mfg_year missing values. Prediction code assume no missing in mfg_year"

        raw_data = self._normalize_text_cols(raw_data, text_cols=self.text_cols)

        raw_data = self._prepare_abcost_cols(raw_data, is_training=False)

        curr_date = pd.to_datetime('today')
        raw_data = self._prepare_time_cols(raw_data, curr_date=curr_date)

        # NOTE: Bug fixed when mfg_year is missing. item_age_yrs 2023 + 1 = 2024. Leave for now to make sure we do not break anything 
        assert raw_data.item_age_yrs.max() < 125, f"item_age_yrs max value is too high: {raw_data.item_age_yrs.max()}" 

        assert 'age_bin' in raw_data.columns, "age_bin column canot be missing"

        raw_data = self._normalize_unknown(raw_data)

        raw_data = self._prepare_usage_cols(raw_data, usage_vars=self._usage_vars)

        assert 'kms' not in raw_data.columns, "kms column should have removed already"
        assert 'hours' in raw_data.columns or 'miles' in raw_data.columns, f"miles or hours column must be present {raw_data.columns}"

        preprocessed_data = self._prepare_taxonomy_cols(raw_data, applier)
        preprocessed_data = self._prepare_location_cols(preprocessed_data)
       
        # NOTE: RB pipeline utilizes days_sinc_auc = 0 in prediction time
        # days_sinc_auc = 0 means number of days passes since the transactions happened
        preprocessed_data['days_sinc_auc'] = 0
        if days_sinc_auc is not None:
            preprocessed_data['days_sinc_auc'] = days_sinc_auc
   
        return preprocessed_data
