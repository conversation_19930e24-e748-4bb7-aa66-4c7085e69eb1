from typing import Tuple
from loguru import logger
import os
import sys
# import datetime as dt
#from datetime import datetime as dt
from datetime import datetime as dt, timedelta

PUBLIC_CACHE_TABLE = 'appraisals-data-prod-707493.ml_for_rouse.ml_for_rouse'
_BQ4DS_TABLE = 'appraisals-data-prod-707493.data_science_models.transactions'

CTE_QUERY_0 = f"""  
WITH CSMmY_data AS (
    SELECT 
        DISTINCT 
            LOWER(category_name) AS category,
            LOWER(subcategory_name) AS type,
            LOWER(make_name) AS make,
            LOWER(model_name) AS model,
            model_year AS mfg_year,
            --LOWER(country_code) AS item_country,

    FROM 
    `{_BQ4DS_TABLE}`

    WHERE 
        category_id  NOT IN (220, 21, 31)   -- exclude Not Appraised (NO taxonomy n ABCost), Miscellaneous and Other Equipment
        AND make_id NOT IN (58137, 78)      -- exclude Not Attributed and Miscellaneous
        AND sale_type NOT IN ('Export','Rental Purchase Option','Other','Auction')      -- Keep Retail, Dealer=Whosale and Unreserved Auction
        AND (sale_date >='2012-01-01')
        AND sale_date < CURRENT_DATE()  -- Add this condition to prevent loading future dates (Bad data)
        AND model_year > 1900 -- same thr i preprocessing
        AND LOWER(country_code) = 'usa'
),
"""

CTE_SELECT_QUERY_2 = f"""
SELECT 
    p.auc_end_time, 
    c.category, 
    c.make, 
    c.type, 
    c.model, 
    p.mfg_year, 
    p.item_country,
    p.meter_reading,
    p.abcost,
    p.auction_prediction, 
    p.retail_prediction

FROM CSMmY_data c
INNER JOIN public_cache p
    ON c.category = p.category 
        AND c.make = p.make
        AND c.type = p.type
        AND c.model = p.model
        AND c.mfg_year = p.mfg_year
        --AND c.item_country = p.item_country
"""

SELECT_QUERY_0 = f"""
SELECT 
    published_date as auc_end_time,
    category_name as category,
    subcategory_name as type,
    make_name as make, 
    model_name as model,
    year as mfg_year,
    country as item_country,
    meter_avg as meter_reading,
    meter_units,
    cost as abcost,
    flv_from_ml4r as auction_prediction,
    fmv_from_ml4r as retail_prediction,
    -- flv as auction_adjusted,
    -- fmv as retail_adjusted,
    -- mpe
FROM `{PUBLIC_CACHE_TABLE}`
"""

# NOTE: Defined by Olga (cuts from price index)
_CONSTRUCTION_COMPONENTS = [
    "0-1,099 lb skid steer loaders",
    "0-1,399 lb compact track loaders",
    "0-109 hp wheel loaders",
    "0-114 hp crawler dozers",
    "0-18 ft electric scissor lifts",
    "0-39 ft electric articulating booms",
    "0-39 ton articulated dump trucks",
    "0-4,999 lb mini excavators",
    "0-49 ft articulating booms",
    "0-49 ft telescopic booms",
    "0-6,999 lb rotating telehandlers",
    "0-6,999 lb telehandlers",
    "0-69 hp backhoe loaders",
    "1,100-2,999 lb skid steer loaders",
    "1,400-1,999 lb compact track loaders",
    "10,000-10,999 lb rotating telehandlers",
    "10,000-10,999 lb telehandlers",
    "100,000+ lb excavators",
    "100,000+ lb long-reach excavators",
    "11,000+ lb rotating telehandlers",
    "11,000+ lb telehandlers",
    "110-189 hp wheel loaders",
    "115-179 hp crawler dozers",
    "130+ ft articulating booms",
    "130+ ft telescopic booms",
    "180-299 hp crawler dozers",
    "19+ ft electric scissor lifts",
    "190-309 hp wheel loaders",
    "2,000+ lb compact track loaders",
    "25,000-44,999 lb excavators",
    "25,000-44,999 lb long-reach excavators",
    "3,000+ lb skid steer loaders",
    "300+ hp crawler dozers",
    "310+ hp wheel loaders",
    "40+ ft electric articulating booms",
    "40+ ton articulated dump trucks",
    "45,000-74,999 lb excavators",
    "45,000-74,999 lb long-reach excavators",
    "5,000-9,499 lb mini excavators",
    "50-79 ft articulating booms",
    "50-79 ft telescopic booms",
    "7,000-9,999 lb rotating telehandlers",
    "7,000-9,999 lb telehandlers",
    "70+ hp backhoe loaders",
    "75,000-99,999 lb excavators",
    "75,000-99,999 lb long-reach excavators",
    "80-129 ft articulating booms",
    "80-129 ft telescopic booms",
    "9,500-24,999 lb long-reach mini excavators",
    "9,500-24,999 lb mini excavators",
    "electric telescopic booms",
    "engine-driven scissor lifts",
    "maintainer motor graders",
    "motor graders",
    "other articulating booms",
    "other compact track loaders",
    "other scissor lifts",
    "other skid steer loaders",
    "other telehandlers",
    "track-driven articulating booms",
    "track-driven telescopic booms"
]


class QueryBuilder:

    def __init__(self):
        pass
    
    def _get_cte_public_cache_query(self, model_version: str) -> str:

        _CTE_PUBLIC_CACHE_QUERY_1 = f"""
        public_cache AS (
            SELECT 

                published_date as auc_end_time,
                category_name as category,
                subcategory_name as type,
                make_name as make, 
                model_name as model,
                year as mfg_year,
                country as item_country,
                meter_avg as meter_reading,
                meter_units,
                cost as abcost,
                flv_from_ml4r as auction_prediction,
                fmv_from_ml4r as retail_prediction,

            FROM `{PUBLIC_CACHE_TABLE}`
            WHERE 
                model_version = '{model_version}'
        )
        """

        return _CTE_PUBLIC_CACHE_QUERY_1

    def _get_filter_query(self, env: str, country: str, limit: int, category: str = None, year_condition: str = None,
                            model_name: str = None) -> str:
        
        curr_year = dt.now().year

        category_filter = f"AND LOWER(category_name) = '{category}'" if category else ""
        model_filter = f"AND LOWER(model_name) = '{model_name}'" if model_name else ""
        year_filter = f"AND ({curr_year} - year) <= 15" if year_condition is None else f"AND {year_condition}"

        FILTER_QUERY = f"""
        WHERE 
            model_version = '{env}'
            AND LOWER(country) = '{country}'
            {category_filter}
            {year_filter}
            {model_filter}
        ORDER BY RAND()
        LIMIT {limit};
        """


        return FILTER_QUERY
        
    def build_query(self,env: str, country: str, limit: int, category: str = None, year_condition: str = None, 
                    model_name: str = None, verbose: bool = False) -> str:
        
      
        filter_query_3 = self._get_filter_query(env, country, limit, category, year_condition, model_name)
        query = SELECT_QUERY_0 + filter_query_3
    
        if verbose:
            logger.info(f"query: {query}")

        return query

    def build_cuts_query_for_price_index(self, env: str, country: str,cuts: str, limit: int, verbose: bool = False) -> str:

        # NOTE: For comparing with Olga's cuts forecast 
        cuts_map = {'constructions': _CONSTRUCTION_COMPONENTS}
        assert cuts in cuts_map.keys(), f"Invalid cuts: {cuts}. Expected one of {list(cuts_map.keys())}"

        #cut_taxonomy = 'category_name' if cuts == 'transportation' else 'subcategory_name'
        cut_taxonomy = 'subcategory_name'
        cuts_components_for_sql = ", ".join(f"'{item}'" for item in cuts_map.get(cuts,_CONSTRUCTION_COMPONENTS))

        filter_query = f"""
        WHERE 
            model_version = '{env}'
            AND LOWER(country) = '{country}'
            AND {cut_taxonomy} IN ({cuts_components_for_sql})
        ORDER BY RAND()
        LIMIT {limit};
        """

        query = SELECT_QUERY_0 + filter_query

        if verbose:
            logger.info(f"query: {query}")

        return query
        
    def build_query_for_age(self, env: str, country: str, limit: int, age: str, verbose: bool = False) -> str:

        FILTER_QUERY = f"""
        WHERE 
            model_version = '{env}'
            AND LOWER(country) = '{country}'
            AND  EXTRACT(YEAR FROM CURRENT_DATE()) - year = {age}
        ORDER BY RAND()
        LIMIT {limit};
        """

        query = SELECT_QUERY_0 + FILTER_QUERY

        if verbose:
            logger.info(f"query: {query}")

        return query

    def build_special_queries(self,env: str, country: str, limit: int, verbose: bool = False) -> Tuple[str,str,str]:
        

        USA_HIGH_SAMPLE_SIZE_MINI_EXC = (
            SELECT_QUERY_0 + 
            f"""
            WHERE 
                model_version = '{env}'
                AND LOWER(country) = '{country}'
                AND year IN (2016, 2017,2018,2019, 2020, 2021, 2022, 2023, 2024,2025)
                AND LOWER(subcategory_name) = '9,500-24,999 lb mini excavators' 
                AND LOWER(model_name) = '306 cr'
            """
        )

        USA_HIGH_SAMPLE_SIZE_TRUCKS = (
            SELECT_QUERY_0 +
            f"""
            WHERE 
                model_version = '{env}'
                AND LOWER(country) = '{country}'
                AND year IN (2016, 2017, 2018, 2019, 2020, 2021, 2022, 2023, 2024, 2025)
                AND LOWER(subcategory_name) = 'sleeper ta truck tractors' -- NOTE: old taxonomy values 'ta truck tractors'
                AND LOWER(model_name) = 'cascadia'
            """
        )

        USA_OLD_CRANES_QUERY = (
            SELECT_QUERY_0 +
            f"""
            WHERE 
                model_version = '{env}'
                AND LOWER(country) = '{country}'
                AND LOWER(category_name) LIKE '%crane%'  -- any category containing the word crane
                AND year >= 1990 AND year < 2000

            ORDER BY RAND()
            LIMIT {limit};
            """
        )

        USA_TARGET_CATEGORY_QUERY = (
            SELECT_QUERY_0 +
            f"""
            WHERE 
                model_version = '{env}'
                AND LOWER(country) = 'usa'
                AND category_id in (15,29,362,32,2515)
                AND year >= 2000 AND year <= 2010

            ORDER BY RAND()
            LIMIT {limit};
            """
        )

        if verbose:
            # logger.info(f"query: {query}")
            #logger.info(f"USA_HIGH_SAMPLE_SIZE_ASSEST: {USA_HIGH_SAMPLE_SIZE_ASSEST}\n\n")
            logger.info(f"USA_HIGH_SAMPLE_SIZE_MINI_EXC: {USA_HIGH_SAMPLE_SIZE_MINI_EXC}\n\n")
            logger.info(f"USA_HIGH_SAMPLE_SIZE_TRUCKS: {USA_HIGH_SAMPLE_SIZE_TRUCKS}\n\n")
            logger.info(f"USA_OLD_CRANES_QUERY: {USA_OLD_CRANES_QUERY}\n\n")
            logger.info(f"USA_TARGET_CATEGORY_QUERY: {USA_TARGET_CATEGORY_QUERY}\n\n")

        return USA_HIGH_SAMPLE_SIZE_MINI_EXC, USA_HIGH_SAMPLE_SIZE_TRUCKS, USA_OLD_CRANES_QUERY, USA_TARGET_CATEGORY_QUERY