

from datetime import datetime,timedelta
import random
import time
from datetime import date
from dateutil.relativedelta import relativedelta
import pandas as pd
import numpy as np
from typing import List
from common.utils import sanity_check_data_is_sorted
import psutil


from pandarallel import pandarallel
pandarallel.initialize()

from loguru import logger
import os
import sys

# NOTE: export LOG_LEVEL=DEBUG  <== for debug messages  (default is INFO)
log_level = os.getenv("LOG_LEVEL", "INFO")

# Set up the logger
logger.remove()  # Remove any previously added sinks
logger.add(sys.stdout, level=log_level)

NUMERICAL_MISSING_VALUE = -1
_LAST_ELEM = -1
STR_UNKNOWN_VALUE = 'unknown'  # misisng value for categorical variables

class LookBack():

    def __init__(self, high_bid_threshold: int = 1000):

        self.high_bid_threshold = high_bid_threshold

        self._sorted_data_is_ready = False


        # TODO: Review. We can be clever in the look back window
        self._lookback_groups = ['type', 
                    'make_type',
                    'sale_type_make_model_0_1','make_model_country', 
                    'sale_type_make_model_0_1_age']
        

        # NOTE: cache_look_back_window_prediction fill this variables (creates the cache)
        self._lookback_cache = {}   # <= dict: group: df_group
        self._currency_cache = None  # <= DataFrame: df_currency

        self._statistics = ['_mean_', '_median_', '_count_']

        logger.info("Generating lookback cols")
        # Ex: type_count_90_0, type_mean_90_0, type_median_90_0, type_count_365_90, ...
        self.lookback_cols = self._generate_lookback_cols()

        logger.debug(self.lookback_cols)

        # check if all groups is represented in look back cols
        all_groups_present = all(any(group in col for col in self.lookback_cols) for group in self._lookback_groups)

        assert all_groups_present, "Not all groups are represented in lookback cols"
        
        self._ROLLING_WINDOW_CONFIGS = [
            # (upper_num, lower_num, window_type),
            (90, 0, 'two_way'),
            (365, 90, 'two_way'),
            (365, 0, 'two_way'),
            (365, 365, 'one_way'),
            (365, 0, 'one_way'),
            (365, 0, 'fixed')
        ]

        # Set seeds for reproducibility
        seed_value = 42  
        os.environ['PYTHONHASHSEED'] = str(seed_value)  
        random.seed(seed_value)         # Python random seed
        np.random.seed(seed_value)      # Numpy random seed

    def _generate_lookback_cols(self) -> List[str]:

        self._windows = ['90_0','365_90' ,'365_0', 'less_0', 'less_365']

        logger.info("Generating lookback cols based on groups, windows and statistics.  ...")
        # Ex: category_count_90_0, category_mean_90_0, category_median_90_0, category_count_365_90,  ...
        lookback_cols = [group + stat + window for group in self._lookback_groups for window in self._windows for stat in self._statistics]

        logger.debug(lookback_cols)

        return lookback_cols

    def _make_loock_back_groups(self, data: pd.DataFrame) -> pd.DataFrame:

        # NOTE: lookback groups to da groups
        # data['make_model'] = data['make'] + '_' + data['model']
        # data['make_model'] = data['make_model'].fillna(f'{STR_UNKNOWN_VALUE}_{STR_UNKNOWN_VALUE}')
        # data['make_model'] = data['make_model'].str.replace(" ", "")

        # NOTE: What is this? Is it important?
        #data['make_unknown'] = np.where(data.make.str.contains(STR_UNKNOWN_VALUE), 1, 0)

        # NOTE: This col is used by label encoder. Consider move to preprocessing or encode preprocessing
        data['make_cat']= data['make'] + '_' + data['category']
        data['make_cat']= data['make_cat'].str.lower()
        
        data['make_type'] = data['make'] + '_' + data['type']
        data['make_type'] = data['make_type'].str.lower()

        # TODO: move type after sale_type. It is easy t inspect the data in excell this way
        # needs to change after depploy because break loock back window name. Cannot be between 1.28.0 and 1.28.1
        # because of reuse of data preprocessing
        data['sale_type_make_model_0_1'] = (
            data['sale_type'] + '_' + data['make'] +
            '_' + data['Item_ModelIndex0'] + 
            '_' + data['Item_ModelIndex1'].astype(str) + 
            '_' + data['Item_ModelIndex2'] + 
            '_' + data['type']
        )

        # TODO: consider rename this col to sale_type_make_model_country. It express beter what it is
        #adding category to this but not changing name
        # country_channel_subcat_make_model
        data['make_model_country'] = data['sale_type_make_model_0_1'] + '_' + data['item_country']
        
        # channel_subcat_make_model_age
        data['sale_type_make_model_0_1_age'] = data['sale_type_make_model_0_1'] + '_' + data.age_bin.astype(str)

        # TODO: consider adding new lookback group and remove 
        # country_channel_subcat_make_model_age (useful for high sample sizes models)

        return data

    def preprocess_and_sort(self, data: pd.DataFrame) -> pd.DataFrame:

        assert 'auc_end_date' in data.columns, 'auc_end_date is missing'

        data_with_groups = self._make_loock_back_groups(data)

        sorted_data  = data_with_groups[data_with_groups['high_bid_usd'] > self.high_bid_threshold]
        
        # NOTE: Potential refactor
        sorted_data['cut_date_2011_12_31'] = datetime.strptime('2011-12-31', '%Y-%m-%d')
        sorted_data['cut_date_2011_12_31'] = pd.to_datetime(sorted_data['cut_date_2011_12_31']).dt.date

        # NOTE: Lookback expect ordered dataframe by date
        sorted_data = sorted_data.sort_values(by=['auc_end_date'], ascending=True).reset_index(drop=True)
        logger.info('Done!', flush=True)

        logger.info(f'sums_data shape: {sorted_data.shape}')
        logger.info(f'memory {psutil.virtual_memory()[2]} used:')
        logger.info(f'cpu used in preprocessing: {psutil.cpu_percent():.1f}')
    
        self._sorted_data_is_ready = True

        return sorted_data

    def _concat_rollig_statistics_col(self,data, df_list=None, group_col=None, lower_num=90, upper_num=0, 
                    window_type='two_way'):

        cat_trail = pd.DataFrame({})
        cat_trail_ = pd.concat(df_list)
        cat_trail = pd.concat([cat_trail, cat_trail_])
        cat_trail = cat_trail.drop_duplicates()

        cat_trail = cat_trail[ self._statistics + [group_col, 'auc_end_date']]
        cat_trail = cat_trail.drop_duplicates()

        cat_trail = self._rename_lookback_cols(window_type, group_col,lower_num,upper_num,
                                               cat_trail)

        data = pd.merge(data, cat_trail, how='left', on=[group_col, 'auc_end_date'])

        return data

    def _make_look_back_window(self, data: pd.DataFrame) -> pd.DataFrame:

        # Check if all columns in self._lookback_groups are in data columns
        missing_groups = set(self._lookback_groups) - set(data.columns)
        _msg = f"datframe data is missing lookback group: {missing_groups}. Did you run self._make_loock_back_groups(data)?"
        assert not missing_groups,_msg

        def log_initial_info():
            start = time.time()
            start_datetime = datetime.fromtimestamp(start)
            logger.info(f'start: {start_datetime}')
            logger.info(f'sums_data shape: {sums_data.shape}')
            logger.info(f'memory used: {psutil.virtual_memory()[2]}')
            logger.info(f'cpu used in preprocessing: {psutil.cpu_percent():.2f}')
            return start

        def get_group_values_by_based_day(based_day) -> List[float]:
            return sums_data[sums_data['auc_end_date'] == based_day][group_col_].tolist()

        def compute_rolling_statistics_and_concat(data, group_col_, lower_num, upper_num, window_type) -> pd.DataFrame:

            df_group['output_df'] = df_group.auc_end_date.parallel_apply(
                lambda x: _compute_rolling_statistics(x, group_col=group_col_, 
                                                     lower_num=lower_num, upper_num=upper_num, 
                                                     window_type=window_type))
            
            df_list = df_group['output_df'].tolist()
            return self._concat_rollig_statistics_col(data, df_list, group_col_, lower_num, upper_num, window_type)

        start = log_initial_info()
        list_auc_date = np.unique(pd.to_datetime(data['auc_end_date']).dt.date)

        # Dictionary to store date-grouped data.
        global date_groups
        date_groups = {}
        
        for group_col_ in self._lookback_groups:
            logger.info(f'############# {group_col_} ##############')

            logger.info(f'length of list_auc_date: {len(list_auc_date)}')

            logger.debug(f"sums_data cols: {sums_data.columns.to_list()}")
            logger.debug(f"group_col_: {group_col_}")

            # Check if group_col_ is already in date_groups. If not, populate it.
            if group_col_ not in date_groups:
                date_groups[group_col_] = {}
                df_group = pd.DataFrame(list_auc_date, columns=['auc_end_date'])
                df_group['grouplist'] = df_group.auc_end_date.parallel_apply(get_group_values_by_based_day)
                date_groups[group_col_] = dict(zip(df_group.auc_end_date, df_group.grouplist))

            # Compute all windows for each group_col_
            for lower, upper, window_type in self._ROLLING_WINDOW_CONFIGS:

                data = compute_rolling_statistics_and_concat(data, group_col_, lower, upper, window_type)
                logger.debug(f'Updated shape after {window_type}, lower: {lower}, upper: {upper}: {data.shape}')

            logger.info(f'Time Elapsed: {(time.time() - start):.2f} seconds')

        return data

    def _get_currency_cache_from_historical_data(self, sums_data): 

        most_recent_currency_view = sums_data.groupby( ['item_country', 'currency_code']).auc_end_date.max().reset_index()
        _cols_to_merge = ['item_country', 'currency_code', 'auc_end_date']
        logger.debug(f"most_recent_currency_view.cols: {most_recent_currency_view.columns.tolist()}\n\n\n")

        df_currency = sums_data.merge(most_recent_currency_view,on=_cols_to_merge, how='inner')
        df_currency = df_currency[['item_country', 'currency_code', 'conversion_rate', 'auc_end_date']].drop_duplicates()
            
        df_currency_count = sums_data[_cols_to_merge].groupby(['item_country', 'currency_code']).agg('count').reset_index()

        df_currency_count.columns=['item_country', 'currency_code','cnt']
        df_currency=pd.merge(df_currency,df_currency_count,how='inner',on=['item_country', 'currency_code'])
       
        return df_currency
    
    def _get_currency_cache_from_loaded_currency_data(self, currency_data: pd.DataFrame, sums_data: pd.DataFrame):
        
        most_recent_currency_view = sums_data.groupby( ['item_country', 'currency_code']).auc_end_date.max().reset_index()
        _cols_to_merge = ['item_country', 'currency_code', 'auc_end_date']
        logger.debug(f"most_recent_currency_view.cols: {most_recent_currency_view.columns.tolist()}\n\n\n")

        most_recent_currency_view = most_recent_currency_view[_cols_to_merge]

        # Calculate conversion_rate based on the last 3 months average
        currency_data['auc_end_date'] = pd.to_datetime(currency_data['auc_end_date'])
        currency_agg = currency_data.groupby(['currency_code'], as_index=True).agg({'conversion_rate': 'mean',
                                                                       'auc_end_date': 'count'
                                                                       }).reset_index()
        currency_agg.columns = ['currency_code', 'conversion_rate', 'cnt']

        # NOTE: needs to merge by currency code because some countries has transaction in multiple currencies
        currency_cache = pd.merge(most_recent_currency_view,  currency_agg, how='inner', on=['currency_code'])

        return currency_cache
    
    # TODO: Consider move this to unit test
    def _sanity_check_currency_cache(self, currency_data: pd.DataFrame, sums_data: pd.DataFrame):

        # NOTE: tmp code to validae new currency cache approach
        currency_cache1 = self._get_currency_cache_from_historical_data(sums_data)
        currency_cache2 = self._get_currency_cache_from_loaded_currency_data(currency_data,sums_data)

        logger.debug(f'currency_cache1 shape: {currency_cache1.shape}')
        logger.debug(f'currency_cache2 shape: {currency_cache2.shape}')

        logger.debug(f'currency cahceh 1 countries: {currency_cache1.item_country.nunique()}')
        logger.debug(f'currency cache 2 countries: {currency_cache2.item_country.nunique()}')

        logger.debug(f'currency cache 1 currency codes: {currency_cache1.currency_code.nunique()}')
        logger.debug(f'currency cache 2 currency codes: {currency_cache2.currency_code.nunique()}')

        logger.debug(f'currency code cache 1 - 2: {set(currency_cache1.currency_code.unique()) - set(currency_cache2.currency_code.unique())}')
        logger.debug(f'countries cache 1 - 2: {set(currency_cache1.item_country.unique()) - set(currency_cache2.item_country.unique())}')

        assert set(currency_cache1.currency_code.unique()) - set(currency_cache2.currency_code.unique()) == set()
        assert set(currency_cache1.item_country.unique()) - set(currency_cache2.item_country.unique()) == set()

    def cache_look_back_window_prediction(self,sorted_data_by_process: pd.DataFrame, currency_data: pd.DataFrame = pd.DataFrame()):

        assert self._sorted_data_is_ready, "Did you call lookback.prepare_summary_data_for_training(data)?"

        logger.info('==> NEW cache_look_back_window_prediction')
        logger.info(f'sums data shape before caching: {sorted_data_by_process.shape}')
        
        based_day = sorted_data_by_process['auc_end_date'].max()
        logger.info(f'based_day: {based_day}')
        
        start = time.time()
        # list comprehension to get currency data
        self._currency_cache = (
                self._get_currency_cache_from_historical_data(sorted_data_by_process)
                if currency_data.empty
                else self._get_currency_cache_from_loaded_currency_data(currency_data, sorted_data_by_process)
        )

        for group_col_ in self._lookback_groups:

            # Initialize the df with the first window configuration to reduce redundant merge operations
            lower, upper, window_type = self._ROLLING_WINDOW_CONFIGS[0]
            df = self._compute_rolling_statistics_for_prediction(sorted_data_by_process, based_day, 
                                                                group_col=group_col_, 
                                                                lower_num=lower, upper_num=upper, window_type=window_type)

            # Compute all windows for each group_col_
            for lower, upper, window_type in self._ROLLING_WINDOW_CONFIGS[1:]:

                temp_df = self._compute_rolling_statistics_for_prediction(sorted_data_by_process, based_day, 
                                                                                    group_col=group_col_, 
                                                                                    lower_num=lower, upper_num=upper, 
                                                                                    window_type=window_type)
                
                df = pd.merge(df, temp_df, on=['auc_end_date', group_col_], how='outer')

            #df.to_csv('LookBackData/prediction_' + group_col_ + '_lookback.csv', index=False)

            self._lookback_cache[ group_col_ + '_lookback'] = df.copy()

            # logger.info(f'(INSIDE CACHE) group_col_: {group_col_}')
            # logger.info(f'(INSIDE CACHE) df shape: {df.shape}')
            # logger.info(f'(INSIDE CACHE) df cols: {df.columns.tolist()}')
            # logger.info(f'(INSIDE CACHE) df index name: {df.index.name}')
        
        logger.info(f'Time Elapsed: {(time.time() - start):.2f} seconds')

    def fit_transform(self, data: pd.DataFrame, preprocessed_data: pd.DataFrame) -> pd.DataFrame:

        assert self._sorted_data_is_ready, "Did you call lookback.preprocess_and_sort(data)?"
        sanity_check_data_is_sorted(preprocessed_data)

        # NOTE: Using global variable speedup pnadarallel processing by a factor of 2.5 times at least
        # DO not remove it. It is going to slow down lookabck window processing
        logger.info('Fitting look back data ...')
        
        # NOTE: keeping old variable name for the other memebr of the DS team
        global sums_data
        sums_data = preprocessed_data
        
        lookback_data = self._make_look_back_window(data)

        lookback_data[self.lookback_cols] = lookback_data[self.lookback_cols].fillna(NUMERICAL_MISSING_VALUE)

        return lookback_data

    def _transform_currency(self, data: pd.DataFrame) -> pd.DataFrame:
        
        assert self._currency_cache is not None, 'Currency cache is not initialized. Did you fit lookback windows?'

        # TODO: Fix bug when the input country is not in the currency cache
        # TODO: Consider convert currency cache to dictionary for faster lookup and simplify the code
        # NOTE: Get the most recent currency conversion rate and code for each country. Remember that CAN has old transaction on USD currency. <== Needs to align/inform with Joydeep and Sunil
        logger.info('transforming currency')

        data['currency_code'] = data.item_country.apply(
                        lambda c: 'usd' 
                        if str(c) == 'usa' 
                        else self._currency_cache[self._currency_cache.item_country == c].currency_code.tolist()[_LAST_ELEM].lower()
        )

        data['conversion_rate'] = data.item_country.apply(
                        lambda c: 1.00 
                        if str(c) == 'usa' 
                        else self._currency_cache[self._currency_cache.item_country == c].conversion_rate.tolist()[_LAST_ELEM]
        )

        return data

    def _transform_lookback(self, data: pd.DataFrame) -> pd.DataFrame:

        # NOTE: Assign the most recent lookback values for all rows in the dataframe
        # NOTE: Does not make sense to use it in the training data for past transactions. 
        # It is designed to use on new and recent data only.

        start=time.time()
        logger.info('making look back window')
        
        for group_col_ in self._lookback_groups:

            # TODO: refactor to: for group_col_, group_cahed in self._lookback_cache.items():
            # Get cached data for group 
            group_cahed = self._lookback_cache.get(group_col_ + '_lookback',None)

            if group_cahed is None:
                logger.warning('WARNING group_cahed is empty. That is not expected')
            else:

                # TODO: Review this code. if group_cahed is None, then group_cahed.shape will fail
                logger.info(f"group_cahed shape: {group_cahed.shape}")

            # select only group values (row) that are in the df
            df_loc = group_cahed[group_cahed[group_col_].isin(list(data[group_col_]))]

            data = pd.merge(data, df_loc[[c for c in df_loc.columns if c not in ['auc_end_date']]], on=group_col_, how='left')

        #logger.info(f'Time Elapsed: {(time.time() - start)}s')
        logger.info(f'Time Elapsed: {(time.time() - start):.2f} seconds')

        return data

    def transform(self, data: pd.DataFrame) -> pd.DataFrame:
        
        assert self._sorted_data_is_ready, "Did you run fit_tranform before?"

        data = self._transform_currency(data)

        data_with_groups = self._make_loock_back_groups(data)
        data = self._transform_lookback(data_with_groups)

        # NOTE: transform can generate missing values for unseen make, model, category, etc.
        data[self.lookback_cols] = data[self.lookback_cols].fillna(NUMERICAL_MISSING_VALUE)

        return data
    
    # NOTE: This is for help with debugging in the case.
    def save_lookback_cache_as_multiple_csv_files(self, path: str) -> None:

        for group_col, group_cahed in self._lookback_cache.items():

            group_cahed.to_csv(os.path.join(path, f'{group_col}.csv'), index=False)

    # NOTE: save cache as one csv for easy debugging. Preferrable method to use. BUT NOT WORKING
    # TODO: Review this. If not working and it i shard consider remove it
    def convert_lookback_cache_to_tabular_data(self) -> pd.DataFrame:
        """ 
        This function converts the lookback cache (dict of dataframe) to tabular data for easy debugging and analysis.
        1. merge all lookback dataframes into one dataframe
        2. add group_col and group_value columns
        3. remove lookback suffix from group_col
        """

        look_back_cache = pd.DataFrame()

        for group_col, group_cahed in self._lookback_cache.items():

            assert 'auc_end_date' in group_cahed.columns, 'auc_end_date is missing in the group_cahed dataframe'

            if look_back_cache.empty:
                # NOTE: first time, just assign the group_cahed to look_back_cache
                look_back_cache = group_cahed
                look_back_cache['group_col'] = group_col

                # remove lookback suffix from group_col
                _col_no_suffix = group_col.replace('_lookback','')
                look_back_cache.rename(columns={_col_no_suffix: 'group_value'}, inplace=True)

                new_cols_name = {c:c.replace(_col_no_suffix,'') for c in look_back_cache.columns if c not in ['group_col', 'group_value', 'auc_end_date']}
                look_back_cache.rename(columns=new_cols_name, inplace=True)

            else:

                df = group_cahed
                df['group_col'] = group_col

                # NOTE: remove lookback suffix from group_col
                _col_no_suffix = group_col.replace('_lookback','')
                df.rename(columns={_col_no_suffix: 'group_value'}, inplace=True)

                new_cols_name = {c:c.replace(_col_no_suffix,'') for c in df.columns if c not in ['group_col', 'group_value', 'auc_end_date']}
                df.rename(columns=new_cols_name, inplace=True)

                look_back_cache = pd.concat([look_back_cache, df])
                
        cols_to_move_to_begin = ['group_col', 'group_value', 'auc_end_date']
        new_order = cols_to_move_to_begin + [col for col in look_back_cache.columns if col not in cols_to_move_to_begin]
        look_back_cache = look_back_cache[new_order]

        look_back_cache = split_tabular_lookback_cache_group(look_back_cache)

        return look_back_cache 

    def _rename_lookback_cols(self, window_type, group_col,lower_num,upper_num, data: pd.DataFrame) -> pd.DataFrame:

        for col in self._statistics:

            if window_type == 'two_way':
                col_name = group_col + col + str(lower_num) + '_' + str(upper_num)
            elif window_type == 'one_way':
                col_name = group_col + col + 'less_' + str(upper_num)
            else:
                col_name = group_col + col + 'post_' + str(15339)

            data = data.rename(columns={col: col_name})

        return data

    def _compute_rolling_statistics_for_prediction(self, sums_data,based_day, group_col=['category'], 
                                                lower_num=90, upper_num=0, 
                                                window_type='two_way'):

        group_ = list(set(sums_data[group_col].tolist()))
        
        window_criteria = _get_window_criteria(window_type, based_day, 
                                          group_col, group_, 
                                          lower_num, upper_num)
    
        window_data = sums_data[window_criteria][[group_col, 'high_bid_usd']]
        output_df = window_data.groupby([group_col]).high_bid_usd.agg(_mean_= np.mean,
                                                                          _median_= np.median,
                                                                          _count_= 'count')

        output_df = output_df.reset_index()
        output_df['auc_end_date'] = based_day

        df_list = []
        df_list.append(output_df)

        cat_trail = pd.DataFrame({})
        cat_trail_ = pd.concat(df_list)
        cat_trail = pd.concat([cat_trail, cat_trail_])
        cat_trail = cat_trail.drop_duplicates()

        cat_trail = cat_trail[ self._statistics + [group_col, 'auc_end_date']]
        cat_trail = cat_trail.drop_duplicates()
    
        cat_trail = self._rename_lookback_cols(window_type, group_col,lower_num,upper_num,
                                               cat_trail)

        return cat_trail

# NOTE: for better saving lookback cache as csv. Make it easy to read and inpect on excel
def split_tabular_lookback_cache_group(lookback_data_cache: pd.DataFrame) -> pd.DataFrame:

    for group_col in lookback_data_cache.group_col.unique():

        #print(f"group_col: {group_col}")

        mask = lookback_data_cache['group_col'] == group_col

        if group_col == 'type_lookback':
            lookback_data_cache.loc[mask, 'country'] = ""
            lookback_data_cache.loc[mask, 'sale_type'] = ""
            lookback_data_cache.loc[mask, 'type'] = lookback_data_cache.loc[mask, 'group_value']
            lookback_data_cache.loc[mask, 'make'] = ""
            lookback_data_cache.loc[mask, 'model'] = ""

        elif group_col == 'make_type_lookback':
            lookback_data_cache.loc[mask, 'country'] = ""
            lookback_data_cache.loc[mask, 'sale_type'] = ""
            lookback_data_cache.loc[mask, 'type'] = lookback_data_cache.loc[mask, 'group_value'].str.split('_').str[1]
            lookback_data_cache.loc[mask, 'make'] = lookback_data_cache.loc[mask, 'group_value'].str.split('_').str[2]
            lookback_data_cache.loc[mask, 'model'] = ""
            lookback_data_cache.loc[mask, 'age_bin'] = ""

        elif group_col == 'sale_type_make_model_0_1_lookback':
            lookback_data_cache.loc[mask, 'country'] = ""
            lookback_data_cache.loc[mask, 'sale_type'] =lookback_data_cache.loc[mask, 'group_value'].str.split('_').str[0]
            lookback_data_cache.loc[mask, 'type'] = lookback_data_cache.loc[mask, 'group_value'].str.split('_').str[5]
            lookback_data_cache.loc[mask, 'make'] = lookback_data_cache.loc[mask, 'group_value'].str.split('_').str[1]
            lookback_data_cache.loc[mask, 'model'] = lookback_data_cache.loc[mask, 'group_value'].str.split('_').str[2:5].str.join('_')
            lookback_data_cache.loc[mask, 'age_bin'] = ""

        elif group_col == 'make_model_country_lookback':
            lookback_data_cache.loc[mask, 'country'] = lookback_data_cache.loc[mask, 'group_value'].str.split('_').str[6]
            lookback_data_cache.loc[mask, 'sale_type'] =lookback_data_cache.loc[mask, 'group_value'].str.split('_').str[0]
            lookback_data_cache.loc[mask, 'type'] = lookback_data_cache.loc[mask, 'group_value'].str.split('_').str[5]
            lookback_data_cache.loc[mask, 'make'] = lookback_data_cache.loc[mask, 'group_value'].str.split('_').str[1]
            lookback_data_cache.loc[mask, 'model'] = lookback_data_cache.loc[mask, 'group_value'].str.split('_').str[2:5].str.join('_')
            lookback_data_cache.loc[mask, 'age_bin'] = ""

        elif group_col == 'sale_type_make_model_0_1_age_lookback':
            lookback_data_cache.loc[mask, 'country'] = ""
            lookback_data_cache.loc[mask, 'sale_type'] =lookback_data_cache.loc[mask, 'group_value'].str.split('_').str[0]
            lookback_data_cache.loc[mask, 'type'] = lookback_data_cache.loc[mask, 'group_value'].str.split('_').str[5]
            lookback_data_cache.loc[mask, 'make'] = lookback_data_cache.loc[mask, 'group_value'].str.split('_').str[1]
            lookback_data_cache.loc[mask, 'model'] = lookback_data_cache.loc[mask, 'group_value'].str.split('_').str[2:5].str.join('_')
            lookback_data_cache.loc[mask, 'age_bin'] = lookback_data_cache.loc[mask, 'group_value'].str.split('_').str[6]


    _goes_first_cols = ['group_col', 'auc_end_date', 'country', 'sale_type', 'type', 'make', 'model', 'age_bin', 'group_value']
    _cols_order = _goes_first_cols + [c for c in lookback_data_cache.columns if c not in _goes_first_cols]

    return lookback_data_cache[_cols_order]

def _get_window_criteria(window_type: str, based_day: date, group_col, group_, 
                        lower_num: int, upper_num: int) -> pd.Series:

    start_date =  based_day - timedelta(lower_num)
    end_date =  based_day - timedelta(upper_num)
    
    _WINDOW_CRITERIAS = {
        # 'two_way': window is defined as range between start_date and end_date based on 
        # lower_num days and upper_num days from the based_day.
        'two_way': (sums_data['auc_end_date'] >= start_date) & \
                              (sums_data['auc_end_date'] < end_date) & \
                              (sums_data[group_col].isin(group_)),

        # 'one_way': all days before end_date (upper_num days from the based_day).
        'one_way': (sums_data['auc_end_date'] < end_date) &\
                    (sums_data[group_col].isin(group_)),

        # For any other type, the filter is based on dates after 'cut_date_2011_12_31' and before upper_num days from based_day.
        # Then aggregate high_bid_usd column for mean, median and count.
        'fixed': (sums_data['auc_end_date'] >= sums_data['cut_date_2011_12_31']) &\
                        (sums_data[group_col].isin(group_)) &\
                        (sums_data['auc_end_date'] < end_date)
        }

    _DEFAULT_CRITERIA = _WINDOW_CRITERIAS['fixed']

    return  _WINDOW_CRITERIAS.get(window_type, _DEFAULT_CRITERIA)


def _compute_rolling_statistics(based_day, group_col=['category'], lower_num=90, upper_num=0, 
                               window_type='two_way'):
    """
    The function determines the look back window for different group_col
    :param group_col: string,
    the group column
    :param lower_num: int,
    the minimum number days before today. 0 indicates today
    :param upper_num: int,
    the maximum number days before from today. 0 indicates today
    :return:
    """

    # Check if the passed day (based_day) exists in the date_groups for the given group_col. 
    # If it does not, take the day with maximum data as the based_day.
    if based_day in date_groups[group_col].keys():
        group_ = date_groups[group_col][based_day]
    else:
        logger.warning("Warning: based_day missing")
        temp = date_groups[group_col]
        based_day = max(temp, key=lambda key: temp[key])
        group_ = date_groups[group_col][based_day]

    window_criteria = _get_window_criteria(window_type, based_day, 
                                          group_col, group_, 
                                          lower_num, upper_num)
      
    window_data = sums_data[window_criteria][[group_col, 'high_bid_usd']] 
    output_df = window_data.groupby([group_col]).high_bid_usd.agg(_mean_=np.mean,
                                                                _median_=np.median, 
                                                                _count_='count')

    output_df = output_df.reset_index()
    output_df['auc_end_date'] = based_day
    return output_df
