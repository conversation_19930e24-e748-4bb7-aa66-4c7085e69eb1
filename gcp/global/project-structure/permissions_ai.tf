resource "google_project_iam_member" "algo_ai_dev_ai_engineering" {
  for_each = toset([
    "roles/aiplatform.user",
    "roles/notebooks.viewer",
    "roles/artifactregistry.repoAdmin",
    "roles/aiplatform.colabEnterpriseAdmin",
    "roles/cloudfunctions.developer",
    "roles/iam.serviceAccountUser",
    "roles/serviceusage.serviceUsageConsumer",
    "roles/chat.owner",
    "roles/appengine.appAdmin",
    "roles/cloudbuild.builds.editor",
    "roles/storage.objectAdmin"
  ])
  role    = each.key
  project = module.project_algo_ai_dev.id
  member  = "group:<EMAIL>"
}

resource "google_project_iam_member" "algo_ai_prod_ai_engineering" {
  for_each = toset([
    "roles/aiplatform.user",
    "roles/notebooks.viewer",
    "roles/artifactregistry.repoAdmin",
    "roles/aiplatform.colabEnterpriseAdmin",
    "roles/cloudfunctions.developer",
    "roles/iam.serviceAccountUser",
    "roles/serviceusage.serviceUsageConsumer",
    "roles/chat.owner",
    "roles/appengine.appCreator",
    "roles/appengine.deployer",
    "roles/storage.objectAdmin"
  ])
  role    = each.key
  project = module.project_algo_ai_prod.id
  member  = "group:<EMAIL>"
}

resource "google_project_iam_member" "platform_services_dev_ai_devs" {
  for_each = toset([
    "roles/aiplatform.user",
    "roles/notebooks.admin",
    "roles/discoveryengine.admin",
    "roles/dataform.codeCreator",
    "roles/aiplatform.expressAdmin",
    "roles/dataform.admin",
    "roles/iam.serviceAccountUser",
    "roles/serviceusage.serviceUsageConsumer",
    "roles/bigquery.user",
    "roles/bigquery.dataOwner",
  ])
  role    = each.key
  project = module.project_services_dev.id
  member  = "group:<EMAIL>"
}

resource "google_project_iam_member" "platform_email_service_dev_ai_devs" {
  for_each = toset([
    "roles/aiplatform.user",
    "roles/notebooks.admin",
    "roles/discoveryengine.admin",
    "roles/aiplatform.expressAdmin",
    "roles/dataform.admin",
    "roles/iam.serviceAccountUser",
    "roles/serviceusage.serviceUsageConsumer",
  ])
  role    = each.key
  project = module.project_email_service_dev.id
  member  = "group:<EMAIL>"
}

resource "google_folder_iam_member" "clients_data_ai_devs" {
  for_each = toset([
    "roles/bigquery.user",
    "roles/bigquery.dataViewer",
    "roles/bigquery.jobUser",
  ])
  role   = each.key
  folder = var.outside_folders["ClientProjects"]
  member = "group:<EMAIL>"
}

resource "google_folder_iam_member" "clients_data_ai_devs_ai_builder" {
  for_each = toset([
    "roles/bigquery.user",
    "roles/bigquery.dataViewer",
    "roles/bigquery.jobUser",
  ])
  role   = each.key
  folder = var.outside_folders["ClientProjects"]
  member = "serviceAccount:<EMAIL>"
}

resource "google_project_iam_custom_role" "ai_builder_permissions_services_dev" {
  role_id     = "ai_builder_permissions"
  title       = "AI Builder Permissions"
  description = "A custom role with permissions for AI service usage and discovery engine operations"
  project     = module.project_services_dev.id

  permissions = [
    "serviceusage.services.enable",
    "discoveryengine.projects.provision",
    "discoveryengine.projects.get",
    "serviceusage.services.list"
  ]
}

resource "google_project_iam_custom_role" "ai_builder_permissions_email_service_dev" {
  role_id     = "ai_builder_permissions"
  title       = "AI Builder Permissions"
  description = "A custom role with permissions for AI service usage and discovery engine operations"
  project     = module.project_email_service_dev.id

  permissions = [
    "serviceusage.services.enable",
    "discoveryengine.projects.provision",
    "discoveryengine.projects.get",
    "serviceusage.services.list"
  ]
}

resource "google_project_iam_member" "ai_platform_services_dev" {
  for_each = toset([
    "roles/discoveryengine.editor",
  ])
  project = module.project_services_dev.id
  member  = "serviceAccount:<EMAIL>"
  role    = each.key
}

resource "google_project_iam_binding" "ai_builder_permissions_services_dev" {
  project = module.project_services_dev.id
  role    = google_project_iam_custom_role.ai_builder_permissions_services_dev.name

  members = [
    "group:<EMAIL>"
  ]
}

resource "google_project_iam_binding" "ai_builder_permissions_email_service_dev" {
  project = module.project_email_service_dev.id
  role    = google_project_iam_custom_role.ai_builder_permissions_email_service_dev.name

  members = [
    "group:<EMAIL>"
  ]
}

resource "google_project_iam_member" "platform_ai_sandbox_ai_devs" {
  for_each = toset([
    "roles/aiplatform.user",
    "roles/notebooks.admin",
    "roles/discoveryengine.admin",
    "roles/dataform.codeCreator",
    "roles/aiplatform.expressAdmin",
    "roles/dataform.admin",
    "roles/iam.serviceAccountUser",
    "roles/serviceusage.serviceUsageConsumer",
    "roles/bigquery.user",
    "roles/bigquery.dataOwner",
    "roles/storage.admin",
  ])
  role    = each.key
  project = module.project_ai_sandbox.id
  member  = "group:<EMAIL>"
}

resource "google_folder_iam_member" "clients_data_ai_devs_ai_sandbox_builder" {
  for_each = toset([
    "roles/bigquery.user",
    "roles/bigquery.dataViewer",
    "roles/bigquery.jobUser",
  ])
  role   = each.key
  folder = var.outside_folders["ClientProjects"]
  member = "serviceAccount:<EMAIL>"
}

resource "google_project_iam_member" "owner_role" {
  for_each = toset([
    "roles/owner",
  ])
  role    = each.key
  project = module.project_ai_sandbox.id
  member  = "user:<EMAIL>"
}

resource "google_project_iam_member" "algo_vms_dev_ai_engineering" {
  for_each = toset([
    "roles/aiplatform.admin",
    "roles/notebooks.admin",
    "roles/artifactregistry.repoAdmin",
    "roles/aiplatform.colabEnterpriseAdmin",
    "roles/cloudfunctions.developer",
    "roles/iam.serviceAccountUser",
    "roles/serviceusage.serviceUsageConsumer",
    "roles/storage.objectAdmin"
  ])
  role    = each.key
  project = module.project_algo_vms_dev.id
  member  = "group:<EMAIL>"
}

resource "google_project_iam_member" "algo_vms_prod_ai_engineering" {
  for_each = toset([
    "roles/aiplatform.admin",
    "roles/notebooks.admin",
    "roles/artifactregistry.repoAdmin",
    "roles/aiplatform.colabEnterpriseAdmin",
    "roles/cloudfunctions.developer",
    "roles/iam.serviceAccountUser",
    "roles/serviceusage.serviceUsageConsumer",
    "roles/storage.objectAdmin"
  ])
  role    = each.key
  project = module.project_algo_vms_prod.id
  member  = "group:<EMAIL>"
}
