resource "google_folder" "vms" {
  display_name = "VMS"
  parent       = google_folder.rouse_services.name
}

module "project_vms_dev" {
  source          = "../../modules/structure/project/v1"
  project_name    = "VMs Dev"
  project_id      = "vms-dev"
  folder          = google_folder.vms
  billing_account = var.billing_account
}

module "project_vms_prod" {
  source          = "../../modules/structure/project/v1"
  project_name    = "VMs Prod"
  project_id      = "vms-prod"
  folder          = google_folder.vms
  billing_account = var.billing_account
}

module "project_data_vms_dev" {
  source          = "../../modules/structure/project/v1"
  project_name    = "Data VMs Dev"
  project_id      = "data-vms-dev"
  folder          = google_folder.vms
  billing_account = var.billing_account
}

module "project_finance_vms_prod" {
  source          = "../../modules/structure/project/v1"
  project_name    = "Finance VMs Prod"
  project_id      = "finance-vms-prod"
  folder          = google_folder.vms
  billing_account = var.billing_account
}

module "project_algo_vms_dev" {
  source          = "../../modules/structure/project/v1"
  project_name    = "Algo VMs Dev"
  project_id      = "algo-vms-dev"
  folder          = google_folder.vms
  billing_account = var.billing_account
}

module "project_algo_vms_prod" {
  source          = "../../modules/structure/project/v1"
  project_name    = "Algo VMs Prod"
  project_id      = "algo-vms-prod"
  folder          = google_folder.vms
  billing_account = var.billing_account
}

module "project_dr_prod" {
  source           = "../../modules/structure/project/v1"
  project_name     = "DR Prod"
  project_id       = "dr-prod"
  folder           = google_folder.vms
  billing_account  = var.billing_account
  firebase_enabled = true
}

module "project_backups_prod" {
  source          = "../../modules/structure/project/v1"
  project_name    = "Backups Prod"
  project_id      = "backups-prod"
  folder          = google_folder.vms
  billing_account = var.billing_account
}

module "project_econic_data_prod" {
  source          = "../../modules/structure/project/v1"
  project_name    = "Econic Data Prod"
  project_id      = "econic-data-prod"
  folder          = google_folder.vms
  billing_account = var.billing_account
}

locals {
  vms_project_ids = merge(
    module.project_vms_dev.project_id,
    module.project_vms_prod.project_id,
    module.project_data_vms_dev.project_id,
    module.project_finance_vms_prod.project_id,
    module.project_algo_vms_dev.project_id,
    module.project_algo_vms_prod.project_id,
    module.project_dr_prod.project_id,
    module.project_backups_prod.project_id,
    module.project_econic_data_prod.project_id,
  )
}
