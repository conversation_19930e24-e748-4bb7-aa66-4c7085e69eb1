module "modelyear_range_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "modelyear-range-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "modelyear_range_etl_bucket_name" {
  value = module.modelyear_range_etl_bucket.bucket_name
}

module "expanded_equipment_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "expanded-equipment-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
  lifecycle_rules = {
    lifecycle_rule = {
      condition = {
        age = 7
      }
      action = {
        type          = "SetStorageClass"
        storage_class = "COLDLINE"
      }
    }
  }
}

output "expanded_equipment_etl_bucket" {
  value = module.expanded_equipment_etl_bucket.bucket_name
}

module "meter_adjustments_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "meter-adjustments-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "meter_adjustments_etl_bucket" {
  value = module.meter_adjustments_etl_bucket.bucket_name
}

module "classification_rules_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "classification-rules-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "classification_rules_etl_bucket" {
  value = module.classification_rules_etl_bucket.bucket_name
}

module "equipment_classification_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "equipment-classification-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "equipment_classification_etl_bucket" {
  value = module.equipment_classification_etl_bucket.bucket_name
}

module "classification_index_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "classification-index-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "classification_index_etl_bucket" {
  value = module.classification_index_etl_bucket.bucket_name
}

module "appraisal_book_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "appraisal-book-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
  lifecycle_rules = {
    lifecycle_rule = {
      condition = {
        age = 7
      }
      action = {
        type          = "SetStorageClass"
        storage_class = "COLDLINE"
      }
    }
  }
}

output "appraisal_book_etl_bucket" {
  value = module.appraisal_book_etl_bucket.bucket_name
}

module "consumer_reports_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "consumer-reports"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "consumer_reports_bucket" {
  value = module.consumer_reports_bucket.bucket_name
}

module "equipment_details_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "equipment-details-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "equipment_details_etl_bucket" {
  value = module.equipment_details_etl_bucket.bucket_name
}

module "region_mappings_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "region-mappings-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
  lifecycle_rules = {
    lifecycle_rule = {
      condition = {
        age = 7
      }
      action = {
        type          = "SetStorageClass"
        storage_class = "COLDLINE"
      }
    }
  }
}

output "region_mappings_etl_bucket" {
  value = module.region_mappings_etl_bucket.bucket_name
}

module "valuations_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "valuations"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "valuations_bucket" {
  value = module.valuations_bucket.bucket_name
}

module "auction_transaction_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "auction-transaction-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "auction_transaction_etl_bucket" {
  value = module.auction_transaction_etl_bucket.bucket_name
}

module "wholesale_value_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "wholesale-value-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "wholesale_value_etl_bucket" {
  value = module.wholesale_value_etl_bucket.bucket_name
}

module "classification_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "classification-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "classification_etl_bucket" {
  value = module.classification_etl_bucket.bucket_name
}


module "book_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "book-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "book_etl_bucket" {
  value = module.book_etl_bucket.bucket_name
}

module "inspections_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "inspections-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "inspections_etl_bucket" {
  value = module.inspections_etl_bucket.bucket_name
}

module "exchange_rates_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "exchange-rates-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "exchange_rates_etl_bucket" {
  value = module.exchange_rates_etl_bucket.bucket_name
}

module "schedules_import_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "schedules-import-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "schedules_import_etl_bucket" {
  value = module.schedules_import_etl_bucket.bucket_name
}

module "schedules_module_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "schedules-module-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "schedules_module_etl_bucket" {
  value = module.schedules_module_etl_bucket.bucket_name
}

module "ritchie_bros_xml_auction_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "ritchie-bros-xml-auction-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "ritchie_bros_xml_auction_etl_bucket" {
  value = module.ritchie_bros_xml_auction_etl_bucket.bucket_name
}

module "dat_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "dat-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "dat_etl_bucket" {
  value = module.dat_etl_bucket.bucket_name
}

module "appraisal_valuation_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1"
  name         = "appraisal-valuation-etl-n"
  project_name = local.project
  region       = var.region
  versioning   = true
  lifecycle_rules = [
    {
      action = { 
        type = "Delete" 
      }
      condition = { 
        with_state = "ARCHIVED" 
        age = 7 
      }
    }
  ]
}

output "appraisal_valuation_etl_bucket" {
  value = module.appraisal_valuation_etl_bucket.bucket_name
}

module "appraisal_valuation_canary_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1"
  name         = "appraisal-valuation-canary-etl"
  project_name = local.project
  region       = var.region
  versioning   = true
  lifecycle_rules = [
    {
      action = { 
        type = "Delete" 
      }
      condition = { 
        with_state = "ARCHIVED" 
        age = 7 
      }
    }
  ]
}

output "appraisal_valuation_canary_etl_bucket" {
  value = module.appraisal_valuation_canary_etl_bucket.bucket_name
}

module "appraisal_cms_canary_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1"
  name         = "appraisal-cms-canary-etl"
  project_name = local.project
  region       = var.region
  versioning   = true
  lifecycle_rules = [
    {
      action = { 
        type = "Delete" 
      }
      condition = { 
        with_state = "ARCHIVED" 
        age = 7 
      }
    }
  ]
}

output "appraisal_cms_canary_etl_bucket" {
  value = module.appraisal_cms_canary_etl_bucket.bucket_name
}

module "auction_data_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1"
  name         = "auction-data-etl"
  project_name = local.project
  region       = var.region
  versioning   = true
  lifecycle_rules = [
    {
      action = { 
        type = "Delete" 
      }
      condition = { 
        with_state = "ARCHIVED" 
        age = 7 
      }
    }
  ]
}

output "auction_data_etl_bucket" {
  value = module.auction_data_etl_bucket.bucket_name
}

module "algo_pricing_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1"
  name         = "algo-pricing"
  project_name = local.project
  region       = var.region
  versioning   = true
  lifecycle_rules = [
    {
      action = { 
        type = "Delete" 
      }
      condition = { 
        with_state = "ARCHIVED" 
        age = 7 
      }
    }
  ]
}

output "algo_pricing_bucket" {
  value = module.algo_pricing_bucket.bucket_name
}

module "auction_values_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1"
  name         = "auction-values-etl"
  project_name = local.project
  region       = var.region
  versioning   = true
  lifecycle_rules = [
    {
      action = { 
        type = "Delete" 
      }
      condition = { 
        with_state = "ARCHIVED" 
        age = 7 
      }
    }
  ]
}

output "auction_values_etl_bucket" {
  value = module.auction_values_etl_bucket.bucket_name
}

module "auctions_scraper_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1"
  name         = "auctions-scraper-etl"
  project_name = local.project
  region       = var.region
  versioning   = true
  lifecycle_rules = [
    {
      action = { 
        type = "Delete" 
      }
      condition = { 
        with_state = "ARCHIVED" 
        age = 7 
      }
    }
  ]
}

output "auctions_scraper_etl_bucket" {
  value = module.auctions_scraper_etl_bucket.bucket_name
}

module "mascus_csv_listings_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "mascus-csv-listings-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "mascus_csv_listings_etl_bucket" {
  value = module.mascus_csv_listings_etl_bucket.bucket_name
}

module "mascus_sales_history_import_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "mascus-sales-history-import"
  project_name = local.project
  region       = var.region
  versioning   = false
  lifecycle_rules = {
    version_retention = {
      condition = {
        age = 30
      }
      action = {
        type          = "SetStorageClass"
        storage_class = "COLDLINE"
      }
    }
  }
}

output "mascus_sales_history_import_bucket" {
  value = module.mascus_sales_history_import_bucket.bucket_name
}


module "inspections_export_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "inspections-export-bucket"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "inspections_export_bucket" {
  value = module.inspections_export_bucket.bucket_name
}

module "inspector_assets_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "inspector-assets"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "inspector_assets_bucket" {
  value = module.inspector_assets_bucket.bucket_name
}

module "rb_equipment_specification_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "rb-equipment-specification-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "rb_equipment_specification_etl_bucket" {
  value = module.rb_equipment_specification_etl_bucket.bucket_name
}

module "rb_competitive_intelligence_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "rb-competitive-intelligence-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "rb_competitive_intelligence_etl_bucket" {
  value = module.rb_competitive_intelligence_etl_bucket.bucket_name
}

module "rouse_rb_taxonomy_mapping_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "rouse-rb-taxonomy-mapping-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "rouse_rb_taxonomy_mapping_etl_bucket" {
  value = module.rouse_rb_taxonomy_mapping_etl_bucket.bucket_name
}

module "data_science_models_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "data-science-models-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "data_science_models_etl_bucket" {
  value = module.data_science_models_etl_bucket.bucket_name
}

module "set_new_ab_cost_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "set-new-ab-cost-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "set_new_ab_cost_etl_bucket" {
  value = module.set_new_ab_cost_etl_bucket.bucket_name
}

module "monitor_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "monitor"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "monitor_bucket" {
  value = module.monitor_bucket.bucket_name
}

module "report_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "report-bucket"
  project_name = local.project
  region       = var.region
  versioning   = false
}

module "stage_report_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "stage-report-bucket"
  project_name = local.project
  region       = var.region
  versioning   = false
}

module "quarantined_report_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "quarantined-dev-report-bucket"
  project_name = local.project
  region       = var.region
  versioning   = false
}

module "unscanned_report_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "unscanned-dev-report-bucket"
  project_name = local.project
  region       = var.region
  versioning   = false
}
module "quarantined_stage_report_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "quarantined-stage-report-bucket"
  project_name = local.project
  region       = var.region
  versioning   = false
}

module "unscanned_stage_report_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "unscanned-stage-report-bucket"
  project_name = local.project
  region       = var.region
  versioning   = false
}

module "cvd_report_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "cvd-report-bucket"
  project_name = local.project
  region       = var.region
  versioning   = false
}


output "report_bucket" {
  value = module.report_bucket.bucket_name
}

module "classification_prediction_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "classification-prediction"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "classification_prediction_bucket" {
  value = module.classification_prediction_bucket.bucket_name
}

module "algo_predictions_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "algo-predictions-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "algo_predictions_etl_bucket" {
  value = module.algo_predictions_etl_bucket.bucket_name
}

module "ml_for_rouse_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "ml-for-rouse-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "ml_for_rouse_etl_bucket" {
  value = module.ml_for_rouse_etl_bucket.bucket_name
}

module "ss_export_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "ss-export"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "ss_export_bucket" {
  value = module.ss_export_bucket.bucket_name
}

module "bq_for_audit_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "bq-for-audit-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "bq_for_audit_bucket" {
  value = module.bq_for_audit_bucket.bucket_name
}

module "smartequip_parts_book_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "smartequip-parts-book-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "smartequip_parts_book_bucket" {
  value = module.smartequip_parts_book_bucket.bucket_name
}

module "analytics_ss_import_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "analytics-ss-import"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "analytics_ss_import_bucket" {
  value = module.analytics_ss_import_bucket.bucket_name
}

module "wellsfargo_data_export_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "wellsfargo-data-export"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "wellsfargo_data_export_bucket" {
  value = module.wellsfargo_data_export_bucket.bucket_name
}

module "dbt_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "dbt_etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "dbt_etl_bucket" {
  value = module.dbt_etl_bucket.bucket_name
}

module "pvac_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "pvac-bucket"
  project_name = local.project
  region       = var.region
  versioning   = false
}

module "pvac_public_bucket" {
  source                                                = "../../modules/storage/public_bucket/v1"
  warning_bucket_allows_public_read_access_to_the_world = true
  name                                                  = "pvac-bucket"
  project_name                                          = local.project
  region                                                = var.region
  versioning                                            = false
  uniform_bucket_level_access                           = true
}

output "pvac_bucket" {
  value = module.pvac_bucket.bucket_name
}

module "smart_equip_ml_classification" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "smart-equip-ml-classification"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "smart_equip_ml_classification" {
  value = module.smart_equip_ml_classification.bucket_name
}

module "appraisals_api_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "appraisals-api"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "appraisals_api_bucket" {
  value = module.appraisals_api_bucket.bucket_name
}

module "price_performance_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "price-performance-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "price_performance_bucket" {
  value = module.price_performance_bucket.bucket_name
}

module "appraisals_boom_and_bucket_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "boom-and-bucket-dev"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "appraisals_boom_and_bucket_bucket" {
  value = module.appraisals_boom_and_bucket_bucket.bucket_name
}

module "valuations_performance_logger_etl_bucket" {
  source       = "../../modules/storage/encrypted_bucket/v1/"
  name         = "valuations-performance-logger-etl"
  project_name = local.project
  region       = var.region
  versioning   = false
}

output "valuations_performance_logger_etl_bucket" {
  value = module.valuations_performance_logger_etl_bucket.bucket_name
}
