# This file is maintained automatically by "terraform init".
# Manual edits may be lost in future updates.

provider "registry.terraform.io/hashicorp/google" {
  version     = "5.0.0"
  constraints = "~> 5.0.0"
  hashes = [
    "h1:nMddLFwfVK+kRPCRDKaSlbe/N2BU8wWYo4U5pKE9rbY=",
    "zh:28ca14826a86d703d1b30474f46d8e4383ddced96e42e8064544bdb1cd0e2200",
    "zh:2cd61ecdfd4b6d787861ccfa593c24a84bc34445c13e3e0ca433fd60128dc1a4",
    "zh:5ad2e73b0eaa349b3dea4deec648cbfb5ba7e8c5353ffe30d5ad2158dea8396a",
    "zh:797295ba11466be70edcf99b9f3c141ea8f883478fdd972152ce735f346b3ac2",
    "zh:7a85dbb8890f9109b3febb760e5f5f16831e31ba2d31523db9da539ccceec6d8",
    "zh:869013bc221a51068f7371f6fa76a796baa092f8eaff46044fce421a177baf06",
    "zh:9397c53bcdfdd641e6d705969b1c54eb6a375c72f45a09650721f422f6d97702",
    "zh:c3c60fd46b5a3132dc90940aa2f6ad65b9d18a48fae8e13166d95ab0dcc56a96",
    "zh:c4f9c44e251f8454969ba088440bbb549428e0eef7c8f1500a6aa712170fe96f",
    "zh:e1e08af2a484a37a22f7603389fdd4220c3dab43164f9f5374fd69efb0bf995e",
    "zh:e9d1c928e797ffbf24722670cf610e15f267c8c1831f725ac912cae81e2ec5b9",
    "zh:f569b65999264a9416862bca5cd2a6177d94ccb0424f3a4ef424428912b9cb3c",
  ]
}

provider "registry.terraform.io/hashicorp/google-beta" {
  version     = "5.0.0"
  constraints = "~> 5.0.0"
  hashes = [
    "h1:ebRIuGrHkLmAuz+Ndlg3a7QTggTqzjmhwAbDqde+qZk=",
    "zh:06cadae6aaa5dba8d1ca3e828b85df972cf966e41df89d6a0f4ebd66f2e796f2",
    "zh:100eef0cfd56d1020142bbca6823e22b1db51f02fa51f3d8389877e13a9da0a7",
    "zh:2a1747d419010e452855aaca5008015cea156e7909e07371c0d496960b8ef27c",
    "zh:3ecbdf71ee0238f2f1876659f713de77be468342f859b574c53853bb20b82070",
    "zh:436135837d52fa285c1e87eefda39d94d6c900936162645286cd75b3c0353a78",
    "zh:599365698a3a0a2ab0b6d3b9cafd95f934244bdd5363afcd160f6e16a0dc7ed7",
    "zh:5d4d7a86959e30d21ee221a8d0913c0e22d2d5480ee7914047400c2e41c90b51",
    "zh:77d2409681616b2a0a7ee195229eccacb2a85a3471c06d609a42acc1f875e0d7",
    "zh:7cb09e942565fcc7e73e912f717f1c35d514b26a0942b39dca4e159c9e725f82",
    "zh:b4f3c1ecc92a69e05926292d84090e351d3a82e1a645abbeeaff8428241c4fc2",
    "zh:ecb96862df8c89ad0c1d528b036183f92746614d33daecce454aec881609ce3f",
    "zh:f569b65999264a9416862bca5cd2a6177d94ccb0424f3a4ef424428912b9cb3c",
  ]
}

provider "registry.terraform.io/hashicorp/random" {
  version = "3.7.2"
  hashes = [
    "h1:hkKSY5xI4R1H4Yrg10HHbtOoxZif2dXa9HFPSbaVg5o=",
    "zh:14829603a32e4bc4d05062f059e545a91e27ff033756b48afbae6b3c835f508f",
    "zh:1527fb07d9fea400d70e9e6eb4a2b918d5060d604749b6f1c361518e7da546dc",
    "zh:1e86bcd7ebec85ba336b423ba1db046aeaa3c0e5f921039b3f1a6fc2f978feab",
    "zh:24536dec8bde66753f4b4030b8f3ef43c196d69cccbea1c382d01b222478c7a3",
    "zh:29f1786486759fad9b0ce4fdfbbfece9343ad47cd50119045075e05afe49d212",
    "zh:4d701e978c2dd8604ba1ce962b047607701e65c078cb22e97171513e9e57491f",
    "zh:78d5eefdd9e494defcb3c68d282b8f96630502cac21d1ea161f53cfe9bb483b3",
    "zh:7b8434212eef0f8c83f5a90c6d76feaf850f6502b61b53c329e85b3b281cba34",
    "zh:ac8a23c212258b7976e1621275e3af7099e7e4a3d4478cf8d5d2a27f3bc3e967",
    "zh:b516ca74431f3df4c6cf90ddcdb4042c626e026317a33c53f0b445a3d93b720d",
    "zh:dc76e4326aec2490c1600d6871a95e78f9050f9ce427c71707ea412a2f2f1a62",
    "zh:eac7b63e86c749c7d48f527671c7aee5b4e26c10be6ad7232d6860167f99dbb0",
  ]
}
