resource "google_project_service" "secret_manager" {
  project            = local.project
  service            = "secretmanager.googleapis.com"
  disable_on_destroy = false
}

# Enable Vertex AI and related services for algo-pricing pipeline integration
resource "google_project_service" "aiplatform" {
  project            = local.project
  service            = "aiplatform.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "artifactregistry" {
  project            = local.project
  service            = "artifactregistry.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "cloudbuild" {
  project            = local.project
  service            = "cloudbuild.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "serviceusage_googleapis_com" {
  project            = local.project
  service            = "serviceusage.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "cloudfunctions_googleapis_com" {
  project            = local.project
  service            = "cloudfunctions.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "aiplatform_googleapis_com" {
  project            = local.project
  service            = "aiplatform.googleapis.com"
  disable_on_destroy = false
}

data "terraform_remote_state" "migration_prod" {
  backend = "gcs"
  config = {
    bucket = "terraform-admin-state-bucket"
    prefix = "migration/migration-prod"
  }
}
