# Vertex AI Integration for algo-pricing Pipeline - Production Environment

This directory contains Terraform configurations for integrating the algo-pricing pipeline with Google Cloud Vertex AI services in the **production** environment.

## 📋 Overview

The Vertex AI integration provides:

- Dedicated service accounts for CircleCI authentication
- Environment-specific GCS buckets for pipeline artifacts
- Proper IAM permissions for Vertex AI operations
- Secure secret management for service account keys

## 🏗️ Infrastructure Components

### Service Accounts

- **Name**: `circleci-vertex-ai-prod@{project-id}.iam.gserviceaccount.com`
- **Purpose**: CircleCI authentication for Vertex AI pipeline operations
- **Key Storage**: Stored securely in Google Secret Manager

### IAM Roles Granted

- `roles/aiplatform.user` - Vertex AI pipeline creation and management
- `roles/storage.objectAdmin` - GCS bucket access
- `roles/artifactregistry.reader` - Container image access
- `roles/serviceusage.serviceUsageConsumer` - Service usage
- `roles/ml.admin` - ML operations
- `roles/logging.logWriter` - Logging
- `roles/monitoring.metricWriter` - Monitoring

### GCS Buckets

| Bucket Purpose | Naming Pattern | Lifecycle | Versioning |
|----------------|----------------|-----------|------------|
| Pipeline Artifacts | `{project}-vertex-ai-artifacts` | 90 days | Enabled |
| Pipeline Definitions | `{project}-vertex-ai-pipeline-definitions` | 90 days | Enabled |
| Model Artifacts | `{project}-vertex-ai-model-artifacts` | 180 days | Enabled |
| Intermediate Data | `{project}-vertex-ai-intermediate-data` | 30 days | Disabled |
| Pipeline Configs | `{project}-vertex-ai-pipeline-configs` | 90 days | Enabled |

## 🚀 Deployment Instructions

### Prerequisites

1. Ensure you have Terraform installed (version >= 1.0)
2. Authenticate with GCP: `gcloud auth application-default login`
3. Set the correct GCP project: `gcloud config set project {project-id}`
4. **PRODUCTION DEPLOYMENT REQUIRES APPROVAL** - Follow change management process

### Deploy Infrastructure

```bash
# Navigate to the algo-vms-prod directory
cd gcp/vms/algo-vms-prod

# Initialize Terraform
terraform init

# Plan the deployment (REQUIRED for production)
terraform plan -out=vertex-ai.tfplan

# Review the plan thoroughly before applying
# Apply the changes (requires approval in production)
terraform apply vertex-ai.tfplan
```

### Verify Deployment

```bash
# Check service account creation
gcloud iam service-accounts list --filter="email:circleci-vertex-ai-prod@*"

# Check bucket creation
gsutil ls -p {project-id} | grep vertex-ai

# Check secret creation
gcloud secrets list --filter="name:circleci-vertex-ai-prod-key"
```

## 🔧 CircleCI Integration

### Environment Variables

After deployment, configure these environment variables in CircleCI:

```bash
# Get the outputs from Terraform
terraform output vertex_ai_circleci_config
```

### Required CircleCI Environment Variables

- `VERTEX_AI_PROJECT_ID` - GCP Project ID
- `VERTEX_AI_REGION` - GCP Region (us-central1)
- `VERTEX_AI_SERVICE_ACCOUNT_EMAIL` - Service account email
- `VERTEX_AI_SERVICE_ACCOUNT_KEY_SECRET` - Secret Manager secret name
- `VERTEX_AI_ARTIFACTS_BUCKET` - Main artifacts bucket
- `VERTEX_AI_PIPELINE_DEFINITIONS_BUCKET` - Pipeline definitions bucket
- `VERTEX_AI_MODEL_ARTIFACTS_BUCKET` - Model artifacts bucket
- `VERTEX_AI_INTERMEDIATE_DATA_BUCKET` - Intermediate data bucket
- `VERTEX_AI_PIPELINE_CONFIGS_BUCKET` - Pipeline configs bucket

### Production CircleCI Configuration

```yaml
version: 2.1

jobs:
  deploy-vertex-ai-pipeline-prod:
    docker:
      - image: google/cloud-sdk:latest
    steps:
      - checkout
      - run:
          name: Authenticate with GCP
          command: |
            echo $VERTEX_AI_SERVICE_ACCOUNT_KEY | base64 -d > /tmp/key.json
            gcloud auth activate-service-account --key-file=/tmp/key.json
            gcloud config set project $VERTEX_AI_PROJECT_ID
      - run:
          name: Deploy Vertex AI Pipeline (Production)
          command: |
            # Production deployment with additional validation
            gcloud ai custom-jobs create --region=$VERTEX_AI_REGION \
              --display-name="algo-pricing-prod-$(date +%Y%m%d-%H%M%S)" \
              --config=pipeline-config.yaml
```

## 🔒 Security Considerations

### Production Security Requirements

- **Service Account Keys**: Rotate keys quarterly
- **Access Reviews**: Monthly access audits required
- **Monitoring**: Enhanced logging and alerting enabled
- **Compliance**: SOC 2 and data privacy requirements

### Service Account Keys

- Keys are stored in Google Secret Manager with automatic encryption
- Access is restricted to the service account itself
- **PRODUCTION**: Implement key rotation schedule
- Consider migrating to Workload Identity Federation

### Bucket Security

- Uniform bucket-level access is enabled
- Lifecycle policies prevent indefinite storage costs
- Access is restricted to the dedicated service account
- **PRODUCTION**: Enable audit logging for all bucket operations

### IAM Best Practices

- Least-privilege access principles applied
- Resource-level IAM bindings where possible
- **PRODUCTION**: Quarterly permission audits required

## 📊 Cost Optimization

### Lifecycle Policies

- **Intermediate Data**: 30 days retention
- **Pipeline Artifacts**: 90 days retention
- **Model Artifacts**: 180 days retention (longer for model versioning)

### Production Cost Controls

- Billing alerts configured at multiple thresholds
- Monthly cost reviews with finance team
- Resource quotas and limits enforced

### Monitoring

- Enable billing alerts for unexpected costs
- Monitor bucket usage with Cloud Monitoring
- Review Vertex AI job costs regularly
- **PRODUCTION**: Daily cost monitoring dashboard

## 🔍 Troubleshooting

### Common Issues

1. **Permission Denied Errors**

   ```bash
   # Check service account permissions
   gcloud projects get-iam-policy {project-id} \
     --flatten="bindings[].members" \
     --filter="bindings.members:serviceAccount:circleci-vertex-ai-prod@*"
   ```

2. **Bucket Access Issues**

   ```bash
   # Test bucket access
   gsutil ls gs://{bucket-name}
   ```

3. **Secret Manager Access**

   ```bash
   # Test secret access
   gcloud secrets versions access latest --secret="circleci-vertex-ai-prod-key"
   ```

### Production Support

For production issues:

1. **IMMEDIATE**: Contact on-call engineer via PagerDuty
2. Check Terraform state: `terraform show`
3. Review GCP logs: `gcloud logging read`
4. Escalate to ML Engineering team lead

### Change Management

All production changes require:

1. Terraform plan review
2. Security team approval
3. Change advisory board (CAB) approval for major changes
4. Rollback plan documentation

## 🚨 Production Alerts

### Monitoring Setup

- Service account usage anomalies
- Bucket access failures
- Vertex AI job failures
- Cost threshold breaches

### Alert Channels

- PagerDuty for critical issues
- Slack #ml-engineering for warnings
- Email for cost alerts

## 📚 Additional Resources

- [Vertex AI Documentation](https://cloud.google.com/vertex-ai/docs)
- [Terraform GCP Provider](https://registry.terraform.io/providers/hashicorp/google/latest/docs)
- [CircleCI GCP Integration](https://circleci.com/docs/2.0/google-cloud-platform/)
- [Production Runbook](internal-link-to-runbook)
- [Incident Response Procedures](internal-link-to-procedures)
