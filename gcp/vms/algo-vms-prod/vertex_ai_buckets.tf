# Vertex AI GCS Buckets for algo-pricing pipeline integration
# This file creates environment-specific buckets for Vertex AI pipeline artifacts

# Generate random suffix for bucket names to ensure global uniqueness
resource "random_id" "vertex_ai_bucket_suffix" {
  byte_length = 4
}

locals {
  # Bucket naming convention: algo-pricing-vertex-{environment}-{random-suffix}
  vertex_ai_bucket_name = "algo-pricing-vertex-${var.environment}-${random_id.vertex_ai_bucket_suffix.hex}"
  
  # Lifecycle policy for cost optimization - delete objects older than 90 days
  vertex_ai_lifecycle_rules = [
    {
      condition = {
        age = 90
      }
      action = {
        type = "Delete"
      }
    },
    {
      condition = {
        age                        = 30
        days_since_noncurrent_time = 7
      }
      action = {
        type = "Delete"
      }
    }
  ]
}

# Main Vertex AI artifacts bucket
module "vertex_ai_artifacts_bucket" {
  source                      = "../../modules/storage/bucket/v1/"
  name                        = "vertex-ai-artifacts"
  project_name                = local.project
  region                      = var.region
  versioning                  = true
  uniform_bucket_level_access = true
  lifecycle_rules             = local.vertex_ai_lifecycle_rules

  labels = {
    environment = var.environment
    service     = "vertex-ai"
    purpose     = "pipeline-artifacts"
    division    = "rbval"
    managed_by  = "terraform"
  }

  # Don't pass members here - handle IAM separately to avoid dependency issues
  members = []
}

# Pipeline definitions bucket for compiled pipeline artifacts
module "vertex_ai_pipeline_definitions_bucket" {
  source                      = "../../modules/storage/bucket/v1/"
  name                        = "vertex-ai-pipeline-definitions"
  project_name                = local.project
  region                      = var.region
  versioning                  = true
  uniform_bucket_level_access = true
  lifecycle_rules             = local.vertex_ai_lifecycle_rules

  labels = {
    environment = var.environment
    service     = "vertex-ai"
    purpose     = "pipeline-definitions"
    division    = "rbval"
    managed_by  = "terraform"
  }

  # Don't pass members here - handle IAM separately to avoid dependency issues
  members = []
}

# Model artifacts bucket for trained model outputs
module "vertex_ai_model_artifacts_bucket" {
  source                      = "../../modules/storage/bucket/v1/"
  name                        = "vertex-ai-model-artifacts"
  project_name                = local.project
  region                      = var.region
  versioning                  = true
  uniform_bucket_level_access = true

  # Extended lifecycle for model artifacts (keep for 180 days)
  lifecycle_rules = [
    {
      condition = {
        age = 180
      }
      action = {
        type = "Delete"
      }
    }
  ]

  labels = {
    environment = var.environment
    service     = "vertex-ai"
    purpose     = "model-artifacts"
    division    = "rbval"
    managed_by  = "terraform"
  }

  # Don't pass members here - handle IAM separately to avoid dependency issues
  members = []
}

# Intermediate data bucket for temporary processing files
module "vertex_ai_intermediate_data_bucket" {
  source                      = "../../modules/storage/bucket/v1/"
  name                        = "vertex-ai-intermediate-data"
  project_name                = local.project
  region                      = var.region
  versioning                  = false
  uniform_bucket_level_access = true

  # Shorter lifecycle for intermediate data (keep for 30 days)
  lifecycle_rules = [
    {
      condition = {
        age = 30
      }
      action = {
        type = "Delete"
      }
    }
  ]

  labels = {
    environment = var.environment
    service     = "vertex-ai"
    purpose     = "intermediate-data"
    division    = "rbval"
    managed_by  = "terraform"
  }

  # Don't pass members here - handle IAM separately to avoid dependency issues
  members = []
}

# Pipeline configurations bucket for configuration and parameter files
module "vertex_ai_pipeline_configs_bucket" {
  source                      = "../../modules/storage/bucket/v1/"
  name                        = "vertex-ai-pipeline-configs"
  project_name                = local.project
  region                      = var.region
  versioning                  = true
  uniform_bucket_level_access = true
  lifecycle_rules             = local.vertex_ai_lifecycle_rules

  labels = {
    environment = var.environment
    service     = "vertex-ai"
    purpose     = "pipeline-configs"
    division    = "rbval"
    managed_by  = "terraform"
  }

  # Don't pass members here - handle IAM separately to avoid dependency issues
  members = []
}

# Grant IAM permissions to CircleCI service account for all buckets
# Using separate resources to avoid dependency issues with for_each

resource "google_storage_bucket_iam_member" "vertex_ai_artifacts_bucket_admin" {
  bucket = module.vertex_ai_artifacts_bucket.bucket_name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${google_service_account.circleci_vertex_ai.email}"
}

resource "google_storage_bucket_iam_member" "vertex_ai_pipeline_definitions_bucket_admin" {
  bucket = module.vertex_ai_pipeline_definitions_bucket.bucket_name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${google_service_account.circleci_vertex_ai.email}"
}

resource "google_storage_bucket_iam_member" "vertex_ai_model_artifacts_bucket_admin" {
  bucket = module.vertex_ai_model_artifacts_bucket.bucket_name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${google_service_account.circleci_vertex_ai.email}"
}

resource "google_storage_bucket_iam_member" "vertex_ai_intermediate_data_bucket_admin" {
  bucket = module.vertex_ai_intermediate_data_bucket.bucket_name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${google_service_account.circleci_vertex_ai.email}"
}

resource "google_storage_bucket_iam_member" "vertex_ai_pipeline_configs_bucket_admin" {
  bucket = module.vertex_ai_pipeline_configs_bucket.bucket_name
  role   = "roles/storage.objectAdmin"
  member = "serviceAccount:${google_service_account.circleci_vertex_ai.email}"
}

# Grant additional bucket-level IAM permissions for logging and monitoring
resource "google_storage_bucket_iam_member" "vertex_ai_artifacts_bucket_reader" {
  bucket = module.vertex_ai_artifacts_bucket.bucket_name
  role   = "roles/storage.legacyBucketReader"
  member = "serviceAccount:${google_service_account.circleci_vertex_ai.email}"
}

resource "google_storage_bucket_iam_member" "vertex_ai_pipeline_definitions_bucket_reader" {
  bucket = module.vertex_ai_pipeline_definitions_bucket.bucket_name
  role   = "roles/storage.legacyBucketReader"
  member = "serviceAccount:${google_service_account.circleci_vertex_ai.email}"
}

resource "google_storage_bucket_iam_member" "vertex_ai_model_artifacts_bucket_reader" {
  bucket = module.vertex_ai_model_artifacts_bucket.bucket_name
  role   = "roles/storage.legacyBucketReader"
  member = "serviceAccount:${google_service_account.circleci_vertex_ai.email}"
}

resource "google_storage_bucket_iam_member" "vertex_ai_intermediate_data_bucket_reader" {
  bucket = module.vertex_ai_intermediate_data_bucket.bucket_name
  role   = "roles/storage.legacyBucketReader"
  member = "serviceAccount:${google_service_account.circleci_vertex_ai.email}"
}

resource "google_storage_bucket_iam_member" "vertex_ai_pipeline_configs_bucket_reader" {
  bucket = module.vertex_ai_pipeline_configs_bucket.bucket_name
  role   = "roles/storage.legacyBucketReader"
  member = "serviceAccount:${google_service_account.circleci_vertex_ai.email}"
}
