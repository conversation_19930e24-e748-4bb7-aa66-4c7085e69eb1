data "terraform_remote_state" "shared_network" {
  backend = "gcs"

  config = {
    bucket = "terraform-admin-state-bucket"
    prefix = "migration/network-prod/"
  }
}

locals {
  network             = module.econic_data_prod_network.network_link
  subnetwork          = module.econic_data_prod_network.subnet_link
  shared_network_link = data.terraform_remote_state.shared_network.outputs.shared_network_id
}

module "econic_data_prod_network" {
  source           = "../../modules/networking/network-regular/v1"
  region           = var.region
  base_ip_range    = "*********/16"
  ip_range_number  = 32
  name             = "econic-data-${var.environment}"
  enable_flow_logs = true
}

output "econic_data_prod_network_id" {
  value = module.econic_data_prod_network.network_link
}

module "vpn_prod_network_peering" {
  source = "../../modules/networking/peering/v1"
  src    = local.shared_network_link
  dst    = local.network
}

resource "google_compute_address" "econic-data-prod-nat-ip" {
  count   = 2
  project = local.project
  name    = "econic-data-prod-nat-ip-${count.index}"
}

module "econic_data_prod_network_nat" {
  source     = "../../modules/networking/nat/v1"
  name       = "econic-data-network-prod-nat"
  region     = var.region
  network_id = module.econic_data_prod_network.network_link
  nat_ips    = google_compute_address.econic-data-prod-nat-ip[*].self_link
}

output "econic_data_prod_nat_ips" {
  value = google_compute_address.econic-data-prod-nat-ip[*].address
}
