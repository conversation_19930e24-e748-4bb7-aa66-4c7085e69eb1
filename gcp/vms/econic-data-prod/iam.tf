// ABOUTME: This file defines IAM bindings for the econic-data-prod project
// ABOUTME: Grants <NAME_EMAIL> group for compute and BigQuery operations

locals {
  econic_group_roles = [
    "roles/compute.viewer",
    "roles/compute.osLogin",
    "roles/compute.instanceAdmin.v1",
    "roles/bigquery.jobUser",
    "roles/bigquery.dataOwner",
    "roles/iam.serviceAccountUser"
  ]
}

resource "google_project_iam_binding" "econic_group_permissions" {
  for_each = toset(local.econic_group_roles)
  
  project = local.project
  role    = each.value
  
  members = [
    "group:<EMAIL>",
  ]
}

resource "google_folder_iam_member" "econic_analytics_bigquery_viewer" {
  folder = "**********"
  role   = "roles/bigquery.dataViewer"
  member = "group:<EMAIL>"
}