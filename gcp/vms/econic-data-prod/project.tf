// Warning: This is an auto generated file.

variable "region" {
  default = "us-central1"
}

locals {
  folder_name            = "vms"
  project_name           = "econic-data-prod"
  project                = data.terraform_remote_state.project_structure.outputs.projects["econic-data-prod"]
  terraform_state_key_id = data.terraform_remote_state.terraform_admin.outputs.state_key_link
}

variable "environment" {
  default = "prod"
}

data "terraform_remote_state" "project_structure" {
  backend = "gcs"
  config = {
    bucket = "terraform-admin-state-bucket"
    prefix = "global/project-structure"
  }
}

data "terraform_remote_state" "terraform_admin" {
  backend = "gcs"
  config = {
    bucket = "terraform-admin-state-bucket"
    prefix = "infrastructure/terraform-admin"
  }
}

provider "google" {
  region  = var.region
  project = local.project
}

provider "google-beta" {
  region  = var.region
  project = local.project
}

output "project" {
  value = local.project
}
