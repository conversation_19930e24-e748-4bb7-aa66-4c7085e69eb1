resource "google_compute_address" "econic_data_server_address" {
  name         = "econic-data-server-prod"
  project      = local.project
  address_type = "INTERNAL"
  subnetwork   = local.subnetwork
}

module "econic_data_server" {
  source       = "../../modules/compute/compute-instance/v1"
  project      = local.project
  name         = "econic-data-server-prod"
  machine_type = "n1-standard-4"
  zone         = "us-central1-b"
  image        = "projects/images-4a3fb6/global/images/windows-server-2019-base-1699297372"
  network_ip   = google_compute_address.econic_data_server_address.address
  subnetwork   = local.subnetwork
  disk_size    = 200
  disk_type    = "pd-balanced"
  tags = [
    "rdp-internal",
    "winrm-internal"
  ]
  can_ip_forward         = false
  environment            = var.environment
  block_project_ssh_keys = false
  additional_labels = {
    division = "shared"
    role     = "data_analysis"
  }
}

