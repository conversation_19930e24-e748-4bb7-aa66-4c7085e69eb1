locals {
  enable_firewall_logs = true
}

resource "google_compute_firewall" "rdp-internal" {
  name    = "rdp-internal"
  network = local.network

  allow {
    protocol = "tcp"
    ports    = ["3389"]
  }

  target_tags   = ["rdp-internal"]
  source_ranges = ["10.0.0.0/8"]

  dynamic "log_config" {
    for_each = local.enable_firewall_logs == true ? [1] : []
    content {
      metadata = "INCLUDE_ALL_METADATA"
    }
  }
}

resource "google_compute_firewall" "allow-rdp-through-iap" {
  name    = "allow-rdp-through-iap"
  network = local.network

  allow {
    protocol = "tcp"
    ports    = ["3389"]
  }

  source_ranges = ["35.235.240.0/20"]
  target_tags   = ["rdp-through-iap"]

  dynamic "log_config" {
    for_each = local.enable_firewall_logs == true ? [1] : []
    content {
      metadata = "INCLUDE_ALL_METADATA"
    }
  }
}

resource "google_compute_firewall" "winrm-internal" {
  name    = "winrm-internal"
  network = local.network

  allow {
    protocol = "tcp"
    ports    = ["5986"]
  }

  target_tags   = ["winrm-internal"]
  source_ranges = ["10.0.0.0/8"]

  dynamic "log_config" {
    for_each = local.enable_firewall_logs == true ? [1] : []
    content {
      metadata = "INCLUDE_ALL_METADATA"
    }
  }
}

resource "google_compute_firewall" "icmp-internal" {
  name    = "icmp-internal"
  network = local.network

  allow {
    protocol = "icmp"
  }

  source_ranges = ["10.0.0.0/8"]

  dynamic "log_config" {
    for_each = local.enable_firewall_logs == true ? [1] : []
    content {
      metadata = "INCLUDE_ALL_METADATA"
    }
  }
}

resource "google_compute_firewall" "fortigate-allow-all" {
  name    = "fortigate-allow-all"
  network = local.network

  priority = 1000

  source_ranges = ["10.129.8.63/32", "10.97.16.0/24"]

  allow {
    protocol = "tcp"
    ports    = ["0-65535"]
  }

  allow {
    protocol = "udp"
    ports    = ["0-65535"]
  }

  allow {
    protocol = "icmp"
  }

  dynamic "log_config" {
    for_each = local.enable_firewall_logs == true ? [1] : []
    content {
      metadata = "INCLUDE_ALL_METADATA"
    }
  }
}

resource "google_compute_firewall" "shared-net-deny-all" {
  name    = "shared-net-deny-all"
  network = local.network

  priority = 1500

  source_ranges = ["10.129.8.0/22", "10.130.8.0/22"]

  deny {
    protocol = "tcp"
    ports    = ["0-65535"]
  }

  deny {
    protocol = "udp"
    ports    = ["0-65535"]
  }

  deny {
    protocol = "icmp"
  }

  dynamic "log_config" {
    for_each = local.enable_firewall_logs == true ? [1] : []
    content {
      metadata = "INCLUDE_ALL_METADATA"
    }
  }
}

resource "google_compute_firewall" "shared-net-deny-all-destination" {
  name    = "shared-net-deny-all-destination"
  network = local.network

  priority = 1500

  direction = "EGRESS"

  destination_ranges = ["10.129.8.0/22", "10.130.8.0/22"]

  deny {
    protocol = "tcp"
    ports    = ["0-65535"]
  }

  deny {
    protocol = "udp"
    ports    = ["0-65535"]
  }

  deny {
    protocol = "icmp"
  }

  dynamic "log_config" {
    for_each = local.enable_firewall_logs == true ? [1] : []
    content {
      metadata = "INCLUDE_ALL_METADATA"
    }
  }
}
