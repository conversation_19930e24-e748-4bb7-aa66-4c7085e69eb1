locals {
  org_id                               = "************"
  pubsub_qradar_service_account_email  = data.terraform_remote_state.gcp_migration.outputs.pubsub_qradar_service_account_email
  pubsub_tenable_service_account_email = data.terraform_remote_state.terraform_admin.outputs.pubsub_tenable_service_account_email
}

module "pubsub_qradar" {
  source      = "../../modules/events/pubsub/v1"
  name        = "qradar"
  environment = var.environment
  division    = "rbcompliance"
  role        = "events"
  project     = local.project
  region      = var.region
  subscribers = [
    "serviceAccount:${local.pubsub_qradar_service_account_email}"
  ]
}

resource "google_project_iam_member" "qradar_service_account" {
  project = local.project
  role    = "roles/pubsub.subscriber"
  member  = "serviceAccount:${local.pubsub_qradar_service_account_email}"
}

resource "google_pubsub_subscription" "pubsub_qradar" {
  name  = "qradar-subscription"
  topic = module.pubsub_qradar.pubsub_topic
  labels = {
    name        = "qradar-subscription"
    environment = var.environment
    division    = "rbcompliance"
    role        = "events"
  }
  retain_acked_messages = false
  ack_deadline_seconds  = 60
}

module "rouseservices_gcp_audit_logs" {
  source         = "../../modules/logging/pubsub-export/organization_log_sink/v1"
  name           = "qradar"
  org_id         = local.org_id
  environment    = var.environment
  pubsub_topic   = module.pubsub_qradar.pubsub_topic
  pubsub_project = local.project
  log_filter     = "organizations/${local.org_id}/logs/cloudaudit.googleapis.com%2Factivity OR organizations/${local.org_id}/logs/cloudaudit.googleapis.com%2Fdata_access OR organizations/${local.org_id}/logs/cloudaudit.googleapis.com%2Fsystem_event OR organizations/${local.org_id}/logs/cloudaudit.googleapis.com%2Fpolicy"
}

module "pubsub_tenable_cs_2" {
  source      = "../../modules/events/pubsub/v1"
  name        = "tenable-audit-logs"
  custom_name = "tenable-audit-logs" #Tenable CS 2.0 integration expects this name. Please don't change it
  environment = var.environment
  division    = "rbcompliance"
  role        = "events"
  project     = local.project
  region      = var.region
  subscribers = [
    "serviceAccount:${local.pubsub_tenable_service_account_email}",
    "serviceAccount:<EMAIL>"
  ]
}

resource "google_pubsub_subscription" "tenable_cs_2_pubsub_topic" {
  name  = "tenable-audit-logs-sub"
  topic = module.pubsub_tenable_cs_2.pubsub_topic
  labels = {
    name        = "tenable-audit-logs-sub"
    environment = var.environment
    division    = "rbcompliance"
    role        = "events"
  }
  retain_acked_messages      = false
  message_retention_duration = "${3 * 24 * pow(60, 2)}s" #3 days
  ack_deadline_seconds       = 60
  retry_policy {
    minimum_backoff = "10s"
    maximum_backoff = "600s"
  }
  expiration_policy {
    ttl = ""
  }
}


resource "google_pubsub_subscription_iam_binding" "subscriber" {
  subscription = google_pubsub_subscription.tenable_cs_2_pubsub_topic.name
  role         = "roles/pubsub.subscriber"
  members = [
    "serviceAccount:${data.terraform_remote_state.terraform_admin.outputs.pubsub_tenable_service_account_email}",
  ]
}


module "rouseservices_tenable_gcp_audit_logs" {
  source           = "../../modules/logging/pubsub-export/organization_log_sink/v1"
  name             = "tenable-audit-logs"
  custom_name      = "tenable-audit-logs" #Tenable CS 2.0 integration expects this name. Please don't change it
  org_id           = local.org_id
  include_children = true
  environment      = var.environment
  pubsub_topic     = module.pubsub_tenable_cs_2.pubsub_topic
  pubsub_project   = local.project
  log_filter       = "LOG_ID(cloudaudit.googleapis.com/activity) OR LOG_ID(cloudaudit.googleapis.com/data_access) OR LOG_ID(cloudaudit.googleapis.com/policy)"
  exclusions = [
    {
      disabled    = false
      description = "exclude-k8s-internal"
      filter      = "protoPayload.authenticationInfo.principalEmail-:* OR protoPayload.authenticationInfo.principalEmail=~\"^system:\" OR protoPayload.authenticationInfo.principalEmail=~\"@container-engine-robot.iam.gserviceaccount.com$\" OR protoPayload.authenticationInfo.principalEmail=~\"@security-center-api.iam.gserviceaccount.com$\""
      name        = "exclude-k8s-internal"
    }
  ]
}

