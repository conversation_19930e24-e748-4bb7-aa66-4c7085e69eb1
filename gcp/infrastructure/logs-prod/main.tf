resource "google_project_service" "bigquery" {
  service            = "bigquery.googleapis.com"
  disable_on_destroy = false
}

data "terraform_remote_state" "gcp_migration" {
  backend = "gcs"
  config = {
    bucket = "terraform-admin-state-bucket"
    prefix = "migration/migration-${var.environment}/"
  }
}

resource "google_service_usage_consumer_quota_override" "bigquery_usage_per_day_per_user" {
  provider       = google-beta
  project        = local.project
  service        = "bigquery.googleapis.com"
  metric         = urlencode("bigquery.googleapis.com/quota/query/usage")
  limit          = urlencode("/d/project/user")
  override_value = 10000000 #mb
  force          = true     # Otherwise the update will fail if the new value from override_value differs from the current value by more than 10%
}