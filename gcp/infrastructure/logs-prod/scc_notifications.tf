

resource "google_scc_notification_config" "public_bucket_notification_config" {
  config_id    = "public-bucket-config"
  organization = local.org_id
  description  = "Cloud Security Command Center Public Bucket Configuration"
  pubsub_topic = module.pubsub_qradar.pubsub_id

  streaming_config {
    filter = "state=\"ACTIVE\" AND NOT mute=\"MUTED\" AND category=\"PUBLIC_BUCKET_ACL\""
  }
}

resource "google_scc_notification_config" "public_bigquery_dataset_notification_config" {
  config_id    = "public-bq-dataset-config"
  organization = local.org_id
  description  = "Cloud Security Command Center Public BigQuery Dataset"
  pubsub_topic = module.pubsub_qradar.pubsub_id

  streaming_config {
    filter = "state=\"ACTIVE\" AND NOT mute=\"MUTED\" AND category=\"PUBLIC_DATASET\""
  }
}
