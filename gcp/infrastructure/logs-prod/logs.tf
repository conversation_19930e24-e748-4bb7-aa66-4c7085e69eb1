locals {
  vms_dev           = data.terraform_remote_state.project_structure.outputs.projects["vms-dev"]
  vms_prod          = data.terraform_remote_state.project_structure.outputs.projects["vms-prod"]
  platform_services = data.terraform_remote_state.project_structure.outputs.projects["services-${var.environment}"]
  shared_network    = "rouse-shared-network-prod"
  sales_data        = data.terraform_remote_state.project_structure.outputs.projects["sales-data-${var.environment}"]

  DAY_IN_MS = 24 * pow(60, 2) * 1000

  // Network Traffic Log Streaming to BigQuery
  vpc_flows_enabled   = true
  vpn_traffic_enabled = true
  vpc_flows_days      = 45
}

data "google_project" "logs_prod" {
  project_id = data.terraform_remote_state.project_structure.outputs.projects["logs-prod"]
}

variable "shared_network_prod" {
  default = "rouse-shared-network-prod"
}

variable "gcp_migration_prod" {
  default = "rouse-gcp-migration-prod"
}

variable "gcp_migration_dev" {
  default = "rouse-gcp-migration-dev"
}

variable "ftp_instance_name" {
  default = "gftp01"
}

module "gftp01_firewall_logs" {
  source           = "../../modules/logging/bigquery-export/v1"
  bigquery_project = local.project
  sink_project     = var.shared_network_prod
  name             = "${var.shared_network_prod}-${var.ftp_instance_name}"
  log_filter       = "logName:(projects/rouse-shared-network-prod/logs/compute.googleapis.com%2Ffirewall) AND jsonPayload.rule_details.reference:(\"network:shared-net/firewall:shared-net-allow-ftp\")"
  environment      = "dev"
  division         = "shared"
}

module "migration_production_lb_logs" {
  source           = "../../modules/logging/bigquery-export/v1"
  bigquery_project = local.project
  sink_project     = var.gcp_migration_prod
  name             = "${var.gcp_migration_prod}-lbs"
  log_filter       = "resource.type=\"http_load_balancer\""
  environment      = var.environment
  division         = "shared"
}

module "services_nginx_ingress_production_logs" {
  source           = "../../modules/logging/bigquery-export/v1"
  bigquery_project = local.project
  sink_project     = local.platform_services
  name             = "${local.platform_services}-nginx-ingress"
  log_filter       = "resource.type=\"k8s_container\" resource.labels.cluster_name=\"services-prod\" resource.labels.namespace_name=\"default\" resource.labels.container_name=\"nginx-ingress-controller\""
  environment      = var.environment
  division         = "platform"
}

module "services_sales_portal_logs" {
  source           = "../../modules/logging/bigquery-export/v1"
  bigquery_project = local.project
  sink_project     = local.platform_services
  name             = "${local.platform_services}-sales-portal"
  log_filter       = "resource.type=\"container\" resource.labels.cluster_name=\"services-prod\" resource.labels.namespace_id=\"default\""
  environment      = var.environment
  division         = "sales"
}

module "services_production_http_lb_logs" {
  source           = "../../modules/logging/bigquery-export/v1"
  bigquery_project = local.project
  sink_project     = local.platform_services
  name             = "${local.platform_services}-https-lbs"
  log_filter       = "resource.type=\"http_load_balancer\""
  environment      = var.environment
  division         = "platform"
}

module "migration_production_nightly_valuation_logs" {
  source           = "../../modules/logging/bigquery-export/v1"
  bigquery_project = local.project
  sink_project     = var.gcp_migration_prod
  name             = "${var.gcp_migration_prod}-nightly-valuation-logs"
  log_filter       = "resource.type=\"gce_instance\" resource.labels.instance_id=\"804774756316274161\" logName=\"projects/rouse-gcp-migration-prod/logs/NightlyValuationLogs\""
  environment      = var.environment
  division         = "sales"
}

module "migration_production_nightly_event_logs" {
  source           = "../../modules/logging/bigquery-export/v1"
  bigquery_project = local.project
  sink_project     = var.gcp_migration_prod
  name             = "${var.gcp_migration_prod}-nightly-event-logs"
  log_filter       = "resource.type=\"global\" logName=\"projects/rouse-gcp-migration-prod/logs/sales-nightly\""
  environment      = var.environment
  division         = "sales"
}

module "vms_prod_nightly_valuation_logs_shard1" {
  source           = "../../modules/logging/bigquery-export/v1"
  bigquery_project = local.project
  sink_project     = local.vms_prod
  name             = "${local.vms_prod}-nightly-valuation-logs-gdb02-shard1"
  log_filter       = "resource.type=\"gce_instance\" resource.labels.instance_id=\"6068407806349677552\" logName=\"projects/vms-prod-362186/logs/NightlyValuationLogs\""
  environment      = var.environment
  division         = "sales"
}

module "vms_prod_nightly_valuation_logs_shard2" {
  source           = "../../modules/logging/bigquery-export/v1"
  bigquery_project = local.project
  sink_project     = local.vms_prod
  name             = "${local.vms_prod}-nightly-valuation-logs-gdb02-shard2"
  log_filter       = "resource.type=\"gce_instance\" resource.labels.instance_id=\"1548319211119639717\" logName=\"projects/vms-prod-362186/logs/NightlyValuationLogs\""
  environment      = var.environment
  division         = "sales"
}

module "vms_prod_nightly_valuation_logs_shard3" {
  source           = "../../modules/logging/bigquery-export/v1"
  bigquery_project = local.project
  sink_project     = local.vms_prod
  name             = "${local.vms_prod}-nightly-valuation-logs-gdb02-shard3"
  log_filter       = "resource.type=\"gce_instance\" resource.labels.instance_id=\"5455482601311946581\" logName=\"projects/vms-prod-362186/logs/NightlyValuationLogs\""
  environment      = var.environment
  division         = "sales"
}

module "vms_dev_nightly_valuation_logs_shard" {
  source           = "../../modules/logging/bigquery-export/v1"
  bigquery_project = local.project
  sink_project     = local.vms_dev
  name             = "${local.vms_dev}-nightly-valuation-logs-gdb02-shard"
  log_filter       = "resource.type=\"gce_instance\" resource.labels.instance_id=\"6041886542209788062\" logName=\"projects/vms-dev-39fe7a/logs/NightlyValuationLogs\""
  environment      = var.environment
  division         = "sales"
}

module "vms_dev_nightly_event_logs" {
  source           = "../../modules/logging/bigquery-export/v1"
  bigquery_project = local.project
  sink_project     = local.vms_dev
  name             = "${local.vms_dev}-nightly-event-logs"
  log_filter       = "resource.type=\"global\" logName=\"projects/${local.vms_dev}/logs/sales-nightly\""
  environment      = "dev"
  division         = "sales"
}

module "vms_prod_nightly_event_logs" {
  source           = "../../modules/logging/bigquery-export/v1"
  bigquery_project = local.project
  sink_project     = local.vms_prod
  name             = "${local.vms_prod}-nightly-event-logs"
  log_filter       = "resource.type=\"global\" logName=\"projects/${local.vms_prod}/logs/sales-nightly\""
  environment      = var.environment
  division         = "sales"
}

module "migration_dev_nightly_event_logs" {
  source           = "../../modules/logging/bigquery-export/v1"
  bigquery_project = local.project
  sink_project     = var.gcp_migration_dev
  name             = "${var.gcp_migration_dev}-nightly-event-logs"
  log_filter       = "resource.type=\"global\" logName=\"projects/${var.gcp_migration_dev}/logs/sales-nightly\""
  environment      = "dev"
  division         = "sales"
}

module "migration_dev_rental_analytics_metrics_benchmark" {
  source           = "../../modules/logging/bigquery-export/v1"
  bigquery_project = local.project
  sink_project     = var.gcp_migration_dev
  name             = "${var.gcp_migration_dev}-rental-analytics-metrics-benchmark"
  log_filter       = "resource.type=\"global\" logName=\"projects/rouse-gcp-migration-dev/logs/rental-analytics-metrics-benchmark\""
  environment      = "dev"
  division         = "analytics"
}

module "migration_prod_rental_analytics_metrics_benchmark" {
  source           = "../../modules/logging/bigquery-export/v1"
  bigquery_project = local.project
  sink_project     = var.gcp_migration_prod
  name             = "${var.gcp_migration_prod}-rental-analytics-metrics-benchmark"
  log_filter       = "resource.type=\"global\" logName=\"projects/rouse-gcp-migration-prod/logs/rental-analytics-metrics-benchmark\""
  environment      = var.environment
  division         = "analytics"
}

module "gke_error_event_logs" {
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = local.platform_services
  name                   = "${local.platform_services}-gke-error-event-logs"
  log_filter             = "resource.type=\"k8s_pod\" resource.labels.project_id=\"${local.platform_services}\" log_name=\"projects/${local.platform_services}/logs/events\" jsonPayload.message:(error OR fail)"
  environment            = var.environment
  division               = "infrastructure"
  use_partitioned_tables = true
}

module "cloudsql_error_logs" {
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = local.platform_services
  name                   = "${local.platform_services}-cloudsql-error-logs"
  log_filter             = "resource.type=\"cloudsql_database\" log_name=(\"projects/${local.platform_services}/logs/cloudsql.googleapis.com%2Fpostgres.log\" OR \"projects/${local.platform_services}/logs/cloudsql.googleapis.com%2Fsqlserver.err\") severity>=ERROR"
  environment            = var.environment
  division               = "infrastructure"
  use_partitioned_tables = true
}

module "vms_prod_erp_sync_logs" {
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = local.vms_prod
  name                   = "${local.vms_prod}-erp-sync-event-logs"
  log_filter             = "logName=\"projects/${local.vms_prod}/logs/erp-sync\""
  use_partitioned_tables = true
  environment            = var.environment
  division               = "sales"
}

module "vms_prod_ims_fleet_ingest_logs" {
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = local.vms_prod
  name                   = "${local.vms_prod}-ims-fleet-ingest-event-logs"
  log_filter             = "logName=\"projects/${local.vms_prod}/logs/ims-fleet-ingest\""
  use_partitioned_tables = true
  environment            = var.environment
  division               = "platform"
}

module "vms_prod_ims_equipment_options_etl_logs" {
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = local.vms_prod
  name                   = "${local.vms_prod}-equipment-options-etl-event-logs"
  log_filter             = "logName=\"projects/${local.vms_prod}/logs/equipment-options-etl\""
  use_partitioned_tables = true
  environment            = var.environment
  division               = "platform"
}

module "vms_prod_manage_equipment_etl_logs" {
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = local.vms_prod
  name                   = "${local.vms_prod}-manage-equipment-etl-event-logs"
  log_filter             = "logName=\"projects/${local.vms_prod}/logs/manage-equipment-etl\""
  use_partitioned_tables = true
  environment            = var.environment
  division               = "platform"
}

module "vms_prod_equipment_valuations_etl_logs" {
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = local.vms_prod
  name                   = "${local.vms_prod}-equipment-valuations-etl-event-logs"
  log_filter             = "logName=\"projects/${local.vms_prod}/logs/equipment-valuations-etl\""
  use_partitioned_tables = true
  environment            = var.environment
  division               = "platform"
}

module "platform_services_ims_fleet_refresh_logs" {
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = local.platform_services
  name                   = "${local.platform_services}-ims-fleet-refresh-event-logs"
  log_filter             = "logName=\"projects/${local.platform_services}/logs/ims-fleet-refresh\""
  use_partitioned_tables = true
  environment            = var.environment
  division               = "platform"
}

module "platform_services_vpc_flows_logs" {
  count                           = local.vpc_flows_enabled ? 1 : 0
  source                          = "../../modules/logging/bigquery-export/v1"
  bigquery_project                = local.project
  sink_project                    = local.platform_services
  name                            = "${local.platform_services}_vpc_flows_logs"
  log_filter                      = "resource.type=\"gce_subnetwork\" logName=\"projects/${local.platform_services}/logs/compute.googleapis.com%2Fvpc_flows\""
  use_partitioned_tables          = true
  default_partition_expiration_ms = local.vpc_flows_days * local.DAY_IN_MS
  environment                     = var.environment
  division                        = "infrastructure"
}

module "vms_prod_vpc_flows_logs" {
  count                           = local.vpc_flows_enabled ? 1 : 0
  source                          = "../../modules/logging/bigquery-export/v1"
  bigquery_project                = local.project
  sink_project                    = local.vms_prod
  name                            = "${local.vms_prod}_vpc_flows_logs"
  log_filter                      = "resource.type=\"gce_subnetwork\" logName=\"projects/${local.vms_prod}/logs/compute.googleapis.com%2Fvpc_flows\""
  use_partitioned_tables          = true
  default_partition_expiration_ms = local.vpc_flows_days * local.DAY_IN_MS
  environment                     = var.environment
  division                        = "infrastructure"
}

module "platform_services_vpn_gateway_traffic" {
  count                  = local.vpn_traffic_enabled ? 1 : 0
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = local.platform_services
  name                   = "${local.platform_services}_vpn_gateway_traffic"
  log_filter             = "resource.type=\"vpn_gateway\" severity=DEBUG textPayload=~\"^(received|sending) packet:\""
  use_partitioned_tables = true
  environment            = var.environment
  division               = "infrastructure"
}

module "vms_prod_vpn_gateway_traffic" {
  count                  = local.vpn_traffic_enabled ? 1 : 0
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = local.vms_prod
  name                   = "${local.vms_prod}_vpn_gateway_traffic"
  log_filter             = "resource.type=\"vpn_gateway\" severity=DEBUG textPayload=~\"^(received|sending) packet:\""
  use_partitioned_tables = true
  environment            = var.environment
  division               = "infrastructure"
}

module "platform_services_equipment_view_publisher_logs" {
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = local.platform_services
  name                   = "${local.platform_services}-equipment-view-publisher-event-logs"
  log_filter             = "logName=\"projects/${local.platform_services}/logs/equipment-view-publisher\""
  use_partitioned_tables = true
  environment            = var.environment
  division               = "platform"
}

module "platform_services_ims_fleet_refresh_mt_logs" {
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = local.platform_services
  name                   = "${local.platform_services}-ims-fleet-refresh-mt-event-logs"
  log_filter             = "logName=\"projects/${local.platform_services}/logs/ims-fleet-refresh-mt\""
  use_partitioned_tables = true
  environment            = var.environment
  division               = "platform"
}

resource "google_bigquery_dataset" "platform_services_prod_trace_dataset" {
  dataset_id = "services_prod_e6fffc_cloud_traces_dataset"
}

resource "null_resource" "platform_services_prod_trace_sink" {
  provisioner "local-exec" {
    command = <<EOT
      gcloud config set project ${local.platform_services}
      gcloud alpha trace sinks create services_prod_e6fffc_trace_sink \
        'bigquery.googleapis.com/projects/${data.google_project.logs_prod.number}/datasets/${google_bigquery_dataset.platform_services_prod_trace_dataset.dataset_id}'
EOT
  }
} # To remove the sink run: gcloud alpha trace sinks delete services_prod_e6fffc_trace_sink

data "external" "trace_sink_service_account" {
  depends_on = [null_resource.platform_services_prod_trace_sink]
  program    = ["bash", "../describe_trace_sink.sh", "${local.platform_services}", "services_prod_e6fffc_trace_sink"]
}

resource "google_project_iam_member" "trace_sink_permissions" {
  project = local.project
  role    = "roles/bigquery.dataEditor"
  member  = "serviceAccount:${data.external.trace_sink_service_account.result.writer_identity}"
}

module "platform_valuations_performance_monitoring_logs" {
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = local.platform_services
  name                   = "${local.platform_services}-valuations-performance-monitoring-logs"
  log_filter             = "resource.type=\"k8s_container\" resource.labels.namespace_name=\"platform-valuation\" resource.labels.container_name=\"web-application\" jsonPayload.event:\"get_values performance data:\""
  use_partitioned_tables = true
  environment            = var.environment
  division               = "platform"
}

module "platform_valuations_taxonomy_performance_monitoring_logs" {
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = local.platform_services
  name                   = "${local.platform_services}-taxonomy-performance-monitoring-logs"
  log_filter             = "resource.type=\"k8s_container\" resource.labels.namespace_name=\"valuation-classification\" resource.labels.container_name=\"web-application\" jsonPayload.event:\"get_taxonomy performance data:\""
  use_partitioned_tables = true
  environment            = var.environment
  division               = "shared"
}

module "ids_logs" {
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = var.gcp_migration_prod
  name                   = "snort-ids-events"
  log_filter             = "logName=\"projects/rouse-gcp-migration-prod/logs/snort\""
  environment            = var.environment
  division               = "shared"
  use_partitioned_tables = true
}

module "platform_valuations_cms_performance_monitoring_logs" {
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = local.platform_services
  name                   = "${local.platform_services}-cms-performance-logs"
  log_filter             = "log_name=\"projects/services-prod-e6fffc/logs/stdout\" resource.type=\"k8s_container\" resource.labels.cluster_name=\"services-prod\" resource.labels.namespace_name=\"valuation-classification\" labels.k8s-pod/app_kubernetes_io/instance=\"prod-cms-app\" labels.k8s-pod/app_kubernetes_io/name=\"web-application\" jsonPayload.event:\"CMS Response:\""
  environment            = var.environment
  division               = "shared"
  use_partitioned_tables = true
}

module "sales_data_dev_sales_invoice_emails_logs" {
  source                 = "../../modules/logging/bigquery-export/v1"
  bigquery_project       = local.project
  sink_project           = local.sales_data
  name                   = "${local.sales_data}-sales-invoice-emails-event-logs"
  log_filter             = "logName=\"projects/${local.sales_data}/logs/sales-invoice-emails\""
  use_partitioned_tables = true
  environment            = var.environment
  division               = "platform"
}

module "notifications_api_errors" {
  source                          = "../../modules/logging/bigquery-export/v1"
  bigquery_project                = local.project
  sink_project                    = local.platform_services
  name                            = "${local.platform_services}-notifications_api_errors"
  log_filter                      = <<EOF
resource.type="k8s_container"
resource.labels.namespace_name="enterprise-notification"
labels.k8s-pod/app_kubernetes_io/instance="notifications"
labels.k8s-pod/app_kubernetes_io/name="web-application"
severity >= "ERROR"
EOF 
  use_partitioned_tables          = true
  environment                     = var.environment
  division                        = "platform"
  default_partition_expiration_ms = 30 * local.DAY_IN_MS
}

