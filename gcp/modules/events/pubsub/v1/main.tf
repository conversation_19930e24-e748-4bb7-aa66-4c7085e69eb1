locals {
  full_name = "${var.name}-${var.environment}"
}
variable "custom_name" {
  type = string
  default = ""
}
variable "name" {}
variable "environment" {}
variable "division" {}
variable "role" {
  default = "events"
}
variable "project" {}
variable "region" {}
variable "kms_rotation_period" {
  default = "31557600s" # one year
}
variable "publishers" {
  type    = list(string)
  default = []
}
variable "subscribers" {
  type    = list(string)
  default = []
}

data "google_project" "project" {
  project_id = var.project
}

variable "subscriptions_definitions" {
  type = map(object({
    ack_deadline = number
  }))
  default = {}
}

# Backward Compatibility
variable "is_legacy" {
  type = bool
  default = true
}

resource "google_pubsub_topic" "module" {
  project      = var.project
  name         = var.custom_name != "" ? var.custom_name : local.full_name
  kms_key_name = google_kms_crypto_key.module.id

  labels = {
    name        = "${var.name}-${var.environment}"
    environment = var.environment
    division    = var.division
    role        = var.role
  }
  depends_on = [google_kms_key_ring_iam_member.module]
}

resource "google_pubsub_subscription" "module" {
  project  = var.project
  for_each = var.subscriptions_definitions
  name     = each.key
  topic    = google_pubsub_topic.module.name

  ack_deadline_seconds = each.value.ack_deadline

  labels = {
    name        = each.key
    environment = var.environment
    division    = var.division
    role        = var.role
  }
}

resource "google_kms_key_ring" "module" {
  name     = "${var.project}-${var.name}"
  location = var.region
  project  = var.project
}

resource "google_kms_crypto_key" "module" {
  name            = "${var.project}-${var.name}"
  key_ring        = google_kms_key_ring.module.id
  rotation_period = var.kms_rotation_period

  lifecycle {
    prevent_destroy = true
  }
}

resource "google_kms_key_ring_iam_member" "module" {
  key_ring_id = var.is_legacy ? "${var.project}/${var.region}/${google_kms_key_ring.module.name}" : google_kms_key_ring.module.id
  role        = "roles/cloudkms.cryptoKeyEncrypterDecrypter"
  member      = "serviceAccount:service-${data.google_project.project.number}@gcp-sa-pubsub.iam.gserviceaccount.com"
  depends_on = [ google_kms_key_ring.module ]
}

resource "google_pubsub_topic_iam_member" "publisher" {
  project  = var.project
  for_each = toset(var.publishers)
  topic    = google_pubsub_topic.module.name
  role     = "roles/pubsub.publisher"
  member   = each.key
}

resource "google_pubsub_topic_iam_member" "subscriber" {
  project  = var.project
  for_each = toset(var.subscribers)
  topic    = google_pubsub_topic.module.name
  role     = "roles/pubsub.subscriber"
  member   = each.key
}

output "pubsub_topic" {
  value = google_pubsub_topic.module.name
}

output "pubsub_id" {
  value = google_pubsub_topic.module.id
}