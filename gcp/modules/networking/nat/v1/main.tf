variable "name" {
}

variable "region" {
}

variable "network_id" {
}

variable "asn" {
  default = 64515
}

variable "keepalive_interval" {
  default = 20
}

variable "nat_ips" { default = [] }

resource "google_compute_router" "module" {
  name    = var.name
  region  = var.region
  network = var.network_id

  bgp {
    keepalive_interval = var.keepalive_interval
    asn = var.asn
  }
}

resource "google_compute_router_nat" "module" {
  name                               = var.name
  router                             = google_compute_router.module.name
  region                             = var.region
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"
  nat_ip_allocate_option             = length(var.nat_ips) > 0 ? "MANUAL_ONLY" : "AUTO_ONLY"
  nat_ips                            = var.nat_ips
}