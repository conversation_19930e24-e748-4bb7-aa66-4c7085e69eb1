resource "google_project_service" "redis" {
  project            = local.project
  service            = "redis.googleapis.com"
  disable_on_destroy = false
}

module "rdo_api_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "rdo-api-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 6
  redis_version   = "REDIS_4_0"
  role            = "cache"
  division        = "analytics"
  memory_size_gb  = 1
}

module "valuations_service_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "valuations-service-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 8
  redis_version   = "REDIS_4_0"
  role            = "cache"
  division        = "rbval"
  memory_size_gb  = 8
}

module "sales_rfm_channels_api_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-rfm-channels-api-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 10
  redis_version   = "REDIS_4_0"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 1
}


module "valuation_classification_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "valuation-classification-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 12
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "rbval"
  memory_size_gb  = 1
}

module "sales_fleet_manager_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-fleet-manager-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 14
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "platform"
  memory_size_gb  = 6
}

module "sales_ingest_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-ingest-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 16
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 1
}

output "rdo_api_cache_redis" {
  value = "${module.rdo_api_cache_redis_instance.host}:${module.rdo_api_cache_redis_instance.port}"
}

# Redis instance for FMI to use for batch job processing
module "fleet_manager_integrations_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "fleet-manager-integrations-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 18
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "platform"
  memory_size_gb  = 2
}

module "sales_catalog_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-catalog-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 20
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 1
}

module "sales_rfm_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-rfm-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 22
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 1
}

module "honeycomb_refinery_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "honeycomb-refinery"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 24
  redis_version   = "REDIS_6_X"
  role            = "events"
  division        = "sales"
  memory_size_gb  = 1
}

module "kafka_retry_service_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "kafka-retry-service-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 26
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 2
}

module "enterprise_media_service_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "enterprise-media-service-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 28
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 1
}

module "appraisals_portal_api_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "appraisals-portal-api-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 32
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "appraisals"
  memory_size_gb  = 1
}

module "enterprise_domain_service_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "enterprise-domain-service-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 34
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 2
}

module "sales_fmx_api_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-fmx-api-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 36
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 2
}

module "rfm_user_service_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "rfm-user-service-std-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 38
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "platform"
  memory_size_gb  = 1
}

module "analytics_rdo_dataservice_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "analytics-rdo-dataservice2-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 44
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "analytics"
  memory_size_gb  = 1
}

module "sales_portal_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-portal-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 52
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 2
}

module "sales_notification_service_cache_redis_instance" {
  source          = "../../modules/storage/redis_instance/v1"
  name            = "sales-notification-service-cache"
  environment     = var.environment
  project         = local.project
  network_link    = module.services_kubernetes_network.network_link
  base_ip_range   = "**********/16"
  ip_range_number = 56
  redis_version   = "REDIS_6_X"
  role            = "cache"
  division        = "sales"
  memory_size_gb  = 1
}
