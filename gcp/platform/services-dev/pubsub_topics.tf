module "platform_tasks__legacy_provision" {
  source      = "../../modules/events/pubsub/v1"
  name        = "platform_tasks__legacy_provision"
  environment = var.environment
  division    = "platform"
  project     = local.project
  region      = var.region

  providers = {
    google = google
  }
}

module "platform_tasks__platform_user_config" {
  source      = "../../modules/events/pubsub/v1"
  name        = "platform_tasks__platform_user_config"
  environment = var.environment
  division    = "platform"
  project     = local.project
  region      = var.region

  providers = {
    google = google
  }
}

module "fleet_manager__asset_changes" {
  source      = "../../modules/events/pubsub/v1"
  name        = "fleet_manager__asset_changes"
  environment = var.environment
  division    = "rbims"
  project     = local.project
  region      = var.region

  providers = {
    google = google
  }
}

module "gke_notifications" {
  source      = "../../modules/events/pubsub/v1"
  name        = "gke_notifications"
  environment = var.environment
  division    = "infrastructure"
  project     = local.project
  region      = var.region

  providers = {
    google = google
  }
}

module "fleet_manager_historian_history_pubsub" {
  source      = "../../modules/events/pubsub/v1"
  name        = "fleet_manager_historian__asset_history"
  environment = var.environment
  division    = "rbims"
  project     = local.project
  region      = var.region
  is_legacy   = false

  providers = {
    google = google
  }
}

module "fleet_manager_historian_preload_pubsub" {
  source      = "../../modules/events/pubsub/v1"
  name        = "fleet_manager_historian__preload"
  environment = var.environment
  division    = "rbims"
  project     = local.project
  region      = var.region
  is_legacy   = false

  providers = {
    google = google
  }
}

module "fleet_manager_historian_dlq_pubsub" {
  source      = "../../modules/events/pubsub/v1"
  name        = "fleet_manager_historian__dlq"
  environment = var.environment
  division    = "rbims"
  project     = local.project
  region      = var.region
  is_legacy   = false

  providers = {
    google = google
  }
}
