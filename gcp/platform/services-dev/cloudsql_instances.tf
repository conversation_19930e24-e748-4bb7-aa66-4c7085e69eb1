locals {
  terraform_admin_project = data.terraform_remote_state.project_structure.outputs.projects["terraform-admin"]
}

data "google_secret_manager_secret_version" "cloudsql_portalcache" {
  secret  = "dbadmin_portalcache_${var.environment}"
  project = local.management
}

module "cloudsql_portalcache_v2" {
  source            = "../../modules/storage/cloud_sql/v1"
  name              = "portalcache02"
  environment       = var.environment
  availability_type = "ZONAL"
  region            = var.region
  project           = local.project
  tier              = "db-custom-4-7680"
  additional_labels = {
    division = "sales"
    role     = "cache"
    critical = "true"
  }
  database_version    = "SQLSERVER_2017_WEB"
  database_name       = "sqlcache"
  private_network     = module.services_kubernetes_network.network_link
  keep_public_ip      = true
  backups_bucket_name = google_storage_bucket.cloudsql_backups.name
  password            = jsondecode(data.google_secret_manager_secret_version.cloudsql_portalcache.secret_data)["password"]
  providers = {
    google = google-beta
  }
}

data "google_secret_manager_secret_version" "cloudsql_identity" {
  secret  = "dbadmin_identity01_${var.environment}"
  project = local.management
}

module "cloudsql_identity_v2" {
  source            = "../../modules/storage/cloud_sql/v1"
  name              = "identity03"
  environment       = var.environment
  availability_type = "ZONAL"
  region            = var.region
  project           = local.project
  tier              = "db-custom-4-7680"
  disk_size         = 10
  password          = jsondecode(data.google_secret_manager_secret_version.cloudsql_identity.secret_data)["password"]
  additional_labels = {
    division = "shared"
    role     = "database"
    critical = "true"
  }
  authorized_networks = [
    {
      name  = "shared-nat-0",
      value = "*************"
    },
    {
      name  = "shared-nat-1",
      value = "************"
    },
    {
      name  = "giqaweb01",
      value = "*************"
    },
    {
      name  = "gfdev04",
      value = "************"
    },
    {
      name  = "vms-dev-nat",
      value = "************"
    },
    {
      name  = "vms-dev-nat2",
      value = "*************"
    },
    {
      name  = "rouse-office",
      value = "**************"
    },
    {
      name  = "gfortigate01-external-ip",
      value = "*************"
    },
    {
      name  = "data-vms-dev-nat1",
      value = "************"
    },
    {
      name  = "data-vms-dev-nat2",
      value = "**************"
    },
    {
      name  = "vms-prod-nat",
      value = "**************"
    },
    {
      name  = "vms-prod-nat2",
      value = "*************"
    },
  ]
  database_version    = "SQLSERVER_2017_WEB"
  database_name       = "identity"
  private_network     = module.services_kubernetes_network.network_link
  keep_public_ip      = true
  require_ssl         = true
  backups_bucket_name = google_storage_bucket.cloudsql_backups.name
  providers = {
    google = google-beta
  }
}

data "google_secret_manager_secret_version" "cloudsql_dttconfig" {
  secret  = "dbadmin_dttconfig01_${var.environment}"
  project = local.management
}

module "cloudsql_dttconfig_v2" {
  source            = "../../modules/storage/cloud_sql/v1"
  name              = "dttconfig02"
  environment       = var.environment
  availability_type = "ZONAL"
  region            = var.region
  project           = local.project
  tier              = "db-custom-2-7680"
  disk_size         = 10
  password          = jsondecode(data.google_secret_manager_secret_version.cloudsql_dttconfig.secret_data)["password"]
  additional_labels = {
    division = "shared"
    role     = "database"
    critical = "true"
  }
  authorized_networks = [
    {
      name  = "vms-dev-nat",
      value = "************"
    },
    {
      name  = "vms-dev-nat2",
      value = "*************"
    },
    {
      name  = "rouse-office",
      value = "**************"
    },
    {
      name  = "vms-prod-nat",
      value = "**************"
    },
    {
      name  = "vms-prod-nat2",
      value = "*************"
    },
  ]
  database_version    = "SQLSERVER_2017_WEB"
  private_network     = module.services_kubernetes_network.network_link
  keep_public_ip      = true
  require_ssl         = true
  backups_bucket_name = google_storage_bucket.cloudsql_backups.name
  providers = {
    google = google-beta
  }
}

resource "google_storage_bucket_iam_member" "identity_data_bucket_v2" {
  for_each = toset([
    "roles/storage.objectAdmin",
    "roles/storage.legacyBucketReader",
  ])
  bucket = module.data_bucket.bucket_name
  role   = each.key
  member = "serviceAccount:${module.cloudsql_identity_v2.instance_service_account}"
}

resource "google_storage_bucket" "cloudsql_backups" {
  name     = "${local.project}-cloudsql-backups"
  location = var.region

  versioning {
    enabled = false
  }
}

output "cloudsql_identity_server_ca_cert_v2" {
  value     = module.cloudsql_identity_v2.instance_server_ca_cert
  sensitive = true
}

data "google_secret_manager_secret_version" "cloudsql_salesglobal" {
  secret  = "dbadmin_salesglobal01_${var.environment}"
  project = local.management
}

module "cloudsql_salesglobal" {
  source            = "../../modules/storage/cloud_sql/v1"
  name              = "salesglobal01"
  environment       = var.environment
  availability_type = "ZONAL"
  region            = var.region
  project           = local.project
  tier              = "db-custom-2-6656"
  disk_size         = 10
  password          = jsondecode(data.google_secret_manager_secret_version.cloudsql_salesglobal.secret_data)["password"]
  additional_labels = {
    division = "sales"
    role     = "database"
    critical = "true"
  }
  authorized_networks = [
    {
      name  = "shared-nat-0",
      value = "*************"
    },
    {
      name  = "shared-nat-1",
      value = "************"
    },
    {
      name  = "giqaweb01",
      value = "*************"
    },
    {
      name  = "gfdev04",
      value = "************"
    },
    {
      name  = "rouse-office",
      value = "**************"
    },
    {
      name  = "vms-dev-nat",
      value = "************"
    },
    {
      name  = "vms-dev-nat2",
      value = "*************"
    },
    {
      name  = "vms-prod-nat",
      value = "**************"
    },
    {
      name  = "vms-prod-nat2",
      value = "*************"
    },
  ]
  database_version    = "SQLSERVER_2017_WEB"
  database_name       = "ras_Messages_Global"
  private_network     = module.services_kubernetes_network.network_link
  keep_public_ip      = true
  require_ssl         = true
  backups_bucket_name = google_storage_bucket.cloudsql_backups.name
  providers = {
    google = google-beta
  }
}


data "google_kms_secret" "sql-salesglobal01-user-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7+/ox+JpqvEBRGHsG8Of8IV784csoYj3qDAAIJAIkSwSPQA4/8/uUHYTzaN3g6fG6MY3Go599KAcKUeLb5xSsUgvlTjQnIPvcJ+WQQFAQPpT5URl+3qc4efPnulJjIM="
}

resource "google_sql_user" "sqlautomation_user" {
  instance = module.cloudsql_salesglobal.instance_name
  name     = "sqlautomation"
  password = data.google_kms_secret.sql-salesglobal01-user-dev.plaintext
  project  = local.project
}

data "google_secret_manager_secret_version" "cloudsql_record360" {
  secret  = "dbadmin_record360_03_${var.environment}"
  project = local.management
}

module "cloudsql_appraisals" {
  source            = "../../modules/storage/cloud_sql/v1"
  name              = "appraisals-01"
  environment       = var.environment
  availability_type = "ZONAL"
  region            = var.region
  project           = local.project
  tier              = "db-custom-4-7680"
  additional_labels = {
    division = "appraisals"
    role     = "database"
    critical = "true"
  }
  authorized_networks = [
    {
      name  = "vms-prod-nat",
      value = "**************"
    },
    {
      name  = "vms-prod-nat2",
      value = "*************"
    },
  ]
  database_version               = "POSTGRES_14"
  database_name                  = "appraisals"
  username                       = "root"
  password                       = jsondecode(data.google_secret_manager_secret_version.cloudsql_record360.secret_data)["password"]
  private_network                = module.services_kubernetes_network.network_link
  keep_public_ip                 = true
  backups_bucket_name            = google_storage_bucket.cloudsql_backups.name
  point_in_time_recovery_enabled = true
  transaction_log_retention_days = 7

  database_flags = [
    { name = "log_connections", value = "on" },
    { name = "log_disconnections", value = "on" },
    { name = "log_checkpoints", value = "on" },
    { name = "log_lock_waits", value = "on" },
    { name = "log_statement", value = "ddl" },
    { name = "work_mem", value = "5120" },
    { name = "effective_cache_size", value = "344064" },
    { name = "maintenance_work_mem", value = "262144" },
    { name = "random_page_cost", value = "1.0" },
    { name = "cloudsql.iam_authentication", value = "on" },
  ]
  providers = {
    google = google-beta
  }
  insights_config = {
    query_string_length = 2048
  }
}

data "google_secret_manager_secret_version" "cloudsql_rfm" {
  secret  = "dbadmin_rfm01_${var.environment}"
  project = local.management
}

# module "cloudsql_rfm_v2" {
#   source            = "../../modules/storage/cloud_sql/v1"
#   name              = "rfm02"
#   environment       = var.environment
#   availability_type = "ZONAL"
#   region            = var.region
#   project           = local.project
#   tier              = "db-custom-32-212992"
#   replicas = {
#     rfm02r2 = {
#       tier = "db-custom-32-212992"
#     }
#   }
#   additional_labels = {
#     division = "rbims"
#     role     = "database"
#   }
#   database_version               = "POSTGRES_14"
#   database_name                  = null
#   username                       = "root"
#   password                       = jsondecode(data.google_secret_manager_secret_version.cloudsql_rfm.secret_data)["password"]
#   private_network                = module.services_kubernetes_network.network_link
#   keep_public_ip                 = true
#   backups_bucket_name            = google_storage_bucket.cloudsql_backups.name
#   point_in_time_recovery_enabled = true
#   transaction_log_retention_days = 7
#   database_flags = [
#     { name = "auto_explain.log_analyze", value = "on" },
#     { name = "auto_explain.log_buffers", value = "on" },
#     { name = "auto_explain.log_format", value = "json" },
#     { name = "auto_explain.log_level", value = "log" },
#     { name = "auto_explain.log_min_duration", value = "30000" },
#     { name = "auto_explain.log_nested_statements", value = "off" },
#     { name = "auto_explain.log_settings", value = "off" },
#     { name = "auto_explain.log_timing", value = "on" },
#     { name = "auto_explain.log_triggers", value = "off" },
#     { name = "auto_explain.log_verbose", value = "on" },
#     { name = "auto_explain.log_wal", value = "off" },
#     { name = "auto_explain.sample_rate", value = "1" },
#     { name = "cloudsql.enable_auto_explain", value = "on" },
#     { name = "cloudsql.iam_authentication", value = "on" },
#     { name = "default_statistics_target", value = "1000" },
#     { name = "hot_standby_feedback", value = "on" },
#     { name = "log_connections", value = "on" },
#     { name = "log_disconnections", value = "on" },
#     { name = "log_checkpoints", value = "on" },
#     { name = "log_lock_waits", value = "on" },
#     { name = "log_statement", value = "ddl" },
#     { name = "log_min_duration_statement", value = "30000" },
#     { name = "max_connections", value = "1500" },
#     { name = "max_standby_streaming_delay", value = "60000" },
#     { name = "max_standby_archive_delay", value = "60000" },
#     { name = "max_wal_size", value = "16384" },
#     { name = "min_wal_size", value = "4096" },
#     { name = "temp_file_limit", value = "2147483647" },
#     { name = "work_mem", value = "13312" },
#     { name = "cloudsql.pg_shadow_select_role", value = "root" },
#     { name = "max_locks_per_transaction", value = "256" },
#     { name = "autovacuum_max_workers", value = "24" },
#     { name = "autovacuum_naptime", value = "10" },
#     { name = "autovacuum_analyze_scale_factor", value = "0.05" },
#     { name = "autovacuum_vacuum_scale_factor", value = "0.1" },
#     { name = "autovacuum_vacuum_cost_delay", value = "0" },
#     { name = "random_page_cost", value = "1.0" },
#     { name = "max_worker_processes", value = "36" },
#     { name = "max_parallel_workers_per_gather", value = "2" },
#     { name = "max_parallel_maintenance_workers", value = "8" },
#     { name = "max_parallel_workers", value = "36" },
#     { name = "maintenance_work_mem", value = "839680" },
#     { name = "vacuum_cost_limit", value = "400" },
#   ]
#   authorized_networks = [
#     {
#       name  = "vms-prod-nat",
#       value = "**************"
#     },
#     {
#       name  = "vms-prod-nat2",
#       value = "*************"
#     },
#   ]
#   insights_config = {
#     query_string_length    = 4500
#     query_plans_per_minute = 10
#   }
#   providers = {
#     google = google-beta
#   }
# }

module "cloudsql_rfm_v3" {
  source            = "../../modules/storage/cloud_sql/v1"
  name              = "rfm03"
  environment       = var.environment
  availability_type = "ZONAL"
  region            = var.region
  project           = local.project
  tier              = "db-custom-8-49152"
  additional_labels = {
    division = "rbims"
    role     = "database"
    critical = "true"
  }
  database_version               = "POSTGRES_14"
  database_name                  = null
  username                       = "root"
  password                       = jsondecode(data.google_secret_manager_secret_version.cloudsql_rfm.secret_data)["password"]
  private_network                = module.services_kubernetes_network.network_link
  keep_public_ip                 = true
  backups_bucket_name            = google_storage_bucket.cloudsql_backups.name
  point_in_time_recovery_enabled = true
  transaction_log_retention_days = 7
  database_flags = [
    { name = "auto_explain.log_analyze", value = "on" },
    { name = "auto_explain.log_buffers", value = "on" },
    { name = "auto_explain.log_format", value = "json" },
    { name = "auto_explain.log_level", value = "log" },
    { name = "auto_explain.log_min_duration", value = "30000" },
    { name = "auto_explain.log_nested_statements", value = "off" },
    { name = "auto_explain.log_settings", value = "off" },
    { name = "auto_explain.log_timing", value = "on" },
    { name = "auto_explain.log_triggers", value = "off" },
    { name = "auto_explain.log_verbose", value = "on" },
    { name = "auto_explain.log_wal", value = "off" },
    { name = "auto_explain.sample_rate", value = "1" },
    { name = "cloudsql.enable_auto_explain", value = "on" },
    { name = "cloudsql.iam_authentication", value = "on" },
    { name = "default_statistics_target", value = "1000" },
    { name = "hot_standby_feedback", value = "on" },
    { name = "log_connections", value = "on" },
    { name = "log_disconnections", value = "on" },
    { name = "log_checkpoints", value = "on" },
    { name = "log_lock_waits", value = "on" },
    { name = "log_statement", value = "ddl" },
    { name = "log_min_duration_statement", value = "30000" },
    { name = "max_connections", value = "500" },
    { name = "max_standby_streaming_delay", value = "60000" },
    { name = "max_wal_size", value = "16384" },
    { name = "min_wal_size", value = "4096" },
    { name = "temp_file_limit", value = "2147483647" },
    { name = "work_mem", value = "13312" },
    { name = "cloudsql.pg_shadow_select_role", value = "root" },
    { name = "max_locks_per_transaction", value = "256" },
    { name = "autovacuum_max_workers", value = "6" },
    { name = "autovacuum_naptime", value = "30" },
    { name = "autovacuum_analyze_scale_factor", value = "0.05" },
    { name = "autovacuum_vacuum_scale_factor", value = "0.1" },
    { name = "random_page_cost", value = "1.0" },
  ]
  authorized_networks = [
    {
      name  = "vms-prod-nat",
      value = "**************"
    },
    {
      name  = "vms-prod-nat2",
      value = "*************"
    },
  ]
  insights_config = {
    query_string_length    = 4500
    query_plans_per_minute = 10
  }
  providers = {
    google = google-beta
  }
}

data "google_secret_manager_secret_version" "rfm04_dev_tier" {
  secret  = "rfm04-dev-tier"
  project = local.project
}

data "google_secret_manager_secret_version" "rfm04-dev-max-connections" {
  secret  = "rfm04-dev-max-connections"
  project = local.project
}

module "cloudsql_rfm_v4" {
  source            = "../../modules/storage/cloud_sql/v1"
  name              = "rfm04r0"
  environment       = var.environment
  availability_type = "ZONAL"
  region            = var.region
  project           = local.project
  tier              = "db-custom-16-106496"
  replicas = {
    rfm04r1 = {
      tier = "db-custom-6-39936"
    }
  }
  additional_labels = {
    division = "rbims"
    role     = "database"
    critical = "true"
  }
  database_version               = "POSTGRES_14"
  database_name                  = null
  username                       = "root"
  password                       = jsondecode(data.google_secret_manager_secret_version.cloudsql_rfm.secret_data)["password"]
  private_network                = module.services_kubernetes_network.network_link
  keep_public_ip                 = true
  backups_bucket_name            = google_storage_bucket.cloudsql_backups.name
  point_in_time_recovery_enabled = true
  transaction_log_retention_days = 7
  database_flags = [
    { name = "auto_explain.log_analyze", value = "on" },
    { name = "auto_explain.log_buffers", value = "on" },
    { name = "auto_explain.log_format", value = "json" },
    { name = "auto_explain.log_level", value = "log" },
    { name = "auto_explain.log_min_duration", value = "30000" },
    { name = "auto_explain.log_nested_statements", value = "off" },
    { name = "auto_explain.log_settings", value = "off" },
    { name = "auto_explain.log_timing", value = "on" },
    { name = "auto_explain.log_triggers", value = "off" },
    { name = "auto_explain.log_verbose", value = "on" },
    { name = "auto_explain.log_wal", value = "off" },
    { name = "auto_explain.sample_rate", value = "1" },
    { name = "cloudsql.enable_auto_explain", value = "on" },
    { name = "cloudsql.iam_authentication", value = "on" },
    { name = "default_statistics_target", value = "1000" },
    { name = "hot_standby_feedback", value = "on" },
    { name = "log_connections", value = "on" },
    { name = "log_disconnections", value = "on" },
    { name = "log_checkpoints", value = "on" },
    { name = "log_lock_waits", value = "on" },
    { name = "log_statement", value = "ddl" },
    { name = "log_min_duration_statement", value = "30000" },
    #    { name = "max_connections", value = "1000" },
    { name = "max_connections", value = nonsensitive(data.google_secret_manager_secret_version.rfm04-dev-max-connections.secret_data) },
    { name = "max_standby_streaming_delay", value = "60000" },
    { name = "max_wal_size", value = "16384" },
    { name = "min_wal_size", value = "4096" },
    { name = "temp_file_limit", value = "2147483647" },
    { name = "work_mem", value = "13312" },
    { name = "cloudsql.pg_shadow_select_role", value = "root" },
    { name = "max_locks_per_transaction", value = "256" },
    { name = "autovacuum_max_workers", value = "6" },
    { name = "autovacuum_naptime", value = "30" },
    { name = "autovacuum_analyze_scale_factor", value = "0.05" },
    { name = "autovacuum_vacuum_scale_factor", value = "0.1" },
    { name = "random_page_cost", value = "1.0" },
    { name = "cloudsql.logical_decoding", value = "on" },
    { name = "cloudsql.enable_pglogical", value = "on" },
    { name = "cloudsql.enable_pg_cron", value = "on" },
    { name = "max_worker_processes", value = "16" },
    { name = "max_parallel_workers_per_gather", value = "6" },
    { name = "max_logical_replication_workers", value = "8" },
    { name = "max_sync_workers_per_subscription", value = "4" },
    { name = "wal_compression", value = "on" },
    { name = "log_min_messages", value = "warning" },
    { name = "wal_sender_timeout", value = "0" },
    { name = "max_wal_senders", value = "20" },
  ]
  authorized_networks = [
    {
      name  = "vms-prod-nat",
      value = "**************"
    },
    {
      name  = "vms-prod-nat2",
      value = "*************"
    },
  ]
  insights_config = {
    query_string_length    = 4500
    query_plans_per_minute = 10
  }
  providers = {
    google = google-beta
  }
}

module "cloudsql_rfm_services01" {
  source            = "../../modules/storage/cloud_sql/v1"
  name              = "rfm-services01"
  environment       = var.environment
  availability_type = "ZONAL"
  region            = var.region
  project           = local.project
  tier              = "db-custom-12-79872"
  add_pgbouncer     = true
  private_subnet    = module.services_kubernetes_network.subnet_link
  additional_labels = {
    division = "rbims"
    role     = "database"
    critical = "true"
  }
  workload_identity_sa_email     = "<EMAIL>"
  database_version               = "POSTGRES_14"
  database_name                  = null
  username                       = "root"
  password                       = jsondecode(data.google_secret_manager_secret_version.cloudsql_rfm.secret_data)["password"]
  private_network                = module.services_kubernetes_network.network_link
  keep_public_ip                 = true
  backups_bucket_name            = google_storage_bucket.cloudsql_backups.name
  point_in_time_recovery_enabled = true
  transaction_log_retention_days = 7
  database_flags = [
    { name = "auto_explain.log_analyze", value = "on" },
    { name = "auto_explain.log_buffers", value = "on" },
    { name = "auto_explain.log_format", value = "json" },
    { name = "auto_explain.log_level", value = "log" },
    { name = "auto_explain.log_min_duration", value = "30000" },
    { name = "auto_explain.log_nested_statements", value = "off" },
    { name = "auto_explain.log_settings", value = "off" },
    { name = "auto_explain.log_timing", value = "on" },
    { name = "auto_explain.log_triggers", value = "off" },
    { name = "auto_explain.log_verbose", value = "on" },
    { name = "auto_explain.log_wal", value = "off" },
    { name = "auto_explain.sample_rate", value = "1" },
    { name = "cloudsql.enable_auto_explain", value = "on" },
    { name = "cloudsql.iam_authentication", value = "on" },
    { name = "default_statistics_target", value = "1000" },
    { name = "hot_standby_feedback", value = "on" },
    { name = "log_connections", value = "on" },
    { name = "log_disconnections", value = "on" },
    { name = "log_checkpoints", value = "on" },
    { name = "log_lock_waits", value = "on" },
    { name = "log_statement", value = "ddl" },
    { name = "log_min_duration_statement", value = "30000" },
    { name = "max_connections", value = "1500" },
    { name = "max_standby_streaming_delay", value = "60000" },
    { name = "max_wal_size", value = "16384" },
    { name = "min_wal_size", value = "4096" },
    { name = "temp_file_limit", value = "2147483647" },
    { name = "work_mem", value = "13312" },
    { name = "cloudsql.pg_shadow_select_role", value = "root" },
    { name = "max_locks_per_transaction", value = "256" },
    { name = "autovacuum_max_workers", value = "6" },
    { name = "autovacuum_naptime", value = "30" },
    { name = "autovacuum_analyze_scale_factor", value = "0.05" },
    { name = "autovacuum_vacuum_scale_factor", value = "0.1" },
    { name = "random_page_cost", value = "1.0" },
  ]
  authorized_networks = [
    {
      name  = "vms-prod-nat",
      value = "**************"
    },
    {
      name  = "vms-prod-nat2",
      value = "*************"
    },
  ]
  insights_config = {
    query_string_length    = 4500
    query_plans_per_minute = 10
  }
  providers = {
    google = google-beta
  }
}

module "cloudsql_rfm_v7" {
  source         = "../../modules/storage/cloud_sql/v1"
  name           = "rfm07"
  environment    = var.environment
  region         = var.region
  edition        = "ENTERPRISE"
  project        = local.project
  tier           = "db-custom-20-133120"
  add_pgbouncer  = true
  private_subnet = module.services_kubernetes_network.subnet_link
  additional_labels = {
    division = "rbims"
    role     = "database"
    critical = "true"
  }
  workload_identity_sa_email     = "<EMAIL>"
  database_version               = "POSTGRES_15"
  database_name                  = null
  username                       = "root"
  password                       = jsondecode(data.google_secret_manager_secret_version.cloudsql_rfm.secret_data)["password"]
  private_network                = module.services_kubernetes_network.network_link
  keep_public_ip                 = true
  backups_bucket_name            = google_storage_bucket.cloudsql_backups.name
  point_in_time_recovery_enabled = true
  transaction_log_retention_days = 7
  database_flags = [
    { name = "auto_explain.log_analyze", value = "on" },
    { name = "auto_explain.log_buffers", value = "on" },
    { name = "auto_explain.log_format", value = "json" },
    { name = "auto_explain.log_level", value = "log" },
    { name = "auto_explain.log_min_duration", value = "30000" },
    { name = "auto_explain.log_nested_statements", value = "off" },
    { name = "auto_explain.log_settings", value = "off" },
    { name = "auto_explain.log_timing", value = "on" },
    { name = "auto_explain.log_triggers", value = "off" },
    { name = "auto_explain.log_verbose", value = "on" },
    { name = "auto_explain.log_wal", value = "off" },
    { name = "auto_explain.sample_rate", value = "1" },
    { name = "cloudsql.enable_auto_explain", value = "on" },
    { name = "cloudsql.iam_authentication", value = "on" },
    { name = "default_statistics_target", value = "1000" },
    { name = "hot_standby_feedback", value = "on" },
    { name = "log_connections", value = "on" },
    { name = "log_disconnections", value = "on" },
    { name = "log_checkpoints", value = "on" },
    { name = "log_lock_waits", value = "on" },
    { name = "log_statement", value = "ddl" },
    { name = "log_min_duration_statement", value = "30000" },
    { name = "max_connections", value = "500" },
    { name = "max_standby_streaming_delay", value = "60000" },
    { name = "max_wal_size", value = "5120" },
    { name = "min_wal_size", value = "4096" },
    { name = "temp_file_limit", value = "2147483647" },
    { name = "work_mem", value = "128000" },
    { name = "cloudsql.pg_shadow_select_role", value = "root" },
    { name = "max_locks_per_transaction", value = "256" },
    { name = "autovacuum_max_workers", value = "6" },
    { name = "autovacuum_naptime", value = "30" },
    { name = "autovacuum_analyze_scale_factor", value = "0.05" },
    { name = "autovacuum_vacuum_scale_factor", value = "0.1" },
    { name = "random_page_cost", value = "1.0" },
    { name = "cloudsql.logical_decoding", value = "on" },
    { name = "cloudsql.enable_pglogical", value = "on" },
    { name = "cloudsql.enable_pg_cron", value = "on" },
    { name = "max_worker_processes", value = "16" },
    { name = "max_parallel_workers_per_gather", value = "6" },
    { name = "max_logical_replication_workers", value = "8" },
    { name = "max_sync_workers_per_subscription", value = "4" },
    { name = "wal_compression", value = "on" },
    { name = "wal_sender_timeout", value = "30000" },
    { name = "max_wal_senders", value = "20" },
    { name = "maintenance_work_mem", value = "838860" },
    { name = "wal_buffers", value = "16384" },
    { name = "logical_decoding_work_mem", value = "2097152" },
    { name = "maintenance_io_concurrency", value = "32" },
    { name = "checkpoint_timeout", value = "300" },
  ]
  authorized_networks = [
    {
      name  = "vms-prod-nat",
      value = "**************"
    },
    {
      name  = "vms-prod-nat2",
      value = "*************"
    },
  ]
  insights_config = {
    query_string_length    = 4500
    query_plans_per_minute = 10
  }
  providers = {
    google = google-beta
  }
  replicas = {
    rfm07r0 = {
      tier = "db-custom-6-39936"
    }
  }
}


data "google_secret_manager_secret_version" "cloudsql_salesglobal_postgres" {
  secret  = "dbadmin_salesglobal02_${var.environment}"
  project = local.management
}

module "cloudsql_salesglobal_postgres" {
  source            = "../../modules/storage/cloud_sql/v1"
  name              = "salesglobal02"
  environment       = var.environment
  availability_type = "ZONAL"
  region            = var.region
  project           = local.project
  tier              = "db-custom-2-6656"
  additional_labels = {
    division = "sales"
    role     = "database"
    critical = "true"
  }
  authorized_networks = [
    {
      name  = "shared-nat-0",
      value = "*************"
    },
    {
      name  = "shared-nat-1",
      value = "************"
    },
    {
      name  = "giqaweb01",
      value = "*************"
    },
    {
      name  = "gfdev04",
      value = "************"
    },
    {
      name  = "rouse-office",
      value = "**************"
    },
    {
      name  = "vms-dev-nat",
      value = "************"
    },
    {
      name  = "vms-dev-nat2",
      value = "*************"
    },
    {
      name  = "vms-prod-nat",
      value = "**************"
    },
    {
      name  = "vms-prod-nat2",
      value = "*************"
    },
  ]
  database_version               = "POSTGRES_14"
  database_name                  = "salesglobal"
  username                       = "root"
  password                       = jsondecode(data.google_secret_manager_secret_version.cloudsql_salesglobal_postgres.secret_data)["password"]
  private_network                = module.services_kubernetes_network.network_link
  keep_public_ip                 = true
  backups_bucket_name            = google_storage_bucket.cloudsql_backups.name
  point_in_time_recovery_enabled = true
  transaction_log_retention_days = 7
  database_flags = [
    { name = "log_connections", value = "on" },
    { name = "log_disconnections", value = "on" },
    { name = "log_checkpoints", value = "on" },
    { name = "log_lock_waits", value = "on" },
    { name = "log_statement", value = "ddl" },
    { name = "random_page_cost", value = "1.0" },
    { name = "cloudsql.iam_authentication", value = "on" },
  ]
  providers = {
    google = google-beta
  }
}

data "google_secret_manager_secret_version" "cloudsql_configs" {
  secret  = "dbadmin_configs01_${var.environment}"
  project = local.management
}

module "cloudsql_configs" {
  source            = "../../modules/storage/cloud_sql/v1"
  name              = "configs01"
  environment       = var.environment
  availability_type = "ZONAL"
  region            = var.region
  project           = local.project
  tier              = "db-custom-2-3840"
  additional_labels = {
    division = "enterprise"
    role     = "database"
    critical = "true"
  }
  authorized_networks = [
    {
      name  = "vms-prod-nat",
      value = "**************"
    },
    {
      name  = "vms-prod-nat2",
      value = "*************"
    },
  ]
  database_version               = "POSTGRES_14"
  database_name                  = "configs"
  username                       = "root"
  password                       = jsondecode(data.google_secret_manager_secret_version.cloudsql_configs.secret_data)["password"]
  private_network                = module.services_kubernetes_network.network_link
  keep_public_ip                 = true
  backups_bucket_name            = google_storage_bucket.cloudsql_backups.name
  point_in_time_recovery_enabled = true
  transaction_log_retention_days = 7
  database_flags = [
    { name = "log_connections", value = "on" },
    { name = "log_disconnections", value = "on" },
    { name = "log_checkpoints", value = "on" },
    { name = "log_lock_waits", value = "on" },
    { name = "log_statement", value = "ddl" },
    { name = "random_page_cost", value = "1.0" },
    { name = "cloudsql.iam_authentication", value = "on" },
  ]
  providers = {
    google = google-beta
  }
  insights_config = {
    query_string_length = 2048
  }
}

data "google_secret_manager_secret_version" "cloudsql_rdo_postgres" {
  secret  = "dbadmin_rdo04_${var.environment}"
  project = local.management
}

data "google_secret_manager_secret_version" "cloudsql_platform01_postgres" {
  secret  = "dbadmin_platform01_${var.environment}"
  project = local.management
}

module "cloudsql_platform01_postgres" {
  source            = "../../modules/storage/cloud_sql/v1"
  name              = "platform01"
  environment       = var.environment
  availability_type = "ZONAL"
  region            = var.region
  project           = local.project
  tier              = "db-custom-16-30720"
  additional_labels = {
    division = "platform"
    role     = "database"
    critical = "true"
  }
  authorized_networks = [
    {
      name  = "vms-prod-nat",
      value = "**************"
    },
    {
      name  = "vms-prod-nat2",
      value = "*************"
    },
  ]
  database_version               = "POSTGRES_14"
  database_name                  = "platform"
  username                       = "root"
  password                       = jsondecode(data.google_secret_manager_secret_version.cloudsql_platform01_postgres.secret_data)["password"]
  private_network                = module.services_kubernetes_network.network_link
  keep_public_ip                 = true
  backups_bucket_name            = google_storage_bucket.cloudsql_backups.name
  point_in_time_recovery_enabled = true
  transaction_log_retention_days = 7
  providers = {
    google = google-beta
  }
  insights_config = {
    query_string_length = 2048
  }
  database_flags = [
    { name = "max_connections", value = "800" },
    { name = "cloudsql.enable_pglogical", value = "on" },
    { name = "cloudsql.logical_decoding", value = "on" },
    { name = "max_replication_slots", value = "10" },
    { name = "max_wal_senders", value = "10" },
    { name = "max_worker_processes", value = "13" },
    { name = "log_connections", value = "on" },
    { name = "log_disconnections", value = "on" },
    { name = "log_checkpoints", value = "on" },
    { name = "log_lock_waits", value = "on" },
    { name = "log_statement", value = "ddl" },
    { name = "random_page_cost", value = "1.0" },
    { name = "cloudsql.iam_authentication", value = "on" },
  ]
}

#data "google_secret_manager_secret_version" "cloudsql_defectdojo_postgres" {
#  secret  = "dbadmin_gdefectdojo01_${var.environment}"
#  project = local.terraform_admin_project
#}

#module "cloudsql_defectdojo_postgres" {
#  source            = "../../modules/storage/cloud_sql/v1"
#  name              = "gdefectdojo01"
#  environment       = var.environment
#  availability_type = "ZONAL"
#  region            = var.region
#  project           = local.project
#  tier              = "db-custom-2-3840"
#  additional_labels = {
#    division = "infrastructure"
#    role     = "database"
#  }
#  database_version    = "POSTGRES_14"
#  database_name       = "defectdojo"
#  username            = "root"
#  password            = jsondecode(data.google_secret_manager_secret_version.cloudsql_defectdojo_postgres.secret_data)["password"]
#  private_network     = module.services_kubernetes_network.network_link
#  keep_public_ip      = false
#  require_ssl         = false // https://github.com/DefectDojo/django-DefectDojo/issues/1716 :'(
#  backups_bucket_name = google_storage_bucket.cloudsql_backups.name
#  database_flags = [
#    { name = "cloudsql.iam_authentication", value = "on" }
#  ]
#  providers = {
#    google = google-beta
#  }
#  insights_config = {
#    query_string_length = 2048
#  }
#}


locals {

  cloudsql_instances_ids = [
    module.cloudsql_portalcache_v2.instance_name,
    module.cloudsql_identity_v2.instance_name,
    module.cloudsql_dttconfig_v2.instance_name,
    module.cloudsql_salesglobal.instance_name,
    module.cloudsql_appraisals.instance_name,
    module.cloudsql_rfm_v3.instance_name,
    module.cloudsql_rfm_v4.instance_name,
    module.cloudsql_rfm_v7.instance_name,
    module.cloudsql_rfm_services01.instance_name,
    module.cloudsql_salesglobal_postgres.instance_name,
    module.cloudsql_configs.instance_name,
    module.cloudsql_platform01_postgres.instance_name,
    #module.cloudsql_defectdojo_postgres.instance_name,
  ]

  cloudsql_instances_db_version = [
    module.cloudsql_portalcache_v2.instance_db_version,
    module.cloudsql_identity_v2.instance_db_version,
    module.cloudsql_dttconfig_v2.instance_db_version,
    module.cloudsql_salesglobal.instance_db_version,
    module.cloudsql_appraisals.instance_db_version,
    module.cloudsql_rfm_v3.instance_db_version,
    module.cloudsql_rfm_v4.instance_db_version,
    module.cloudsql_rfm_v7.instance_db_version,
    module.cloudsql_rfm_services01.instance_db_version,
    module.cloudsql_salesglobal_postgres.instance_db_version,
    module.cloudsql_configs.instance_db_version,
    module.cloudsql_platform01_postgres.instance_db_version,
    #module.cloudsql_defectdojo_postgres.instance_db_version,
  ]

  cloudsql_instances_disk_size = [
    module.cloudsql_portalcache_v2.instance_disk_size,
    module.cloudsql_identity_v2.instance_disk_size,
    module.cloudsql_dttconfig_v2.instance_disk_size,
    module.cloudsql_salesglobal.instance_disk_size,
    module.cloudsql_appraisals.instance_disk_size,
    module.cloudsql_rfm_v3.instance_disk_size,
    module.cloudsql_rfm_v4.instance_disk_size,
    module.cloudsql_rfm_v7.instance_disk_size,
    module.cloudsql_rfm_services01.instance_disk_size,
    module.cloudsql_salesglobal_postgres.instance_disk_size,
    module.cloudsql_configs.instance_disk_size,
    module.cloudsql_platform01_postgres.instance_disk_size,
    #module.cloudsql_defectdojo_postgres.instance_disk_size,
  ]

  cloudsql_instances_memory_size = [
    module.cloudsql_portalcache_v2.instance_memory_size,
    module.cloudsql_identity_v2.instance_memory_size,
    module.cloudsql_dttconfig_v2.instance_memory_size,
    module.cloudsql_salesglobal.instance_memory_size,
    module.cloudsql_appraisals.instance_memory_size,
    module.cloudsql_rfm_v3.instance_memory_size,
    module.cloudsql_rfm_v4.instance_memory_size,
    module.cloudsql_rfm_v7.instance_memory_size,
    module.cloudsql_rfm_services01.instance_memory_size,
    module.cloudsql_salesglobal_postgres.instance_memory_size,
    module.cloudsql_configs.instance_memory_size,
    module.cloudsql_platform01_postgres.instance_memory_size,
  ]

  cloudsql_instances_max_connections = [
    module.cloudsql_portalcache_v2.instance_max_connections,
    module.cloudsql_identity_v2.instance_max_connections,
    module.cloudsql_dttconfig_v2.instance_max_connections,
    module.cloudsql_salesglobal.instance_max_connections,
    module.cloudsql_appraisals.instance_max_connections,
    module.cloudsql_rfm_v3.instance_max_connections,
    module.cloudsql_rfm_v4.instance_max_connections,
    module.cloudsql_rfm_v7.instance_max_connections,
    module.cloudsql_rfm_services01.instance_max_connections,
    module.cloudsql_salesglobal_postgres.instance_max_connections,
    module.cloudsql_configs.instance_max_connections,
    module.cloudsql_platform01_postgres.instance_max_connections,
  ]

  cloudsql_info = [
    for index in range(length(local.cloudsql_instances_ids)) : {
      "instance_id"     = local.cloudsql_instances_ids[index],
      "db_version"      = local.cloudsql_instances_db_version[index],
      "disk_size"       = local.cloudsql_instances_disk_size[index],
      "memory_size"     = local.cloudsql_instances_memory_size[index],
      "max_connections" = local.cloudsql_instances_max_connections[index]
    }
  ]

}

output "cloudsql_instances_info" {
  value = {
    (local.project) = local.cloudsql_info
  }
}

#locals {
#  instance_root_passwords = {
#    cloudsql_portalcache           = { name = module.cloudsql_portalcache.instance_name, password = module.cloudsql_portalcache.instance_password, username = module.cloudsql_portalcache.instance_username },
#    cloudsql_identity              = { name = module.cloudsql_identity.instance_name, password = module.cloudsql_identity.instance_password, username = module.cloudsql_identity.instance_username },
#    cloudsql_dttconfig             = { name = module.cloudsql_dttconfig.instance_name, password = module.cloudsql_dttconfig.instance_password, username = module.cloudsql_dttconfig.instance_username },
#    cloudsql_salesglobal           = { name = module.cloudsql_salesglobal.instance_name, password = module.cloudsql_salesglobal.instance_password, username = module.cloudsql_salesglobal.instance_username },
#    cloudsql_record360             = { name = module.cloudsql_record360.instance_name, password = module.cloudsql_record360.instance_password, username = module.cloudsql_record360.instance_username },
#    cloudsql_rfm                   = { name = module.cloudsql_rfm.instance_name, password = module.cloudsql_rfm.instance_password, username = module.cloudsql_rfm.instance_username },
#    cloudsql_salesglobal_postgres  = { name = module.cloudsql_salesglobal_postgres.instance_name, password = module.cloudsql_salesglobal_postgres.instance_password, username = module.cloudsql_salesglobal_postgres.instance_username },
#    cloudsql_configs               = { name = module.cloudsql_configs.instance_name, password = module.cloudsql_configs.instance_password, username = module.cloudsql_configs.instance_username },
#    cloudsql_rdo_postgres          = { name = module.cloudsql_rdo_postgres.instance_name, password = module.cloudsql_rdo_postgres.instance_password, username = module.cloudsql_rdo_postgres.instance_username },
#  }
#}
#
#resource "google_secret_manager_secret" "instance_root_passwords" {
#  for_each  = local.instance_root_passwords
#  secret_id = "dbadmin_${replace(each.value["name"], "-", "_")}"
#  replication {
#    automatic = true
#  }
#  project = local.management
#}
#
#resource "google_secret_manager_secret_version" "instance_root_passwords" {
#  for_each    = local.instance_root_passwords
#  secret      = google_secret_manager_secret.instance_root_passwords[each.key].id
#  secret_data = jsonencode({ username = each.value["username"], password = each.value["password"] })
#}

data "google_secret_manager_secret_version" "dcs_dev_root_password" {
  secret  = "dcs_postgres_root_user_${var.environment}"
  project = local.project
}

module "cloudsql_dcs_postgres" {
  source            = "../../modules/storage/cloud_sql/v1"
  name              = "dcs01"
  environment       = var.environment
  availability_type = "ZONAL"
  region            = var.region
  project           = local.project
  tier              = "db-custom-1-3840"
  additional_labels = {
    division = "appraisals"
    role     = "database"
    critical = "true"
  }
  database_version               = "POSTGRES_14"
  database_name                  = "dcs"
  username                       = "root"
  password                       = jsondecode(data.google_secret_manager_secret_version.dcs_dev_root_password.secret_data)["password"]
  private_network                = module.services_kubernetes_network.network_link
  keep_public_ip                 = true
  backups_bucket_name            = google_storage_bucket.cloudsql_backups.name
  point_in_time_recovery_enabled = true
  transaction_log_retention_days = 7
  providers = {
    google = google-beta
  }
  insights_config = {
    query_string_length = 2048
  }
  database_flags = [
    { name = "max_connections", value = "14" },
    { name = "cloudsql.enable_pglogical", value = "on" },
    { name = "cloudsql.logical_decoding", value = "on" },
    { name = "max_replication_slots", value = "10" },
    { name = "max_wal_senders", value = "10" },
    { name = "max_worker_processes", value = "8" },
    { name = "log_connections", value = "on" },
    { name = "log_disconnections", value = "on" },
    { name = "log_checkpoints", value = "on" },
    { name = "log_lock_waits", value = "on" },
    { name = "log_statement", value = "ddl" },
    { name = "random_page_cost", value = "1.0" },
    { name = "cloudsql.iam_authentication", value = "on" },
  ]
}

data "google_secret_manager_secret_version" "smartequip_dev_root_password" {
  secret  = "dbadmin_smartequip_root_${var.environment}"
  project = local.management
}
