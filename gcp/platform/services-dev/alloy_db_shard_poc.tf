resource "google_alloydb_cluster" "rfm1001_cluster" {
  cluster_id       = "rfm1001-${var.environment}"
  location         = var.region
  database_version = "POSTGRES_15"

  network_config {
    network            = local.kubernetes_network_id
    allocated_ip_range = "service-network-alloydb"
  }

  automated_backup_policy {
    enabled = false
    time_based_retention {
      retention_period = "1209600s"
    }
  }

  continuous_backup_config {
    enabled              = true
    recovery_window_days = 7
  }
}

resource "google_alloydb_instance" "rfm1001" {
  cluster           = google_alloydb_cluster.rfm1001_cluster.name
  instance_id       = "rfm1001-${var.environment}"
  instance_type     = "PRIMARY"
  availability_type = "REGIONAL"

  database_flags = {
    "google_columnar_engine.enabled"                     = "off",
    "google_columnar_engine.enable_vectorized_join"      = "on",
    "google_columnar_engine.enable_auto_columnarization" = "off",
    "statement_timeout"                                  = "0",
    "alloydb.iam_authentication"                         = "on",
    "max_connections"                                    = "4000",
  }

  network_config {
    enable_public_ip = false
  }

  machine_config {
    cpu_count = 16
  }

  client_connection_config {
    ssl_config {
      ssl_mode = "ENCRYPTED_ONLY"
    }
  }

  lifecycle {
    ignore_changes = [
      machine_config[0].cpu_count,
      network_config,
    ]
  }
}

resource "google_alloydb_instance" "rfm1001_read_pool_instance" {
  cluster       = google_alloydb_cluster.rfm1001_cluster.name
  instance_id   = "rfm1001rp0-${var.environment}"
  instance_type = "READ_POOL"

  database_flags = {
    "google_columnar_engine.enabled"                     = "off",
    "google_columnar_engine.enable_vectorized_join"      = "on",
    "google_columnar_engine.enable_auto_columnarization" = "off",
    "statement_timeout"                                  = "0",
    "alloydb.iam_authentication"                         = "on",
    "max_connections"                                    = "4000",
  }

  network_config {
    enable_public_ip = false
  }

  machine_config {
    cpu_count = 2
  }

  read_pool_config {
    node_count = 1
  }

  lifecycle {
    ignore_changes = [
      machine_config[0].cpu_count,
      read_pool_config[0].node_count,
      database_flags["google_columnar_engine.relations"],
      network_config,
    ]
  }

  depends_on = [
    google_alloydb_cluster.rfm1001_cluster
  ]
}

resource "google_alloydb_instance" "rfm1001_read_pool_1_instance" {
  cluster       = google_alloydb_cluster.rfm1001_cluster.name
  instance_id   = "rfm1001rp1-${var.environment}"
  instance_type = "READ_POOL"

  database_flags = {
    "google_columnar_engine.enabled"                     = "on",
    "google_columnar_engine.enable_vectorized_join"      = "on",
    "google_columnar_engine.enable_auto_columnarization" = "off",
    "statement_timeout"                                  = "0",
    "alloydb.iam_authentication"                         = "on",
    "max_connections"                                    = "4000",
  }

  network_config {
    enable_public_ip = false
  }

  machine_config {
    cpu_count = 8
  }

  read_pool_config {
    node_count = 1
  }

  lifecycle {
    ignore_changes = [
      machine_config[0].cpu_count,
      read_pool_config[0].node_count,
      database_flags["google_columnar_engine.relations"],
      network_config,
    ]
  }

  depends_on = [
    google_alloydb_cluster.rfm1001_cluster
  ]
}

data "google_secret_manager_secret_version" "alloydb-rfm1001-admin" {
  secret  = "alloydb-rfm1001-admin-dev"
  project = local.project
}

resource "google_alloydb_user" "alloydb_rfm1001_user" {
  cluster   = google_alloydb_cluster.rfm1001_cluster.name
  user_id   = "admin"
  user_type = "ALLOYDB_BUILT_IN"
  password  = data.google_secret_manager_secret_version.alloydb-rfm1001-admin.secret_data
  database_roles = [
    "alloydbsuperuser",
    "pg_read_all_data",
    "pg_read_all_stats",
    "pg_write_all_data",
    "root",
  ]
  depends_on = [google_alloydb_instance.rfm1001]
}
