locals {
  namespaces = [
    "default",
    "algo-predictions-etl",
    "analytics-api",
    "analytics-ccs",
    "analytics-dataextractor-api",
    "analytics-dtt",
    "analytics-ect",
    "analytics-metrics",
    "analytics-public-api",
    "analytics-public-py-api",
    "analytics-rdo-api",
    "analytics-rdo-dataservice",
    "analytics-rdo-ui",
    "analytics-rdo-webmqa",
    "analytics-salestax",
    "appraisals-aas",
    "appraisals-auctions",
    "appraisals-classify",
    "appraisals-clientsite",
    "appraisals-coda",
    "appraisals-client-portal",
    "appraisals-dcs-api",
    "appraisals-legacy-api-v2",
    "appraisals-portal",
    "appraisals-rabbitmq",
    "appraisals-record360",
    "appraisals-residuals",
    "appraisals-valuations",
    "appraisals-valuator",
    "burp-enterprise",
    "castai-agent",
    "confluentd-monitoring",
    "dcs-portal",
    "enterprise-authentication",
    "enterprise-circleci",
    "enterprise-circleci-priv",
    "enterprise-config-service",
    "enterprise-defectdojo",
    "enterprise-domain-service",
    "enterprise-image-service",
    "enterprise-media-service",
    "enterprise-keda",
    "enterprise-messaging",
    "enterprise-notification",
    "enterprise-pg-bouncer",
    "enterprise-pgbouncer-rfm03",
    "enterprise-pgbouncer-rfm04",
    "enterprise-pgbouncer-rfm06",
    "enterprise-pgbouncer-rfm07",
    "enterprise-pgbouncer-rfm1001",
    "enterprise-pgbouncer-alloydb",
    "enterprise-pgbouncer-rdo101",
    "pgbouncer-rfmservices01",
    "enterprise-pgbouncer-dev",
    "enterprise-pgbouncer-platform",
    "enterprise-scim",
    "enterprise-selenium-grid",
    "fleet-manager-historian",
    "fleet-manager-integrations",
    "gcsbucketproxy-test",
    "honeycomb-refinery",
    "kafka-retry-service",
    "monitoring",
    "platform-alloydb",
    "platform-classification",
    "platform-service-template",
    "platform-tasks",
    "platform-user-config",
    "platform-valuation",
    "playwright-gcsbucketproxy",
    "pypi-repo",
    "rdo-user-service",
    "rouse-debug-tools",
    "sales-api",
    "sales-cache",
    "sales-catalog",
    "sales-cge",
    "sales-classification",
    "sales-fleet-manager",
    "sales-fleet-manager-tools",
    "sales-fmx-api",
    "sales-ims-migrations",
    "sales-ingest",
    "sales-portal",
    "sales-proposals",
    "sales-notification-service",
    "sales-redirect-service",
    "sales-rfm-channels-api",
    "sales-txns",
    "sales-webshop",
    "sysdig-agent",
    "test-managed-prometheus",
    "valuation-classification",
    "vals-performance-logger-etl",
    "mobile-assets"
  ]
} 

resource "google_service_account" "workload_identity" {
  for_each     = toset(local.namespaces)
  account_id   = each.key
  display_name = substr("GCP SA bound to K8S SA ${each.key}", 0, 100)
  project      = local.project
}

resource "kubernetes_namespace" "workload_identity" {
  for_each = toset(local.namespaces)
  metadata {
    name = each.key
  }
  provider = kubernetes.services-dev
}

resource "kubernetes_service_account" "workload_identity" {
  for_each = toset(local.namespaces)
  metadata {
    name      = each.key
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
    annotations = {
      "iam.gke.io/gcp-service-account" = google_service_account.workload_identity[each.key].email
    }
  }
  provider = kubernetes.services-dev
}

resource "google_service_account_iam_member" "workload_identity" {
  for_each           = toset(local.namespaces)
  service_account_id = google_service_account.workload_identity[each.key].name
  role               = "roles/iam.workloadIdentityUser"
  member             = "serviceAccount:${local.project}.svc.id.goog[${each.key}/${kubernetes_service_account.workload_identity[each.key].metadata[0].name}]"
}

output "workload_identity_k8s_accounts" {
  value = {
    for namespace in local.namespaces :
    namespace => kubernetes_service_account.workload_identity[namespace].metadata[0].name
  }
}

output "workload_identity_gcp_service_accounts" {
  value = {
    for namespace in local.namespaces :
    namespace => google_service_account.workload_identity[namespace].email
  }
}
