locals {
  namespaces = [
    "abod-json-to-gcs-etl",
    "adjustment-factors-etl",
    "age-of-sale-etl",
    "airflow-monitoring-system",
    "airflow-monitoring-system-ims",
    "airflow-monitoring-system-valuations",
    "airflow-pgbouncer-upscale",
    "algo-predictions-etl",
    "algo-vms-lei-results-insert",
    "analytics-admar",
    "analytics-eemphasys",
    "analytics-email-grabber",
    "analytics-irental",
    "analytics-powerbi",
    "analytics-salesforce",
    "analytics-ss-import",
    "analytics-ip-whitelist",
    "analytics-irental-wheeler",
    "api-testing",
    "application-usage-etl",
    "appraisals-api",
    "appraisal-book-etl",
    "appraisal-book-region-adjusters-etl",
    "appraisal-book-region-mappings-etl",
    "appraisals-dcs-api",
    "appraisal-valuation-etl",
    "appraisal-valuation-canary-etl",
    "appraisal-cms-canary-etl",
    "appraisal-valuation-copy-models",
    "appraisals-auction-values-etl",
    "appraisals-inspections",
    "archive-nightly-logging-files",
    "asset-change-report",
    "auction-data-etl",
    "auction-transactions-etl",
    "auctions-etl",
    "auctions-scraper-etl",
    "audit-logs-auth0",
    "audit-logs-github",
    "audit-logs-onepassword",
    "backup-github-repositories",
    "bidadoo-etl",
    "bigquery-physical-storage",
    "book-classification-etl",
    "book-etl",
    "bq-for-audit-etl",
    "bucket-backups",
    "change-history-etl",
    "check-firewall-tags",
    "check-unused-disks",
    "classification-etl",
    "classification-index-etl",
    "classification-photos",
    "classification-rules-etl",
    "client-etl",
    "client-metrics",
    "countries-etl",
    "client-projects-monitor",
    "cloudsql-backups",
    "copy-images",
    "crane-network",
    "create-empty-lookup-values-tables",
    "csmm-relevancy-etl",
    "custom-equipment-etl",
    "custom-grids-benchmark-transactional-detail-etl",
    "custom-grids-fleet-data-etl",
    "custom-grids-performance-vs-benchmark-etl",
    "data-science-models-etl",
    "datastream-ss-export",
    "db-coda-dep-auction-data",
    "db-deploy",
    "dbt-etl",
    "default",
    "delete-node-affinity-pods",
    "enterprise-service-cleanup",
    "equipment-catalog-etl",
    "equipment-classification-etl",
    "equipment-configuration-variants-etl",
    "equipment-details-etl",
    "equipment-files-etl",
    "equipment-notes-history-etl",
    "equipment-options-etl",
    "equipment-photos-etl",
    "equipment-photos-sync-monitor",
    "equipment-sync-monitor",
    "equipment-syndication-etl",
    "equipment-valuations-etl",
    "equipment-values-history-etl",
    "equipment-values-residuals-etl",
    "equipment-view-publisher",
    "fleet-manager-consolidated-data",
    "fleet-manager-expected-time-to-sell-etl",
    "fleet-manager-fleet-customers-monitoring",
    "fleet-manager-fleet-listings",
    "fleet-manager-fleet-metrics",
    "error-logs-alerts",
    "exchange-rates-etl",
    "expanded-equipment-etl",
    "firestore-backup",
    "external-attack-surface",
    "fleet-manager-backfill",
    "fleet-manager-cat-product-group",
    "fleet-manager-customers-metrics-reporting",
    "fleet-manager-multi-checks",
    "fleet-manager-partition-management",
    "fleet-manager-pg-export",
    "fleet-manager-pg-export-multi-tenant",
    "fleet-manager-pg-export-milt-report",
    "fleet-manager-pg-export-change-history",
    "fleet-manager-publish-insights-taxonomy-and-localization-etl",
    "fleet-manager-supercategory-etl",
    "fleet-manager-sales-subcategory",
    "fleet-manager-shard-server-mapping",
    "fleet-manager-sync",
    "fleet-manager-users-etl",
    "fleet-manager-valuations-history-etl",
    "gcp-gce-logging",
    "generate-site-map-etl",
    "github-tracker",
    "health-checks",
    "iam-policy-report",
    "identity-etl",
    "identity-user-pipeline",
    "ietl-admin-deployment",
    "ietl-archive-history-data-etl",
    "ietl-copy-analytics-etl",
    "ietl-input-file-etl",
    "ietl-sold-channel-keywords-etl",
    "ims-fleet-ingest",
    "ims-fleet-refresh",
    "inspections-etl",
    "k8s-vpa-recommendations",
    "k8-sysdig-vulnerability-report",
    "legacy-provisioning-etl",
    "maintenance-walls",
    "manage-equipment-etl",
    "market-segments-etl",
    "marketable-life-etl",
    "marketing-metrics",
    "mascus-csv-listings-etl",
    "mascus-sales-history-import",
    "meter-adjustments-etl",
    "migrations-etl",
    "mixer-export-etl",
    "mixer-values-lookup-configuration-sync",
    "ml-for-rouse-etl",
    "modelyear-range-etl",
    "mpe-kpi-metrics-etl",
    "mpe-transactions-etl",
    "nightly-asset",
    "nightly-deployment",
    "nightly-execution",
    "nightly-files-sync",
    "octopus-cert-renewal",
    "ops-agent-install",
    "pagerduty-stats-collection",
    "pd-snapshots",
    "pgbouncer-cloudsql-ip-update",
    "pglogical-monitoring-rfm101",
    "platform-classification",
    "playwright",
    "pod-cleanup",
    "postgresql-create-db-users",
    "price-performance-etl",
    "proposal-history-etl",
    "public-repo-alerts",
    "pypi-index",
    "rabbitmq-monitoring",
    "random-file-generation",
    "raw-files-sync",
    "rbme-sales-reporting-support-etl",
    "rb-competitive-intelligence-etl",
    "rb-equipment-specification-etl",
    "rb-list-views-etl",
    "rb-list-web-leads-etl",
    "rb-taxonomy-etl",
    "rbc-equipment-classification-etl",
    "rdo-translation",
    "rebasing-automated-run",
    "renew-iis-certs",
    "rambo-smartequip-pipeline",
    "rental-insights-etl",
    "rental-metrics-aggs",
    "rental-insights-data-integrity-checks",
    "resize-spot-node-pool",
    "restore-etl",
    "restore-sharded-dbs",
    "rfm-archiving-etl",
    "rfm-resizing",
    "rfm-copy-logos",
    "riei-combo-classification-etl",
    "ritchie-bros-xml-auction-etl",
    "role-permission-sync-monitor",
    "rouse-rb-taxonomy-mapping-etl",
    "runtime-playbooks",
    "sales-automate-analyst-qa-reporting",
    "sales-configuration-model",
    "sales-erp-sync",
    "sales-fleet-manager-scaling",
    "sales-google-analytics-page-views",
    "sales-invoice-emails",
    "sales-portal-restart",
    "sales-prevailing-exchange-rates-etl",
    "sales-salesforce-reporting",
    "sales-shard-restart",
    "sales-rfm-user-config-reporting",
    "sales-transactions",
    "sales-values-lookup",
    "schedules-import-etl",
    "schedules-module-etl",
    "scout-suite",
    "security-assessment-scope-report",
    "selling-channel-reporting-etl",
    "sentry-alerts",
    "service-monitoring",
    "set-new-ab-cost-etl",
    "setup-os-login",
    "sharded-db-backup-sql-server",
    "sharded-db-reindex",
    "sharded-nightly-updates",
    "smartequip-parts-book-etl",
    "ssis-deployment",
    "ss-export",
    "tenable-cs-dag",
    "training-etl",
    "transaction-volume-auction-etl",
    "transaction-volume-retail-etl",
    "united-xvalues-export-etl",
    "uptime-services-monitor",
    "user-config-rule-change-log-reporting",
    "value-trends-etl",
    "values-lookup-schema-etl",
    "valuations-algo-vm-monitor",    
    "vod-logs",
    "vulnerability-management-report",
    "webshop-pagespeed-metrics",
    "web-leads-etl",
    "wellsfargo-data-export",
    "winrm-setup",
    "wholesale-values-etl",
    "wholesale-values-monitor",
  ]
}

provider "kubernetes" {
  alias                  = "composer-jobs-v3"
  host                   = "https://${module.jobs_cluster_v3.gke_cluster.endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(module.jobs_cluster_v3.gke_cluster.master_auth.0.cluster_ca_certificate)
}

resource "google_service_account" "workload_identity" {
  for_each     = toset(local.namespaces)
  account_id   = length(each.key) < 29 ? each.key : join("", [substr(each.key, 0, 20), random_string.random[each.key].result])
  display_name = substr("GCP SA bound to K8S SA ${each.key}", 0, 100)
  project      = local.project
}

resource "kubernetes_namespace" "workload_identity" {
  for_each = toset(local.namespaces)
  metadata {
    name = each.key
  }
  provider = kubernetes.composer-jobs-v3
}

resource "kubernetes_service_account" "workload_identity" {
  for_each = toset(local.namespaces)
  metadata {
    name      = each.key
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
    annotations = {
      "iam.gke.io/gcp-service-account" = google_service_account.workload_identity[each.key].email
    }
  }
  provider = kubernetes.composer-jobs-v3
}

resource "random_string" "random" {
  for_each = toset(local.namespaces)
  length   = 8
  special  = false
  upper    = false
}

resource "google_service_account_iam_member" "workload_identity" {
  for_each           = toset(local.namespaces)
  service_account_id = google_service_account.workload_identity[each.key].name
  role               = "roles/iam.workloadIdentityUser"
  member             = "serviceAccount:${local.project}.svc.id.goog[${each.key}/${kubernetes_service_account.workload_identity[each.key].metadata[0].name}]"
}

output "workload_identity_k8s_accounts" {
  value = {
    for namespace in local.namespaces :
    namespace => kubernetes_service_account.workload_identity[namespace].metadata[0].name
  }
}

output "workload_identity_gcp_service_accounts" {
  value = {
    for namespace in local.namespaces :
    namespace => google_service_account.workload_identity[namespace].email
  }
}
