data "google_client_config" "default" {}

data "google_kms_secret" "slack_token" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU76LLgdVofkSMMsm9iktSLOFc3E2GdmSY9ZGHFbB5XD8SVgCc5a1pbcduqex2NsQCLNcsvFSeNkHj6+Ru9ihRZoUdSGPotccKNYbZ0NS9ExiaTvgb1xkWca0iiDYsQyzYpt/YlVefQewvfnkkcSgNoVABIGdVOTbf"
}

data "google_kms_secret" "sendgrid_api_key" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU76zCNFOYDIM0AL0WIbA+8ruxkuMcjUK01sWCU8kAPi8SbgCc5a1pUgjDYLbS5NwlTpW7JIWRTcQT8vnnutQZMfSmaUvi/b9/12xNSmTtLvC5jT4hefDM7xO3bDNm/bBmTd6d1aYz14mi1C1C/LjhOGae+9WZDphO0A0UlfiyYO+DEvG5wG3K9NDfzjRqHYq5"
}

data "google_kms_secret" "platform_winrm_prod_pass" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU79PhoBY6QDjlOB72BBaP0Jc3vou2O4/v2CIxtztKTBISNwCc5a1pA66i2dCOi+bkC9vo345Y2N6iBZkoRdS+uy0WINr5tgmGOj+LB9iWp/QDObz5waGM+4I="
}

resource "kubernetes_secret" "platform-winrm-prod-creds-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "analytics-eemphasys",
    "analytics-irental",
    "analytics-email-grabber",
    "nightly-files-sync",
    "analytics-admar",
    "analytics-salesforce",
    "rental-metrics-aggs"
  ])

  metadata {
    name      = "platform-winrm-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "rasgcp\\svc_ETL_WINRM_prod"
    password = data.google_kms_secret.platform_winrm_prod_pass.plaintext
  }
}

resource "kubernetes_secret" "platform-winrm-prod-creds-private" {
  provider = kubernetes.airflow-jobs-private

  for_each = toset([
    "analytics-powerbi",
  ])

  metadata {
    name      = "platform-winrm-prod"
    namespace = kubernetes_namespace.private_cluster_workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "rasgcp\\svc_ETL_WINRM_prod"
    password = data.google_kms_secret.platform_winrm_prod_pass.plaintext
  }
}

data "google_kms_secret" "modelyear_credentials" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "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"
}

resource "kubernetes_secret" "de-modelyear-range-gsheet-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "value-trends-etl",
    "modelyear-range-etl",
    "expanded-equipment-etl",
    "custom-grids-benchmark-transactional-detail-etl",
    "auctions-scraper-etl",
    "rb-competitive-intelligence-etl",
    "ims-fleet-ingest",
    "schedules-module-etl",
    "rbme-sales-reporting-support-etl",
    "data-science-models-etl",
    "rouse-rb-taxonomy-mapping-etl",
    "smartequip-parts-book-etl",
    "ietl-sold-channel-keywords-etl",
    "price-performance-etl",
  ])

  metadata {
    name      = "de-modelyear-range-gsheet-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    credentials = data.google_kms_secret.modelyear_credentials.plaintext
  }
}

data "google_kms_secret" "svc_eqclass_etl_prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7xXsHq4kjqH6mk4Ywdp+r2IdTRW9Wlj5dFXgAaPaJYcSNwCc5a1pWrmx8T/Dl+aG7hZGWDiaN4XueomDTSAakvRsL9/FygQW8a6rKLVJNZIoXSccG+dOu0Y="
}

resource "kubernetes_secret" "de-equipment-classification-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-classification-etl",
  ])

  metadata {
    name      = "de-equipment-classification-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_eqclass_etl"
    password = data.google_kms_secret.svc_eqclass_etl_prod.plaintext
  }
}

data "google_kms_secret" "svc_classrl_etl_prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7yF3sM9zJY7Xf8IcgFPVLReSzPwAvtVxo4eyZX0RoZkSNwCc5a1p3EFSePpeFS6nOPfzl6Gf1r5HYoyotIkcUX3q0o4nCcbbOwUJ8MQ52YqebzDviOmqxmU="
}

resource "kubernetes_secret" "de-classification-rules-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "classification-rules-etl",
  ])

  metadata {
    name      = "de-classification-rules-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_classrl_etl"
    password = data.google_kms_secret.svc_classrl_etl_prod.plaintext
  }
}

data "google_kms_secret" "de-expanded-equipment-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU75vAX0v/Jz6UN+35CgnGBL6Ss3u1eZChIzP0M1Rfiv0SNwA4/8/uCzxGuHUJeGYuLVxpd7r3WgWzKrJIKT+B6X71HxYNSwhnXYsjBXWmq56GNdAtThapM9Y="
}

resource "kubernetes_secret" "de-expanded-equipment-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "expanded-equipment-etl",
  ])

  metadata {
    name      = "de-expanded-equipment-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_expanded_eq_etl"
    password = data.google_kms_secret.de-expanded-equipment-prod.plaintext
  }
}

data "google_kms_secret" "algolia_api_key" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7zVwPZta34j32cnYb5Yol3Cku+XMVOA2MuK3Cl3TNSQSSAA4/8/uobWkDv+Jt52wIrvxED5wuEiJqHDlWZbCI7vG2vi6OuLEhLibduXLuwqeKcQgf5nGcRPfTzyTHE2disEThovxTa2xzA=="
}

resource "kubernetes_secret" "de_algolia_prod_wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "classification-index-etl",
    "restore-etl",
    "values-lookup-schema-etl"
  ])

  metadata {
    name      = "de-algolia-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    api-key = data.google_kms_secret.algolia_api_key.plaintext
  }
}

data "google_kms_secret" "de_appraisal_book_prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7690ic88+yhfK5DnvevdWrjTr6JDxo8z6/1Xy2SZwTESNwCc5a1pGV6t9K1FWAQxFnZGE60JILw972uDX2YQ8GeQb6QFVRQRbqoU9GFhl4EemtOvQBU7DUY="
}

resource "kubernetes_secret" "de_appraisal_book_prod_wi" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "appraisal-book-region-mappings-etl",
    "appraisal-book-etl",
    "appraisal-book-region-adjusters-etl",
  ])
  metadata {
    name      = "de-appraisal-book-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_appbook_etl"
    password = data.google_kms_secret.de_appraisal_book_prod.plaintext
  }
}

data "google_kms_secret" "de-equipment-details-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7/6OswDWRmcQwsv7Bo/4aG6XEK0dEyjIjgpGn8XkLlASOAA4/8/uj1eEEpsWLm8+7w0KCL4+bPDRta5j2FcQ0F9IrrszLONrTNsw0PoUfCoqreqUxzQKTO4W"
}

resource "kubernetes_secret" "de-equipment-details-prod-wi" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "equipment-details-etl",
  ])

  metadata {
    name      = "de-equipment-details-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_eqpdet_etl"
    password = data.google_kms_secret.de-equipment-details-prod.plaintext
  }
}

data "google_kms_secret" "sql_sales_user_prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7ypDjGKLSecCAB/o8MfX7mXi2/s4lXYIc6iN2pKiqnQSOAA4/8/uq/sgtNM/7rVnt+FHE/uL0cFEUFet6cEKcwFUuLKZpbmv1O3BzWhSH+x0kRJV/AiQSQ4G"
}

resource "kubernetes_secret" "sql_sales_user_prod_wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "db-deploy",
    "sharded-nightly-updates",
  ])

  metadata {
    name      = "sql-sales-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "sql_sales_user_prod"
    password = data.google_kms_secret.sql_sales_user_prod.plaintext
  }
}

data "google_kms_secret" "sql_sa_user_vms_prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU77ATIC3NYjttBz28pjd9V4WqFYZycK0qeFXhrjq0PrQSNwA4/8/uUa0FZ5pymBkSWea8sOouADx6gY4Q72kPSosYA4IkaPQWNCcrqt01IriKlQXivC/K1IY="
}

resource "kubernetes_secret" "sql_sa_user_vms_prod_wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-valuations-etl",
    "marketing-metrics",
    "nightly-asset",
    "sales-transactions",
    "vod-logs",
    "db-coda-dep-auction-data",
    "db-deploy",
    "sharded-db-reindex",
    "nightly-deployment",
    "sharded-db-backup-sql-server",
    "sharded-nightly-updates",
  ])

  metadata {
    name      = "sql-sa-user-vms"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "sa"
    password = data.google_kms_secret.sql_sa_user_vms_prod.plaintext
  }
}

resource "kubernetes_secret" "windows_account_vms_prod_wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "restore-sharded-dbs",
    "service-monitoring",
    "nightly-execution",
    "db-deploy",
    "nightly-deployment",
    "archive-nightly-logging-files",
    "sharded-nightly-updates",
    "sharded-db-backup-sql-server",
    "ssis-deployment",
    "security-assessment-scope-report",
    "equipment-syndication-etl",
    "octopus-cert-renewal",
    "winrm-setup",
    "rebasing-automated-run",
  ])

  metadata {
    name      = "windows-account-vms-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "rasgcp\\automation"
    password = data.google_secret_manager_secret_version.windows_account_vms_prod.secret_data
  }
}

data "google_secret_manager_secret_version" "windows_account_vms_prod" {
  secret  = "ansible_rasgcp_automation"
  project = local.terraform_admin
}

resource "kubernetes_secret" "windows_account_vms_dev_wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "nightly-files-sync",
    "service-monitoring",
  ])

  metadata {
    name      = "windows-account-vms-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "rasgcp-dev\\automation"
    password = data.google_secret_manager_secret_version.windows_account_vms_dev.secret_data
  }
}

data "google_secret_manager_secret_version" "windows_account_vms_dev" {
  secret  = "ansible_rasgcp_dev_automation"
  project = local.terraform_admin
}

data "google_kms_secret" "de-auction-transaction" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU73XSB+DEKNiaCZk3JMtMEUiV99nLrdHytHMRvDJBQGYSNQA4/8/ujEnixN+zNrDNynXvSIhWEiznI+wKnoszAvzdcK9oRUM5CqT3UiTEJo0yaZCxX8dM"
}

resource "kubernetes_secret" "de-auction-transaction-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "auction-transactions-etl",
  ])

  metadata {
    name      = "de-auction-transaction-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_auctiontnx_etl"
    password = data.google_kms_secret.de-auction-transaction.plaintext
  }
}

data "google_kms_secret" "sql_sa_user_gdb02" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7yAgQf5WC/1SP09Nghe/jkei9xE5sQIXwLqeIi/+NToSOAA4/8/u/lRsjNrTWR8YtdVh7aezdDC5zhHdMGXQShI5Dk9qN6xAjJn3/SFaJmpayTY2kQ7L/8ra"
}

resource "kubernetes_secret" "sql_sa_user_gdb02_wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "nightly-deployment",
  ])

  metadata {
    name      = "sql-sa-user-gdb02"
    namespace = each.key
  }

  data = {
    username = "sa"
    password = data.google_kms_secret.sql_sa_user_gdb02.plaintext
  }
}

data "google_kms_secret" "de-identity-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU793QdAM3hc6cD0LbLW1VFV84d5pCZBFWEBE/n+6uvSASNwA4/8/ugTUpAoAqMpEdHyTAnW94nK1HGCzthzYnprdrXDPhSr67MYXJdo0yQo+bgJmqWcecXmY="
}

resource "kubernetes_secret" "de-identity-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "identity-etl",
    "custom-grids-benchmark-transactional-detail-etl"
  ])

  metadata {
    name      = "de-identity-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_identity_etl"
    password = data.google_kms_secret.de-identity-prod.plaintext
  }
}

data "google_kms_secret" "de-classification-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU774kqDAKb+8WmY4cEMrFUloLxa9iEnqX5aNgOJcurUASNQA4/8/uY8x9NuyXK4m5km2lQZNuQahACWFtbuv4yKzX1HQIAsohoXv671mYCLyVFM2leJg3"
}

resource "kubernetes_secret" "de-classification-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "classification-etl"
  ])

  metadata {
    name      = "de-classification-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_classification_etl"
    password = data.google_kms_secret.de-classification-prod.plaintext
  }
}

data "google_kms_secret" "de-rdo-translation-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7x1KUo65mtKyI1HB2abddvlaWhnVqJMxy8XbT564hNYSNQA4/8/uWDcOvsWLTrnkh2GaBSF4SHRMBQWCGG2Nm2bCNBd3Lrn44GreV5oa0BdQ/d/zGPzi"
}

resource "kubernetes_secret" "de-rdo-translation-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "rdo-translation",
  ])

  metadata {
    name      = "de-rdo-translation-prod-wi"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_rdo_translation_etl"
    password = data.google_kms_secret.de-rdo-translation-prod.plaintext
  }
}


data "google_kms_secret" "de-book-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7xIOATyghUjlU80uXMHtYUA1C6frW+ehGxfgsSWH01oSNQA4/8/uqMGws2OjsdFSqf9vLO/+A+K370MPCABcY5Z0GzNvD6enMve9kTFd9sTFHI1r9H7k"
}

resource "kubernetes_secret" "de-book-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "book-etl",
    "book-classification-etl"
  ])

  metadata {
    name      = "de-book-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_book_etl"
    password = data.google_kms_secret.de-book-prod.plaintext
  }
}

data "google_secret_manager_secret_version" "qa-check-prod" {
  secret  = "PyAPI-DataChecks-prod"
  project = local.project
}

resource "kubernetes_secret" "qa-check-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "sales-transactions",
  ])

  metadata {
    name      = "qa-check-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  // PyAPI-DataChecks-prod
  data = {
    username = "data-checks"
    password = data.google_secret_manager_secret_version.qa-check-prod.secret_data
  }
}

data "google_kms_secret" "de-stcc-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU70zgMUVWS8SsDtCwcrH+hLR/bAFY7KFrmMc8rm0wY0wSNwA4/8/u3s6iZKhSIfHGA7ahh30X4Ezc7xyYVeeCZuvIInBgQnflfSK804jcqtlvUb33gOU3QYQ="
}

resource "kubernetes_secret" "de-stcc-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "sales-transactions",
  ])

  metadata {
    name      = "de-stcc-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_sales_qa_etl"
    password = data.google_kms_secret.de-stcc-prod.plaintext
  }
}


data "google_secret_manager_secret_version" "salesforce_client_smartequip_api" {
  secret  = "salesforce-client-smartequip-api"
  project = local.project
}

resource "kubernetes_secret" "salesforce_client_smartequip_api_secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "appraisals-api",
  ])

  metadata {
    name      = "salesforce-client-smartequip-api"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.salesforce_client_smartequip_api.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.salesforce_client_smartequip_api.secret_data), "client_secret")
    password      = lookup(jsondecode(data.google_secret_manager_secret_version.salesforce_client_smartequip_api.secret_data), "password")
    username      = lookup(jsondecode(data.google_secret_manager_secret_version.salesforce_client_smartequip_api.secret_data), "username")
  }
}


data "google_secret_manager_secret_version" "kafka_appraisals_secret" {
  secret  = "kafka-appraisals"
  project = local.project
}

resource "kubernetes_secret" "kafka_appraisals_secret_secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "appraisals-api",
  ])

  metadata {
    name      = "kafka-appraisals"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.kafka_appraisals_secret.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.kafka_appraisals_secret.secret_data), "password")
  }
}

data "google_kms_secret" "de-modelyear-range-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7/zQuZIiccxiFVb6p2uQxSnlqrRjzfkBSy2yacVzQAkSOAA4/8/u+aIgBN0Q/86OJsMliDNLF/W/wDf7lT2LjuVlMIvg19rFpHisI2mWyuk+0sKRslGftbW2"
}

resource "kubernetes_secret" "de-modelyear-range-prod-wi" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "modelyear-range-etl",
  ])

  metadata {
    name      = "de-modelyear-range-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_modelyr_etl"
    password = data.google_kms_secret.de-modelyear-range-prod.plaintext
  }
}

data "google_secret_manager_secret_version" "analytics_irental_api_credentials" {
  secret  = "irental-api-credentials"
  project = local.project
}

resource "kubernetes_secret" "analytics_irental_api_credentials" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "analytics-irental-wheeler"
  ])

  metadata {
    name      = "irental-api-credentials"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username      = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_irental_api_credentials.secret_data), "username")
    password      = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_irental_api_credentials.secret_data), "password")
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_irental_api_credentials.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_irental_api_credentials.secret_data), "client_secret")
    dealer_id     = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_irental_api_credentials.secret_data), "dealer_id")
  }
}

data "google_kms_secret" "irental_service_api_password" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7z2T01R6ZJVgwFmj+nrRDJXQ1PtDHshAjLJuMe45Re4SMgA4/8/u2NvU3pc3emfKPoiTN3kxxZi9hqyOauJw+7+dDCKfEiE9yaRTRbqtdul56Vie"
}
data "google_kms_secret" "irental_service_stb_client_secret" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU72Ij5+4hIYyduqOTfAizy3cTxRyvEEro/l7EHWQtLuASTAA4/8/uTqg8mB8bqQ3sqmtC0ktvrmNuhD4peb++t5AkyyoagtYQWIu9SMVG4+Ty5yzN6y/Dh3XzQXRNVLBc8VziqUkNLdW/uFgHT7o="
}
data "google_kms_secret" "irental_service_wha_client_secret" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU75VVz+9KF/77STtLz8YlSH7UDtMtFitejGkHzWPFlyMSTQA4/8/uwkAW9Qfck5uIoaIN7LG3XdJFdi5D/I2FZgvKxM19G1a3+asAYKznqOfFu+khUG5hDgkdZy81snO/tkn5dnzRMndA9VTKojOn"
}
data "google_kms_secret" "irental_service_ohc_client_secret" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU72OteW68Lw2OzdZ4fiQMlnK9e2glbqQ13n/I9w5v4pESTAA4/8/uHk5TRQjVKzHyOb7ayxMXuqGwA0lkATy0UOh3FK3FHSsaqKu+9Tsj5jdh9VTTXGDfMc1nxLlaIHGjQq4FTeDaTgKNE0x7xiI="
}

data "google_secret_manager_secret_version" "analytics_irentals_tom_api_permissions_prod" {
  secret  = "analytics_irentals_tom_api_permissions_prod"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_irentals_hoby_api_permissions_prod" {
  secret  = "analytics_irentals_hoby_api_permissions_prod"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_irentals_kbs_api_permissions_prod" {
  secret  = "analytics_irentals_kbs_api_permissions_prod"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_irentals_rms_api_permissions_prod" {
  secret  = "analytics_irentals_rms_api_permissions_prod"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_irentals_rpm_api_permissions_prod" {
  secret  = "analytics_irentals_rpm_api_permissions_prod"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_irentals_gee_api_permissions_prod" {
  secret  = "analytics_irentals_gee_api_permissions_prod"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_irentals_rex_api_permissions_prod" {
  secret  = "analytics_irentals_rex_api_permissions_prod"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_irentals_wst_api_permissions_prod" {
  secret  = "analytics_irentals_wst_api_permissions_prod"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_irentals_kaige_api_permissions_prod" {
  secret  = "analytics_irentals_kaige_api_permissions_prod"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_irentals_pmc_api_permissions_prod" {
  secret  = "analytics_irentals_pmc_api_permissions_prod"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_irentals_bacn_api_permissions_prod" {
  secret  = "analytics_irentals_bacn_api_permissions_prod"
  project = local.project
}

data "google_secret_manager_secret_version" "analytics_irentals_bra_api_permissions_prod" {
  secret  = "analytics_irentals_bra_api_permissions_prod"
  project = local.project
}

resource "kubernetes_secret" "irental-service-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "analytics-irental",
  ])

  metadata {
    name      = "irental-service-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    api_username        = "RouseApi"
    api_password        = data.google_kms_secret.irental_service_api_password.plaintext
    stb_client_secret   = data.google_kms_secret.irental_service_stb_client_secret.plaintext
    wha_client_secret   = data.google_kms_secret.irental_service_wha_client_secret.plaintext
    ohc_client_secret   = data.google_kms_secret.irental_service_ohc_client_secret.plaintext
    tom_client_secret   = data.google_secret_manager_secret_version.analytics_irentals_tom_api_permissions_prod.secret_data
    hoby_client_secret  = data.google_secret_manager_secret_version.analytics_irentals_hoby_api_permissions_prod.secret_data
    kbs_client_secret   = data.google_secret_manager_secret_version.analytics_irentals_kbs_api_permissions_prod.secret_data
    rms_client_secret   = data.google_secret_manager_secret_version.analytics_irentals_rms_api_permissions_prod.secret_data
    rpm_client_secret   = data.google_secret_manager_secret_version.analytics_irentals_rpm_api_permissions_prod.secret_data
    gee_client_secret   = data.google_secret_manager_secret_version.analytics_irentals_gee_api_permissions_prod.secret_data
    rex_client_secret   = data.google_secret_manager_secret_version.analytics_irentals_rex_api_permissions_prod.secret_data
    wst_client_secret   = data.google_secret_manager_secret_version.analytics_irentals_wst_api_permissions_prod.secret_data
    kaige_client_secret = data.google_secret_manager_secret_version.analytics_irentals_kaige_api_permissions_prod.secret_data
    pmc_client_secret   = data.google_secret_manager_secret_version.analytics_irentals_pmc_api_permissions_prod.secret_data
    bacn_client_secret  = data.google_secret_manager_secret_version.analytics_irentals_bacn_api_permissions_prod.secret_data
    bra_client_secret   = data.google_secret_manager_secret_version.analytics_irentals_bra_api_permissions_prod.secret_data
  }
}

data "google_kms_secret" "email-grabber-secret" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "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"
}

resource "kubernetes_secret" "email-grabber-secret-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "analytics-email-grabber",
  ])

  metadata {
    name      = "email-grabber-secret-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    "secrets.json" : data.google_kms_secret.email-grabber-secret.plaintext
  }
}


data "google_kms_secret" "de-inspections-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU71kq7/LDmvV774v8BnCXnjwZbOcwBFVY+EHmU6ZuTnUSNwA4/8/u9draOxsSI0bpXz0v9Xveafi+c+CW0fZjmtrlofYThL3InlC8OAdi0j/JO0hFGiZB6u0="
}

resource "kubernetes_secret" "de_inspections_prod_wi" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "inspections-etl",
  ])

  metadata {
    name      = "de-inspections-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_inspections_etl"
    password = data.google_kms_secret.de-inspections-prod.plaintext
  }
}

data "google_kms_secret" "qa-portal-prod-password" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU737sAkJs2zU8YYyUGP2Qs4pFplgsQhXKJbaA7NvKlSkSOwA4/8/u4xnt9nk0HJr4ruZt9QIv93szMWN5/+z+2gXZjWqFIaY8aZ5wEZbvHVbw1ekh8AS0kgQopF8j"
}

resource "kubernetes_secret" "qa-portal-prod-password-jobs-v3" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "qa-portal-prod-password"
    namespace = kubernetes_namespace.workload_identity["api-testing"].metadata[0].name
  }

  data = {
    password = data.google_kms_secret.qa-portal-prod-password.plaintext
  }
}

data "google_kms_secret" "qa-portal-auth0-client-id-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7y1vCCRAtdQAzO3aQOTt6QwXrswNuI3C6hTmyYsN9NYSTQA4/8/uwD5qshEM9gpM0gRs0F2O7U7pI9mLfrN047OQ3W0Ln/xwwSAHyQySv+6lYNJWk/eiB/Pht3n42FBA/97pJQAMhrN1aozTz4eP"
}

data "google_kms_secret" "qa-portal-auth0-client-secret-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7+g5EjaTZqF1M2HgS8ffQaLEQ4QEhX7c0eRsReJFENMSTQA4/8/u7lpT3VVGSv09Zked8IJqB6xfvqOGr6KL/JMuRztRWBqF0k0LFfUVyufaedaJcgnUBBqq5f6ckeWsfIVG7+8Xp9tvK/5dafvj"
}

resource "kubernetes_secret" "qa-portal-auth0-keys-prod-jobs-v3" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "qa-portal-auth0-keys-prod"
    namespace = kubernetes_namespace.workload_identity["api-testing"].metadata[0].name
  }

  data = {
    client_id     = data.google_kms_secret.qa-portal-auth0-client-id-prod.plaintext
    client_secret = data.google_kms_secret.qa-portal-auth0-client-secret-prod.plaintext
  }
}

data "google_kms_secret" "de-market-segments-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU76OHnDJD9prTy7woMBsbjwp9KTuzUP1RTXveytxfp1gSNwA4/8/u0yr7k+Jtls0lIq24/W6OabOLrQkSn6BuJjqrk2fTqEQ+aImZYNRtSUXJ/DHaP7H39OY="
}

resource "kubernetes_secret" "de-market-segments-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "market-segments-etl",
  ])

  metadata {
    name      = "de-market-segments-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_market_segments_etl"
    password = data.google_kms_secret.de-market-segments-prod.plaintext
  }
}

data "google_kms_secret" "de-client-projects-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU701sXeI6EsWsjnr43AVl/TVE3dwYQWxxZWFFlJEQH9gSNwA4/8/uDSW64/1TMrc9WXC1ldzBoF8G2xhu6bArLSjLWNN4hELPwGjToCvCQBVUBuGS8AbMpZg="
}

resource "kubernetes_secret" "de-client-projects-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-client-projects-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  for_each = toset([
    "client-projects-monitor",
  ])

  data = {
    username = "svc_client_prj_etl"
    password = data.google_kms_secret.de-client-projects-prod.plaintext
  }
}


data "google_kms_secret" "de-wholesale-factors-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU78h05o/TGORUXuYhorj2iqJa7HMDzNCdLOgTI4f/rewSNgA4/8/uqfRnociqefKghQffUzOs+tYe3pBRbLp6yBLGVZqsuUAs6TPjpIemN6C9qhqmYH9nAw=="
}

resource "kubernetes_secret" "de-wholesale-factors-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "wholesale-values-etl",
  ])

  metadata {
    name      = "de-wholesale-factors-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_wholesale_factors_etl"
    password = data.google_kms_secret.de-wholesale-factors-prod.plaintext
  }
}

data "google_kms_secret" "github-username-secrets" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7xsABCxL6mZ3TxtekaYqHoCnqMTy+dsSnxBUvZMitqISNAA4/8/upwBIxI1Q7o/Pwcl1MA2J2lbpTOunawhGFPePMEbXwIvW6e/8Ba4ozZ6DtRS+enU="
}

data "google_kms_secret" "github-token-secrets" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7wuQajvUBbY2PLMhCguntE+gVncLYwWHwpUiDRsbyfwSUAA4/8/uoBfIPRtLDmlOGldYWTxJVkSJlFjh5uygY7cc7aZb0Wn+PYgYl5Ngblx28iRlY90rTGdRgFyp1jP1TfzePCGsSrBjjtD+pkTQcw/0"
}

resource "kubernetes_secret" "github-secrets-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "backup-github-repositories",
    "equipment-valuations-etl",
    "pgbouncer-cloudsql-ip-update"
  ])

  metadata {
    name      = "github-secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = data.google_kms_secret.github-username-secrets.plaintext
    token    = data.google_kms_secret.github-token-secrets.plaintext
  }
}

data "google_kms_secret" "sql-salesglobal01-user-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU78Bh2CE06Zt5UPH0yHMuoBgxWR2GEm9unwogUYjbZr8SPQA4/8/ulGMdQG2sSxuUYuylrEMuDp8EJ9usi+RxnfwarVfMqoKr1947iffD/dP9uakXsL3pgKS0RHBrQD0="
}

resource "kubernetes_secret" "sql-salesglobal01-user-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "db-coda-dep-auction-data",
  ])

  metadata {
    name      = "sql-salesglobal01-user-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "sqlautomation"
    password = data.google_kms_secret.sql-salesglobal01-user-prod.plaintext
  }
}

data "google_kms_secret" "de-adjustment-factors-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU74lpDg4pmjpD+sWNFSNYqvISlzWl/3OeIrz7bDtPuAQSNwA4/8/uDFN6k6ElXSiclrcPvK+wt+35rQstKi+aHf+Sm9efaQlA33Y1J9hBvU97t6ZxoKDTu3A="
}

resource "kubernetes_secret" "de-adjustment-factors-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "adjustment-factors-etl",
  ])

  metadata {
    name      = "de-adjustment-factors-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_adjustment_factors_etl"
    password = data.google_kms_secret.de-adjustment-factors-prod.plaintext
  }
}

data "google_kms_secret" "de-equipment-valuations-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU79hfEYW4eF7Xg9SlX+5ADPZiidMm+fAUScj5ewF7T7wSNwA4/8/ufJBc8gLHLTfLfu7DtNkfGmNSVn+FOd1eng6yGyHuX8q8ZzJXilOxPsQD4//eyvB/j8Y="
}

resource "kubernetes_secret" "de-equipment-valuations-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-valuations-etl",
  ])

  metadata {
    name      = "de-equipment-valuations-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_equipment_valuations_etl"
    password = data.google_kms_secret.de-equipment-valuations-prod.plaintext
  }
}

data "google_secret_manager_secret_version" "qa-slack-url" {
  secret  = "qa-slack-url-${var.environment}"
  project = local.qa_tools
}

resource "kubernetes_secret" "qa-slack-url-jobs-v3" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "qa-slack-url-${var.environment}"
    namespace = kubernetes_namespace.workload_identity["api-testing"].metadata[0].name
  }

  data = {
    url = data.google_secret_manager_secret_version.qa-slack-url.secret_data
  }
}

data "google_kms_secret" "de-meter-adjustments-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7xPUmZ0Y6Cy4SBKW/XCbZN38PnVUqexTJlQOfp4wh9YSNwA4/8/uHH4bKD5sdm2QGlr6dfkDtg4HeRMatPqlk9VSjj/9Z/tDO4OLTPWs5fXfxeNqOu+Qubs="
}

resource "kubernetes_secret" "de-meter-adjustments-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "meter-adjustments-etl",
  ])

  metadata {
    name      = "de-meter-adjustments-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_meter_adjustments_etl"
    password = data.google_kms_secret.de-meter-adjustments-prod.plaintext
  }
}

data "google_kms_secret" "de-record360-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU72w7kbCK+KnVC4nSPOCEHfkLntG9dLnREz0T7et4SIwSOQA4/8/u0Lc0CG0v7S4tgAhwV8BCguvOtE5g9lbWwl7QDFuhPGTAHXGQL36Y8iwDWua2J8iOYDwOow=="
}

resource "kubernetes_secret" "de-record360-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "inspections-etl",
  ])

  metadata {
    name      = "de-record360-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_kms_secret.de-record360-prod.plaintext
  }
}

resource "kubernetes_secret" "appraisals-prod-root" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "inspections-etl",
  ])

  metadata {
    name      = "appraisals-prod-root"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_kms_secret.de-record360-prod.plaintext
  }
}

data "google_secret_manager_secret_version" "analytics_salesforce_api_credentials" {
  secret  = "analytics_salesforce_api_credentials"
  project = local.services
}

resource "kubernetes_secret" "analytics_salesforce_api_credentials" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "analytics-salesforce-api-credentials"
    namespace = kubernetes_namespace.workload_identity["rental-metrics-aggs"].metadata[0].name
  }

  data = {
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_salesforce_api_credentials.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_salesforce_api_credentials.secret_data), "client_secret")
  }
}


data "google_secret_manager_secret_version" "analytics_salesforce_smartequip_credentials" {
  secret  = "analytics_salesforce_smartequip_credentials"
  project = local.services
}

resource "kubernetes_secret" "analytics_salesforce_smartequip_credentials" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "rental-metrics-aggs",
    "rambo-smartequip-pipeline",
  ])

  metadata {
    name      = "analytics-salesforce-smartequip-credentials"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    client_id       = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_salesforce_smartequip_credentials.secret_data), "client_id")
    client_secret   = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_salesforce_smartequip_credentials.secret_data), "client_secret")
    client_username = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_salesforce_smartequip_credentials.secret_data), "client_username")
    client_password = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_salesforce_smartequip_credentials.secret_data), "client_password")
  }
}


data "google_kms_secret" "de-rambo-export-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7wMXheotManxQMmp+k3J298Ch8XLtjAghmIq96WOfeMSNQA4/8/uOiCyat/VYddlLS21M36bK6Q6tG4lGSvCSrltptQyLIMJbIT412cScN+L9uNMrpcZ"
}

resource "kubernetes_secret" "de-rambo-export-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "rental-metrics-aggs",
    "custom-grids-benchmark-transactional-detail-etl",
    "rambo-smartequip-pipeline",
    "rdo-translation",
  ])

  metadata {
    name      = "de-rambo-export-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_rambo_export"
    password = data.google_kms_secret.de-rambo-export-prod.plaintext
  }
}

data "google_kms_secret" "de-exchange-rates" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU73maPY6hLOyg5u6/s5BUrQhAmldZmKyv3INk3FSv2EgSNwA4/8/ul7/17fTJPqtVN+y+8UAhUdkH5SOZ5yJ8XSgwgfABfTHtXZ9QlEfFxPV9EM0S8xn5ZfY="
}

resource "kubernetes_secret" "de-exchange-rates" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "exchange-rates-etl",
  ])

  metadata {
    name      = "de-exchange-rates-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_exchange_rates_etl"
    password = data.google_kms_secret.de-exchange-rates.plaintext
  }
}

data "google_kms_secret" "de-eqp-values-residuals" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU75Sg5LRxkpj7P/78E7fsjdTbQAIYtYh2bVrLkcTU4o8SNwA4/8/uuD232/0YsKlowRlSu4R6HqMPwceSR4lQS8GqsPfKD7zvfb6KusbuSnXR4jwL5y+20M4="
}

resource "kubernetes_secret" "de-eqp-values-residuals" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-values-residuals-etl",
  ])

  metadata {
    name      = "de-eqp-values-residuals-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_eqp_values_residuals_etl"
    password = data.google_kms_secret.de-eqp-values-residuals.plaintext
  }
}

data "google_kms_secret" "rfm01-root-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU72Kg9uBTyC/D+gmDJ+dnO1jp5WjqPTD8PQYG+WIOgoASPQA4/8/uXfstslAPNJm/pQ67ToD3M23UBggBap568EIUlxQRKuijXETZcGba26NFDgqqOliJKiREpkOtVlg="
}


resource "kubernetes_secret" "rfm01-root-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "postgresql-create-db-users",
    "pglogical-monitoring-rfm101"
  ])

  metadata {
    name      = "rfm01-root"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_kms_secret.rfm01-root-prod.plaintext
  }
}

data "google_kms_secret" "salesglobal02-sqlautomation-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU78J+lDf1YDCr6PQCEOCKxePRYOzaoT4Wmj5m11OYz6QSPQA4/8/uz9sWshCFScaP5mT8HancbC1xUtaK4OTE4vgBGOtc19BRxUL41RmV/zBWAWNPBJ7Syu7BVsUVzyQ="
}

resource "kubernetes_secret" "salesglobal02-sqlautomation-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "postgresql-create-db-users",
  ])

  metadata {
    name      = "salesglobal02-sqlautomation"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "sqlautomation"
    password = data.google_kms_secret.salesglobal02-sqlautomation-prod.plaintext
  }
}

data "google_kms_secret" "vnext-service-account-password" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU79UfT1gDMmXcrIp7LFnH0tdyW1Morq+UB7rK4FpCW8ISPQA4/8/uvXtLnZnnEydtGRdcs0Bj9nTM4uPG9ocVjs34pOuAJRnq1775pO7Bb0rbKoRyAN4dGaMzUk0hsLE="
}

data "google_kms_secret" "vnext-service-account-token" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU73LZtrOoaLCoGGQ7Ec5E2Gq1HJ+srHbJMmoQCTuagsQSaQA4/8/ueHVr6AFUWfV4s+wqMg62T++Fqp43ul2aBXAqif2FntuFOt4DDn0wvNzzMcovq3QqH3ROaUf34wt9+HZ436+b3An4F4fclydyPYRZa4GIjW6TFnFZlRGp/Ige0uJF/tqs6oQC/A=="
}

resource "kubernetes_secret" "vnext-service-account-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "crane-network",
    "mascus-csv-listings-etl",
    "ritchie-bros-xml-auction-etl",
    "rb-competitive-intelligence-etl",
  ])

  metadata {
    name      = "vnext-service-account"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "<EMAIL>"
    password = data.google_kms_secret.vnext-service-account-password.plaintext
    token    = data.google_kms_secret.vnext-service-account-token.plaintext
  }
}

data "google_kms_secret" "de-prev-exch-rates" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7yFVJDR9P3CvKHiX/PbFXblHHQrHaW5JKnQGRsVQ5eMSNwA4/8/udWf8mzEsAI9HtqvgH9jRlxrOVBJBD3UZYjRjUoWSDAjHf9G23sVGIx5+W7osfmpmH4c="
}

resource "kubernetes_secret" "de-prev-exch-rates" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "sales-prevailing-exchange-rates-etl",
  ])

  metadata {
    name      = "de-prev-exch-rates-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_prev_exch_rate_etl"
    password = data.google_kms_secret.de-prev-exch-rates.plaintext
  }
}

data "google_kms_secret" "de-manage-equipment" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7/IJ6kQp3MBmVZLZzdNm4M1LMfEZJJwCW1NKVAlX3twSNwA4/8/uvW1gfQ3MowKFCurWa9COl2vKU2DC5Mp5UhWNticHGITlx4WgoV26cmzfx2GDZrvn9Ig="
}

resource "kubernetes_secret" "de-manage-equipment" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "manage-equipment-etl",
    "equipment-sync-monitor",
  ])

  metadata {
    name      = "de-manage-equipment-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_equipment_etl"
    password = data.google_kms_secret.de-manage-equipment.plaintext
  }
}

data "google_secret_manager_secret_version" "mssql_equipment_options_etl" {
  secret  = "mssql_equipment_options_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-equipment-options-etl-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-equipment-options-etl-prod"
    namespace = kubernetes_namespace.workload_identity["equipment-options-etl"].metadata[0].name
  }

  data = {
    username = "svc_equipment_options"
    password = data.google_secret_manager_secret_version.mssql_equipment_options_etl.secret_data
  }
}

data "google_kms_secret" "de-pg-manage-equipment" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7+RDVzuwS/gH1Kg2AjQrs6rnI2Y6NEK5OmaQifh9/VsSPQA4/8/ujQFDGMYqipF1Pr3wJRu2ZkLFZ6sUASDlLKLU4Y11gsMx2AZv6KMSyxmnJg3nxj5j5obUri9bKh4="
}

resource "kubernetes_secret" "de-pg-manage-equipment" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "manage-equipment-etl",
    "equipment-sync-monitor",
  ])

  metadata {
    name      = "de-pg-manage-equipment-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "equipment_etl"
    password = data.google_kms_secret.de-pg-manage-equipment.plaintext
  }
}


data "google_kms_secret" "de-equipment-photos" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU73mtZMYW0zgQYQ4ECn6HsCkY9kzEc4XpceqShvDQp04SNwA4/8/unFjS+X+KmaPp4nEDjO392gRipOEgti7h1P09IEkjoy2x0VRmwuxNsx3pvf4VhN1sBK8="
}

resource "kubernetes_secret" "de-equipment-photos" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-photos-etl",
    "equipment-photos-sync-monitor",
  ])

  metadata {
    name      = "de-equipment-photos-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_equipment_photos_etl"
    password = data.google_kms_secret.de-equipment-photos.plaintext
  }
}


data "google_kms_secret" "de-pg-equipment-photos" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7/jPGHEVF834F+EFH39QsAtJg6/w164pGxFlwMPN9AsSPQA4/8/uL0MNfOXRQjCsS5vh5OM/rf3PKAe6vq8BcC68/SLULArgYwwX8aGFKOK19d0N92z7u9zEs7PyQsg="
}

resource "kubernetes_secret" "de-pg-equipment-photos" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-photos-etl",
    "equipment-photos-sync-monitor",
  ])

  metadata {
    name      = "de-pg-equipment-photos-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "equipment_photos_etl"
    password = data.google_kms_secret.de-pg-equipment-photos.plaintext
  }
}


data "google_kms_secret" "de-equipment-catalog-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7+4LrimJchJ+HTOWpuAKIg9DpyAmx32O3aIDxqKOXUQSNwA4/8/uZfl7t0IMTPlQAuth6OTIPr/PK/HYTFPYvijWs7E/XDTZvsjXyAewk8vhzB7UxQskwOc="
}

resource "kubernetes_secret" "de-equipment-catalog-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-catalog-etl",
    "equipment-configuration-variants-etl",
  ])

  metadata {
    name      = "de-equipment-catalog-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_equipment_catalog_etl"
    password = data.google_kms_secret.de-equipment-catalog-prod.plaintext
  }
}


data "google_kms_secret" "de-pg-equipment-catalog-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7yCYEPuvW3VT9NTv39nJmffD1DRlgjsajnvuK3QBlbwSPQA4/8/uXyfz3k9hmJaDnX2G95Rdf5hsoTdrylq2WOi9a+cwO7N1lUR2awVe449EqJQe0xKjJTteY8EcTkw="
}

resource "kubernetes_secret" "de-pg-equipment-catalog-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-catalog-etl",
    "classification-photos",
  ])

  metadata {
    name      = "de-pg-equipment-catalog-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "equipment_catalog_etl"
    password = data.google_kms_secret.de-pg-equipment-catalog-prod.plaintext
  }
}


data "google_kms_secret" "de-clients-excluded-from-reports-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7/KSTzBuVg8xSL25LxM5O0EAuYEr6H2IjNnLTxT1hTwSNwA4/8/u43r+HzxwxX1mGuACTL/e9d+3JLB9JcxutP3SGVp8kxfZxD01xmgA4Mwg1N5yn5bpoJs="
}

resource "kubernetes_secret" "de-clients-excluded-from-reports-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "client-etl",
  ])

  metadata {
    name      = "de-clients-excluded-from-reports-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_clients_excluded_from_reports_etl"
    password = data.google_kms_secret.de-clients-excluded-from-reports-prod.plaintext
  }
}


data "google_kms_secret" "de-schedules-import-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7+wFTKZ4PEOEDFRB/3JATO0QnG4AyU7dhDbhVXAbXP4SNwA4/8/ubtQkiisFcwyK3kZU64iVHkyn93plwgARN3x5fK73oActOYIKNuPGWvCW7fe1t4YZQWY="
}

resource "kubernetes_secret" "de-schedules-import-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "schedules-import-etl",
  ])

  metadata {
    name      = "de-schedules-import-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_schedule_import_etl"
    password = data.google_kms_secret.de-schedules-import-prod.plaintext
  }
}


data "google_kms_secret" "de-eqp-values-history-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU74G4qLWeCBYPNVKK/tJ5+X8sye9uAclSCcf3DGRGmY0SNwA4/8/uub6vX/Z3RxZFXOr39wk/Tfz900eXncoZsmxJvB5e1fO+5bOuqn75TtfR7n4URopAF4Q="
}

resource "kubernetes_secret" "de-eqp-values-history-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-values-history-etl",
  ])

  metadata {
    name      = "de-eqp-values-history-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_equipment_values_history_etl"
    password = data.google_kms_secret.de-eqp-values-history-prod.plaintext
  }
}


data "google_kms_secret" "de-pg-eqp-values-history-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7/Zx+yy0GLsiv+eVkVDvSFeaRQil5MHzbaghBNItjfQSPQA4/8/uczFxbsFZv+bl/+CY3yzkRFp4OocE7bS4b35kPvNYuPRkUTKSLp7Pmcu516OOy9FVy7ftT2naCCY="
}

resource "kubernetes_secret" "de-pg-eqp-values-history-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-values-history-etl",
  ])

  metadata {
    name      = "de-pg-eqp-values-history-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "equipment_values_history_etl"
    password = data.google_kms_secret.de-pg-eqp-values-history-prod.plaintext
  }
}


data "google_kms_secret" "de-pg-sales-txns-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU72f3lvptRd0MReEI1Rr70VFEzlsflOZJIxyy7FWwvysSPQA4/8/u9GImhJ3w9Ez05K4pFoFV+ML59eqzwbKMnMi4HDG8X9Jkq6yB5ytHyz1nxkQPzDBEt0B61xKiBB4="
}

resource "kubernetes_secret" "de-pg-sales-txns-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "sales-transactions",
  ])

  metadata {
    name      = "de-pg-sales-txns-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "sales_txns_etl"
    password = data.google_kms_secret.de-pg-sales-txns-prod.plaintext
  }
}

data "google_kms_secret" "de-pg-mkt-metrics-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU77W34FqiVI8Oz8Ch7/Df1oS6ZsOtNAJfmHN/LkAHkxISPQA4/8/uDISyhx+HaTAABmb3818oc77ieHBLf/lkjTccxNpVcClLtgeVzFcgRiodQzZZ07nZ7e5WyMSw32Q="
}

resource "kubernetes_secret" "de-pg-mkt-metrics-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "marketing-metrics",
  ])

  metadata {
    name      = "de-pg-mkt-metrics-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "mkt_metrics_etl"
    password = data.google_kms_secret.de-pg-mkt-metrics-prod.plaintext
  }
}

data "google_kms_secret" "de-application-usage-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU74AipfUYIrc79j0PbwDiRONDMbYnOMle08eXHU6OhdESNwA4/8/ugzCdJojpBCci4mfM1MKVo855QOAyvrwXCTfsCYcnUtCAbcz9GfLlwo2F13LCiWWPdMU="
}

resource "kubernetes_secret" "de-application-usage-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "application-usage-etl",
  ])

  metadata {
    name      = "de-application-usage-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_application_usage_etl"
    password = data.google_kms_secret.de-application-usage-prod.plaintext
  }
}

data "google_kms_secret" "de-cg-benchmark-transactional-detail-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU72UQayRQduvkjosh/lO9N8H1HFNEF7duo5yahoQiBpASNwA4/8/uf/OOCJY0l699BtYpUMrp3ZnVX24HvJGTxTApQ8LHdbu2wIvY2jL9a+yGTd6HkYPwpGM="
}

resource "kubernetes_secret" "de-cg-benchmark-transactional-detail-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "custom-grids-benchmark-transactional-detail-etl",
    "rental-metrics-aggs",
    "rdo-translation",
  ])

  metadata {
    name      = "de-cg-benchmark-transactional-detail-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_cg_sales_rep_book_var_etl"
    password = data.google_kms_secret.de-cg-benchmark-transactional-detail-prod.plaintext
  }
}

data "google_kms_secret" "de-pg-cg-benchmark-transactional-detail-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU750xOPkPrq+pPAXTcf7x4qk0FTZXB4P2h4iszbO07Z8SPQBp1KhiuOInscPSHCP1gNnvU/Hrule1AvP3PSu2+OTdyN3iwOLD4dpJsXkHkpjD/BOxuLgFjY/p55+4WsE="
}

resource "kubernetes_secret" "de-pg-cg-benchmark-transactional-detail-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "custom-grids-benchmark-transactional-detail-etl",
    "rental-metrics-aggs",
  ])

  metadata {
    name      = "de-pg-cg-benchmark-transactional-detail-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "custom_grids_sales_rep_book_variance_etl"
    password = data.google_kms_secret.de-pg-cg-benchmark-transactional-detail-prod.plaintext
  }
}

data "google_kms_secret" "de-cg-fleet-data-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU77AkoMNabIYm78xKT2Uzrf2Ho40LEjh2kZ6lldthyvMSNwA4/8/ugVf99VjRKTTGAsLwA43nI4eiLW4kcPwVF7IEaJfxrrhc3Va3bLAkRzFmI/fDYDjgbkE="
}

resource "kubernetes_secret" "de-cg-fleet-data-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "custom-grids-fleet-data-etl",
  ])

  metadata {
    name      = "de-cg-fleet-data-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_cg_fleet_data_etl"
    password = data.google_kms_secret.de-cg-fleet-data-prod.plaintext
  }
}

data "google_secret_manager_secret_version" "analytics_rdo_scheduler_auth0_config_prod" {
  secret  = "analytics_rdo_scheduler_auth0_config"
  project = local.services
}

resource "kubernetes_secret" "analytics_rdo_scheduler_auth0_config_prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "custom-grids-benchmark-transactional-detail-etl",
    "analytics-ip-whitelist",
    "rental-metrics-aggs",
  ])

  metadata {
    name      = "analytics-rdo-scheduler-auth0-config-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_scheduler_auth0_config_prod.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_scheduler_auth0_config_prod.secret_data), "client_secret")
  }
}

data "google_kms_secret" "rdo04-root-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7/yweIBOa2XuDD5US1eJX3Zmd7mtkmdi8PqQ2X+LMsMSPQA4/8/uxPpyw1ATa4u6cK4YSjnrn/t0odl5XtmTidHlVEGlURQvZulrvBPIYwESdN0gYrdkIoN2TYAjPkI="
}

resource "kubernetes_secret" "rdo04-root-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "postgresql-create-db-users",
  ])

  metadata {
    name      = "rdo04-root"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }
  data = {
    username = "root"
    password = data.google_kms_secret.rdo04-root-prod.plaintext
  }
}

resource "kubernetes_secret" "platform01-root-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "postgresql-create-db-users",
  ])

  metadata {
    name      = "platform01-root"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.de_pg_platform_dbadmin.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.de_pg_platform_dbadmin.secret_data), "password")
  }
}

data "google_kms_secret" "de-pg-cg-fleet-data-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU78MjUxf7HmQsm/DuBUEBcuBQqemzcapGq6nLzJf+9/8SPQBp1Khi0oKUX3Tfyu8565eCZH7pjsY/BmxanKFD1XuJALHlTPXY0tuiRQHoK1nruFr51WEbugiUasuccAo="
}

resource "kubernetes_secret" "de-pg-cg-fleet-data-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "custom-grids-fleet-data-etl",
  ])

  metadata {
    name      = "de-pg-cg-fleet-data-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "custom_grids_fleet_data_etl"
    password = data.google_kms_secret.de-pg-cg-fleet-data-prod.plaintext
  }
}

data "google_secret_manager_secret_version" "dbuser_platform" {
  for_each = toset([
    "fleet_manager_sales_subcategory",
    "model_year_range_etl",
    "equipment_details_etl",
  ])
  secret  = "dbuser_platform_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_platform" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    fleet_manager_sales_subcategory = {
      namespace   = "fleet-manager-sales-subcategory",
      secret_name = "de-pg-fleet-manager-sales-subcategory-${var.environment}"
    }
    model_year_range_etl = {
      namespace   = "modelyear-range-etl",
      secret_name = "de-pg-model-year-range-${var.environment}"
    }
    equipment_details_etl = {
      namespace   = "equipment-details-etl",
      secret_name = "de-pg-equipment-details-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    username = each.key
    password = data.google_secret_manager_secret_version.dbuser_platform[each.key].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_rfm" {
  for_each = toset([
    "appraisal_valuation_canary_etl",
    "auctions_etl",
    "create_empty_lookup_values_etl",
    "equipment_configuration_variants",
    "equipment_sync_monitor",
    "transaction_volume_etl",
    "transaction_volume_auction_etl",
    "value_trends_etl",
    "equipment_values_residuals_etl",
    "equipment_valuations_etl",
    "equipment_view_publisher",
    "fleet_manager_consolidated_data",
    "fleet_manager_expected_time_to_sell_etl",
    "fleet_manager_fleet_customers_monitoring", 
    "fleet_manager_fleet_listings", 
    "fleet_manager_fleet_metrics",
    "client_etl",
    "countries_etl",
    "client_asset_etl",
    "csmm_relevancy_etl",
    "copy_classification_photos_etl",
    "equipment_catalog_api",
    "equipment_classification_etl",
    "rouse_rb_taxonomy_mapping_etl",
    "fleet_manager_customers_metrics_reporting",
    "fleet_manager_partition_management_pipeline",
    "fleet_manager_pg_export",
    "fleet_manager_pg_export_multi_tenant",
    "fleet_manager_publish_insights_taxonomy_and_localization_etl",
    "fleet_manager_backfill",
    "fleet_manager_supercategory_etl",
    "fleet_manager_sync",
    "fleet_manager_users_etl",
    "fleet_manager_valuations_history_etl",
    "fleet_manager_cat_product_group",
    "equipment_syndication_etl",
    "equipment_notes_history_etl",
    "sales_invoice_emails",
    "sales_transactions_etl",
    "proposal_history_etl",
    "change_history_etl",
    "user_profiles",
    "equipment_files_etl",
    "web_leads_etl",
    "legacy_provisioning_etl",
    "marketable_life_etl",
    "migrations_etl",
    "mpe_kpi_metrics_etl",
    "mpe_transactions_etl",
    "rfm_archiving_etl",
    "restore_etl",
    "health_check",
    "fleet_manager_multi_checks",
    "rental_insights_etl",
    "mixer_export_etl",
    "mixer_values_lookup_configuration_sync",
    "rb_list_views_etl",
    "rb_list_web_leads_etl",
    "rb_taxonomy_etl",
    "sales_erp_sync",
    "sales_google_analytics_page_views",
    "sales_rfm_user_config_reporting",
    "selling_channel_reporting_etl",
    "smartequip_parts_book_etl",
    "ss_export",
    "user_config_rule_change_log_reporting",
    "united_xvalues_export_etl",
  ])
  secret  = "dbuser_rfm_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_rfm" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    appraisal_valuation_canary_etl = {
      namespace   = "appraisal-valuation-canary-etl",
      secret_name = "de-pg-appraisal-valuation-canary-${var.environment}"
    }
    auctions_etl = {
      namespace   = "auctions-etl",
      secret_name = "de-pg-auctions-${var.environment}"
    }
    create_empty_lookup_values_etl = {
      namespace   = "create-empty-lookup-values-tables",
      secret_name = "de-pg-create-empty-lookup-values-${var.environment}"
    }
    equipment_configuration_variants = {
      namespace   = "equipment-configuration-variants-etl",
      secret_name = "de-pg-configruation-variants-${var.environment}"
    }
    equipment_sync_monitor = {
      namespace   = "equipment-sync-monitor",
      secret_name = "de-pg-equipment-sync-${var.environment}"
    }
    transaction_volume_etl = {
      namespace   = "transaction-volume-retail-etl",
      secret_name = "de-pg-transaction-volume-${var.environment}"
    }
    transaction_volume_auction_etl = {
      namespace   = "transaction-volume-auction-etl",
      secret_name = "de-pg-transaction-volume-auction-${var.environment}"
    }
    value_trends_etl = {
      namespace   = "value-trends-etl",
      secret_name = "de-pg-value-trends-${var.environment}"
    }
    equipment_values_residuals_etl = {
      namespace   = "equipment-values-residuals-etl",
      secret_name = "de-pg-values-residuals-${var.environment}"
    }
    equipment_valuations_etl = {
      namespace   = "equipment-valuations-etl",
      secret_name = "de-pg-equipment-valuations-${var.environment}"
    }
    equipment_view_publisher = {
      namespace   = "equipment-view-publisher",
      secret_name = "de-pg-equipment-view-publisher-${var.environment}"
    }
    fleet_manager_consolidated_data = {
      namespace   = "fleet-manager-consolidated-data",
      secret_name = "de-pg-fleet-manager-consolidated-data-${var.environment}"
    }
    fleet_manager_expected_time_to_sell_etl = {
      namespace   = "fleet-manager-expected-time-to-sell-etl",
      secret_name = "de-pg-fleet-manager-expected-time-to-sell-etl-${var.environment}"
    }
    fleet_manager_fleet_customers_monitoring = {
      namespace   = "fleet-manager-fleet-customers-monitoring",
      secret_name = "de-pg-fleet-manager-fleet-customers-monitoring-${var.environment}"
    }
    fleet_manager_fleet_listings = {
      namespace   = "fleet-manager-fleet-listings",
      secret_name = "de-pg-fleet-manager-fleet-listings-${var.environment}"
    }
    fleet_manager_fleet_metrics = {
      namespace   = "fleet-manager-fleet-metrics",
      secret_name = "de-pg-fleet-manager-fleet-metrics-${var.environment}"
    }
    client_etl = {
      namespace   = "client-etl",
      secret_name = "de-pg-client-${var.environment}"
    }
    countries_etl = {
      namespace   = "countries-etl",
      secret_name = "de-pg-countries-etl-${var.environment}"
    }
    copy_classification_photos_etl = {
      namespace   = "classification-photos",
      secret_name = "de-pg-copy-classification-photos-${var.environment}"
    }
    equipment_catalog_api = {
      namespace   = "generate-site-map-etl"
      secret_name = "de-pg-generate-site-map-etl-${var.environment}"
    }
    equipment_classification_etl = {
      namespace   = "equipment-classification-etl",
      secret_name = "de-pg-equipment-classification-${var.environment}"
    }
    rouse_rb_taxonomy_mapping_etl = {
      namespace   = "rouse-rb-taxonomy-mapping-etl",
      secret_name = "de-pg-rouse-rb-taxonomy-mapping-etl-${var.environment}"
    }
    fleet_manager_pg_export = {
      namespace   = "fleet-manager-pg-export",
      secret_name = "de-pg-fleet-manager-pg-export-${var.environment}"
    }
    fleet_manager_pg_export_multi_tenant = {
      namespace   = "fleet-manager-pg-export-multi-tenant",
      secret_name = "de-pg-fleet-manager-pg-export-multi-tenant-${var.environment}"
    }
    fleet_manager_publish_insights_taxonomy_and_localization_etl = {
      namespace   = "fleet-manager-publish-insights-taxonomy-and-localization-etl",
      secret_name = "de-pg-fleet-manager-publish-insights-taxonomy-and-localization-etl-${var.environment}"
    }
    fleet_manager_backfill = {
      namespace   = "fleet-manager-backfill",
      secret_name = "de-pg-fleet-manager-backfill-${var.environment}"
    }
    fleet_manager_supercategory_etl = {
      namespace   = "fleet-manager-supercategory-etl",
      secret_name = "de-pg-fleet-manager-supercategory-etl-${var.environment}"
    }
    fleet_manager_sync = {
      namespace   = "fleet-manager-sync",
      secret_name = "de-pg-fleet-manager-sync-${var.environment}"
    }
    fleet_manager_users_etl = {
      namespace   = "fleet-manager-users-etl",
      secret_name = "de-pg-fleet-manager-users-etl-${var.environment}"
    }
    fleet_manager_valuations_history_etl = {
      namespace   = "fleet-manager-valuations-history-etl",
      secret_name = "de-pg-fleet-manager-valuations-history-etl-${var.environment}"
    }
    fleet_manager_cat_product_group = {
      namespace   = "fleet-manager-cat-product-group",
      secret_name = "de-pg-fleet-manager-cat-product-group-${var.environment}"
    }
    fleet_manager_customers_metrics_reporting = {
      namespace   = "fleet-manager-customers-metrics-reporting",
      secret_name = "de-pg-fleet-manager-customers-metrics-reporting-${var.environment}"
    }
    equipment_syndication_etl = {
      namespace   = "equipment-syndication-etl",
      secret_name = "de-pg-equipment-syndication-etl-${var.environment}"
    }
    equipment_notes_history_etl = {
      namespace   = "equipment-notes-history-etl",
      secret_name = "de-pg-equipment-notes-history-etl-${var.environment}"
    }
    sales_erp_sync = {
      namespace   = "sales-erp-sync",
      secret_name = "de-pg-sales-erp-sync-${var.environment}"
    }
    sales_invoice_emails = {
      namespace   = "sales-invoice-emails",
      secret_name = "de-pg-sales-invoice-emails-${var.environment}"
    }
    sales_transactions_etl = {
      namespace   = "sales-transactions",
      secret_name = "de-pg-sales-transactions-etl-${var.environment}"
    }
    ss_export = {
      namespace   = "ss-export",
      secret_name = "de-pg-ss-export-${var.environment}"
    }
    proposal_history_etl = {
      namespace   = "proposal-history-etl",
      secret_name = "de-pg-proposal-history-etl-${var.environment}"
    }
    change_history_etl = {
      namespace   = "change-history-etl",
      secret_name = "de-pg-change-history-etl-${var.environment}"
    }
    user_profiles = {
      namespace   = "fleet-manager-backfill",
      secret_name = "de-pg-user-profiles-backfill-${var.environment}",
    }
    equipment_files_etl = {
      namespace   = "equipment-files-etl",
      secret_name = "de-pg-equipment-files-${var.environment}"
    }
    web_leads_etl = {
      namespace   = "web-leads-etl",
      secret_name = "de-pg-web-leads-${var.environment}"
    }
    legacy_provisioning_etl = {
      namespace   = "legacy-provisioning-etl",
      secret_name = "de-pg-legacy-provisioning-${var.environment}"
    }
    marketable_life_etl = {
      namespace   = "marketable-life-etl",
      secret_name = "de-pg-marketable-life-${var.environment}"
    }
    migrations_etl = {
      namespace   = "migrations-etl",
      secret_name = "de-pg-migrations-${var.environment}"
    }
    mpe_kpi_metrics_etl = {
      namespace   = "mpe-kpi-metrics-etl",
      secret_name = "de-pg-mpe-kpi-metrics-${var.environment}"
    }
    mpe_transactions_etl = {
      namespace   = "mpe-transactions-etl",
      secret_name = "de-pg-mpe-transactions-etl-${var.environment}"
    }
    rfm_archiving_etl = {
      namespace   = "rfm-archiving-etl",
      secret_name = "de-pg-rfm-archiving-${var.environment}"
    }
    restore_etl = {
      namespace   = "restore-etl",
      secret_name = "de-pg-restore-${var.environment}"
    }
    health_check = {
      namespace   = "health-checks",
      secret_name = "de-pg-health-check-${var.environment}"
    }
    fleet_manager_multi_checks = {
      namespace   = "fleet-manager-multi-checks",
      secret_name = "de-pg-fleet-manager-multi-checks-${var.environment}"
    }
    rental_insights_etl = {
      namespace   = "rental-insights-etl",
      secret_name = "de-pg-rental-insights-etl-${var.environment}"
    }
    mixer_export_etl = {
      namespace   = "mixer-export-etl",
      secret_name = "de-pg-mixer-export-etl-${var.environment}"
    }

    mixer_values_lookup_configuration_sync = {
      namespace   = "mixer-values-lookup-configuration-sync",
      secret_name = "de-pg-mixer-values-lookup-configuration-sync-${var.environment}"
    }
    rb_list_views_etl = {
      namespace   = "rb-list-views-etl",
      secret_name = "de-pg-rb-list-views-${var.environment}"
    }
    rb_list_web_leads_etl = {
      namespace   = "rb-list-web-leads-etl",
      secret_name = "de-pg-rb-list-web-leads-${var.environment}"
    }
    rb_taxonomy_etl = {
      namespace   = "rb-taxonomy-etl",
      secret_name = "de-pg-rb-taxonomy-${var.environment}"
    }
    fleet_manager_partition_management_pipeline = {
      namespace   = "fleet-manager-partition-management",
      secret_name = "de-pg-rfm-fleet-manager-partition-management-${var.environment}"
    }

    sales_google_analytics_page_views = {
      namespace   = "sales-google-analytics-page-views",
      secret_name = "de-pg-sales-google-analytics-page-views-${var.environment}"
    }

    sales_rfm_user_config_reporting = {
      namespace   = "sales-rfm-user-config-reporting",
      secret_name = "de-pg-rfm-sales-rfm-user-config-reporting-${var.environment}"
    }
    selling_channel_reporting_etl = {
      namespace   = "selling-channel-reporting-etl",
      secret_name = "de-pg-rfm-selling-channel-reporting-${var.environment}"
    }
    smartequip_parts_book_etl = {
      namespace   = "smartequip-parts-book-etl",
      secret_name = "de-pg-smartequip-parts-book-${var.environment}"
    }
    user_config_rule_change_log_reporting = {
      namespace   = "user-config-rule-change-log-reporting",
      secret_name = "de-pg-user-config-rule-change-log-reporting-${var.environment}"
    }
    csmm_relevancy_etl = {
      namespace   = "csmm-relevancy-etl",
      secret_name = "de-pg-csmm-relevancy-etl-${var.environment}"
    }
    united_xvalues_export_etl = {
      namespace   = "united-xvalues-export-etl",
      secret_name = "de-pg-united-xvalues-export-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    username = each.key
    password = data.google_secret_manager_secret_version.dbuser_rfm[each.key].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_salesglobal" {
  for_each = toset([
    "values_lookup_schema_etl",
    "coda_rw",
  ])
  secret  = "dbuser_salesglobal_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_salesglobal" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    values_lookup_schema_etl = {
      namespace   = "values-lookup-schema-etl",
      secret_name = "de-pg-values-lookup-schema-${var.environment}"
    }
    coda_rw = {
      namespace   = "equipment-configuration-variants-etl",
      secret_name = "de-equipment-configuration-variants-etl-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    username = each.key
    password = data.google_secret_manager_secret_version.dbuser_salesglobal[each.key].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_ras_datamart" {
  for_each = toset([
    "svc_fleet_manager_backfill",
    "svc_fleet_manager_users_etl",
    "svc_fleet_manager_pg_export",
    "svc_mpe_kpi_metrics_etl",
  ])
  secret  = "dbuser_ras_datamart_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_ras_datamart" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    svc_fleet_manager_backfill = {
      namespace   = "fleet-manager-backfill",
      secret_name = "de-fleet-manager-backfill-${var.environment}"
    }
    svc_fleet_manager_users_etl = {
      namespace   = "fleet-manager-users-etl",
      secret_name = "de-fleet-manager-users-etl-${var.environment}"
    }
    svc_fleet_manager_pg_export = {
      namespace   = "fleet-manager-pg-export",
      secret_name = "de-fleet-manager-pg-export-${var.environment}"
    }
    svc_mpe_kpi_metrics_etl = {
      namespace   = "mpe-kpi-metrics-etl",
      secret_name = "de-mpe-kpi-metrics-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    username = each.key
    password = data.google_secret_manager_secret_version.dbuser_ras_datamart[each.key].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_valuations" {
  for_each = toset([
    "model_etl",
  ])
  secret  = "dbuser_valuations_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_valuations" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    appraisal-valuation-etl = {
      username    = "model_etl",
      secret_name = "de-pg-appraisal-valuation-${var.environment}"
    }
    appraisals-auction-values-etl = {
      username    = "model_etl",
      secret_name = "de-pg-appraisal-valuation-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = each.value["username"]
    password = data.google_secret_manager_secret_version.dbuser_valuations[each.value["username"]].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_ras_sas" {
  for_each = toset([
    "svc_airflow_monitoring_system_valuations_etl",
    "svc_appraisals_valuations_etl",
    "svc_appraisal_valuation_canary_etl",
    "svc_data_science_models_etl",
    "svc_legacy_provisioning_etl",
    "model_etl",
    "svc_sales_configuration_model",
    "svc_random_file_generation",
    "svc_schedule_module_etl",
    "svc_set_new_ab_cost_etl",
    "svc_db_coda_dependencies_etl",
    "svc_ss_export",
    "rebasing_automated_run",
    "mascus_sales_history_import"
  ])
  secret  = "dbuser_ras_sas_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_ras_sas" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    svc_airflow_monitoring_system_valuations_etl = {
      namespace   = "airflow-monitoring-system-valuations",
      secret_name = "de-mssql-airflow-monitor-system-valuations-${var.environment}"
    }
    svc_appraisals_valuations_etl = {
      namespace   = "appraisal-valuation-etl",
      secret_name = "de-appraisal-valuation-${var.environment}"
    }
    svc_appraisal_valuation_canary_etl = {
      namespace   = "appraisal-valuation-canary-etl",
      secret_name = "de-appraisal-valuation-canary-${var.environment}"
    }
    svc_data_science_models_etl = {
      namespace   = "data-science-models-etl",
      secret_name = "de-data-science-models-${var.environment}"
    }
    svc_legacy_provisioning_etl = {
      namespace   = "legacy-provisioning-etl",
      secret_name = "de-legacy-provisioning-${var.environment}"
    }
    model_etl = {
      namespace   = "appraisals-auction-values-etl",
      secret_name = "de-ras-sas-appraisals-auction-values-${var.environment}"
    }
    svc_sales_configuration_model = {
      namespace   = "sales-configuration-model",
      secret_name = "de-sales-configuration-model-${var.environment}"
    }
    svc_random_file_generation = {
      namespace   = "random-file-generation",
      secret_name = "de-random-file-generation-${var.environment}"
    }
    svc_schedule_module_etl = {
      namespace   = "schedules-module-etl",
      secret_name = "de-schedules-module-${var.environment}"
    }
    svc_set_new_ab_cost_etl = {
      namespace   = "set-new-ab-cost-etl",
      secret_name = "de-set-new-ab-cost-${var.environment}"
    }
    svc_db_coda_dependencies_etl = {
      namespace   = "db-coda-dep-auction-data",
      secret_name = "de-db-coda-dependencies-${var.environment}"
    }
    svc_ss_export = {
      namespace   = "ss-export",
      secret_name = "de-ss-export-${var.environment}"
    }
    rebasing_automated_run = {
      namespace   = "rebasing-automated-run",
      secret_name = "de-ss-rebasing-automated-run-${var.environment}"
    }
    mascus_sales_history_import = {
      namespace   = "mascus-sales-history-import",
      secret_name = "de-ss-mascus-sales-history-import-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    username = each.key
    password = data.google_secret_manager_secret_version.dbuser_ras_sas[each.key].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_auctions" {
  for_each = toset([
    "svc_rouse_rb_taxonomy_mapping_etl",
  ])
  secret  = "dbuser_auctions_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_auctions" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    svc_rouse_rb_taxonomy_mapping_etl = {
      namespace   = "rouse-rb-taxonomy-mapping-etl",
      secret_name = "de-rouse-rb-taxonomy-mapping-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    username = each.key
    password = data.google_secret_manager_secret_version.dbuser_auctions[each.key].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_classify" {
  for_each = toset([
    "svc_smartequip_parts_book_etl",
  ])
  secret  = "dbuser_classify_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_classify" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    svc_smartequip_parts_book_etl = {
      namespace   = "smartequip-parts-book-etl",
      secret_name = "de-smartequip-parts-book-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    username = each.key
    password = data.google_secret_manager_secret_version.dbuser_classify[each.key].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_rbma" {
  for_each = toset([
    "svc_sales_values_lookup_etl",
  ])
  secret  = "dbuser_rbma_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_rbma" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    svc_sales_values_lookup_etl = {
      namespace   = "sales-values-lookup",
      secret_name = "de-rbma-sales-values-lookup-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    username = each.key
    password = data.google_secret_manager_secret_version.dbuser_rbma[each.key].secret_data
  }
}

data "google_kms_secret" "vault-ansible" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7xp/6m0V/CCUX+No8z5NusuQuPQPlFhf0OAbh1AZ+CcSUQBp1KhiY3wxvRWTcZuLggFlt0O5qicJnIAU0HI/rn4kXBP6JvYXJ2A+z33bY+EG+TqZVDFcAuVP44NqY6fIC8A0C9cqWkorDKUQOIUKFs5Qbg=="
}

resource "kubernetes_secret" "rouse-ansible" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "ssis-deployment",
    "ietl-admin-deployment",
    "ops-agent-install",
    "runtime-playbooks"
  ])

  metadata {
    name      = "rouse-ansible"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    ansible_user           = "rasgcp\\automation"
    ansible_password       = data.google_secret_manager_secret_version.windows_account_vms_prod.secret_data
    ansible_vault_password = data.google_kms_secret.vault-ansible.plaintext
  }
}

data "google_kms_secret" "de-rfm-auction-data-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU761qmPGc18FB/Aj+dIKaEY9RrElej6kCiTMOgxCBIrkSQQBp1KhiIblxoKDiET1owknAazlS86RdnnQ/F6wzJGMf7S7grinQ1TxrJNO+qAXwhv37cJwC0byvpApL9wuuAMfb"
}

resource "kubernetes_secret" "de-rfm-auction-data-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "auction-data-etl",
  ])

  metadata {
    name      = "de-rfm-auction-data-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "auction_data_etl"
    password = data.google_kms_secret.de-rfm-auction-data-prod.plaintext
  }
}

data "google_kms_secret" "de-sales-auction-data-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU778pr8gw3/ZuNGS5TV7sJdAfrNhR5cwZTk5dQN73d50SQQBp1KhiJw1h/DfxDqcCmim4DbsAF9IH09d6R+Ks+a+wo94irWjFHz2dHNU69JvlD8LRBp0J3cstqjcOy8AqOtBY"
}

resource "kubernetes_secret" "de-sales-auction-data-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "auction-data-etl",
  ])

  metadata {
    name      = "de-sales-auction-data-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "auction_data_etl"
    password = data.google_kms_secret.de-sales-auction-data-prod.plaintext
  }
}

data "google_kms_secret" "de-pg-prev-exch-rates-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU74CbbroOlLbg7BFhAsvyR7Mg657SQmQXHQIG3dZxszYSQQBp1KhiqDrJ9eF8scY9ghexxuEYprJuXtsNwVn60HxG0e+gITj4eA84jUfuL5LiVAhnzOP1pn3u8olPE6+STm+Y"
}

resource "kubernetes_secret" "de-pg-prev-exch-rates-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "sales-prevailing-exchange-rates-etl",
  ])

  metadata {
    name      = "de-pg-prev-exch-rates-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "sales_prevailing_exchange_rates_etl"
    password = data.google_kms_secret.de-pg-prev-exch-rates-prod.plaintext
  }
}

data "google_secret_manager_secret_version" "dbuser_sharded" {
  for_each = toset([
    "svc_monitoring_etl"
  ])
  secret  = "dbuser_sharded_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "platform-etl-prod-svc-monitoring-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    "health-checks" = "svc_monitoring_etl"
  }

  metadata {
    name      = "platform-etl-prod-sql-account"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_monitoring_etl"
    password = data.google_secret_manager_secret_version.dbuser_sharded[each.value].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_health_checks" {
  for_each = toset([
    "mssql_message_queue_monitoring"
  ])
  secret  = each.key
  project = local.project
}

resource "kubernetes_secret" "health_checks_prod" {
  provider = kubernetes.composer-jobs-v3
  for_each = {
    "de-message-queue-monitoring-prod" = "mssql_message_queue_monitoring",
  }
  metadata {
    name      = each.key
    namespace = kubernetes_namespace.workload_identity["health-checks"].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_health_checks[each.value].secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_health_checks[each.value].secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "dbuser_auctionvalues01" {
  for_each = toset([
    "root"
  ])
  secret  = "dbuser_auctionvalues01_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "de-pg-appraisals-auction-values-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    "secret1" = {
      "wi"     = "appraisals-auction-values-etl",
      "secret" = "root"
    }
  }

  metadata {
    name      = "de-pg-appraisals-auction-values-prod"
    namespace = kubernetes_namespace.workload_identity[each.value.wi].metadata[0].name
  }

  data = {
    username = each.value.secret
    password = data.google_secret_manager_secret_version.dbuser_auctionvalues01[each.value.secret].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_rdo04" {
  secret  = "dbuser_rdo04_custom_grids_performance_vs_benchmark_etl"
  project = local.services
}

resource "kubernetes_secret" "dbuser_rdo04" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "custom-grids-performance-vs-benchmark-etl",
    "rental-metrics-aggs"
  ])

  metadata {
    name      = "de-pg-cg-perf-vs-bench-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "custom_grids_performance_vs_benchmark_etl"
    password = data.google_secret_manager_secret_version.dbuser_rdo04.secret_data
  }
}

data "google_kms_secret" "de-cg-perf-vs-bench-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU71H45xbhhz2U6HLoWc37I/XOYXbw/pNGW6Av2kDRMwkSNwBp1Khir+Gmhq5xIfW5Xy9wwQDOxeOeVYGaIWblB7Ard9xKLVSNG+7P0xYZsS/7hYbymSimutI="
}

resource "kubernetes_secret" "de-cg-perf-vs-bench-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "custom-grids-performance-vs-benchmark-etl",
  ])

  metadata {
    name      = "de-cg-perf-vs-bench-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_cg_perf_vs_bench_etl"
    password = data.google_kms_secret.de-cg-perf-vs-bench-prod.plaintext
  }
}

data "google_secret_manager_secret_version" "mssql_svc_sales_erp_sync_etl" {
  secret  = "mssql_svc_sales_erp_sync_etl"
  project = local.project
}

resource "kubernetes_secret" "de-sales-erp-sync-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-sales-erp-sync-prod"
    namespace = kubernetes_namespace.workload_identity["sales-erp-sync"].metadata[0].name
  }

  data = {
    username = "svc_sales_erp_sync_etl"
    password = data.google_secret_manager_secret_version.mssql_svc_sales_erp_sync_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "ws_sales_erp_sync_etl" {
  secret  = "ws_sales_erp_sync_etl"
  project = local.project
}

resource "kubernetes_secret" "de-ws-sales-erp-sync-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    "secret1" = {
      "wi"     = "sales-erp-sync",
      "domain" = "rasgcp"
      "secret" = "ws_sales_erp_sync_etl"
    }
  }

  metadata {
    name      = "de-ws-sales-erp-sync-prod"
    namespace = kubernetes_namespace.workload_identity["sales-erp-sync"].metadata[0].name
  }

  data = {
    domain   = "rasgcp"
    username = "svc_erp_sync"
    password = data.google_secret_manager_secret_version.ws_sales_erp_sync_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "ftp_sales_erp_sync_etl" {
  secret  = "ftp_sales_erp_sync_etl"
  project = local.project
}

resource "kubernetes_secret" "de-ftp-sales-erp-sync-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-ftp-sales-erp-sync-prod"
    namespace = kubernetes_namespace.workload_identity["sales-erp-sync"].metadata[0].name
  }

  data = {
    ftp_credentials = data.google_secret_manager_secret_version.ftp_sales_erp_sync_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "service_sales_erp_sync_etl" {
  secret  = "service_sales_erp_sync_etl"
  project = local.project
}

resource "kubernetes_secret" "de-service-sales-erp-sync-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-service-sales-erp-sync-prod"
    namespace = kubernetes_namespace.workload_identity["sales-erp-sync"].metadata[0].name

  }

  data = {
    service_credentials = data.google_secret_manager_secret_version.service_sales_erp_sync_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "cloudsql_credentials_dev" {
  for_each = toset([
    "configs01",
    "dttconfig01",
    "identity01",
    "platform01",
    "portalcache",
    "rbma01",
    "rdo04",
    "record360_03",
    "rfm01",
    "rmbaglobal01",
    "salesglobal01",
    "salesglobal02",
    "valuations01"
  ])
  secret  = "dbadmin_${each.key}_dev"
  project = local.management_dev
}

resource "kubernetes_secret" "cloudsql_backups_credentials_dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = data.google_secret_manager_secret_version.cloudsql_credentials_dev

  metadata {
    name      = replace(each.value.secret, "_", "-")
    namespace = kubernetes_namespace.workload_identity["cloudsql-backups"].metadata[0].name
  }

  data = {
    credentials = each.value.secret_data
  }
}

data "google_secret_manager_secret_version" "cloudsql_credentials_prod" {
  for_each = toset([
    "configs01",
    "dttconfig01",
    "identity01",
    "platform01",
    "portalcache01",
    "rbma01",
    "rdo04",
    "record360_01",
    "rfm01",
    "rmbaglobal01",
    "salesglobal01",
    "salesglobal02"
  ])
  secret  = "dbadmin_${each.key}_prod"
  project = local.project
}

resource "kubernetes_secret" "cloudsql_backups_credentials_prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = data.google_secret_manager_secret_version.cloudsql_credentials_prod

  metadata {
    name      = replace(each.value.secret, "_", "-")
    namespace = kubernetes_namespace.workload_identity["cloudsql-backups"].metadata[0].name
  }

  data = {
    credentials = each.value.secret_data
  }
}

data "google_secret_manager_secret_version" "sql_svc_backup_sql_server" {
  secret  = "dbuser_sharded_svc_backup_sql_server"
  project = local.services
}

resource "kubernetes_secret" "sql_svc_backup_sql_server_wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "sharded-db-backup-sql-server",
    "db-deploy",
  ])

  metadata {
    name      = "sql-backup-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_backup_sql_server"
    password = data.google_secret_manager_secret_version.sql_svc_backup_sql_server.secret_data
  }
}

data "google_secret_manager_secret_version" "sql_svc_backup_sql_server_dev" {
  secret  = "dbuser_sharded_svc_backup_sql_server"
  project = local.services_dev
}

resource "kubernetes_secret" "sql_svc_backup_sql_server_dev_wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "sharded-db-backup-sql-server",
  ])

  metadata {
    name      = "sql-backup-user-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_backup_sql_server"
    password = data.google_secret_manager_secret_version.sql_svc_backup_sql_server_dev.secret_data
  }
}

data "google_kms_secret" "de-rambo-export-gnoco-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU75xMjw72Olu/x8bkSZXiD56VCYAikAxTQwWo2RsVz9YSQQBp1KhiwV6QiJR/MRnY+w4SqDOhAi+Q2vm/paq+wQyi3YdAONHVQikuLUjPo94sMESNUCrI5nVLD+7Xth6XSifC"
}

resource "kubernetes_secret" "de-rambo-export-gnoco-prod-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "rental-metrics-aggs",
  ])

  metadata {
    name      = "de-rambo-export-gnoco"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_rambo_export"
    password = data.google_kms_secret.de-rambo-export-gnoco-prod.plaintext
  }
}

data "google_secret_manager_secret_version" "de-rambo-export-gdb05-rdo" {
  secret  = "mssql_rambo_export_gdb05_rdo"
  project = local.project
}

resource "kubernetes_secret" "de-rambo-export-rdo" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-rambo-export-rdo"
    namespace = kubernetes_namespace.workload_identity["rental-metrics-aggs"].metadata[0].name
  }

  data = {
    username = "svc_rambo_export"
    password = data.google_secret_manager_secret_version.de-rambo-export-gdb05-rdo.secret_data
  }
}

data "google_secret_manager_secret_version" "pg_sales_values_lookup_reporting" {
  secret  = "dbuser_rfm_sales_values_lookup_reporting"
  project = local.services
}

resource "kubernetes_secret" "de-sales-values-lookup-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-sales-values-lookup-prod"
    namespace = kubernetes_namespace.workload_identity["sales-values-lookup"].metadata[0].name
  }

  data = {
    username = "sales_values_lookup_reporting"
    password = data.google_secret_manager_secret_version.pg_sales_values_lookup_reporting.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_ims_fleet_ingest_etl" {
  secret  = "mssql_ims_fleet_ingest_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-ims-fleet-ingest-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-ims-fleet-ingest-prod"
    namespace = kubernetes_namespace.workload_identity["ims-fleet-ingest"].metadata[0].name
  }

  data = {
    username = "ims_fleet_ingest_etl"
    password = data.google_secret_manager_secret_version.mssql_ims_fleet_ingest_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_platform_classification_etl" {
  secret  = "mssql_platform_classification_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-platform-classification-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-platform-classification-prod"
    namespace = kubernetes_namespace.workload_identity["platform-classification"].metadata[0].name
  }

  data = {
    username = "svc_platform_classification"
    password = data.google_secret_manager_secret_version.mssql_platform_classification_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_equipment_syndication_etl" {
  secret  = "mssql_equipment_syndication_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-equipment-syndication-etl-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-equipment-syndication-etl-${var.environment}"
    namespace = kubernetes_namespace.workload_identity["equipment-syndication-etl"].metadata[0].name
  }

  data = {
    username = "svc_equipment_syndication_etl"
    password = data.google_secret_manager_secret_version.mssql_equipment_syndication_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "postgres_platform_classification_etl" {
  secret  = "dbuser_platform01_platform_classification_etl"
  project = local.services
}

resource "kubernetes_secret" "de-postgres-platform-classification-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-postgres-platform-classification-prod"
    namespace = kubernetes_namespace.workload_identity["platform-classification"].metadata[0].name
  }

  data = {
    username = "platform_classification_etl"
    password = data.google_secret_manager_secret_version.postgres_platform_classification_etl.secret_data
  }
}

resource "kubernetes_secret" "astronomer_bootstrap" {
  provider = kubernetes.astronomer-prod

  metadata {
    name      = "astronomer-bootstrap"
    namespace = "astronomer"
  }

  data = {
    connection = "postgres://${jsondecode(data.google_secret_manager_secret_version.cloudsql_astronomer.secret_data)["username"]}:${jsondecode(data.google_secret_manager_secret_version.cloudsql_astronomer.secret_data)["password"]}@${module.cloudsql_astronomer_v2.instance_private_ip}:5432"
  }
}

data "google_secret_manager_secret_version" "astronomer_service_account" {
  secret  = "astronomer_service_account"
  project = local.project
}

resource "kubernetes_secret" "astronomer_service_account" {
  provider = kubernetes.astronomer-prod

  for_each = toset([
    "astronomer",
  ])

  metadata {
    name      = "astronomer-gcs-keyfile"
    namespace = each.key
  }

  data = {
    astronomer-gcs-keyfile = data.google_secret_manager_secret_version.astronomer_service_account.secret_data
  }
}

data "google_secret_manager_secret_version" "winuser_abod_json_to_gcs_etl" {
  secret  = "winuser_abod_json_to_gcs_etl"
  project = local.services
}

resource "kubernetes_secret" "de-abod-json-to-gcs-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-abod-json-to-gcs-prod"
    namespace = kubernetes_namespace.workload_identity["abod-json-to-gcs-etl"].metadata[0].name
  }

  data = {
    domain   = "RASGCP"
    username = "svc_abod_json_to_gcs"
    password = data.google_secret_manager_secret_version.winuser_abod_json_to_gcs_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "de_pg_platform_dbadmin" {
  secret  = "dbadmin_platform01_${var.environment}"
  project = local.project
}

resource "kubernetes_secret" "de_pg_platform_dbadmin" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "appraisals-auction-values-etl",
    "appraisal-valuation-etl",
    "auction-transactions-etl",
    "auctions-etl",
  ])

  metadata {
    name      = "de-pg-platform-dbadmin-prod"
    namespace = each.key
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.de_pg_platform_dbadmin.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.de_pg_platform_dbadmin.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "winuser_sales_configuration_model" {
  secret  = "winuser_sales_configuration_model"
  project = local.services
}

resource "kubernetes_secret" "secret_sales_configuration_model" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-ws-sales-configuration-model-${var.environment}"
    namespace = kubernetes_namespace.workload_identity["sales-configuration-model"].metadata[0].name
  }

  data = {
    domain   = "RASGCP.NET"
    username = "svc_config_model_etl"
    password = data.google_secret_manager_secret_version.winuser_sales_configuration_model.secret_data
  }
}

data "google_secret_manager_secret_version" "winuser_random_file_generation" {
  secret  = "winuser_random_file_generation"
  project = local.services
}

resource "kubernetes_secret" "secret_random_file_generation" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-ws-random-file-generation-${var.environment}"
    namespace = kubernetes_namespace.workload_identity["random-file-generation"].metadata[0].name
  }

  data = {
    domain   = "RASGCP.NET"
    username = "svc_random_file_generation"
    password = data.google_secret_manager_secret_version.winuser_random_file_generation.secret_data
  }
}

data "google_secret_manager_secret_version" "winuser_set_new_ab_cost_etl" {
  secret  = "winuser_set_new_ab_cost_etl"
  project = local.services
}

resource "kubernetes_secret" "de-ws-set-new-ab-cost-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-ws-set-new-ab-cost-prod"
    namespace = kubernetes_namespace.workload_identity["set-new-ab-cost-etl"].metadata[0].name
  }

  data = {
    domain   = "RASDOMAIN.ENT"
    username = "svc_ws_set_new_ab_cost_etl"
    password = data.google_secret_manager_secret_version.winuser_set_new_ab_cost_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "sharepoint_schedules_module_etl" {
  secret  = "sharepoint_schedules_module_etl"
  project = local.services
}

resource "kubernetes_secret" "de-sharepoint-schedules-module-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-sharepoint-schedules-module-prod"
    namespace = kubernetes_namespace.workload_identity["schedules-module-etl"].metadata[0].name
  }

  data = {
    username = "<EMAIL>"
    password = data.google_secret_manager_secret_version.sharepoint_schedules_module_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "winuser_united_xvalues_export_etl" {
  secret  = "winuser_united_xvalues_export_etl"
  project = local.services
}

resource "kubernetes_secret" "de-ws-united-xvalues-export-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-ws-united-xvalues-export-prod"
    namespace = kubernetes_namespace.workload_identity["united-xvalues-export-etl"].metadata[0].name
  }

  data = {
    domain   = "RASGCP.NET"
    username = "svc_ws_united_xvalues_export_etl"
    password = data.google_secret_manager_secret_version.winuser_united_xvalues_export_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_ras_datamart_sales_united_xvalues_export_etl" {
  secret  = "dbuser_ras_datamart_sales_united_xvalues_export_etl"
  project = local.services
}

resource "kubernetes_secret" "de-united-xvalues-export-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-united-xvalues-export-prod"
    namespace = kubernetes_namespace.workload_identity["united-xvalues-export-etl"].metadata[0].name
  }

  data = {
    username = "svc_united_xvalues_export_etl"
    password = data.google_secret_manager_secret_version.dbuser_ras_datamart_sales_united_xvalues_export_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "pg_ims_fleet_refresh_etl" {
  secret  = "dbuser_rfm_ims_fleet_refresh_etl"
  project = local.services
}

resource "kubernetes_secret" "de-pg-ims-fleet-refresh-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-pg-ims-fleet-refresh-prod"
    namespace = kubernetes_namespace.workload_identity["ims-fleet-refresh"].metadata[0].name
  }

  data = {
    username = "ims_fleet_refresh_etl"
    password = data.google_secret_manager_secret_version.pg_ims_fleet_refresh_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "pg_ims_fleet_ingest_etl" {
  secret  = "dbuser_rfm_ims_fleet_ingest_etl"
  project = local.services
}

resource "kubernetes_secret" "de-pg-ims-fleet-ingest-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-pg-ims-fleet-ingest-prod"
    namespace = kubernetes_namespace.workload_identity["ims-fleet-ingest"].metadata[0].name
  }

  data = {
    username = "ims_fleet_ingest_etl"
    password = data.google_secret_manager_secret_version.pg_ims_fleet_ingest_etl.secret_data
  }
}


data "google_secret_manager_secret_version" "analytics_eemphasys_api_keys" {
  secret  = "analytics_eemphasys_api_keys"
  project = local.project
}

resource "kubernetes_secret" "analytics_eemphasys_api_keys" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "analytics-eemphasys-api-keys"
    namespace = kubernetes_namespace.workload_identity["analytics-eemphasys"].metadata[0].name
  }

  data = {
    api_keys = data.google_secret_manager_secret_version.analytics_eemphasys_api_keys.secret_data
  }
}

data "google_secret_manager_secret_version" "analytics_powerbi_api_keys" {
  secret  = "analytics_powerbi_api_keys"
  project = local.project
}

resource "kubernetes_secret" "analytics_powerbi_api_keys" {
  provider = kubernetes.airflow-jobs-private

  metadata {
    name      = "analytics-powerbi-api-keys"
    namespace = kubernetes_namespace.private_cluster_workload_identity["analytics-powerbi"].metadata[0].name
  }

  data = {
    api_keys = data.google_secret_manager_secret_version.analytics_powerbi_api_keys.secret_data
  }
}

data "google_secret_manager_secret_version" "analytics_admar_api_permissions_prod" {
  secret  = "analytics_admar_api_permissions_prod"
  project = local.project
}

resource "kubernetes_secret" "analytics_admar_api_permissions_prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "analytics-admar-api-permissions-prod"
    namespace = kubernetes_namespace.workload_identity["analytics-admar"].metadata[0].name
  }

  data = {
    username = "rouse"
    password = data.google_secret_manager_secret_version.analytics_admar_api_permissions_prod.secret_data
  }
}

resource "kubernetes_secret" "private_cluster_analytics_smartequip_mysql_credentials" {
  provider = kubernetes.airflow-jobs-private

  metadata {
    name      = "analytics-smartequip-mysql-credentials"
    namespace = kubernetes_namespace.workload_identity["rambo-smartequip-pipeline"].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_secret_manager_secret_version.analytics_smartequip_mysql_credentials.secret_data
  }
}

data "google_secret_manager_secret_version" "analytics_smartequip_mysql_credentials" {
  secret  = "analytics_smartequip_mysql_credentials"
  project = local.project
}

resource "kubernetes_secret" "analytics_smartequip_mysql_credentials" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "analytics-smartequip-mysql-credentials"
    namespace = kubernetes_namespace.workload_identity["rambo-smartequip-pipeline"].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_secret_manager_secret_version.analytics_smartequip_mysql_credentials.secret_data
  }
}


data "google_secret_manager_secret_version" "vendor_config_smartequip_mysql_credentials" {
  secret  = "vendor_config_smartequip_mysql_credentials"
  project = local.project
}

resource "kubernetes_secret" "vendor_config_smartequip_mysql_credentials" {
  provider = kubernetes.airflow-jobs-private

  metadata {
    namespace = kubernetes_namespace.private_cluster_workload_identity["vendor-config-smartequip-etl"].metadata[0].name
    name      = "vendor-config-smartequip-mysql-credentials"
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.vendor_config_smartequip_mysql_credentials.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.vendor_config_smartequip_mysql_credentials.secret_data), "password")
  }
}

resource "kubernetes_secret" "private_cluster_vendor_config_smartequip_mysql_credentials" {
  provider = kubernetes.airflow-jobs-private

  metadata {
    name      = "vendor-config-smartequip-mysql-credentials"
    namespace = kubernetes_namespace.private_cluster_workload_identity["vendor-config-smartequip-etl"].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.vendor_config_smartequip_mysql_credentials.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.vendor_config_smartequip_mysql_credentials.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "analytics_airflow_pg_connection_prod" {
  secret  = "analytics_airflow_pg_connection_prod"
  project = local.project
}

resource "kubernetes_secret" "analytics_airflow_pg_connection_prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "analytics-airflow-pg-connection-prod"
    namespace = kubernetes_namespace.workload_identity["rental-metrics-aggs"].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_secret_manager_secret_version.analytics_airflow_pg_connection_prod.secret_data
  }
}


data "google_secret_manager_secret_version" "rdo101-admin" {
  secret  = "rdo101-admin"
  project = local.services
}

resource "kubernetes_secret" "rdo101-admin" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "custom-grids-benchmark-transactional-detail-etl",
    "custom-grids-fleet-data-etl",
    "custom-grids-performance-vs-benchmark-etl",
    "rental-metrics-aggs",
  ])

  metadata {
    name      = "rdo101-admin"
    namespace = each.key
  }

  data = {
    username = "admin"
    password = data.google_secret_manager_secret_version.rdo101-admin.secret_data
  }
}


data "google_secret_manager_secret_version" "loggly_api_secret_name" {
  secret  = "loggly_api_key_prod"
  project = local.project
}

resource "kubernetes_secret" "loggly_api_secret_name" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "analytics-admar",
    "analytics-salesforce"
  ])

  metadata {
    name      = "loggly-api-key-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    loggly_api_key = data.google_secret_manager_secret_version.loggly_api_secret_name.secret_data
  }
}

data "google_secret_manager_secret_version" "analytics_salesforce_api_keys" {
  secret  = "analytics_salesforce_api_keys"
  project = local.project
}

resource "kubernetes_secret" "analytics_salesforce_api_keys" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "analytics-salesforce-api-keys"
    namespace = kubernetes_namespace.workload_identity["analytics-salesforce"].metadata[0].name
  }

  data = {
    api_keys = data.google_secret_manager_secret_version.analytics_salesforce_api_keys.secret_data
  }
}

data "google_secret_manager_secret_version" "ansible-devops-admin" {
  secret  = "ansible_devops_admin"
  project = local.vms
}

resource "kubernetes_secret" "ansible-devops-admin" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-syndication-etl",
  ])

  metadata {
    name      = "ansible-devops-admin-${var.environment}"
    namespace = each.key
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.ansible-devops-admin.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.ansible-devops-admin.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "ritchie_aws_competitor_feed_access_key_id" {
  secret  = "ritchie_aws_competitor_feed_access_key_id"
  project = local.project
}

data "google_secret_manager_secret_version" "ritchie_aws_competitor_feed_secret_value" {
  secret  = "ritchie_aws_competitor_feed_secret_value"
  project = local.project
}

resource "kubernetes_secret" "de-rb-competitive-intelligence-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-rb-competitive-intelligence-prod"
    namespace = kubernetes_namespace.workload_identity["rb-competitive-intelligence-etl"].metadata[0].name
  }

  data = {
    access_key_id = data.google_secret_manager_secret_version.ritchie_aws_competitor_feed_access_key_id.secret_data
    secret_value  = data.google_secret_manager_secret_version.ritchie_aws_competitor_feed_secret_value.secret_data
  }
}

data "google_secret_manager_secret_version" "open_exchange_rates_app_id" {
  secret  = "open_exchange_rates_app_id"
  project = local.project
}

resource "kubernetes_secret" "de-open-exchange-rates-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-open-exchange-rates-prod"
    namespace = kubernetes_namespace.workload_identity["exchange-rates-etl"].metadata[0].name
  }

  data = {
    app_id = data.google_secret_manager_secret_version.open_exchange_rates_app_id.secret_data
  }
}

# identity user pipeline
data "google_secret_manager_secret_version" "mssql-user-identity-pipeline-secret" {
  secret  = "identity_user_pipeline_sql_user"
  project = local.project
}


resource "kubernetes_secret" "mssql-user-identity-pipeline-secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "identity-user-pipeline",
  ])

  metadata {
    name      = "identity-user-pipeline-sql-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    WIN_USER     = lookup(jsondecode(data.google_secret_manager_secret_version.mssql-user-identity-pipeline-secret.secret_data), "WIN_USER")
    WIN_PASSWORD = lookup(jsondecode(data.google_secret_manager_secret_version.mssql-user-identity-pipeline-secret.secret_data), "WIN_PASSWORD")
  }
}

data "google_secret_manager_secret_version" "mssql_ims_fleet_refresh_etl" {
  secret  = "mssql_ims_fleet_refresh_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-ims-fleet-refresh-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-ims-fleet-refresh-prod"
    namespace = kubernetes_namespace.workload_identity["ims-fleet-refresh"].metadata[0].name
  }

  data = {
    username = "svc_ims_fleet_refresh_etl"
    password = data.google_secret_manager_secret_version.mssql_ims_fleet_refresh_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "sentry-slack-webhook" {
  secret  = "infrastructure_slack_webhook"
  project = local.project
}

resource "kubernetes_secret" "sentry-slack-webhook" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "slack-sentry-webhook-url"
    namespace = "sentry-alerts"
  }

  data = {
    url = data.google_secret_manager_secret_version.sentry-slack-webhook.secret_data
  }
}

resource "kubernetes_secret" "check-unused-disks-slack-webhook" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "infrastructure-webhook-url"
    namespace = "check-unused-disks"
  }

  data = {
    url = data.google_secret_manager_secret_version.sentry-slack-webhook.secret_data
  }
}

data "google_secret_manager_secret_version" "public-repo-slack-webhook" {
  secret  = "infrastructure_slack_webhook"
  project = local.project
}

resource "kubernetes_secret" "public-repo-slack-webhook" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "slack-public-repos-webhook-url"
    namespace = "public-repo-alerts"
  }

  data = {
    url = data.google_secret_manager_secret_version.public-repo-slack-webhook.secret_data
  }
}

data "google_secret_manager_secret_version" "enterprise-service-cleanup-slack-sales-webhook" {
  secret  = "enterprise_service_cleanup_slack_sales_webhook"
  project = local.project
}

resource "kubernetes_secret" "enterprise-slack-sales" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "slack-sales-webhook-url"
    namespace = "enterprise-service-cleanup"
  }

  data = {
    url = data.google_secret_manager_secret_version.enterprise-service-cleanup-slack-sales-webhook.secret_data
  }
}

data "google_secret_manager_secret_version" "enterprise-service-cleanup-slack-appraisals-webhook" {
  secret  = "enterprise_service_cleanup_slack_appraisals_webhook"
  project = local.project
}

resource "kubernetes_secret" "enterprise-slack-appraisals" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "slack-appraisals-webhook-url"
    namespace = "enterprise-service-cleanup"
  }

  data = {
    url = data.google_secret_manager_secret_version.enterprise-service-cleanup-slack-appraisals-webhook.secret_data
  }
}

data "google_secret_manager_secret_version" "enterprise-service-cleanup-slack-analytics-webhook" {
  secret  = "enterprise_service_cleanup_slack_analytics_webhook"
  project = local.project
}

resource "kubernetes_secret" "enterprise-slack-analytics" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "slack-analytics-webhook-url"
    namespace = "enterprise-service-cleanup"
  }

  data = {
    url = data.google_secret_manager_secret_version.enterprise-service-cleanup-slack-analytics-webhook.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_svc_fleet_manager_sync" {
  secret  = "mssql_svc_fleet_manager_sync"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-fleet-manager-sync-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-fleet-manager-sync-prod"
    namespace = kubernetes_namespace.workload_identity["fleet-manager-sync"].metadata[0].name
  }

  data = {
    username = "svc_fleet_manager_sync"
    password = data.google_secret_manager_secret_version.mssql_svc_fleet_manager_sync.secret_data
  }
}

data "google_secret_manager_secret_version" "sentry-analytics-bearer-token" {
  secret  = "sentry_analytics_bearer_token"
  project = local.project
}

resource "kubernetes_secret" "sentry-analytics-bearer-token" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "sentry-analytics-bearer-token"
    namespace = "sentry-alerts"
  }

  data = {
    url = data.google_secret_manager_secret_version.sentry-analytics-bearer-token.secret_data
  }
}

data "google_secret_manager_secret_version" "sentry-appraisals-bearer-token" {
  secret  = "sentry_appraisals_bearer_token"
  project = local.project
}

resource "kubernetes_secret" "sentry-appraisals-bearer-token" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "sentry-appraisals-bearer-token"
    namespace = "sentry-alerts"
  }

  data = {
    url = data.google_secret_manager_secret_version.sentry-appraisals-bearer-token.secret_data
  }
}

data "google_secret_manager_secret_version" "sentry-sales-bearer-token" {
  secret  = "sentry_sales_bearer_token"
  project = local.project
}

resource "kubernetes_secret" "sentry-sales-bearer-token" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "sentry-sales-bearer-token"
    namespace = "sentry-alerts"
  }

  data = {
    url = data.google_secret_manager_secret_version.sentry-sales-bearer-token.secret_data
  }
}

data "google_secret_manager_secret_version" "sentry-services-bearer-token" {
  secret  = "sentry_services_bearer_token"
  project = local.project
}

resource "kubernetes_secret" "sentry-saservicesles-bearer-token" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "sentry-services-bearer-token"
    namespace = "sentry-alerts"
  }

  data = {
    url = data.google_secret_manager_secret_version.sentry-services-bearer-token.secret_data
  }
}

data "google_secret_manager_secret_version" "sales_fleet_manager_api_auth_pipeline_dev" {
  secret  = "sales_fleet_manager_auth_token"
  project = local.services_dev
}

data "google_secret_manager_secret_version" "sales_fleet_manager_api_auth_pipeline" {
  secret  = "sales_fleet_manager_auth_token"
  project = local.services
}

data "google_secret_manager_secret_version" "sales_fleet_manager_integrations_api_auth" {
  secret  = "sales_fleet_manager_integrations_auth_token"
  project = local.services
}

resource "kubernetes_secret" "de-sales-fleet-manager-api-auth-ingest-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-sales-fleet-manager-api-auth-ingest-prod"
    namespace = kubernetes_namespace.workload_identity["ims-fleet-ingest"].metadata[0].name
  }

  data = {
    token = data.google_secret_manager_secret_version.sales_fleet_manager_api_auth_pipeline.secret_data
  }
}

resource "kubernetes_secret" "de-sales-fleet-manager-api-auth-refresh-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-sales-fleet-manager-api-auth-refresh-prod"
    namespace = kubernetes_namespace.workload_identity["ims-fleet-refresh"].metadata[0].name
  }

  data = {
    token = data.google_secret_manager_secret_version.sales_fleet_manager_api_auth_pipeline.secret_data
  }
}

resource "kubernetes_secret" "de-sales-fleet-manager-api-auth-token" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-classification-etl",
    "fleet-manager-publish-insights-taxonomy-and-localization-etl",
    "restore-etl",
  ])

  metadata {
    name      = "de-sales-fleet-manager-api-auth-token-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    token = data.google_secret_manager_secret_version.sales_fleet_manager_api_auth_pipeline.secret_data
  }
}

resource "kubernetes_secret" "de-sales-fleet-manager-integrations-auth-token" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "fleet-manager-fleet-metrics",
  ])

  metadata {
    name      = "de-sales-fleet-manager-integrations-auth-token-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    token = data.google_secret_manager_secret_version.sales_fleet_manager_integrations_api_auth.secret_data
  }
}

resource "kubernetes_secret" "de-sales-fleet-manager-api-auth-token-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "restore-etl",
  ])

  metadata {
    name      = "de-sales-fleet-manager-api-auth-token-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    token = data.google_secret_manager_secret_version.sales_fleet_manager_api_auth_pipeline_dev.secret_data
  }
}

data "google_secret_manager_secret_version" "platform_user_config_api_auth_pipeline" {
  secret  = "platform_user_config_auth_token"
  project = local.services
}

data "google_secret_manager_secret_version" "platform_classification_api_auth_pipeline" {
  secret  = "platform_classification_auth_token"
  project = local.services
}

resource "kubernetes_secret" "de-platform-classification-api-auth" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "fleet-manager-supercategory-etl",
    "platform-classification",
  ])

  metadata {
    name      = "de-platform-classification-api-auth-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    token = data.google_secret_manager_secret_version.platform_classification_api_auth_pipeline.secret_data
  }
}

resource "kubernetes_secret" "de-platform-user-config-api-auth-refresh-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "ims-fleet-ingest",
    "ims-fleet-refresh",
    "fleet-manager-shard-server-mapping",
    "rental-insights-etl",
  ])

  metadata {
    name      = "de-platform-user-config-api-auth-refresh-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    token = data.google_secret_manager_secret_version.platform_user_config_api_auth_pipeline.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_airflow_monitor_system_ims" {
  secret  = "mssql_airflow_monitor_system_ims"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-airflow-monitor-system-ims-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-airflow-monitor-system-ims-prod"
    namespace = kubernetes_namespace.workload_identity["airflow-monitoring-system-ims"].metadata[0].name
  }

  data = {
    username = "svc_airflow_monitor_system_ims"
    password = data.google_secret_manager_secret_version.mssql_airflow_monitor_system_ims.secret_data
  }
}

data "google_secret_manager_secret_version" "pgbouncer-server-ca-secret" {
  secret  = "pgbouncer_default_ca_server_ca"
  project = local.services
}


resource "kubernetes_secret" "pgbouncer-server-ca-secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "ims-fleet-ingest",
    "ims-fleet-refresh",
    "rouse-rb-taxonomy-mapping-etl",
    "fleet-manager-pg-export",
    "fleet-manager-pg-export-multi-tenant",
    "fleet-manager-backfill",
    "change-history-etl",
    "equipment-classification-etl",
    "equipment-valuations-etl",
    "auction-data-etl",
    "equipment-details-etl",
    "equipment-values-residuals-etl",
    "fleet-manager-cat-product-group",
    "fleet-manager-customers-metrics-reporting",
    "fleet-manager-sales-subcategory",
    "fleet-manager-supercategory-etl",
    "fleet-manager-sync",
    "fleet-manager-users-etl",
    "marketing-metrics",
    "mpe-kpi-metrics-etl",
    "mpe-transactions-etl",
    "modelyear-range-etl",
    "sales-prevailing-exchange-rates-etl",
    "transaction-volume-auction-etl",
    "value-trends-etl",
    "web-leads-etl",
    "appraisal-valuation-canary-etl",
    "auctions-etl",
    "classification-photos",
    "client-etl",
    "countries-etl",
    "create-empty-lookup-values-tables",
    "csmm-relevancy-etl",
    "equipment-catalog-etl",
    "equipment-configuration-variants-etl",
    "equipment-files-etl",
    "equipment-notes-history-etl",
    "equipment-photos-etl",
    "equipment-photos-sync-monitor",
    "equipment-sync-monitor",
    "equipment-syndication-etl",
    "equipment-values-history-etl",
    "manage-equipment-etl",
    "marketable-life-etl",
    "migrations-etl",
    "equipment-view-publisher",
    "fleet-manager-consolidated-data",
    "fleet-manager-expected-time-to-sell-etl",
    "fleet-manager-fleet-customers-monitoring",
    "fleet-manager-fleet-listings",
    "fleet-manager-fleet-metrics",
    "fleet-manager-publish-insights-taxonomy-and-localization-etl",
    "proposal-history-etl",
    "rfm-archiving-etl",
    "sales-erp-sync",
    "sales-google-analytics-page-views",
    "sales-invoice-emails",
    "sales-transactions",
    "sales-values-lookup",
    "ss-export",
    "transaction-volume-retail-etl",
    "restore-etl",
    "fleet-manager-valuations-history-etl",
    "health-checks",
    "generate-site-map-etl",
    "legacy-provisioning-etl",
    "rental-insights-etl",
    "mixer-export-etl",
    "mixer-values-lookup-configuration-sync",
    "rb-list-views-etl",
    "rb-list-web-leads-etl",
    "rb-taxonomy-etl",
    "fleet-manager-multi-checks",
    "values-lookup-schema-etl",
    "fleet-manager-partition-management",
    "sales-rfm-user-config-reporting",
    "selling-channel-reporting-etl",
    "smartequip-parts-book-etl",
    "user-config-rule-change-log-reporting",
    "united-xvalues-export-etl",
    "custom-grids-benchmark-transactional-detail-etl",
    "rental-metrics-aggs",
  ])

  metadata {
    name      = "pgbouncer-server-ca-cert"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    SSL_ROOT_CERT = data.google_secret_manager_secret_version.pgbouncer-server-ca-secret.secret_data
  }
}

data "google_secret_manager_secret_version" "pgbouncer-rfm02-prod-client-cert-secret" {
  secret  = "pgbouncer_rfm02_prod_client_cert"
  project = local.services
}


resource "kubernetes_secret" "pgbouncer-rfm02-prod-client-cert-secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "ims-fleet-ingest",
    "ims-fleet-refresh",
    "rouse-rb-taxonomy-mapping-etl",
    "fleet-manager-pg-export",
    "fleet-manager-pg-export-multi-tenant",
    "fleet-manager-backfill",
    "change-history-etl",
    "equipment-classification-etl",
    "equipment-valuations-etl",
    "auction-data-etl",
    "equipment-details-etl",
    "equipment-values-residuals-etl",
    "fleet-manager-cat-product-group",
    "fleet-manager-customers-metrics-reporting",
    "fleet-manager-sales-subcategory",
    "fleet-manager-supercategory-etl",
    "fleet-manager-sync",
    "fleet-manager-users-etl",
    "marketing-metrics",
    "mpe-kpi-metrics-etl",
    "mpe-transactions-etl",
    "modelyear-range-etl",
    "sales-prevailing-exchange-rates-etl",
    "transaction-volume-auction-etl",
    "value-trends-etl",
    "web-leads-etl",
    "appraisal-valuation-canary-etl",
    "auctions-etl",
    "classification-photos",
    "client-etl",
    "countries-etl",
    "create-empty-lookup-values-tables",
    "csmm-relevancy-etl",
    "equipment-catalog-etl",
    "equipment-configuration-variants-etl",
    "equipment-files-etl",
    "equipment-notes-history-etl",
    "equipment-photos-etl",
    "equipment-photos-sync-monitor",
    "equipment-sync-monitor",
    "equipment-syndication-etl",
    "equipment-values-history-etl",
    "manage-equipment-etl",
    "marketable-life-etl",
    "migrations-etl",
    "equipment-view-publisher",
    "fleet-manager-consolidated-data",
    "fleet-manager-expected-time-to-sell-etl",
    "fleet-manager-fleet-customers-monitoring",
    "fleet-manager-fleet-listings",
    "fleet-manager-fleet-metrics",
    "fleet-manager-publish-insights-taxonomy-and-localization-etl",
    "proposal-history-etl",
    "rfm-archiving-etl",
    "sales-erp-sync",
    "sales-google-analytics-page-views",
    "sales-invoice-emails",
    "sales-transactions",
    "sales-values-lookup",
    "ss-export",
    "transaction-volume-retail-etl",
    "restore-etl",
    "fleet-manager-valuations-history-etl",
    "health-checks",
    "generate-site-map-etl",
    "legacy-provisioning-etl",
    "rental-insights-etl",
    "mixer-export-etl",
    "mixer-values-lookup-configuration-sync",
    "rb-list-views-etl",
    "rb-list-web-leads-etl",
    "rb-taxonomy-etl",
    "fleet-manager-multi-checks",
    "values-lookup-schema-etl",
    "fleet-manager-partition-management",
    "sales-rfm-user-config-reporting",
    "selling-channel-reporting-etl",
    "smartequip-parts-book-etl",
    "user-config-rule-change-log-reporting",
    "united-xvalues-export-etl",
    "custom-grids-benchmark-transactional-detail-etl",
    "rental-metrics-aggs",
  ])

  metadata {
    name      = "pgbouncer-rfm02-prod-client-cert"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    SSL_CERT = data.google_secret_manager_secret_version.pgbouncer-rfm02-prod-client-cert-secret.secret_data
  }
}

data "google_secret_manager_secret_version" "pgbouncer-rfm02-prod-client-key-secret" {
  secret  = "pgbouncer_rfm02_prod_client_key"
  project = local.services
}


resource "kubernetes_secret" "pgbouncer-rfm02-prod-client-key-secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "ims-fleet-ingest",
    "ims-fleet-refresh",
    "rouse-rb-taxonomy-mapping-etl",
    "fleet-manager-pg-export",
    "fleet-manager-pg-export-multi-tenant",
    "fleet-manager-backfill",
    "change-history-etl",
    "equipment-classification-etl",
    "equipment-valuations-etl",
    "auction-data-etl",
    "equipment-details-etl",
    "equipment-values-residuals-etl",
    "fleet-manager-cat-product-group",
    "fleet-manager-customers-metrics-reporting",
    "fleet-manager-sales-subcategory",
    "fleet-manager-supercategory-etl",
    "fleet-manager-sync",
    "fleet-manager-users-etl",
    "marketing-metrics",
    "mpe-kpi-metrics-etl",
    "mpe-transactions-etl",
    "modelyear-range-etl",
    "sales-prevailing-exchange-rates-etl",
    "transaction-volume-auction-etl",
    "value-trends-etl",
    "web-leads-etl",
    "appraisal-valuation-canary-etl",
    "auctions-etl",
    "classification-photos",
    "client-etl",
    "countries-etl",
    "create-empty-lookup-values-tables",
    "csmm-relevancy-etl",
    "equipment-catalog-etl",
    "equipment-configuration-variants-etl",
    "equipment-files-etl",
    "equipment-notes-history-etl",
    "equipment-photos-etl",
    "equipment-photos-sync-monitor",
    "equipment-sync-monitor",
    "equipment-syndication-etl",
    "equipment-values-history-etl",
    "manage-equipment-etl",
    "marketable-life-etl",
    "migrations-etl",
    "equipment-view-publisher",
    "fleet-manager-consolidated-data",
    "fleet-manager-expected-time-to-sell-etl",
    "fleet-manager-fleet-customers-monitoring",
    "fleet-manager-fleet-listings",
    "fleet-manager-fleet-metrics",
    "fleet-manager-publish-insights-taxonomy-and-localization-etl",
    "proposal-history-etl",
    "rfm-archiving-etl",
    "sales-erp-sync",
    "sales-google-analytics-page-views",
    "sales-invoice-emails",
    "sales-transactions",
    "sales-values-lookup",
    "ss-export",
    "transaction-volume-retail-etl",
    "restore-etl",
    "fleet-manager-valuations-history-etl",
    "health-checks",
    "generate-site-map-etl",
    "legacy-provisioning-etl",
    "rental-insights-etl",
    "mixer-export-etl",
    "mixer-values-lookup-configuration-sync",
    "rb-list-views-etl",
    "rb-list-web-leads-etl",
    "rb-taxonomy-etl",
    "fleet-manager-multi-checks",
    "values-lookup-schema-etl",
    "fleet-manager-partition-management",
    "sales-rfm-user-config-reporting",
    "selling-channel-reporting-etl",
    "smartequip-parts-book-etl",
    "user-config-rule-change-log-reporting",
    "united-xvalues-export-etl",
    "custom-grids-benchmark-transactional-detail-etl",
    "rental-metrics-aggs",
  ])

  metadata {
    name      = "pgbouncer-rfm02-prod-client-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    SSL_KEY = data.google_secret_manager_secret_version.pgbouncer-rfm02-prod-client-key-secret.secret_data
  }
}

data "google_secret_manager_secret_version" "pfx-cert-password" {
  secret  = "pfx_cert_password"
  project = local.project
}

resource "kubernetes_secret" "pfx-cert-password" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "octopus-cert-renewal",
    "winrm-setup",
  ])

  metadata {
    name      = "pfx-cert-password"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    password = data.google_secret_manager_secret_version.pfx-cert-password.secret_data
  }
}

data "google_secret_manager_secret_version" "octopus-api-key" {
  secret  = "octopus_api_key"
  project = local.project
}

resource "kubernetes_secret" "octopus-api-key" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "octopus-cert-renewal",
  ])

  metadata {
    name      = "octopus-api-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    api_key = data.google_secret_manager_secret_version.octopus-api-key.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_svc_marketing_metrics_etl" {
  secret  = "mssql_svc_marketing_metrics_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-marketing-metrics-etl-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-marketing-metrics-etl-prod"
    namespace = kubernetes_namespace.workload_identity["marketing-metrics"].metadata[0].name
  }

  data = {
    username = "svc_marketing_metrics_etl"
    password = data.google_secret_manager_secret_version.mssql_svc_marketing_metrics_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "web_taylor_martin_auctions_scraper_etl" {
  secret  = "web_taylor_martin_auctions_scraper_etl"
  project = local.project
}

resource "kubernetes_secret" "de-taylor-martin-auctions-scraper-etl-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-taylor-martin-auctions-scraper-etl-prod"
    namespace = kubernetes_namespace.workload_identity["auctions-scraper-etl"].metadata[0].name
  }

  data = {
    username = "<EMAIL>"
    password = data.google_secret_manager_secret_version.web_taylor_martin_auctions_scraper_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "web_big_iron_auctions_scraper_etl" {
  secret  = "web_big_iron_auctions_scraper_etl"
  project = local.project
}

resource "kubernetes_secret" "de-web-big-iron-auctions-scraper-etl-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-web-big-iron-auctions-scraper-etl-prod"
    namespace = kubernetes_namespace.workload_identity["auctions-scraper-etl"].metadata[0].name
  }

  data = {
    email    = "<EMAIL>"
    password = data.google_secret_manager_secret_version.web_big_iron_auctions_scraper_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "web_ip_vendor_auctions_scraper_etl" {
  secret  = "web_ip_vendor_auctions_scraper_etl"
  project = local.project
}

resource "kubernetes_secret" "de-web-ip-vendor-auctions-scraper-etl-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-web-ip-vendor-auctions-scraper-etl-prod"
    namespace = kubernetes_namespace.workload_identity["auctions-scraper-etl"].metadata[0].name
  }

  data = {
    username = "resirouseservices"
    password = data.google_secret_manager_secret_version.web_ip_vendor_auctions_scraper_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_ietl_archive_history_data_etl" {
  secret  = "mssql_ietl_archive_history_data_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-ietl-archive-history-data-etl-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-ietl-archive-history-data-etl-prod"
    namespace = kubernetes_namespace.workload_identity["ietl-archive-history-data-etl"].metadata[0].name
  }

  data = {
    username = "svc_ietl_archive_history_data_etl"
    password = data.google_secret_manager_secret_version.mssql_ietl_archive_history_data_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_ietl_copy_analytics_etl" {
  secret  = "mssql_ietl_copy_analytics_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-ietl-copy-analytics-etl-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-ietl-copy-analytics-etl-prod"
    namespace = kubernetes_namespace.workload_identity["ietl-copy-analytics-etl"].metadata[0].name
  }

  data = {
    username = "svc_ietl_copy_analytics_etl"
    password = data.google_secret_manager_secret_version.mssql_ietl_copy_analytics_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_ietl_sold_channel_keywords_etl" {
  secret  = "mssql_ietl_sold_channel_keywords_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-ietl-sold-channel-keywords-etl-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-ietl-sold-channel-keywords-etl-prod"
    namespace = kubernetes_namespace.workload_identity["ietl-sold-channel-keywords-etl"].metadata[0].name
  }

  data = {
    username = "svc_ietl_sold_channel_keywords_etl"
    password = data.google_secret_manager_secret_version.mssql_ietl_sold_channel_keywords_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_riei_combo_classification_etl" {
  secret  = "mssql_riei_combo_classification_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-riei-combo-classification-etl-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-riei-combo-classification-etl-prod"
    namespace = kubernetes_namespace.workload_identity["riei-combo-classification-etl"].metadata[0].name
  }

  data = {
    username = "svc_riei_combo_classification_etl"
    password = data.google_secret_manager_secret_version.mssql_riei_combo_classification_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "ws_svc_ietl_inputfile_transform" {
  secret  = "ws_svc_ietl_inputfile_transform"
  project = local.project
}

resource "kubernetes_secret" "de-ws-ietl-input-file-etl-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-ws-ietl-input-file-etl-prod"
    namespace = kubernetes_namespace.workload_identity["ietl-input-file-etl"].metadata[0].name
  }

  data = {
    domain   = "rasgcp"
    username = "svc_ietl_inputfile_t"
    password = data.google_secret_manager_secret_version.ws_svc_ietl_inputfile_transform.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_svc_sales_salesforce_reporting" {
  secret  = "mssql_svc_sales_salesforce_reporting"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-svc-sales-salesforce-reporting-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-svc-sales-salesforce-reporting-prod"
    namespace = kubernetes_namespace.workload_identity["sales-salesforce-reporting"].metadata[0].name
  }

  data = {
    username = "svc_sales_salesforce_reporting"
    password = data.google_secret_manager_secret_version.mssql_svc_sales_salesforce_reporting.secret_data
  }
}

data "google_secret_manager_secret_version" "mascus_leads_api_credentials_secret" {
  secret  = "mascus_leads_api_credentials"
  project = local.project
}

resource "kubernetes_secret" "mascus_leads_api_credentials_secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "rb-list-views-etl",
    "rb-list-web-leads-etl",
  ])

  metadata {
    name      = "mascus-leads-api-credentials"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.mascus_leads_api_credentials_secret.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.mascus_leads_api_credentials_secret.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "mascus_api_credentials_secret" {
  secret  = "mascus_api_credentials"
  project = local.services
}

resource "kubernetes_secret" "mascus_api_credentials_secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "rb-taxonomy-etl",
  ])

  metadata {
    name      = "mascus-api-credentials"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.mascus_api_credentials_secret.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.mascus_api_credentials_secret.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "record360-01-prod-record360rw_credentials_secret" {
  secret  = "record360-01-prod-record360rw"
  project = local.project
}

resource "kubernetes_secret" "record360-01-prod-record360rw_credentials_secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "appraisals-api",
  ])

  metadata {
    name      = "record360-01-prod-record360rw"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.record360-01-prod-record360rw_credentials_secret.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.record360-01-prod-record360rw_credentials_secret.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "salesforce_client_api_secret" {
  secret  = "salesforce_client_api"
  project = local.project
}

resource "kubernetes_secret" "salesforce_client_api_secret_secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "appraisals-api",
  ])

  metadata {
    name      = "salesforce-client-api"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.salesforce_client_api_secret.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.salesforce_client_api_secret.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "pgbouncer-platform01-prod-client-cert-secret" {
  secret  = "pgbouncer_platform01_prod_client_cert"
  project = local.services
}

resource "kubernetes_secret" "pgbouncer-platform01-prod-client-cert-secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "client-etl",
    "equipment-syndication-etl",
    "fleet-manager-backfill",
    "fleet-manager-partition-management",
    "fleet-manager-users-etl",
    "ims-fleet-refresh",
    "rb-list-views-etl",
    "rb-list-web-leads-etl",
    "sales-rfm-user-config-reporting",
  ])

  metadata {
    name      = "pgbouncer-platform01-prod-client-cert"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    SSL_CERT = data.google_secret_manager_secret_version.pgbouncer-platform01-prod-client-cert-secret.secret_data
  }
}

data "google_secret_manager_secret_version" "pgbouncer-platform01-prod-client-key-secret" {
  secret  = "pgbouncer_platform01_prod_client_key"
  project = local.services
}

resource "kubernetes_secret" "pgbouncer-platform01-prod-client-key-secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "client-etl",
    "equipment-syndication-etl",
    "fleet-manager-backfill",
    "fleet-manager-partition-management",
    "fleet-manager-users-etl",
    "ims-fleet-refresh",
    "rb-list-views-etl",
    "rb-list-web-leads-etl",
    "sales-rfm-user-config-reporting",
  ])

  metadata {
    name      = "pgbouncer-platform01-prod-client-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    SSL_KEY = data.google_secret_manager_secret_version.pgbouncer-platform01-prod-client-key-secret.secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_kafka_rb" {
  for_each = toset([
    "prod",
  ])
  secret  = "dbuser_kafka_rb_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_kafka_rb" {
  provider = kubernetes.composer-jobs-v3
  for_each = data.google_secret_manager_secret_version.dbuser_kafka_rb

  metadata {
    name      = "rb-kafka-${each.key}"
    namespace = kubernetes_namespace.workload_identity["rb-taxonomy-etl"].metadata[0].name
  }

  data = {
    kafka_api_key          = lookup(jsondecode(each.value.secret_data), "kafka_api_key")
    kafka_api_secret       = lookup(jsondecode(each.value.secret_data), "kafka_api_secret")
    schema_registry_key    = lookup(jsondecode(each.value.secret_data), "schema_registry_key")
    schema_registry_secret = lookup(jsondecode(each.value.secret_data), "schema_registry_secret")
  }
}

data "google_secret_manager_secret_version" "sales_rb_list_views_etl_auth0_credentials" {
  secret  = "sales_rb_list_views_etl_auth0_credentials"
  project = local.services
}

resource "kubernetes_secret" "de-rb-list-views-auth0-credentials-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-rb-list-views-auth0-credentials-prod"
    namespace = kubernetes_namespace.workload_identity["rb-list-views-etl"].metadata[0].name
  }

  data = {
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rb_list_views_etl_auth0_credentials.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rb_list_views_etl_auth0_credentials.secret_data), "client_secret")
  }
}

resource "kubernetes_secret" "listing-sync-monitor-auth0-credentials-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "listing-sync-monitor-auth0-credentials"
    namespace = kubernetes_namespace.workload_identity["equipment-sync-monitor"].metadata[0].name
  }

  data = {
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rb_list_views_etl_auth0_credentials.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rb_list_views_etl_auth0_credentials.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "sales_google_analytics_page_views_etl_auth0_credentials" {
  secret  = "sales_google_analytics_page_views_etl_auth0_credentials"
  project = local.services
}

resource "kubernetes_secret" "de-sales-google-analytics-page-views-auth0-credentials-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-sales-google-analytics-page-views-auth0-credentials-prod"
    namespace = kubernetes_namespace.workload_identity["sales-google-analytics-page-views"].metadata[0].name
  }

  data = {
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_google_analytics_page_views_etl_auth0_credentials.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.sales_google_analytics_page_views_etl_auth0_credentials.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "valuations_cms_auth0_credentials" {
  secret  = "valuations_cms_auth0_credentials"
  project = local.services
}

resource "kubernetes_secret" "de-cms-auth0-credentials-prod" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "expanded-equipment-etl",
    "appraisal-cms-canary-etl",
    "smartequip-parts-book-etl",
  ])

  metadata {
    name      = "de-cms-auth0-credentials-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.valuations_cms_auth0_credentials.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.valuations_cms_auth0_credentials.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "appraisals-prod-rw" {
  secret  = "appraisals-prod-rw"
  project = local.project
}

resource "kubernetes_secret" "appraisals-prod-rw" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "appraisals-api",
    "inspections-etl"
  ])

  metadata {
    name      = "appraisals-prod-rw"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals-prod-rw.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals-prod-rw.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "dcs-api-gfs04-prod-user" {
  secret  = "dcs-api-gfs04-prod-user"
  project = local.project
}

resource "kubernetes_secret" "dcs-api-gfs04-prod-user" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "appraisals-dcs-api",
  ])
  metadata {
    name      = "dcs-gfs04-prod-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.dcs-api-gfs04-prod-user.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.dcs-api-gfs04-prod-user.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "dcs_postgres_dcs_api_user_prod" {
  secret  = "dcs_postgres_dcs_api_user_prod"
  project = local.project
}

resource "kubernetes_secret" "appraisals-dcs-api-db-user" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "appraisals-dcs-api",
  ])
  metadata {
    name      = "dcs-postgres-dcs-api-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.dcs_postgres_dcs_api_user_prod.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.dcs_postgres_dcs_api_user_prod.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "analytics_classify_export_credentials" {
  secret  = "analytics_classify_export_credentials"
  project = local.services
}

resource "kubernetes_secret" "analytics-classify-import-ss-credentials-prod" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "analytics-ss-import",
  ])

  metadata {
    name      = "analytics-classify-import-ss-credentials-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_classify_export_credentials.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_classify_export_credentials.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "valuations_wells_fargo_ssh_key" {
  secret  = "valuations_wells_fargo_ssh_key"
  project = local.management_prod
}
resource "kubernetes_secret" "wellsfargo-data-export-ssh-key-prod" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "wellsfargo-data-export",
  ])

  metadata {
    name      = "wellsfargo-data-export-ssh-key-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }
  data = {
    SSH_KEY = data.google_secret_manager_secret_version.valuations_wells_fargo_ssh_key.secret_data
  }
}

data "google_secret_manager_secret_version" "airflow_api_user" {
  secret  = "astronomer-airflow-api-key-${var.environment}"
  project = local.terraform_admin
}

resource "kubernetes_secret" "airflow_api_user" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "rebasing-automated-run"
  ])

  metadata {
    name      = "airflow-api-key-${var.environment}"
    namespace = each.key
  }
  data = {
    password = data.google_secret_manager_secret_version.airflow_api_user.secret_data
    username = "admin"
  }
}

data "google_secret_manager_secret_version" "rfm101-prod-admin" {
  secret  = "rfm101-prod-admin"
  project = local.management_prod
}

resource "kubernetes_secret" "rfm101-prod-admin" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "postgresql-create-db-users"
  ])

  metadata {
    name      = "rfm101-prod-admin"
    namespace = each.key
  }
  data = {
    password = data.google_secret_manager_secret_version.rfm101-prod-admin.secret_data
    username = "admin"
  }
}

data "google_secret_manager_secret_version" "dbuser_replication_lag_user" {
  secret  = "dbuser_replication_lag_user"
  project = local.services_prod
}

resource "kubernetes_secret" "dbuser_replication_lag_user" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "pglogical-monitoring-rfm101"
  ])

  metadata {
    name      = "dbuser-replication-lag-user"
    namespace = each.key
  }
  data = {
    password = data.google_secret_manager_secret_version.dbuser_replication_lag_user.secret_data
    username = "replication_lag_user"
  }
}

data "google_secret_manager_secret_version" "platform-valuation-base-image-webhook-trigger" {
  secret  = "platform-valuation-base-image-webhook-trigger"
  project = local.project
}

resource "kubernetes_secret" "platform-valuation-base-image-webhook-trigger" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "ml-for-rouse-etl",
    "appraisal-valuation-etl"
  ])

  metadata {
    name      = "platform-valuation-base-image-webhook-trigger"
    namespace = each.key
  }
  data = {
    webhook = data.google_secret_manager_secret_version.platform-valuation-base-image-webhook-trigger.secret_data
  }
}

data "google_secret_manager_secret_version" "rfm-analytics-api-framework" {
  secret  = "analytics-api-framework"
  project = local.qa_tools
}

resource "kubernetes_secret" "rfm-analytics-api-credentials" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "analytics-api-framework"
    namespace = "rental-insights-data-integrity-checks"
  }

  data = {
    analytics_credentials = data.google_secret_manager_secret_version.rfm-analytics-api-framework.secret_data
  }
}

data "google_secret_manager_secret_version" "rfm-qa-dev-secrets" {
  for_each = toset([
    "api-client-id",
    "api-client-secret",
    "sales-password-prod"
  ])
  secret  = each.key
  project = local.rfm_qa_prod
}

resource "kubernetes_secret" "rfm-qa-dev-api-client-id" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "api-client-id"
    namespace = "rental-insights-data-integrity-checks"
  }

  data = {
    client_id = data.google_secret_manager_secret_version.rfm-qa-dev-secrets["api-client-id"].secret_data
  }
}

resource "kubernetes_secret" "rfm-qa-dev-api-client-secret" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "api-client-secret"
    namespace = "rental-insights-data-integrity-checks"
  }

  data = {
    client_secret = data.google_secret_manager_secret_version.rfm-qa-dev-secrets["api-client-secret"].secret_data
  }
}

resource "kubernetes_secret" "rfm-qa-dev-sales-password-prod" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "sales-password-prod"
    namespace = "rental-insights-data-integrity-checks"
  }

  data = {
    sales_credentials = data.google_secret_manager_secret_version.rfm-qa-dev-secrets["sales-password-prod"].secret_data
  }
}

data "google_secret_manager_secret_version" "sales_fleet_manager_pagespeed_api_key_pipeline" {
  secret  = "sales_fleet_manager_pagespeed_api_key"
  project = local.services
}

resource "kubernetes_secret" "de-webshop-pagespeed-metrics-pagespeed-api-key" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-webshop-pagespeed-metrics-pagespeed-api-key-${var.environment}"
    namespace = kubernetes_namespace.workload_identity["webshop-pagespeed-metrics"].metadata[0].name
  }

  data = {
    token = data.google_secret_manager_secret_version.sales_fleet_manager_pagespeed_api_key_pipeline.secret_data
  }
}

# secrets data integrity airflow-private
resource "kubernetes_secret" "rfm-analytics-api-credentials-airflow-private" {
  provider = kubernetes.airflow-jobs-private
  metadata {
    name      = "analytics-api-framework"
    namespace = "rental-insights-data-integrity-checks"
  }

  data = {
    analytics_credentials = data.google_secret_manager_secret_version.rfm-analytics-api-framework.secret_data
  }
}

resource "kubernetes_secret" "rfm-qa-dev-api-client-id-airflow-private" {
  provider = kubernetes.airflow-jobs-private
  metadata {
    name      = "api-client-id"
    namespace = "rental-insights-data-integrity-checks"
  }

  data = {
    client_id = data.google_secret_manager_secret_version.rfm-qa-dev-secrets["api-client-id"].secret_data
  }
}

resource "kubernetes_secret" "rfm-qa-dev-api-client-secret-airflow-private" {
  provider = kubernetes.airflow-jobs-private
  metadata {
    name      = "api-client-secret"
    namespace = "rental-insights-data-integrity-checks"
  }

  data = {
    client_secret = data.google_secret_manager_secret_version.rfm-qa-dev-secrets["api-client-secret"].secret_data
  }
}

resource "kubernetes_secret" "rfm-qa-dev-sales-password-prod-airflow-private" {
  provider = kubernetes.airflow-jobs-private
  metadata {
    name      = "sales-password-prod"
    namespace = "rental-insights-data-integrity-checks"
  }

  data = {
    sales_credentials = data.google_secret_manager_secret_version.rfm-qa-dev-secrets["sales-password-prod"].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_ras_messages_global_client_metrics" {
  secret  = "dbuser_ras_messages_global_client_metrics"
  project = local.services
}

resource "kubernetes_secret" "de_client_metrics_ras_messages_global" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-client-metrics-ras-messages-global-${var.environment}"
    namespace = "client-metrics"
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_ras_messages_global_client_metrics.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_ras_messages_global_client_metrics.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "sales_fleet_manager_tools_auth0_client_id" {
  secret  = "sales_fleet_manager_tools_auth0_client_id"
  project = local.services
}

data "google_secret_manager_secret_version" "sales_fleet_manager_tools_auth0_client_secret" {
  secret  = "sales_fleet_manager_tools_auth0_client_secret"
  project = local.services
}

data "google_secret_manager_secret_version" "platform_user_config_sso_integration_token" {
  secret  = "platform_user_config_sso_integration_token"
  project = local.services
}

data "google_secret_manager_secret_version" "platform_user_config_auth_token" {
  secret  = "platform_user_config_auth_token"
  project = local.services
}

resource "kubernetes_secret" "role-sync-monitor-credentials-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "role-sync-monitor-credentials-prod"
    namespace = kubernetes_namespace.workload_identity["role-permission-sync-monitor"].metadata[0].name
  }

  data = {
    user_management_auth0_client_id     = data.google_secret_manager_secret_version.sales_fleet_manager_tools_auth0_client_id.secret_data
    user_management_auth0_client_secret = data.google_secret_manager_secret_version.sales_fleet_manager_tools_auth0_client_secret.secret_data
    user_service_sso_token              = data.google_secret_manager_secret_version.platform_user_config_sso_integration_token.secret_data
    user_service_static_token           = data.google_secret_manager_secret_version.platform_user_config_auth_token.secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_smartequip" {
  for_each = toset([
    "appcontent_smartequip_etl",
    "appdata_smartequip_etl",
  ])
  secret  = "dbuser_smartequip_${each.key}"
  project = local.project
}

resource "kubernetes_secret" "dbuser_smartequip" {
  provider = kubernetes.airflow-jobs-private

  for_each = {
    appcontent_smartequip_etl = {
      namespace   = "appcontent-smartequip-etl",
      secret_name = "de-mysql-appcontent-smartequip-etl"
    }
    appdata_smartequip_etl = {
      namespace   = "appdata-smartequip-etl",
      secret_name = "de-mysql-appdata-smartequip-etl"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.private_cluster_workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    password = lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_smartequip[each.key].secret_data), "password")
    username = lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_smartequip[each.key].secret_data), "username")
  }
}

data "google_secret_manager_secret_version" "alloydb_rfm1001_admin_prod" {
  secret  = "alloydb-rfm1001-admin-prod"
  project = local.services
}

resource "kubernetes_secret" "alloydb_rfm1001_prod_admin_composer_jobs" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "postgresql-create-db-users",
  ])

  metadata {
    name      = "alloydb-rfm1001-admin-prod"
    namespace = each.key
  }
  data = {
    password = data.google_secret_manager_secret_version.alloydb_rfm1001_admin_prod.secret_data
    username = "admin"
  }
}
