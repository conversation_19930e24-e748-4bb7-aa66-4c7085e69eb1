
resource "google_project_iam_member" "mascus_sales_import_appraisals" {
  for_each = toset([
    "roles/storage.objectAdmin",
    "roles/bigquery.dataOwner",
    "roles/bigquery.jobUser",
  ])
  role    = each.key
  project = local.appraisals
  member  = "serviceAccount:${google_service_account.workload_identity["mascus-sales-history-import"].email}"
}

resource "google_project_iam_member" "mascus_sales_import_services" {
  for_each = toset([
    "roles/viewer",
    "roles/cloudsql.instanceUser",
    "roles/cloudsql.client",
    "roles/secretmanager.viewer",
    "roles/secretmanager.secretAccessor",
    "roles/container.developer",
  ])
  role    = each.key
  project = local.services
  member  = "serviceAccount:${google_service_account.workload_identity["mascus-sales-history-import"].email}"
}