resource "google_organization_iam_member" "algo_pricing_pipeline" {
  for_each = toset([
    "roles/aiplatform.user",                    # For Vertex AI pipeline creation and management
    "roles/storage.objectAdmin",                # For GCS bucket access
    "roles/artifactregistry.reader",            # For container image access
    "roles/serviceusage.serviceUsageConsumer",  # For service usage
    "roles/ml.admin",                          # For ML operations
    "roles/logging.logWriter",                 # For logging
    "roles/monitoring.metricWriter"            # For monitoring
  ])
  org_id = local.org_id
  role   = each.key
  member = "serviceAccount:${google_service_account.workload_identity["algo-pricing-pipeline"].email}"
}