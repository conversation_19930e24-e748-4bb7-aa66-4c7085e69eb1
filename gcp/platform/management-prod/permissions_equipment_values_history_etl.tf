resource "google_folder_iam_member" "equipment_values_history_etl_client_folder" {
  for_each = toset([
    "roles/storage.objectAdmin",
    "roles/bigquery.dataOwner",
    "roles/bigquery.jobUser",
    "roles/bigquery.readSessionUser",
  ])

  folder = data.terraform_remote_state.client_projects.outputs.client_projects_folder
  role   = each.key
  member = "serviceAccount:${google_service_account.workload_identity["equipment-values-history-etl"].email}"
}

resource "google_project_iam_member" "equipment_values_history_etl_management" {
  for_each = toset([
    "roles/pubsub.editor",
    "roles/pubsub.subscriber",
    "roles/pubsub.publisher",
    "roles/pubsub.viewer",
  ])
  role    = each.key
  project = local.project
  member  = "serviceAccount:${google_service_account.workload_identity["equipment-values-history-etl"].email}"
}

resource "google_project_iam_member" "equipment_values_history_etl_services" {
  for_each = toset([
    "roles/cloudsql.client",
  ])
  role    = each.key
  project = local.services
  member  = "serviceAccount:${google_service_account.workload_identity["equipment-values-history-etl"].email}"
}

resource "google_project_iam_member" "equipment_values_history_etl_appraisals" {
  for_each = toset([
    "roles/pubsub.editor",
    "roles/pubsub.subscriber",
    "roles/pubsub.publisher",
    "roles/pubsub.viewer",
    "roles/bigquery.dataOwner",
    "roles/bigquery.jobUser",
    "roles/bigquery.readSessionUser",
  ])
  role    = each.key
  project = local.appraisals
  member  = "serviceAccount:${google_service_account.workload_identity["equipment-values-history-etl"].email}"
}

resource "google_project_iam_member" "equipment_values_history_etl_rfm_permissions" {
  for_each = toset([
    "roles/storage.objectAdmin",
    "roles/bigquery.dataOwner",
    "roles/bigquery.jobUser",
  ])
  role    = each.key
  project = local.rfm
  member  = "serviceAccount:${google_service_account.workload_identity["equipment-values-history-etl"].email}"
}