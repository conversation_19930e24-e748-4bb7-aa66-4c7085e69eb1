data "google_kms_secret" "sendgrid_api_key" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU76zCNFOYDIM0AL0WIbA+8ruxkuMcjUK01sWCU8kAPi8SbgCc5a1pUgjDYLbS5NwlTpW7JIWRTcQT8vnnutQZMfSmaUvi/b9/12xNSmTtLvC5jT4hefDM7xO3bDNm/bBmTd6d1aYz14mi1C1C/LjhOGae+9WZDphO0A0UlfiyYO+DEvG5wG3K9NDfzjRqHYq5"
}

data "google_client_config" "default" {}

data "google_secret_manager_secret_version" "dbuser_health_checks" {
  for_each = toset([
    "mssql_message_queue_monitoring"
  ])
  secret  = each.key
  project = local.project
}

resource "kubernetes_secret" "health_checks_dev" {
  provider = kubernetes.composer-jobs-v3
  for_each = {
    "de-message-queue-monitoring-dev" = "mssql_message_queue_monitoring",
  }
  metadata {
    name      = each.key
    namespace = kubernetes_namespace.workload_identity["health-checks"].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_health_checks[each.value].secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_health_checks[each.value].secret_data), "password")
  }
}

data "google_kms_secret" "modelyear_credentials" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "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"
}

resource "kubernetes_secret" "de-modelyear-range-gsheet-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "value-trends-etl",
    "modelyear-range-etl",
    "expanded-equipment-etl",
    "custom-grids-benchmark-transactional-detail-etl",
    "auctions-scraper-etl",
    "rb-competitive-intelligence-etl",
    "ims-fleet-ingest",
    "schedules-module-etl",
    "rbme-sales-reporting-support-etl",
    "data-science-models-etl",
    "rouse-rb-taxonomy-mapping-etl",
    "smartequip-parts-book-etl",
    "ietl-sold-channel-keywords-etl",
    "price-performance-etl",
  ])

  metadata {
    name      = "de-modelyear-range-gsheet-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    credentials = data.google_kms_secret.modelyear_credentials.plaintext
  }
}

data "google_kms_secret" "svc_eqclass_etl_dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7/ZkDTVCRaLoyhMwZANs+4USDxwJRDaqAMnNxBzHpNsSNwCc5a1pTQ83ku1L/1qljKHpZl7uKYXh06/RVyE5MO31jIPx8cW+kjA+kcy9fzcfF60b941XsnI="
}

resource "kubernetes_secret" "de-equipment-classification-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-classification-etl",
  ])

  metadata {
    name      = "de-equipment-classification-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_eqclass_etl"
    password = data.google_kms_secret.svc_eqclass_etl_dev.plaintext
  }
}

data "google_kms_secret" "svc_classrl_etl_dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU71+Ttd6JqC64FetvxQNY+Mmoxq+Aue6497YEjlictvISNwCc5a1pENUdt5Kqrwz88bAuLYAQ+Vm4hiRCGMBo1xsFz8mT+r4QCPyrGY4imAlsK4sCxgzKIg0="
}

resource "kubernetes_secret" "de-classification-rules-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "classification-rules-etl",
  ])

  metadata {
    name      = "de-classification-rules-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_classrl_etl"
    password = data.google_kms_secret.svc_classrl_etl_dev.plaintext
  }
}

data "google_kms_secret" "de-expanded-equipment-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU71ogMUm3GdrUa8GoTJoMe3YoQ03lzwezyd3QAH5kxU4SNwA4/8/uQ35DyVXuC45avrO6JBZpRy2J4qvlDyfCFdyxEybcWAYKE5Vq52jn5mx0Gme3XBRrzhI="
}

resource "kubernetes_secret" "de-expanded-equipment-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "expanded-equipment-etl",
  ])

  metadata {
    name      = "de-expanded-equipment-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_expanded_eq_etl"
    password = data.google_kms_secret.de-expanded-equipment-dev.plaintext
  }
}

data "google_kms_secret" "platform_winrm_prod_pass" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU79PhoBY6QDjlOB72BBaP0Jc3vou2O4/v2CIxtztKTBISNwCc5a1pA66i2dCOi+bkC9vo345Y2N6iBZkoRdS+uy0WINr5tgmGOj+LB9iWp/QDObz5waGM+4I="
}

resource "kubernetes_secret" "platform_winrm_prod_creds_wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "nightly-files-sync",
  ])

  metadata {
    name      = "platform-winrm-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "rasgcp\\svc_ETL_WINRM_prod"
    password = data.google_kms_secret.platform_winrm_prod_pass.plaintext
  }
}

data "google_kms_secret" "algolia_api_key" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7zVwPZta34j32cnYb5Yol3Cku+XMVOA2MuK3Cl3TNSQSSAA4/8/uobWkDv+Jt52wIrvxED5wuEiJqHDlWZbCI7vG2vi6OuLEhLibduXLuwqeKcQgf5nGcRPfTzyTHE2disEThovxTa2xzA=="
}

resource "kubernetes_secret" "de_algolia_dev_wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "classification-index-etl",
    "restore-etl",
    "values-lookup-schema-etl",
  ])

  metadata {
    name      = "de-algolia-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    api-key = data.google_kms_secret.algolia_api_key.plaintext
  }
}

data "google_kms_secret" "de_appraisal_book_dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7+3aVXd464Q6nIL0QhCEsts0q1ZjnYMuO8gVDR59VX0SNwCc5a1pIOw4KR35m87NKYHNpZoP2ndjDypdqghLcPrgBXVjLN69b1XHl+3f5pZan9Y/kMQ5oFI="
}

resource "kubernetes_secret" "de_appraisal_book_dev_wi" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "appraisal-book-region-mappings-etl",
    "appraisal-book-region-adjusters-etl",
    "appraisal-book-etl",
  ])

  metadata {
    name      = "de-appraisal-book-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_appbook_etl"
    password = data.google_kms_secret.de_appraisal_book_dev.plaintext
  }
}

data "google_kms_secret" "de-equipment-details-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7+rNgaHOLNKSA8Je3/s94aUAVO1jZZvT36RyRq/85BcSOAA4/8/uXhs5thoaUY6ycRa32b1u4CSDAmuAtPXrNVJn+nGe3D8ZTa+rdnjVTI8C6wusqivfY/eU"
}

resource "kubernetes_secret" "de-equipment-details-dev-wi" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "equipment-details-etl",
  ])

  metadata {
    name      = "de-equipment-details-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_eqpdet_etl"
    password = data.google_kms_secret.de-equipment-details-dev.plaintext
  }
}

data "google_kms_secret" "sql_sales_user_dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7wbVV+TLjPmedXS2mIjRIiRVL+n+e/hxdEi8d/Ue3CkSPQA4/8/u7koCsAlYd61Hr8iEEDwVJwNqNufeikr6pli1bjTqx1owfcKdiQMucgZztLLd/vLuIkw3nurqzYU="
}

resource "kubernetes_secret" "sql_sales_user_dev_wi" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "sharded-nightly-updates",
    "db-deploy",
  ])

  metadata {
    name      = "sql-sales-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "sql_sales_user_dev"
    password = data.google_kms_secret.sql_sales_user_dev.plaintext
  }
}

data "google_secret_manager_secret_version" "sql_sa_user_vms_dev" {
  secret  = "mssql_sharded_sa_user_vms"
  project = local.project
}

data "google_secret_manager_secret_version" "sql_svc_backup_sql_server" {
  secret  = "dbuser_sharded_svc_backup_sql_server"
  project = local.services
}

resource "kubernetes_secret" "sql_svc_backup_sql_server_wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "sharded-db-backup-sql-server",
    "db-deploy",
  ])

  metadata {
    name      = "sql-backup-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_backup_sql_server"
    password = data.google_secret_manager_secret_version.sql_svc_backup_sql_server.secret_data
  }
}

resource "kubernetes_secret" "sql_svc_backup_sql_server_dev_wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "sharded-db-backup-sql-server",
    "db-deploy",
  ])

  metadata {
    name      = "sql-backup-user-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_backup_sql_server"
    password = data.google_secret_manager_secret_version.sql_svc_backup_sql_server.secret_data
  }
}

resource "kubernetes_secret" "sql_sa_user_vms_dev_wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-valuations-etl",
    "marketing-metrics",
    "nightly-asset",
    "sales-transactions",
    "vod-logs",
    "db-coda-dep-auction-data",
    "restore-sharded-dbs",
    "db-deploy",
    "sharded-db-reindex",
    "nightly-deployment",
    "sharded-nightly-updates",
  ])

  metadata {
    name      = "sql-sa-user-vms"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "sa"
    password = data.google_secret_manager_secret_version.sql_sa_user_vms_dev.secret_data
  }
}

data "google_kms_secret" "windows_account_vms_dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU78u4ErQv5oho3S3NhjFYVfKNZD3UCev5+O8NHtgdcroSPQA4/8/uo5ihFvZsiMcuLYWJ169Wk3flMbftuR7mkNLu7r3vMNcFk6hLy0ZYtcnKuGRqOY0yhXYKuCHbg5M="
}

resource "kubernetes_secret" "windows_account_vms_dev_wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "analytics-eemphasys",
    "analytics-irental",
    "analytics-email-grabber",
    "restore-sharded-dbs",
    "service-monitoring",
    "nightly-execution",
    "db-deploy",
    "nightly-deployment",
    "archive-nightly-logging-files",
    "sharded-nightly-updates",
    "sharded-db-backup-sql-server",
    "nightly-files-sync",
    "security-assessment-scope-report",
    "analytics-admar",
    "octopus-cert-renewal",
    "winrm-setup",
    "analytics-salesforce",
    "rebasing-automated-run",
    "rental-metrics-aggs",
  ])

  metadata {
    name      = "windows-account-vms-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "rasgcp-dev\\automation"
    password = data.google_secret_manager_secret_version.windows_account_vms_dev.secret_data
  }
}

resource "kubernetes_secret" "windows_account_vms_dev_wi_private_cluster" {
  provider = kubernetes.airflow-jobs-private

  for_each = toset([
    "analytics-powerbi"
  ])

  metadata {
    name      = "windows-account-vms-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "rasgcp-dev\\automation"
    password = data.google_secret_manager_secret_version.windows_account_vms_dev.secret_data
  }
}

data "google_secret_manager_secret_version" "windows_account_vms_dev" {
  secret  = "ansible_rasgcp_dev_automation"
  project = local.terraform_admin
}

data "google_secret_manager_secret_version" "windows_account_vms_prod" {
  secret  = "ansible_rasgcp_automation"
  project = local.terraform_admin
}

resource "kubernetes_secret" "windows_account_vms_prod_wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "restore-sharded-dbs",
    "ssis-deployment",
    "security-assessment-scope-report",
    "service-monitoring",
    "equipment-syndication-etl",
    "nightly-execution",
    "sharded-db-backup-sql-server",
    "octopus-cert-renewal",
    "winrm-setup",
    "rebasing-automated-run"
  ])

  metadata {
    name      = "windows-account-vms-prod"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "rasgcp\\automation"
    password = data.google_secret_manager_secret_version.windows_account_vms_prod.secret_data
  }
}

data "google_kms_secret" "de-rambo-export-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7+V8/lln22kbQrXH6fgW5NUw5/q2w313qqUSeF27AXESNQA4/8/u7n9DKVbmPK4UN+vNgyFDLnKtQDoWkfdT6dT2rcnOsc/Vi5f6AURvfTh8uJEMpWn6"
}

resource "kubernetes_secret" "de-rambo-export-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "rental-metrics-aggs",
    "custom-grids-benchmark-transactional-detail-etl",
    "rambo-smartequip-pipeline",
    "rdo-translation",
  ])

  metadata {
    name      = "de-rambo-export-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_rambo_export"
    password = data.google_kms_secret.de-rambo-export-dev.plaintext
  }
}


data "google_secret_manager_secret_version" "analytics_salesforce_api_credentials" {
  secret  = "analytics_salesforce_api_credentials"
  project = local.services
}

resource "kubernetes_secret" "analytics_salesforce_api_credentials" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "analytics-salesforce-api-credentials"
    namespace = kubernetes_namespace.workload_identity["rental-metrics-aggs"].metadata[0].name
  }

  data = {
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_salesforce_api_credentials.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_salesforce_api_credentials.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "analytics_salesforce_smartequip_credentials" {
  secret  = "analytics_salesforce_smartequip_credentials"
  project = local.services
}

resource "kubernetes_secret" "analytics_salesforce_smartequip_credentials" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "rental-metrics-aggs",
    "rambo-smartequip-pipeline",
  ])

  metadata {
    name      = "analytics-salesforce-smartequip-credentials"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }
  

  data = {
    client_id       = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_salesforce_smartequip_credentials.secret_data), "client_id")
    client_secret   = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_salesforce_smartequip_credentials.secret_data), "client_secret")
    client_username = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_salesforce_smartequip_credentials.secret_data), "client_username")
    client_password = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_salesforce_smartequip_credentials.secret_data), "client_password")
  }
}


data "google_secret_manager_secret_version" "analytics_salesforce_services_credentials" {
  secret  = "analytics_salesforce_services_credentials"
  project = local.services
}

resource "kubernetes_secret" "analytics_salesforce_services_credentials" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "analytics-salesforce-services-credentials"
    namespace = kubernetes_namespace.workload_identity["rental-metrics-aggs"].metadata[0].name
  }


  data = {
    client_id       = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_salesforce_services_credentials.secret_data), "client_id")
    client_secret   = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_salesforce_services_credentials.secret_data), "client_secret")
    client_username = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_salesforce_services_credentials.secret_data), "client_username")
    client_password = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_salesforce_services_credentials.secret_data), "client_password")
  }
  
}



data "google_kms_secret" "de-auction-transaction-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU77bZsONApcwQapR7YoG+XuSZlq4B6elKhx/TsgXyjhkSNQA4/8/uFbTsEK87dDYP8NOGyQfNIESqYXUsmuO4ckw+IrkOg6OVqsJo4ZxaPWqKOdsxZVfv"
}

resource "kubernetes_secret" "de-auction-transaction-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "auction-transactions-etl",
  ])

  metadata {
    name      = "de-auction-transaction-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_auctiontnx_etl"
    password = data.google_kms_secret.de-auction-transaction-dev.plaintext
  }
}

data "google_kms_secret" "de-identity-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU70dcP5cnKAfMdTxzjaIc9YG2+V20jut7RBM1M1YegbYSNQA4/8/ujnfcNZRK+Mmc0lhqFa6IWGn3uDmSMIUXV2OxDph32Z9lkoUkNlJkV+KspbgpTbuZ"
}

resource "kubernetes_secret" "de-identity-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "identity-etl",
    "custom-grids-benchmark-transactional-detail-etl"
  ])

  metadata {
    name      = "de-identity-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_identity_etl" 
    password = data.google_kms_secret.de-identity-dev.plaintext
  }
}

data "google_kms_secret" "de-classification-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7x7+JNo9Vp33fe4Cqy3jc7Wo7lu9wGaGvJWKZHFCHV4SNQA4/8/u5b5sJV0WO6mEjcxsWjyAszMKdGNVifPA2rRBDv1NN+eQgJsD/9xFelkBSMhU0abO"
}

resource "kubernetes_secret" "de-classification-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "classification-etl"
  ])

  metadata {
    name      = "de-classification-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_classification_etl"
    password = data.google_kms_secret.de-classification-dev.plaintext
  }
}

data "google_kms_secret" "de-rdo-translation-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU747X84c9rCFwQ/3HRKGStGNs8c+c7zNuccU3QrFdcGoSNQA4/8/urWC8rrlalYq1Yl2Z4SwX6hkc5+68vc0+ZoJrRb9nRYD6m44X2Plt0RgUQsN1FLW9"
}

resource "kubernetes_secret" "de-rdo-translation-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "rdo-translation",
  ])

  metadata {
    name      = "de-rdo-translation-dev-wi"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_rdo_translation_etl"
    password = data.google_kms_secret.de-rdo-translation-dev.plaintext
  }
}

data "google_kms_secret" "de-book-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7zncNKEeb4+BjIvXM3uQCt4NqP2fXvsoaMHQLkeODx0SNQA4/8/ufYR8y+Rdu7h579SAYVwkx7aFID7ywbFJ0B2TpMbadGSzDIodyu6Fb1Wh0Q+BNPeU"
}

resource "kubernetes_secret" "de-book-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "book-etl",
    "book-classification-etl"
  ])

  metadata {
    name      = "de-book-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_book_etl"
    password = data.google_kms_secret.de-book-dev.plaintext
  }
}

data "google_secret_manager_secret_version" "qa-check-dev" {
  secret  = "PyAPI-DataChecks"
  project = local.project
}

resource "kubernetes_secret" "qa-check-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "sales-transactions",
  ])

  metadata {
    name      = "qa-check-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "data-checks"
    password = data.google_secret_manager_secret_version.qa-check-dev.secret_data
  }
}

data "google_kms_secret" "de-stcc-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU77aV3Trn/4bKDeWUTKZCe6wrt8KiIAtO/aNpUGZ9Ho0SNwA4/8/uIB/H5pRl1+DkefnmHxSHicnNeRDwqfTLdhnGnNjM68e3LRrSpFHgtUqjHzZpDexODXE="
}

resource "kubernetes_secret" "de-stcc-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "sales-transactions",
  ])

  metadata {
    name      = "de-stcc-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_sales_qa_etl"
    password = data.google_kms_secret.de-stcc-dev.plaintext
  }
}

data "google_kms_secret" "de-adjustment-factors-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7x4xryruYACgKQN3J3W5R0G7/tkCzRB520bdaGRnz6USNwA4/8/uoKRCag/d0iNlAdvTLf2iRHzgU2XXQewTBu0htG5mYrfrtlaVt0aLNpHfIw9PiVxhdug="
}

resource "kubernetes_secret" "de-adjustment-factors-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "adjustment-factors-etl",
  ])

  metadata {
    name      = "de-adjustment-factors-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_adjustment_factors_etl"
    password = data.google_kms_secret.de-adjustment-factors-dev.plaintext
  }
}

data "google_kms_secret" "de-equipment-valuations-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU73kRmWhRB65S4A6ZzViDoVooUyTXFlbW7tEl16z+O24SNwA4/8/uPfFhT5RMi7Q08TxfdU4s4yY0C2b+2JNxbheTpp9A3/TGRUbrsf7ve6lPtwj/tS/bM/Y="
}

resource "kubernetes_secret" "de-equipment-valuations-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-valuations-etl",
  ])

  metadata {
    name      = "de-equipment-valuations-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_equipment_valuations_etl"
    password = data.google_kms_secret.de-equipment-valuations-dev.plaintext
  }
}

data "google_kms_secret" "de-modelyear-range-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU71vAKF4fW0q6QYcAQW5HkhJvs307lKaREuA+Ri5Q+04SOAA4/8/ugHvRU6M8CVe1+mtfXugN2MCVcXBlpKd4QTDLNlt2LIAfnFom6/mSeicgKWiH821rvaOx"
}

resource "kubernetes_secret" "de-modelyear-range-dev-wi" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "modelyear-range-etl",
  ])

  metadata {
    name      = "de-modelyear-range-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_modelyr_etl"
    password = data.google_kms_secret.de-modelyear-range-dev.plaintext
  }
}

data "google_kms_secret" "de-inspections-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7/8PXCk/KEXHLZHWwFk2XlGV6KIkwz87Zz3mqv3wXqISNwA4/8/uDNGVVq1Rpa2B3QAhhrgBAv1GBBuXTQ26uDS5u4sE4bZhNDeLvqgBsc6nRef8uMvMsRI="
}

resource "kubernetes_secret" "de_inspections_dev_wi" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "inspections-etl",
  ])

  metadata {
    name      = "de-inspections-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_inspections_etl"
    password = data.google_kms_secret.de-inspections-dev.plaintext
  }
}

data "google_secret_manager_secret_version" "analytics_irental_api_credentials" {
  secret  = "irental-api-credentials"
  project = local.project
}

resource "kubernetes_secret" "analytics_irental_api_credentials" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "analytics-irental-wheeler"
  ])

  metadata {
    name      = "irental-api-credentials"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username      = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_irental_api_credentials.secret_data), "username")
    password      = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_irental_api_credentials.secret_data), "password")
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_irental_api_credentials.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_irental_api_credentials.secret_data), "client_secret")
    dealer_id     = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_irental_api_credentials.secret_data), "dealer_id")
  }
}

data "google_secret_manager_secret_version" "analytics_irentals_tom_api_permissions_dev" {
  secret  = "analytics_irentals_tom_api_permissions_dev"
  project = local.project
}

data "google_kms_secret" "irental_service_api_password" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7z2T01R6ZJVgwFmj+nrRDJXQ1PtDHshAjLJuMe45Re4SMgA4/8/u2NvU3pc3emfKPoiTN3kxxZi9hqyOauJw+7+dDCKfEiE9yaRTRbqtdul56Vie"
}
data "google_kms_secret" "irental_service_stb_client_secret" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU72Ij5+4hIYyduqOTfAizy3cTxRyvEEro/l7EHWQtLuASTAA4/8/uTqg8mB8bqQ3sqmtC0ktvrmNuhD4peb++t5AkyyoagtYQWIu9SMVG4+Ty5yzN6y/Dh3XzQXRNVLBc8VziqUkNLdW/uFgHT7o="
}
data "google_kms_secret" "irental_service_wha_client_secret" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU75VVz+9KF/77STtLz8YlSH7UDtMtFitejGkHzWPFlyMSTQA4/8/uwkAW9Qfck5uIoaIN7LG3XdJFdi5D/I2FZgvKxM19G1a3+asAYKznqOfFu+khUG5hDgkdZy81snO/tkn5dnzRMndA9VTKojOn"
}
data "google_kms_secret" "irental_service_ohc_client_secret" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU72OteW68Lw2OzdZ4fiQMlnK9e2glbqQ13n/I9w5v4pESTAA4/8/uHk5TRQjVKzHyOb7ayxMXuqGwA0lkATy0UOh3FK3FHSsaqKu+9Tsj5jdh9VTTXGDfMc1nxLlaIHGjQq4FTeDaTgKNE0x7xiI="
}

data "google_secret_manager_secret_version" "analytics_irentals_hoby_api_permissions_dev" {
  secret  = "analytics_irentals_hoby_api_permissions_dev"
  project = local.project
}

resource "kubernetes_secret" "irental-service-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "analytics-irental",
  ])

  metadata {
    name      = "irental-service-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    api_username       = "RouseApi"
    api_password       = data.google_kms_secret.irental_service_api_password.plaintext
    stb_client_secret  = data.google_kms_secret.irental_service_stb_client_secret.plaintext
    wha_client_secret  = data.google_kms_secret.irental_service_wha_client_secret.plaintext
    ohc_client_secret  = data.google_kms_secret.irental_service_ohc_client_secret.plaintext
    tom_client_secret  = data.google_secret_manager_secret_version.analytics_irentals_tom_api_permissions_dev.secret_data
    hoby_client_secret = data.google_secret_manager_secret_version.analytics_irentals_hoby_api_permissions_dev.secret_data
  }
}

data "google_kms_secret" "email-grabber-secret" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "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"
}

resource "kubernetes_secret" "email-grabber-secret-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "analytics-email-grabber",
  ])

  metadata {
    name      = "email-grabber-secret-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    "secrets.json" : data.google_kms_secret.email-grabber-secret.plaintext
  }
}

data "google_kms_secret" "de-market-segments-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7xcFeWNLDzSya/icg84K7lj3qu0RK7qMCMCC1u+WkP8SNwA4/8/uKdZ8e7T+OnWXVmYghhDS8fKuDpNtPLeJM1jB2L/IkJlBa4XqHiJt/PcpNf4h/DgN6Kk="
}

resource "kubernetes_secret" "de-market-segments-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "market-segments-etl",
  ])

  metadata {
    name      = "de-market-segments-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_market_segments_etl"
    password = data.google_kms_secret.de-market-segments-dev.plaintext
  }
}

data "google_kms_secret" "de-client-projects-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7xmeB/1LfNjzTvjpUKrb5/KuTqxHNtBrwqDHm2Z1B84SNwA4/8/uGq5z/xERzrsheRtVpibygqRXSlgYFwM8D31oSjxrS8VXtnkTnn89owxIGbv7LO+h7B4="
}

resource "kubernetes_secret" "de-client-projects-dev-wi" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "de-client-projects-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  for_each = toset([
    "client-projects-monitor",
  ])

  data = {
    username = "svc_client_prj_etl"
    password = data.google_kms_secret.de-client-projects-dev.plaintext
  }
}

data "google_kms_secret" "de-wholesale-factors-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU74onzo8JAcuMyNE6tzYRF5JG/PInTH3YNX2WUhF6jdASNwA4/8/u8XCKkofddzzcY9L9qUG7wEmrEgrg6Ua4MzTRbJdUn1ZepiIpGmMRxI88e7QPE1S6lU4="
}

resource "kubernetes_secret" "de-wholesale-factors-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "wholesale-values-etl",
  ])

  metadata {
    name      = "de-wholesale-factors-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_wholesale_factors_etl"
    password = data.google_kms_secret.de-wholesale-factors-dev.plaintext
  }
}

data "google_kms_secret" "qa-portal-prod-password" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU737sAkJs2zU8YYyUGP2Qs4pFplgsQhXKJbaA7NvKlSkSOwA4/8/u4xnt9nk0HJr4ruZt9QIv93szMWN5/+z+2gXZjWqFIaY8aZ5wEZbvHVbw1ekh8AS0kgQopF8j"
}

data "google_kms_secret" "qa-portal-dev-password" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU71zmZEYLhwnBHFtvDQz0s2fO/ZiuccXomjzv4OKHT2oSOAA4/8/uH82g5AObND4BxgvCStZQQs54EeGE8HskszlYYvgP0Lh0M5SG7+bqTSBqhS0XrHddhSdd"
}

resource "kubernetes_secret" "qa-portal-dev-password" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "qa-portal-dev-password"
    namespace = kubernetes_namespace.workload_identity["api-testing"].metadata[0].name
  }

  data = {
    password = data.google_kms_secret.qa-portal-dev-password.plaintext
  }
}

data "google_kms_secret" "qa-portal-auth0-client-id-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU718hL6xzkkFM2ac7yFYW17L7zA6jeSnFA7fOhmsLsWsSTQA4/8/uEcx78zQRZi1te+2x8cc00EsIf9umDIE5F1jOchYc+7VbIBDIE08yGiRF7hj0Rdj3VZoINZAC2DFF4Tis2RtOkUTnBzKmYC1F"
}

data "google_kms_secret" "qa-portal-auth0-client-secret-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7/+n8U1yX8H76FF/Xy28vsTk4pKsLnWmii7ZXhc3iEcSTQA4/8/ukOSl4ghuZi0Txm7jqJgRrsHPC8vG8uCsoiFlaAjc6emDsP23mULt0nKMmKlTJ2i1K/6oj0D+J1XGAhVNtDsOLKlRR+sKS3u5"
}

resource "kubernetes_secret" "qa-portal-auth0-keys-dev" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "qa-portal-auth0-keys-dev"
    namespace = kubernetes_namespace.workload_identity["api-testing"].metadata[0].name
  }

  data = {
    client_id     = data.google_kms_secret.qa-portal-auth0-client-id-dev.plaintext
    client_secret = data.google_kms_secret.qa-portal-auth0-client-secret-dev.plaintext
  }
}

data "google_kms_secret" "qa-portal-auth0-client-id-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7y1vCCRAtdQAzO3aQOTt6QwXrswNuI3C6hTmyYsN9NYSTQA4/8/uwD5qshEM9gpM0gRs0F2O7U7pI9mLfrN047OQ3W0Ln/xwwSAHyQySv+6lYNJWk/eiB/Pht3n42FBA/97pJQAMhrN1aozTz4eP"
}

data "google_kms_secret" "qa-portal-auth0-client-secret-prod" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7+g5EjaTZqF1M2HgS8ffQaLEQ4QEhX7c0eRsReJFENMSTQA4/8/u7lpT3VVGSv09Zked8IJqB6xfvqOGr6KL/JMuRztRWBqF0k0LFfUVyufaedaJcgnUBBqq5f6ckeWsfIVG7+8Xp9tvK/5dafvj"
}

data "google_kms_secret" "github-username-secrets" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7xsABCxL6mZ3TxtekaYqHoCnqMTy+dsSnxBUvZMitqISNAA4/8/upwBIxI1Q7o/Pwcl1MA2J2lbpTOunawhGFPePMEbXwIvW6e/8Ba4ozZ6DtRS+enU="
}

data "google_kms_secret" "github-token-secrets" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7wuQajvUBbY2PLMhCguntE+gVncLYwWHwpUiDRsbyfwSUAA4/8/uoBfIPRtLDmlOGldYWTxJVkSJlFjh5uygY7cc7aZb0Wn+PYgYl5Ngblx28iRlY90rTGdRgFyp1jP1TfzePCGsSrBjjtD+pkTQcw/0"
}

resource "kubernetes_secret" "github-secrets-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "backup-github-repositories",
    "equipment-valuations-etl",
    "pgbouncer-cloudsql-ip-update",
  ])

  metadata {
    name      = "github-secrets"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = data.google_kms_secret.github-username-secrets.plaintext
    token    = data.google_kms_secret.github-token-secrets.plaintext
  }
}

data "google_kms_secret" "sql-salesglobal01-user-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7+/ox+JpqvEBRGHsG8Of8IV784csoYj3qDAAIJAIkSwSPQA4/8/uUHYTzaN3g6fG6MY3Go599KAcKUeLb5xSsUgvlTjQnIPvcJ+WQQFAQPpT5URl+3qc4efPnulJjIM="
}

resource "kubernetes_secret" "sql-salesglobal01-user-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "db-coda-dep-auction-data",
  ])

  metadata {
    name      = "sql-salesglobal01-user-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "sqlautomation"
    password = data.google_kms_secret.sql-salesglobal01-user-dev.plaintext
  }
}


data "google_secret_manager_secret_version" "qa-slack-url" {
  secret  = "qa-slack-url-${var.environment}"
  project = local.qa_tools
}

resource "kubernetes_secret" "qa-slack-url" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "qa-slack-url-${var.environment}"
    namespace = kubernetes_namespace.workload_identity["api-testing"].metadata[0].name
  }

  data = {
    url = data.google_secret_manager_secret_version.qa-slack-url.secret_data
  }
}

data "google_kms_secret" "de-meter-adjustments-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7yyssyJy76DNQGCzi2ma4kQoXJSJwOTOG7hG8gwdxbsSNwA4/8/uQkOFWA+s8Sq37BpB7EySmGjWee1RhH3Zjex/UAuzY4yG2iD+Vv2mS7H86HGY4CnHoXA="
}

resource "kubernetes_secret" "de-meter-adjustments-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "meter-adjustments-etl",
  ])

  metadata {
    name      = "de-meter-adjustments-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_meter_adjustments_etl"
    password = data.google_kms_secret.de-meter-adjustments-dev.plaintext
  }
}

data "google_kms_secret" "de-record360-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7+oewvvqkJ2kRdfcfdEvIByAlL+Ues8QaK30Zk3FSLsSOQA4/8/uX29+llh5/A8EdAYy9YV9mu2AL7KKCUb50fqN3nCXoTShbZKF19MgePudLH/z3i4//ZPP7Q=="
}

resource "kubernetes_secret" "de-record360-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "inspections-etl",
  ])

  metadata {
    name      = "de-record360-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_kms_secret.de-record360-dev.plaintext
  }
}

resource "kubernetes_secret" "appraisals-dev-root" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "inspections-etl",
  ])

  metadata {
    name      = "appraisals-dev-root"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_kms_secret.de-record360-dev.plaintext
  }
}

data "google_kms_secret" "rfm01-root-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7/S9CPpF4atMQ9Vj8P9m9TXIMnMxdyOBhA/jgLiJyuESOQA4/8/ur2js+uLME3v/0fvaq6cnP2K/wVQANU2HZ9I4hx7wDwVt+wYtrxVDBOmsbnRkfl4cmpSisA=="
}

resource "kubernetes_secret" "rfm01-root-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "postgresql-create-db-users",
    "pglogical-monitoring-rfm101"
  ])

  metadata {
    name      = "rfm01-root"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_kms_secret.rfm01-root-dev.plaintext
  }
}

data "google_kms_secret" "de-exchange-rates" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU717HjNnYDKvk0+ZlfxRaPtA8JFmaiyZ2HviDwmga3IkSNwA4/8/uaZrFhJR3Kg9yZPkDx8/TrBlep9+LSUuNQmJfS4uj4WR+U7TOasEyoqtEXti+ekinMfQ="
}

resource "kubernetes_secret" "de-exchange-rates" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "exchange-rates-etl",
  ])

  metadata {
    name      = "de-exchange-rates-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_exchange_rates_etl"
    password = data.google_kms_secret.de-exchange-rates.plaintext
  }
}

data "google_kms_secret" "de-eqp-values-residuals" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU72J6DppdpFf6a9apwJmyiR9OGsGaZcNtDOqZzFy85FcSNwA4/8/uJbUZ5OAIk/KuNXH0j0TgpCCm3ZpDbvSaApyyLTJ1jis1iy7GONKVs16Ydri6MHAUB+8="
}

resource "kubernetes_secret" "de-eqp-values-residuals" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-values-residuals-etl",
  ])

  metadata {
    name      = "de-eqp-values-residuals-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_eqp_values_residuals_etl"
    password = data.google_kms_secret.de-eqp-values-residuals.plaintext
  }
}

data "google_kms_secret" "salesglobal02-sqlautomation-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU76fYQ/DMPVFuY5lcgGFuEe+GntrwRxbKWoaVTfZeNucSPQA4/8/uDhEZH9PQzGITkyB3jLfPOWmM1g9QO5W/hKsILYhIm0akRGsXX4x1ptF9kfulalTKrZ03VW4jqEk="
}

resource "kubernetes_secret" "salesglobal02-sqlautomation-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "postgresql-create-db-users",
  ])

  metadata {
    name      = "salesglobal02-sqlautomation"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "sqlautomation"
    password = data.google_kms_secret.salesglobal02-sqlautomation-dev.plaintext
  }
}
data "google_kms_secret" "rdo04-root-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU763Hx6Rq3eh13sex5t/o/NWzJqw5/epKjSV5veNfSWESPQA4/8/uXuDJ/dRNw0iQJO/fiB8nn7ERu4bUiRULukaLcvGJKUOqP0oqv6Nf2xCEZBCtDk+mhhozYLY3hZc="
}

resource "kubernetes_secret" "rdo04-root-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "postgresql-create-db-users",
  ])

  metadata {
    name      = "rdo04-root"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_kms_secret.rdo04-root-dev.plaintext
  }
}

resource "kubernetes_secret" "platform01-root-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "postgresql-create-db-users",
  ])

  metadata {
    name      = "platform01-root"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.de_pg_platform_dbadmin.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.de_pg_platform_dbadmin.secret_data), "password")
  }
}

data "google_kms_secret" "vnext-service-account-password" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU79UfT1gDMmXcrIp7LFnH0tdyW1Morq+UB7rK4FpCW8ISPQA4/8/uvXtLnZnnEydtGRdcs0Bj9nTM4uPG9ocVjs34pOuAJRnq1775pO7Bb0rbKoRyAN4dGaMzUk0hsLE="
}

data "google_kms_secret" "vnext-service-account-token" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7xEH72ukjcvJHClW9qZIgEbzT6XbZmCG59os+mhFjPcSaQBp1KhiOLJr0imayWE3dknr/L7dCAhMu3CYGoBzFIJMAM7MCdd6cunyftyV8x4F61nSgHKZdeffdzud1bjw5lrj6eOWMWn0qyDp7pAM0VlQeGeA92Y3B2tB15gFf/D+4lEk8JJRU1t3AQ=="
}

resource "kubernetes_secret" "vnext-service-account-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "crane-network",
    "mascus-csv-listings-etl",
    "ritchie-bros-xml-auction-etl",
    "rb-competitive-intelligence-etl",
  ])

  metadata {
    name      = "vnext-service-account"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "<EMAIL>"
    password = data.google_kms_secret.vnext-service-account-password.plaintext
    token    = data.google_kms_secret.vnext-service-account-token.plaintext
  }
}

data "google_kms_secret" "de-prev-exch-rates" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU74BjO+H1xm0aXwb/aJtHFnVwMoLvVbPZdaQScwY+VcESNwA4/8/uGygHOtBJQqkh6+5832zzPSK30jMHVittXVBhJX2pZBjseLzDegYjlq3RU1AcuzPXK6M="
}

resource "kubernetes_secret" "de-prev-exch-rates" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "sales-prevailing-exchange-rates-etl",
  ])

  metadata {
    name      = "de-prev-exch-rates-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_prev_exch_rate_etl"
    password = data.google_kms_secret.de-prev-exch-rates.plaintext
  }
}

data "google_kms_secret" "de-manage-equipment" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU71ptMWr5CK+XJ3DNICbB0qjH6xOkYOUPrCXgOjynY/8SNwA4/8/uFxKXK+mSYdUhHf06r0BppmXLO805WBVf2BN4W7swpd61Vbhf3981uA84XpUp+IVdVyM="
}

resource "kubernetes_secret" "de-manage-equipment" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "manage-equipment-etl",
    "equipment-sync-monitor",
  ])

  metadata {
    name      = "de-manage-equipment-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_equipment_etl"
    password = data.google_kms_secret.de-manage-equipment.plaintext
  }
}

data "google_secret_manager_secret_version" "mssql_equipment_options_etl" {
  secret  = "mssql_equipment_options_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-equipment-options-etl-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-equipment-options-etl-dev"
    namespace = kubernetes_namespace.workload_identity["equipment-options-etl"].metadata[0].name
  }

  data = {
    username = "svc_equipment_options"
    password = data.google_secret_manager_secret_version.mssql_equipment_options_etl.secret_data
  }
}


data "google_kms_secret" "de-pg-manage-equipment" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU79P7+YEmwQuhO9HrnbeGdC9jGucy9T8O42zCa8w9nygSPQA4/8/uriEtQ/NzgfxQnsPOj6Zj94EBKQ7bYGel/zI3Z8mFmGKZogxdOf5Mc7igkZFasmCfUCbk12p7qXs="
}

resource "kubernetes_secret" "de-pg-manage-equipment" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "manage-equipment-etl",
    "equipment-sync-monitor",
  ])

  metadata {
    name      = "de-pg-manage-equipment-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "equipment_etl"
    password = data.google_kms_secret.de-pg-manage-equipment.plaintext
  }
}


data "google_kms_secret" "de-equipment-photos" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU74orwCS1InWUs4hR8yZZ828YCV/iJ6fWCTFxOYpDSs0SNwA4/8/uiEqtTH6/IN2pvkIoWLZV+UwNZ2DX8vNRqSMLZ7LQPBbu836BFJzVXdoFowaqOxC6KrA="
}

resource "kubernetes_secret" "de-equipment-photos" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-photos-etl",
    "equipment-photos-sync-monitor",
  ])

  metadata {
    name      = "de-equipment-photos-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_equipment_photos_etl"
    password = data.google_kms_secret.de-equipment-photos.plaintext
  }
}


data "google_kms_secret" "de-pg-equipment-photos" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7523RVIQ5Or2N+yxjWwl59+ETKZV3Jw62qGmdX9ywOESPQA4/8/u04Yu+KtrjJSUefzAHhF7Ef8ZbLJk+PAlpX/7NIDxIiEh63LjAjjt8cPcrMESn1j2KvmOgZGVaU8="
}

resource "kubernetes_secret" "de-pg-equipment-photos" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-photos-etl",
    "equipment-photos-sync-monitor",
  ])

  metadata {
    name      = "de-pg-equipment-photos-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "equipment_photos_etl"
    password = data.google_kms_secret.de-pg-equipment-photos.plaintext
  }
}


data "google_kms_secret" "de-equipment-catalog-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7/i4RFXvwS/JainA8zZtFWiFUa3cHhd7cZYkHIfmfxcSNwA4/8/u7DG0SS6BJ3iWqWFz23GpS8182ltVjFIBmKvfeszjFpQBR2Dr2E7Nt8nkuB0Q1sAEOEQ="
}

resource "kubernetes_secret" "de-equipment-catalog-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-catalog-etl",
    "equipment-configuration-variants-etl",
  ])

  metadata {
    name      = "de-equipment-catalog-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_equipment_catalog_etl"
    password = data.google_kms_secret.de-equipment-catalog-dev.plaintext
  }
}


data "google_kms_secret" "de-pg-equipment-catalog-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7+ey6nJAC4/YR/dpUhk57hoCguSTXcDGqSYgQF6H3fYSPQA4/8/uZxoL10vgHMuVxAgONxuh8YFPtHkjrhBBetTt5+Ec4W1OvFN9i6kFjhERvR5zes5GDwjzXMOhhWQ="
}

resource "kubernetes_secret" "de-pg-equipment-catalog-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-catalog-etl",
    "classification-photos",
  ])

  metadata {
    name      = "de-pg-equipment-catalog-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "equipment_catalog_etl"
    password = data.google_kms_secret.de-pg-equipment-catalog-dev.plaintext
  }
}


data "google_kms_secret" "de-clients-excluded-from-reports-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU766QZ8e+JwTG+a+CU/bGbdRfONbiyaCxQRALkDJJLgQSNwA4/8/u42tJXmD8cPBHzSahSZHSSTyWQTx2leMkKx4cd0osgrpffIouIeS2RJBc4BIfyr6g6Eg="
}

resource "kubernetes_secret" "de-clients-excluded-from-reports-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "client-etl",
  ])

  metadata {
    name      = "de-clients-excluded-from-reports-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_clients_excluded_from_reports_etl"
    password = data.google_kms_secret.de-clients-excluded-from-reports-dev.plaintext
  }
}


data "google_kms_secret" "de-schedules-import-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7zpPVbhJylIyENU7ICezd3qEGMXWEKvacB2b981xn/4SNwA4/8/uQJeczfXfM+ptw+G5W49OTQSRUCGQlKp9oHSMoMjFYIJF1uKO+0w58S1VMk1SyDuXU9E="
}

resource "kubernetes_secret" "de-schedules-import-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "schedules-import-etl",
  ])

  metadata {
    name      = "de-schedules-import-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_schedule_import_etl"
    password = data.google_kms_secret.de-schedules-import-dev.plaintext
  }
}


data "google_kms_secret" "de-eqp-values-history-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU73TWKSWa21z/DMSzxFS0iGnNFbTXxkPqU4tNOrQIUaISNwA4/8/uwDqA9Q/kjetH5SEkSAFhtJgTH010BQIBu0dDi0pzVETIbnbIN/KS9BEwXCAn5sN6wFw="
}

resource "kubernetes_secret" "de-eqp-values-history-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-values-history-etl",
  ])

  metadata {
    name      = "de-eqp-values-history-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_equipment_values_history_etl"
    password = data.google_kms_secret.de-eqp-values-history-dev.plaintext
  }
}


data "google_kms_secret" "de-pg-eqp-values-history-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU76yw5lGfmrwue9+/rlJeESEKzcpglkB7FssrRfCwjfkSPQA4/8/uztg+NYubchFb+hnMqcZwT8kruWv/OJv3EQzs6FzrWcTywaziE5FUpZlahVRhbZEuS/9TEMt6UgE="
}

resource "kubernetes_secret" "de-pg-eqp-values-history-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-values-history-etl",
  ])

  metadata {
    name      = "de-pg-eqp-values-history-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "equipment_values_history_etl"
    password = data.google_kms_secret.de-pg-eqp-values-history-dev.plaintext
  }
}

data "google_kms_secret" "valuations01-root" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7xb8JuA9FJaBb4iJT6qnbCb2/QzT1o3YHKi7kjZ/68kSPQA4/8/ubhvnrovWXQ8vtpEICuCXl4T28ZEKrqTiA54VcYetBykGuv2HrQ2kR7m55kuxnNA5VpeE8WQsuKQ="
}

resource "kubernetes_secret" "valuations01-root-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "postgresql-create-db-users",
  ])

  metadata {
    name      = "valuations01-root"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_kms_secret.valuations01-root.plaintext
  }
}

data "google_kms_secret" "de-pg-sales-txns-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU741K9UxBa4TRTunOnDetsH6xiQr6uyVnyXXMeqxaXGcSPQA4/8/u9xu5t42x8LZnUbxfG425POI0AJQjXwDxwSTO2J6qWe85VzHZVzpcltz840IM0XVITLi/A1iRgwg="
}

resource "kubernetes_secret" "de-pg-sales-txns-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "sales-transactions",
  ])

  metadata {
    name      = "de-pg-sales-txns-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "sales_txns_etl"
    password = data.google_kms_secret.de-pg-sales-txns-dev.plaintext
  }
}

data "google_kms_secret" "de-pg-mkt-metrics-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7yN76iL90ClTRWcCrxtXGhNXo90KgZyc2286JMV0604SPQA4/8/ulX8TRtD1qAAWn3rOW/CRPkwflyQM+C2kRaVUfC0gY519kAbZm/OX7tLnQ441JfK8+q6wwEYCuMQ="
}

resource "kubernetes_secret" "de-pg-mkt-metrics-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "marketing-metrics",
  ])

  metadata {
    name      = "de-pg-mkt-metrics-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "mkt_metrics_etl"
    password = data.google_kms_secret.de-pg-mkt-metrics-dev.plaintext
  }
}

data "google_kms_secret" "de-application-usage-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU70BACCqbHo+kNoCNBia56lVOKLVCXklQIdkmPs7cHDwSNwA4/8/uYxiFtyjPdzJnD197eReCY89aWOg1dWbyNaYKwJPwR407NeQrVu/X98GIf00Kzyci3vA="
}

resource "kubernetes_secret" "de-application-usage-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "application-usage-etl",
  ])

  metadata {
    name      = "de-application-usage-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_application_usage_etl"
    password = data.google_kms_secret.de-application-usage-dev.plaintext
  }
}

data "google_secret_manager_secret_version" "de_rdo_alpha_gfdev01_account" {
  secret  = "de_rdo_alpha_gfdev01_account"
  project = local.services
}

resource "kubernetes_secret" "de_rdo_alpha_gfdev01_account" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-rdo-alpha-gfdev01-account"
    namespace = kubernetes_namespace.workload_identity["rental-metrics-aggs"].metadata[0].name
  }

  data = {
    username = "sa"
    password = data.google_secret_manager_secret_version.de_rdo_alpha_gfdev01_account.secret_data
  }
}

data "google_secret_manager_secret_version" "de_rdo_beta_gfdev02_account" {
  secret  = "de_rdo_beta_gfdev02_account"
  project = local.services
}

resource "kubernetes_secret" "de_rdo_beta_gfdev02_account" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-rdo-beta-gfdev02-account"
    namespace = kubernetes_namespace.workload_identity["rental-metrics-aggs"].metadata[0].name
  }

  data = {
    username = "sa"
    password = data.google_secret_manager_secret_version.de_rdo_beta_gfdev02_account.secret_data
  }
}

data "google_secret_manager_secret_version" "de_rdo_stage_giqadb02s_account" {
  secret  = "de_rdo_stage_giqadb02s_account"
  project = local.services
}

resource "kubernetes_secret" "de_rdo_stage_giqadb02s_account" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-rdo-stage-giqadb02s-account"
    namespace = kubernetes_namespace.workload_identity["rental-metrics-aggs"].metadata[0].name
  }

  data = {
    username = "sa"
    password = data.google_secret_manager_secret_version.de_rdo_stage_giqadb02s_account.secret_data
  }
}

data "google_kms_secret" "de-cg-benchmark-transactional-detail-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7yQzvQLtSs4Zv5DdAXsTlzYIrbxvZ/pCXpzEsOcJc+oSNwA4/8/uGMiW4nrShHaYBJV/LatMqzR29QwGApb6gjyg7ehwKEOwyjjXqGcfxU9TFI//86L917Y="
}

resource "kubernetes_secret" "de-cg-benchmark-transactional-detail-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "custom-grids-benchmark-transactional-detail-etl",
    "rental-metrics-aggs",
    "rdo-translation",
  ])

  metadata {
    name      = "de-cg-benchmark-transactional-detail-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_cg_sales_rep_book_var_etl"
    password = data.google_kms_secret.de-cg-benchmark-transactional-detail-dev.plaintext
  }
}

data "google_kms_secret" "de-pg-cg-benchmark-transactional-detail-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7xjZzhTY/oU/g0kgqplOI39olCdQSYGAUd7y5An6mZUSPQBp1KhicNzyTriByWMU5Pbrksle17WGu33USTYYnKTB641hIww8aSrSSaHu1JRUt9a7OjUoIY7GROkU1IA="
}

resource "kubernetes_secret" "de-pg-cg-benchmark-transactional-detail-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "custom-grids-benchmark-transactional-detail-etl",
    "rental-metrics-aggs",
  ])

  metadata {
    name      = "de-pg-cg-benchmark-transactional-detail-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "custom_grids_sales_rep_book_variance_etl"
    password = data.google_kms_secret.de-pg-cg-benchmark-transactional-detail-dev.plaintext
  }
}

data "google_secret_manager_secret_version" "cg_alloydb_test_user" {
  secret  = "cg_alloydb_test_user"
  project = local.project
}

resource "kubernetes_secret" "cg_alloydb_test_user" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "cg-alloydb-test-user"
    namespace = kubernetes_namespace.workload_identity["custom-grids-benchmark-transactional-detail-etl"].metadata[0].name
  }

  data = {
    username = "postgres"
    password = data.google_secret_manager_secret_version.cg_alloydb_test_user.secret_data
  }
}

data "google_secret_manager_secret_version" "analytics_rdo_scheduler_auth0_config" {
  secret  = "analytics_rdo_scheduler_auth0_config"
  project = local.services
}

resource "kubernetes_secret" "analytics_rdo_scheduler_auth0_config" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "custom-grids-benchmark-transactional-detail-etl",
    "analytics-ip-whitelist",
    "rental-metrics-aggs",
  ])

  metadata {
    name      = "analytics-rdo-scheduler-auth0-config"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_scheduler_auth0_config.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_rdo_scheduler_auth0_config.secret_data), "client_secret")
  }
}

data "google_kms_secret" "de-cg-fleet-data-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7yCJi7yf4zw7zMS/mdZVU6IlXkG9nwZmulBNGMeDFgQSNwA4/8/uQP3VBrfpzL+LnF7Z+EZyCRPP+jLAGh+T9Z3C4JLgCvxfqexUTTWXs0vwzGBBjjDiK7Q="
}

resource "kubernetes_secret" "de-cg-fleet-data-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "custom-grids-fleet-data-etl",
  ])

  metadata {
    name      = "de-cg-fleet-data-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_cg_fleet_data_etl"
    password = data.google_kms_secret.de-cg-fleet-data-dev.plaintext
  }
}

data "google_kms_secret" "de-pg-cg-fleet-data-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU78vhrXvcTaXjCUMoWAfcSNhgGJ5jUrxsV7mwXk8AKXcSPQA4/8/uyOm+3T/M5Q64/d40g0CJLOYker2V/mjWKLWxK4F9LXVkP0GSkGVwq8/Y/q4MzFOLbiHr6BmL3k8="
}

resource "kubernetes_secret" "de-pg-cg-fleet-data-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "custom-grids-fleet-data-etl",
  ])

  metadata {
    name      = "de-pg-cg-fleet-data-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "custom_grids_fleet_data_etl"
    password = data.google_kms_secret.de-pg-cg-fleet-data-dev.plaintext
  }
}

data "google_secret_manager_secret_version" "dbuser_platform" {
  for_each = toset([
    "fleet_manager_sales_subcategory",
    "model_year_range_etl",
    "equipment_details_etl",
  ])
  secret  = "dbuser_platform_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_platform" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    fleet_manager_sales_subcategory = {
      namespace   = "fleet-manager-sales-subcategory",
      secret_name = "de-pg-fleet-manager-sales-subcategory-${var.environment}"
    }
    model_year_range_etl = {
      namespace   = "modelyear-range-etl",
      secret_name = "de-pg-model-year-range-${var.environment}"
    }
    equipment_details_etl = {
      namespace   = "equipment-details-etl",
      secret_name = "de-pg-equipment-details-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    username = each.key
    password = data.google_secret_manager_secret_version.dbuser_platform[each.key].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_rfm" {
  for_each = toset([
    "appraisal_valuation_canary_etl",
    "auctions_etl",
    "create_empty_lookup_values_etl",
    "equipment_configuration_variants",
    "equipment_sync_monitor",
    "transaction_volume_etl",
    "transaction_volume_auction_etl",
    "value_trends_etl",
    "equipment_values_residuals_etl",
    "equipment_valuations_etl",
    "equipment_view_publisher",
    "fleet_manager_consolidated_data",
    "fleet_manager_expected_time_to_sell_etl",
    "fleet_manager_fleet_customers_monitoring",
    "fleet_manager_fleet_listings",
    "fleet_manager_fleet_metrics",
    "client_asset_etl",
    "client_etl",
    "countries_etl",
    "csmm_relevancy_etl",
    "copy_classification_photos_etl",
    "equipment_catalog_api",
    "equipment_classification_etl",
    "rouse_rb_taxonomy_mapping_etl",
    "fleet_manager_customers_metrics_reporting",
    "fleet_manager_partition_management_pipeline",
    "fleet_manager_pg_export",
    "fleet_manager_pg_export_multi_tenant",
    "fleet_manager_publish_insights_taxonomy_and_localization_etl",
    "fleet_manager_backfill",
    "fleet_manager_supercategory_etl",
    "fleet_manager_sync",
    "fleet_manager_users_etl",
    "fleet_manager_valuations_history_etl",
    "fleet_manager_cat_product_group",
    "equipment_syndication_etl",
    "equipment_notes_history_etl",
    "sales_erp_sync",
    "sales_google_analytics_page_views",
    "sales_invoice_emails",
    "sales_transactions_etl",
    "proposal_history_etl",
    "change_history_etl",
    "user_profiles",
    "equipment_files_etl",
    "web_leads_etl",
    "legacy_provisioning_etl",
    "marketable_life_etl",
    "migrations_etl",
    "mpe_kpi_metrics_etl",
    "mpe_transactions_etl",
    "rfm_archiving_etl",
    "restore_etl",
    "health_check",
    "fleet_manager_multi_checks",
    "rental_insights_etl",
    "mixer_export_etl",
    "mixer_values_lookup_configuration_sync",
    "rb_list_views_etl",
    "rb_list_web_leads_etl",
    "rb_taxonomy_etl",
    "sales_rfm_user_config_reporting",
    "selling_channel_reporting_etl",
    "smartequip_parts_book_etl",
    "ss_export",
    "user_config_rule_change_log_reporting",
    "united_xvalues_export_etl",
  ])
  secret  = "dbuser_rfm_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_rfm" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    appraisal_valuation_canary_etl = {
      namespace   = "appraisal-valuation-canary-etl",
      secret_name = "de-pg-appraisal-valuation-canary-${var.environment}"
    }
    auctions_etl = {
      namespace   = "auctions-etl",
      secret_name = "de-pg-auctions-${var.environment}"
    }
    create_empty_lookup_values_etl = {
      namespace   = "create-empty-lookup-values-tables",
      secret_name = "de-pg-create-empty-lookup-values-${var.environment}"
    }
    equipment_configuration_variants = {
      namespace   = "equipment-configuration-variants-etl",
      secret_name = "de-pg-configruation-variants-${var.environment}"
    }
    equipment_sync_monitor = {
      namespace   = "equipment-sync-monitor",
      secret_name = "de-pg-equipment-sync-${var.environment}"
    }
    transaction_volume_etl = {
      namespace   = "transaction-volume-retail-etl",
      secret_name = "de-pg-transaction-volume-${var.environment}"
    }
    transaction_volume_auction_etl = {
      namespace   = "transaction-volume-auction-etl",
      secret_name = "de-pg-transaction-volume-auction-${var.environment}"
    }
    value_trends_etl = {
      namespace   = "value-trends-etl",
      secret_name = "de-pg-value-trends-${var.environment}"
    }
    equipment_values_residuals_etl = {
      namespace   = "equipment-values-residuals-etl",
      secret_name = "de-pg-values-residuals-${var.environment}"
    }
    equipment_valuations_etl = {
      namespace   = "equipment-valuations-etl",
      secret_name = "de-pg-equipment-valuations-${var.environment}"
    }
    equipment_view_publisher = {
      namespace   = "equipment-view-publisher",
      secret_name = "de-pg-equipment-view-publisher-${var.environment}"
    }
    fleet_manager_consolidated_data = {
      namespace   = "fleet-manager-consolidated-data",
      secret_name = "de-pg-fleet-manager-consolidated-data-${var.environment}"
    }
    fleet_manager_expected_time_to_sell_etl = {
      namespace   = "fleet-manager-expected-time-to-sell-etl",
      secret_name = "de-pg-fleet-manager-expected-time-to-sell-etl-${var.environment}"
    }
    fleet_manager_fleet_listings = {
      namespace   = "fleet-manager-fleet-listings",
      secret_name = "de-pg-fleet-manager-fleet-listings-${var.environment}"
    }

    fleet_manager_fleet_customers_monitoring = {
      namespace   = "fleet-manager-fleet-customers-monitoring",
      secret_name = "de-pg-fleet-manager-fleet-customers-monitoring-${var.environment}"
    }
    fleet_manager_fleet_metrics = {
      namespace   = "fleet-manager-fleet-metrics",
      secret_name = "de-pg-fleet-manager-fleet-metrics-${var.environment}"
    }
    client_etl = {
      namespace   = "client-etl",
      secret_name = "de-pg-client-${var.environment}"
    }
    countries_etl = {
      namespace   = "countries-etl",
      secret_name = "de-pg-countries-etl-${var.environment}"
    }
    copy_classification_photos_etl = {
      namespace   = "classification-photos",
      secret_name = "de-pg-copy-classification-photos-${var.environment}"
    }
    equipment_catalog_api = {
      namespace   = "generate-site-map-etl"
      secret_name = "de-pg-generate-site-map-etl-${var.environment}"
    }
    equipment_classification_etl = {
      namespace   = "equipment-classification-etl",
      secret_name = "de-pg-equipment-classification-${var.environment}"
    }
    rouse_rb_taxonomy_mapping_etl = {
      namespace   = "rouse-rb-taxonomy-mapping-etl",
      secret_name = "de-pg-rouse-rb-taxonomy-mapping-etl-${var.environment}"
    }
    fleet_manager_pg_export = {
      namespace   = "fleet-manager-pg-export",
      secret_name = "de-pg-fleet-manager-pg-export-${var.environment}"
    }
    fleet_manager_pg_export_multi_tenant = {
      namespace   = "fleet-manager-pg-export-multi-tenant",
      secret_name = "de-pg-fleet-manager-pg-export-multi-tenant-${var.environment}"
    }
    fleet_manager_publish_insights_taxonomy_and_localization_etl = {
      namespace   = "fleet-manager-publish-insights-taxonomy-and-localization-etl",
      secret_name = "de-pg-fleet-manager-publish-insights-taxonomy-and-localization-etl-${var.environment}"
    }
    fleet_manager_backfill = {
      namespace   = "fleet-manager-backfill",
      secret_name = "de-pg-fleet-manager-backfill-${var.environment}"
    }
    fleet_manager_supercategory_etl = {
      namespace   = "fleet-manager-supercategory-etl",
      secret_name = "de-pg-fleet-manager-supercategory-etl-${var.environment}"
    }
    fleet_manager_sync = {
      namespace   = "fleet-manager-sync",
      secret_name = "de-pg-fleet-manager-sync-${var.environment}"
    }
    fleet_manager_users_etl = {
      namespace   = "fleet-manager-users-etl",
      secret_name = "de-pg-fleet-manager-users-etl-${var.environment}"
    }
    fleet_manager_valuations_history_etl = {
      namespace   = "fleet-manager-valuations-history-etl",
      secret_name = "de-pg-fleet-manager-valuations-history-etl-${var.environment}"
    }
    fleet_manager_cat_product_group = {
      namespace   = "fleet-manager-cat-product-group",
      secret_name = "de-pg-fleet-manager-cat-product-group-${var.environment}"
    }
    fleet_manager_customers_metrics_reporting = {
      namespace   = "fleet-manager-customers-metrics-reporting",
      secret_name = "de-pg-fleet-manager-customers-metrics-reporting-${var.environment}"
    }
    equipment_syndication_etl = {
      namespace   = "equipment-syndication-etl",
      secret_name = "de-pg-equipment-syndication-etl-${var.environment}"
    }
    equipment_notes_history_etl = {
      namespace   = "equipment-notes-history-etl",
      secret_name = "de-pg-equipment-notes-history-etl-${var.environment}"
    }
    sales_erp_sync = {
      namespace   = "sales-erp-sync",
      secret_name = "de-pg-sales-erp-sync-${var.environment}"
    }
    sales_google_analytics_page_views = {
      namespace   = "sales-google-analytics-page-views",
      secret_name = "de-pg-sales-google-analytics-page-views-${var.environment}"
    }
    sales_invoice_emails = {
      namespace   = "sales-invoice-emails",
      secret_name = "de-pg-sales-invoice-emails-${var.environment}"
    }
    sales_transactions_etl = {
      namespace   = "sales-transactions",
      secret_name = "de-pg-sales-transactions-etl-${var.environment}"
    }
    ss_export = {
      namespace   = "ss-export",
      secret_name = "de-pg-ss-export-${var.environment}"
    }
    proposal_history_etl = {
      namespace   = "proposal-history-etl",
      secret_name = "de-pg-proposal-history-etl-${var.environment}"
    }
    change_history_etl = {
      namespace   = "change-history-etl",
      secret_name = "de-pg-change-history-etl-${var.environment}"
    }
    user_profiles = {
      namespace   = "fleet-manager-backfill",
      secret_name = "de-pg-user-profiles-backfill-${var.environment}",
    }
    equipment_files_etl = {
      namespace   = "equipment-files-etl",
      secret_name = "de-pg-equipment-files-${var.environment}"
    }
    web_leads_etl = {
      namespace   = "web-leads-etl",
      secret_name = "de-pg-web-leads-${var.environment}"
    }
    legacy_provisioning_etl = {
      namespace   = "legacy-provisioning-etl",
      secret_name = "de-pg-legacy-provisioning-${var.environment}"
    }
    marketable_life_etl = {
      namespace   = "marketable-life-etl",
      secret_name = "de-pg-marketable-life-${var.environment}"
    }
    migrations_etl = {
      namespace   = "migrations-etl",
      secret_name = "de-pg-migrations-${var.environment}"
    }
    mpe_kpi_metrics_etl = {
      namespace   = "mpe-kpi-metrics-etl",
      secret_name = "de-pg-mpe-kpi-metrics-${var.environment}"
    }
    mpe_transactions_etl = {
      namespace   = "mpe-transactions-etl",
      secret_name = "de-pg-mpe-transactions-etl-${var.environment}"
    }
    rfm_archiving_etl = {
      namespace   = "rfm-archiving-etl",
      secret_name = "de-pg-rfm-archiving-${var.environment}"
    }
    restore_etl = {
      namespace   = "restore-etl",
      secret_name = "de-pg-restore-${var.environment}"
    }
    health_check = {
      namespace   = "health-checks",
      secret_name = "de-pg-health-check-${var.environment}"
    }
    fleet_manager_multi_checks = {
      namespace   = "fleet-manager-multi-checks",
      secret_name = "de-pg-fleet-manager-multi-checks-${var.environment}"
    }
    rental_insights_etl = {
      namespace   = "rental-insights-etl",
      secret_name = "de-pg-rental-insights-etl-${var.environment}"
    }
    mixer_export_etl = {
      namespace   = "mixer-export-etl",
      secret_name = "de-pg-mixer-export-etl-${var.environment}"
    }
    mixer_values_lookup_configuration_sync = {
      namespace   = "mixer-values-lookup-configuration-sync",
      secret_name = "de-pg-mixer-values-lookup-configuration-sync-${var.environment}"
    }
    rb_list_views_etl = {
      namespace   = "rb-list-views-etl",
      secret_name = "de-pg-rb-list-views-${var.environment}"
    }
    rb_list_web_leads_etl = {
      namespace   = "rb-list-web-leads-etl",
      secret_name = "de-pg-rb-list-web-leads-${var.environment}"
    }
    rb_taxonomy_etl = {
      namespace   = "rb-taxonomy-etl",
      secret_name = "de-pg-rb-taxonomy-${var.environment}"
    }
    fleet_manager_partition_management_pipeline = {
      namespace   = "fleet-manager-partition-management",
      secret_name = "de-pg-rfm-fleet-manager-partition-management-${var.environment}"
    }
    sales_rfm_user_config_reporting = {
      namespace   = "sales-rfm-user-config-reporting",
      secret_name = "de-pg-rfm-sales-rfm-user-config-reporting-${var.environment}"
    }
    selling_channel_reporting_etl = {
      namespace   = "selling-channel-reporting-etl",
      secret_name = "de-pg-rfm-selling-channel-reporting-${var.environment}"
    }
    smartequip_parts_book_etl = {
      namespace   = "smartequip-parts-book-etl",
      secret_name = "de-pg-smartequip-parts-book-${var.environment}"
    }
    user_config_rule_change_log_reporting = {
      namespace   = "user-config-rule-change-log-reporting",
      secret_name = "de-pg-user-config-rule-change-log-reporting-${var.environment}"
    }
    csmm_relevancy_etl = {
      namespace   = "csmm-relevancy-etl",
      secret_name = "de-pg-csmm-relevancy-etl-${var.environment}"
    }
    united_xvalues_export_etl = {
      namespace   = "united-xvalues-export-etl",
      secret_name = "de-pg-united-xvalues-export-${var.environment}"
    }

  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    username = each.key
    password = data.google_secret_manager_secret_version.dbuser_rfm[each.key].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_salesglobal" {
  for_each = toset([
    "values_lookup_schema_etl",
    "coda_rw"
  ])
  secret  = "dbuser_salesglobal_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_salesglobal" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    values_lookup_schema_etl = {
      namespace   = "values-lookup-schema-etl",
      secret_name = "de-pg-values-lookup-schema-${var.environment}"
    }
    coda_rw = {
      namespace   = "equipment-configuration-variants-etl",
      secret_name = "de-equipment-configuration-variants-etl-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    username = each.key
    password = data.google_secret_manager_secret_version.dbuser_salesglobal[each.key].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_ras_datamart" {
  for_each = toset([
    "svc_fleet_manager_backfill",
    "svc_fleet_manager_users_etl",
    "svc_fleet_manager_pg_export",
    "svc_mpe_kpi_metrics_etl",
  ])
  secret  = "dbuser_ras_datamart_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_ras_datamart" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    svc_fleet_manager_backfill = {
      namespace   = "fleet-manager-backfill",
      secret_name = "de-fleet-manager-backfill-${var.environment}"
    }
    svc_fleet_manager_users_etl = {
      namespace   = "fleet-manager-users-etl",
      secret_name = "de-fleet-manager-users-etl-${var.environment}"
    }
    svc_fleet_manager_pg_export = {
      namespace   = "fleet-manager-pg-export",
      secret_name = "de-fleet-manager-pg-export-${var.environment}"
    }
    svc_mpe_kpi_metrics_etl = {
      namespace   = "mpe-kpi-metrics-etl",
      secret_name = "de-mpe-kpi-metrics-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    username = each.key
    password = data.google_secret_manager_secret_version.dbuser_ras_datamart[each.key].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_valuations" {
  for_each = toset([
    "model_etl",
  ])
  secret  = "dbuser_valuations_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_valuations" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    appraisal-valuation-etl = {
      username    = "model_etl",
      secret_name = "de-pg-appraisal-valuation-${var.environment}"
    }
    appraisals-auction-values-etl = {
      username    = "model_etl",
      secret_name = "de-pg-appraisal-valuation-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = each.value["username"]
    password = data.google_secret_manager_secret_version.dbuser_valuations[each.value["username"]].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_ras_sas" {
  for_each = toset([
    "svc_airflow_monitoring_system_valuations_etl",
    "svc_appraisals_valuations_etl",
    "svc_appraisal_valuation_canary_etl",
    "svc_data_science_models_etl",
    "svc_legacy_provisioning_etl",
    "model_etl",
    "svc_sales_configuration_model",
    "svc_random_file_generation",
    "svc_schedule_module_etl",
    "svc_set_new_ab_cost_etl",
    "svc_db_coda_dependencies_etl",
    "svc_ss_export",
    "rebasing_automated_run",
    "mascus_sales_history_import"
  ])
  secret  = "dbuser_ras_sas_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_ras_sas" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    svc_airflow_monitoring_system_valuations_etl = {
      namespace   = "airflow-monitoring-system-valuations",
      secret_name = "de-mssql-airflow-monitor-system-valuations-${var.environment}"
    }
    svc_appraisals_valuations_etl = {
      namespace   = "appraisal-valuation-etl",
      secret_name = "de-appraisal-valuation-${var.environment}"
    }
    svc_appraisal_valuation_canary_etl = {
      namespace   = "appraisal-valuation-canary-etl",
      secret_name = "de-appraisal-valuation-canary-${var.environment}"
    }
    svc_data_science_models_etl = {
      namespace   = "data-science-models-etl",
      secret_name = "de-data-science-models-${var.environment}"
    }
    svc_legacy_provisioning_etl = {
      namespace   = "legacy-provisioning-etl",
      secret_name = "de-legacy-provisioning-${var.environment}"
    }
    model_etl = {
      namespace   = "appraisals-auction-values-etl",
      secret_name = "de-ras-sas-appraisals-auction-values-${var.environment}"
    }
    svc_sales_configuration_model = {
      namespace   = "sales-configuration-model",
      secret_name = "de-sales-configuration-model-${var.environment}"
    }
    svc_random_file_generation = {
      namespace   = "random-file-generation",
      secret_name = "de-random-file-generation-${var.environment}"
    }
    svc_schedule_module_etl = {
      namespace   = "schedules-module-etl",
      secret_name = "de-schedules-module-${var.environment}"
    }
    svc_set_new_ab_cost_etl = {
      namespace   = "set-new-ab-cost-etl",
      secret_name = "de-set-new-ab-cost-${var.environment}"
    }
    svc_db_coda_dependencies_etl = {
      namespace   = "db-coda-dep-auction-data",
      secret_name = "de-db-coda-dependencies-${var.environment}"
    }
    svc_ss_export = {
      namespace   = "ss-export",
      secret_name = "de-ss-export-${var.environment}"
    }
    rebasing_automated_run = {
      namespace   = "rebasing-automated-run",
      secret_name = "de-ss-rebasing-automated-run-${var.environment}"
    }
    mascus_sales_history_import = {
      namespace   = "mascus-sales-history-import",
      secret_name = "de-ss-mascus-sales-history-import-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    username = each.key
    password = data.google_secret_manager_secret_version.dbuser_ras_sas[each.key].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_auctions" {
  for_each = toset([
    "svc_rouse_rb_taxonomy_mapping_etl",
  ])
  secret  = "dbuser_auctions_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_auctions" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    svc_rouse_rb_taxonomy_mapping_etl = {
      namespace   = "rouse-rb-taxonomy-mapping-etl",
      secret_name = "de-rouse-rb-taxonomy-mapping-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    username = each.key
    password = data.google_secret_manager_secret_version.dbuser_auctions[each.key].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_classify" {
  for_each = toset([
    "svc_smartequip_parts_book_etl",
  ])
  secret  = "dbuser_classify_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_classify" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    svc_smartequip_parts_book_etl = {
      namespace   = "smartequip-parts-book-etl",
      secret_name = "de-smartequip-parts-book-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    username = each.key
    password = data.google_secret_manager_secret_version.dbuser_classify[each.key].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_rbma" {
  for_each = toset([
    "svc_sales_values_lookup_etl",
  ])
  secret  = "dbuser_rbma_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_rbma" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    svc_sales_values_lookup_etl = {
      namespace   = "sales-values-lookup",
      secret_name = "de-rbma-sales-values-lookup-${var.environment}"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    username = each.key
    password = data.google_secret_manager_secret_version.dbuser_rbma[each.key].secret_data
  }
}

data "google_kms_secret" "vault-ansible" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7xp/6m0V/CCUX+No8z5NusuQuPQPlFhf0OAbh1AZ+CcSUQBp1KhiY3wxvRWTcZuLggFlt0O5qicJnIAU0HI/rn4kXBP6JvYXJ2A+z33bY+EG+TqZVDFcAuVP44NqY6fIC8A0C9cqWkorDKUQOIUKFs5Qbg=="
}

resource "kubernetes_secret" "rouse-ansible" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "ssis-deployment",
    "ietl-admin-deployment",
    "ops-agent-install",
    "runtime-playbooks"
  ])

  metadata {
    name      = "rouse-ansible"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    ansible_user           = "rasgcp\\automation"
    ansible_password       = data.google_secret_manager_secret_version.windows_account_vms_prod.secret_data
    ansible_vault_password = data.google_kms_secret.vault-ansible.plaintext
  }
}

data "google_kms_secret" "de-rfm-auction-data-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU734NElb6hhGeUXCju5h1nrVZjNnP6J2W9kHhkq/FB/kSQQBp1Khi0swNh94jJaPJuifkW+4/EtOD87MJproLlOd+/DYWOxmKetRcpboCMc0JxAdP2z0RBW+kE0Zym18sX83s"
}

resource "kubernetes_secret" "de-rfm-auction-data-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "auction-data-etl",
  ])

  metadata {
    name      = "de-rfm-auction-data-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "auction_data_etl"
    password = data.google_kms_secret.de-rfm-auction-data-dev.plaintext
  }
}

data "google_kms_secret" "de-sales-auction-data-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU7+nahSh2g2iknJxb/dLx+20V9vLF+iPB8F9uj5nQm1QSQQBp1Khi/5pIbT7U8/sFXUD5qe32axeqIYbA2Xi9IzlWZJiiMVuV+j4CPa2sV9zyD2424SAQos7+jTDRyuCOWLmO"
}

resource "kubernetes_secret" "de-sales-auction-data-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "auction-data-etl",
  ])

  metadata {
    name      = "de-sales-auction-data-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "auction_data_etl"
    password = data.google_kms_secret.de-sales-auction-data-dev.plaintext
  }
}

data "google_kms_secret" "de-pg-prev-exch-rates-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU79Nqs3id4mf47Rw00ka0ECj/mPGhAaacTyp0vdH+4mYSQQBp1KhijaoZf+cBCfJ4XsNPwd31LNl0ufYqW7lYmeUr21VejiNxwtLi/JTUyvBd0NgwYAWBWxmnblDIrtTx02qM"
}

resource "kubernetes_secret" "de-pg-prev-exch-rates-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "sales-prevailing-exchange-rates-etl",
  ])

  metadata {
    name      = "de-pg-prev-exch-rates-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "sales_prevailing_exchange_rates_etl"
    password = data.google_kms_secret.de-pg-prev-exch-rates-dev.plaintext
  }
}

data "google_secret_manager_secret_version" "dbuser_sharded" {
  for_each = toset([
    "svc_monitoring_etl"
  ])
  secret  = "dbuser_sharded_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "platform-etl-prod-svc-monitoring-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    "health-checks" = "svc_monitoring_etl"
  }

  metadata {
    name      = "platform-etl-prod-sql-account"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_monitoring_etl"
    password = data.google_secret_manager_secret_version.dbuser_sharded[each.value].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_auctionvalues01" {
  for_each = toset([
    "root"
  ])
  secret  = "dbuser_auctionvalues01_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "de-pg-appraisals-auction-values-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    "secret1" = {
      "wi"     = "appraisals-auction-values-etl",
      "secret" = "root"
    }
  }

  metadata {
    name      = "de-pg-appraisals-auction-values-dev"
    namespace = kubernetes_namespace.workload_identity[each.value.wi].metadata[0].name
  }

  data = {
    username = each.value.secret
    password = data.google_secret_manager_secret_version.dbuser_auctionvalues01[each.value.secret].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_rdo04" {
  secret  = "dbuser_rdo04_custom_grids_performance_vs_benchmark_etl"
  project = local.services
}

resource "kubernetes_secret" "dbuser_rdo04" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "custom-grids-performance-vs-benchmark-etl",
    "rental-metrics-aggs"
  ])

  metadata {
    name      = "de-pg-cg-perf-vs-bench-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "custom_grids_performance_vs_benchmark_etl"
    password = data.google_secret_manager_secret_version.dbuser_rdo04.secret_data
  }
}

data "google_kms_secret" "de-cg-perf-vs-bench-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU73PUkR6SBQJayLKkbQUd1mD4hidKwiXd8qlumB6uRMMSNwBp1Khi4+p7j8S/TIEaQmr7W/WCSEoC8Sh1HG/zsBJa+OwG3zt9WPb/5LmRBWRI0feTIhcD1Vg="
}

resource "kubernetes_secret" "de-cg-perf-vs-bench-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "custom-grids-performance-vs-benchmark-etl",
  ])

  metadata {
    name      = "de-cg-perf-vs-bench-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_cg_perf_vs_bench_etl"
    password = data.google_kms_secret.de-cg-perf-vs-bench-dev.plaintext
  }
}

data "google_secret_manager_secret_version" "mssql_svc_sales_erp_sync_etl" {
  secret  = "mssql_svc_sales_erp_sync_etl"
  project = local.project
}

resource "kubernetes_secret" "de-sales-erp-sync-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-sales-erp-sync-dev"
    namespace = kubernetes_namespace.workload_identity["sales-erp-sync"].metadata[0].name
  }

  data = {
    username = "svc_sales_erp_sync_etl"
    password = data.google_secret_manager_secret_version.mssql_svc_sales_erp_sync_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "ws_sales_erp_sync_etl" {
  secret  = "ws_sales_erp_sync_etl"
  project = local.project
}

resource "kubernetes_secret" "de-ws-sales-erp-sync-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = {
    "secret1" = {
      "wi"     = "sales-erp-sync",
      "domain" = "rasgcp"
      "secret" = "ws_sales_erp_sync_etl"
    }
  }

  metadata {
    name      = "de-ws-sales-erp-sync-dev"
    namespace = kubernetes_namespace.workload_identity["sales-erp-sync"].metadata[0].name
  }

  data = {
    domain   = "rasgcp"
    username = "svc_erp_sync"
    password = data.google_secret_manager_secret_version.ws_sales_erp_sync_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "ftp_sales_erp_sync_etl" {
  secret  = "ftp_sales_erp_sync_etl"
  project = local.project
}

resource "kubernetes_secret" "de-ftp-sales-erp-sync-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-ftp-sales-erp-sync-dev"
    namespace = kubernetes_namespace.workload_identity["sales-erp-sync"].metadata[0].name
  }

  data = {
    ftp_credentials = data.google_secret_manager_secret_version.ftp_sales_erp_sync_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "service_sales_erp_sync_etl" {
  secret  = "service_sales_erp_sync_etl"
  project = local.project
}

resource "kubernetes_secret" "de-service-sales-erp-sync-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-service-sales-erp-sync-dev"
    namespace = kubernetes_namespace.workload_identity["sales-erp-sync"].metadata[0].name
  }

  data = {
    service_credentials = data.google_secret_manager_secret_version.service_sales_erp_sync_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "cloudsql_credentials_dev" {
  for_each = toset([
    "configs01",
    "dttconfig01",
    "identity01",
    "platform01",
    "portalcache",
    "rbma01",
    "rdo04",
    "record360_03",
    "rfm01",
    "rmbaglobal01",
    "salesglobal01",
    "salesglobal02",
    "valuations01"
  ])
  secret  = "dbadmin_${each.key}_dev"
  project = local.management_dev
}

resource "kubernetes_secret" "cloudsql_backups_credentials_dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = data.google_secret_manager_secret_version.cloudsql_credentials_dev

  metadata {
    name      = replace(each.value.secret, "_", "-")
    namespace = kubernetes_namespace.workload_identity["cloudsql-backups"].metadata[0].name
  }

  data = {
    credentials = each.value.secret_data
  }
}

data "google_kms_secret" "de-rambo-export-gnoco-dev" {
  crypto_key = local.terraform_state_key_id
  ciphertext = "CiQAAbvU75xMjw72Olu/x8bkSZXiD56VCYAikAxTQwWo2RsVz9YSQQBp1KhiwV6QiJR/MRnY+w4SqDOhAi+Q2vm/paq+wQyi3YdAONHVQikuLUjPo94sMESNUCrI5nVLD+7Xth6XSifC"
}

resource "kubernetes_secret" "de-rambo-export-gnoco-dev-wi" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "rental-metrics-aggs",
  ])

  metadata {
    name      = "de-rambo-export-gnoco"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = "svc_rambo_export"
    password = data.google_kms_secret.de-rambo-export-gnoco-dev.plaintext
  }
}

data "google_secret_manager_secret_version" "pg_sales_values_lookup_reporting" {
  secret  = "dbuser_rfm_sales_values_lookup_reporting"
  project = local.services
}

resource "kubernetes_secret" "de-sales-values-lookup-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-sales-values-lookup-dev"
    namespace = kubernetes_namespace.workload_identity["sales-values-lookup"].metadata[0].name
  }

  data = {
    username = "sales_values_lookup_reporting"
    password = data.google_secret_manager_secret_version.pg_sales_values_lookup_reporting.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_ims_fleet_ingest_etl" {
  secret  = "mssql_ims_fleet_ingest_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-ims-fleet-ingest-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-ims-fleet-ingest-dev"
    namespace = kubernetes_namespace.workload_identity["ims-fleet-ingest"].metadata[0].name
  }

  data = {
    username = "ims_fleet_ingest_etl"
    password = data.google_secret_manager_secret_version.mssql_ims_fleet_ingest_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_platform_classification_etl" {
  secret  = "mssql_platform_classification_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-platform-classification-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-platform-classification-dev"
    namespace = kubernetes_namespace.workload_identity["platform-classification"].metadata[0].name
  }

  data = {
    username = "svc_platform_classification"
    password = data.google_secret_manager_secret_version.mssql_platform_classification_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_equipment_syndication_etl" {
  secret  = "mssql_equipment_syndication_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-equipment-syndication-etl-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-equipment-syndication-etl-${var.environment}"
    namespace = kubernetes_namespace.workload_identity["equipment-syndication-etl"].metadata[0].name
  }
  data = {
    username = "svc_equipment_syndication_etl"
    password = data.google_secret_manager_secret_version.mssql_equipment_syndication_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "postgres_platform_classification_etl" {
  secret  = "dbuser_platform01_platform_classification_etl"
  project = local.services
}

resource "kubernetes_secret" "de-postgres-platform-classification-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-postgres-platform-classification-dev"
    namespace = kubernetes_namespace.workload_identity["platform-classification"].metadata[0].name
  }

  data = {
    username = "platform_classification_etl"
    password = data.google_secret_manager_secret_version.postgres_platform_classification_etl.secret_data
  }
}

resource "kubernetes_secret" "astronomer_bootstrap" {
  provider = kubernetes.astronomer-dev

  metadata {
    name      = "astronomer-bootstrap"
    namespace = "astronomer"
  }

  data = {
    connection = "postgres://${jsondecode(data.google_secret_manager_secret_version.cloudsql_astronomer.secret_data)["username"]}:${jsondecode(data.google_secret_manager_secret_version.cloudsql_astronomer.secret_data)["password"]}@${module.cloudsql_astronomer_v2.instance_private_ip}:5432"
  }
}

data "google_secret_manager_secret_version" "astronomer_service_account" {
  secret  = "astronomer_service_account"
  project = local.project
}

resource "kubernetes_secret" "astronomer_service_account" {
  provider = kubernetes.astronomer-dev

  for_each = toset([
    "astronomer",
  ])

  metadata {
    name      = "astronomer-gcs-keyfile"
    namespace = each.key
  }

  data = {
    astronomer-gcs-keyfile = data.google_secret_manager_secret_version.astronomer_service_account.secret_data
  }
}

data "google_secret_manager_secret_version" "winuser_abod_json_to_gcs_etl" {
  secret  = "winuser_abod_json_to_gcs_etl"
  project = local.services
}

resource "kubernetes_secret" "de-abod-json-to-gcs-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-abod-json-to-gcs-dev"
    namespace = kubernetes_namespace.workload_identity["abod-json-to-gcs-etl"].metadata[0].name
  }

  data = {
    domain   = "RASGCP"
    username = "svc_abod_json_to_gcs"
    password = data.google_secret_manager_secret_version.winuser_abod_json_to_gcs_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "de_pg_platform_dbadmin" {
  secret  = "dbadmin_platform01_${var.environment}"
  project = local.project
}

resource "kubernetes_secret" "de_pg_platform_dbadmin" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "appraisals-auction-values-etl",
    "appraisal-valuation-etl",
    "auction-transactions-etl",
    "auctions-etl",
  ])

  metadata {
    name      = "de-pg-platform-dbadmin-dev"
    namespace = each.key
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.de_pg_platform_dbadmin.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.de_pg_platform_dbadmin.secret_data), "password")
  }
}


data "google_secret_manager_secret_version" "winuser_sales_configuration_model" {
  secret  = "winuser_sales_configuration_model"
  project = local.services
}

resource "kubernetes_secret" "secret_sales_configuration_model" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-ws-sales-configuration-model-${var.environment}"
    namespace = kubernetes_namespace.workload_identity["sales-configuration-model"].metadata[0].name
  }

  data = {
    domain   = "RASGCP.NET"
    username = "svc_config_model_etl"
    password = data.google_secret_manager_secret_version.winuser_sales_configuration_model.secret_data
  }
}

data "google_secret_manager_secret_version" "winuser_random_file_generation" {
  secret  = "winuser_random_file_generation"
  project = local.services
}

resource "kubernetes_secret" "secret_random_file_generation" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-ws-random-file-generation-${var.environment}"
    namespace = kubernetes_namespace.workload_identity["random-file-generation"].metadata[0].name
  }

  data = {
    domain   = "RASGCP.NET"
    username = "svc_random_file_generation"
    password = data.google_secret_manager_secret_version.winuser_random_file_generation.secret_data
  }
}

data "google_secret_manager_secret_version" "winuser_set_new_ab_cost_etl" {
  secret  = "winuser_set_new_ab_cost_etl"
  project = local.services
}

resource "kubernetes_secret" "de-ws-set-new-ab-cost-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-ws-set-new-ab-cost-dev"
    namespace = kubernetes_namespace.workload_identity["set-new-ab-cost-etl"].metadata[0].name
  }

  data = {
    domain   = "RASDOMAIN.ENT"
    username = "svc_ws_set_new_ab_cost_etl"
    password = data.google_secret_manager_secret_version.winuser_set_new_ab_cost_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "sharepoint_schedules_module_etl" {
  secret  = "sharepoint_schedules_module_etl"
  project = local.services
}

resource "kubernetes_secret" "de-sharepoint-schedules-module-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-sharepoint-schedules-module-dev"
    namespace = kubernetes_namespace.workload_identity["schedules-module-etl"].metadata[0].name
  }

  data = {
    username = "<EMAIL>"
    password = data.google_secret_manager_secret_version.sharepoint_schedules_module_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "winuser_united_xvalues_export_etl" {
  secret  = "winuser_united_xvalues_export_etl"
  project = local.services
}

resource "kubernetes_secret" "de-ws-united-xvalues-export-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-ws-united-xvalues-export-dev"
    namespace = kubernetes_namespace.workload_identity["united-xvalues-export-etl"].metadata[0].name
  }

  data = {
    domain   = "RASGCP.NET"
    username = "svc_ws_united_xvalues_export_etl"
    password = data.google_secret_manager_secret_version.winuser_united_xvalues_export_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_ras_datamart_sales_united_xvalues_export_etl" {
  secret  = "dbuser_ras_datamart_sales_united_xvalues_export_etl"
  project = local.services
}

resource "kubernetes_secret" "de-united-xvalues-export-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-united-xvalues-export-dev"
    namespace = kubernetes_namespace.workload_identity["united-xvalues-export-etl"].metadata[0].name
  }

  data = {
    username = "svc_united_xvalues_export_etl"
    password = data.google_secret_manager_secret_version.dbuser_ras_datamart_sales_united_xvalues_export_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "pg_ims_fleet_refresh_etl" {
  secret  = "dbuser_rfm_ims_fleet_refresh_etl"
  project = local.services
}

resource "kubernetes_secret" "de-pg-ims-fleet-refresh-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-pg-ims-fleet-refresh-dev"
    namespace = kubernetes_namespace.workload_identity["ims-fleet-refresh"].metadata[0].name
  }

  data = {
    username = "ims_fleet_refresh_etl"
    password = data.google_secret_manager_secret_version.pg_ims_fleet_refresh_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "pg_ims_fleet_ingest_etl" {
  secret  = "dbuser_rfm_ims_fleet_ingest_etl"
  project = local.services
}

resource "kubernetes_secret" "de-pg-ims-fleet-ingest-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-pg-ims-fleet-ingest-dev"
    namespace = kubernetes_namespace.workload_identity["ims-fleet-ingest"].metadata[0].name
  }

  data = {
    username = "ims_fleet_ingest_etl"
    password = data.google_secret_manager_secret_version.pg_ims_fleet_ingest_etl.secret_data
  }
}


data "google_secret_manager_secret_version" "analytics_eemphasys_api_keys" {
  secret  = "analytics_eemphasys_api_keys"
  project = local.project
}

resource "kubernetes_secret" "analytics_eemphasys_api_keys" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "analytics-eemphasys-api-keys"
    namespace = kubernetes_namespace.workload_identity["analytics-eemphasys"].metadata[0].name
  }

  data = {
    api_keys = data.google_secret_manager_secret_version.analytics_eemphasys_api_keys.secret_data
  }
}

data "google_secret_manager_secret_version" "analytics_powerbi_api_keys" {
  secret  = "analytics_powerbi_api_keys"
  project = local.project
}

resource "kubernetes_secret" "analytics_powerbi_api_keys" {
  provider = kubernetes.airflow-jobs-private

  metadata {
    name      = "analytics-powerbi-api-keys"
    namespace = kubernetes_namespace.workload_identity["analytics-powerbi"].metadata[0].name
  } 

  data = {
    api_keys = data.google_secret_manager_secret_version.analytics_powerbi_api_keys.secret_data
  }
}


data "google_secret_manager_secret_version" "analytics_admar_api_permissions_dev" {
  secret  = "analytics_admar_api_permissions_dev"
  project = local.project
}

resource "kubernetes_secret" "analytics_admar_api_permissions_dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "analytics-admar-api-permissions-dev"
    namespace = kubernetes_namespace.workload_identity["analytics-admar"].metadata[0].name
  }

  data = {
    username = "rouse"
    password = data.google_secret_manager_secret_version.analytics_admar_api_permissions_dev.secret_data
  }
}

data "google_secret_manager_secret_version" "analytics_smartequip_mysql_credentials" {
  secret  = "analytics_smartequip_mysql_credentials"
  project = local.project
}

resource "kubernetes_secret" "analytics_smartequip_mysql_credentials" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "analytics-smartequip-mysql-credentials"
    namespace = kubernetes_namespace.workload_identity["rambo-smartequip-pipeline"].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_secret_manager_secret_version.analytics_smartequip_mysql_credentials.secret_data
  }
}

resource "kubernetes_secret" "private_cluster_analytics_smartequip_mysql_credentials" {
  provider = kubernetes.airflow-jobs-private

  metadata {
    name      = "analytics-smartequip-mysql-credentials"
    namespace = kubernetes_namespace.workload_identity["rambo-smartequip-pipeline"].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_secret_manager_secret_version.analytics_smartequip_mysql_credentials.secret_data
  }
}


data "google_secret_manager_secret_version" "vendor_config_smartequip_mysql_credentials" {
  secret  = "vendor_config_smartequip_mysql_credentials"
  project = local.project
}

resource "kubernetes_secret" "vendor_config_smartequip_mysql_credentials" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "vendor-config-smartequip-mysql-credentials"
    namespace = kubernetes_namespace.workload_identity["vendor-config-smartequip-etl"].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.vendor_config_smartequip_mysql_credentials.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.vendor_config_smartequip_mysql_credentials.secret_data), "password")
  }
}

resource "kubernetes_secret" "private_cluster_vendor_config_smartequip_mysql_credentials" {
  provider = kubernetes.airflow-jobs-private

  metadata {
    name      = "vendor-config-smartequip-mysql-credentials"
    namespace = kubernetes_namespace.workload_identity["vendor-config-smartequip-etl"].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.vendor_config_smartequip_mysql_credentials.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.vendor_config_smartequip_mysql_credentials.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "analytics_airflow_pg_connection_dev" {
  secret  = "analytics_airflow_pg_connection_dev"
  project = local.project
}

resource "kubernetes_secret" "analytics_airflow_pg_connection_dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "analytics-airflow-pg-connection-dev"
    namespace = kubernetes_namespace.workload_identity["rental-metrics-aggs"].metadata[0].name
  }

  data = {
    username = "root"
    password = data.google_secret_manager_secret_version.analytics_airflow_pg_connection_dev.secret_data
  }
}

data "google_secret_manager_secret_version" "analytics_alloydb_cg_dev" {
  secret  = "analytics_alloydb_cg_dev"
  project = local.project
}

resource "kubernetes_secret" "analytics_alloydb_cg_dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "custom-grids-benchmark-transactional-detail-etl",
    "custom-grids-fleet-data-etl",
    "custom-grids-performance-vs-benchmark-etl",
    "rental-metrics-aggs",
  ])

  metadata {
    name      = "analytics-alloydb-cg-dev"
    namespace = each.key
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_alloydb_cg_dev.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_alloydb_cg_dev.secret_data), "password")
  }
}


data "google_secret_manager_secret_version" "loggly_api_secret_name" {
  secret  = "loggly_api_key_dev"
  project = local.project
}

resource "kubernetes_secret" "loggly_api_secret_name" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "analytics-admar",
    "analytics-salesforce"
  ])

  metadata {
    name      = "loggly-api-key-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    loggly_api_key = data.google_secret_manager_secret_version.loggly_api_secret_name.secret_data
  }
}

data "google_secret_manager_secret_version" "analytics_salesforce_api_keys" {
  secret  = "analytics_salesforce_api_keys"
  project = local.project
}

resource "kubernetes_secret" "analytics_salesforce_api_keys" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "analytics-salesforce-api-keys"
    namespace = kubernetes_namespace.workload_identity["analytics-salesforce"].metadata[0].name
  }

  data = {
    api_keys = data.google_secret_manager_secret_version.analytics_salesforce_api_keys.secret_data
  }
}

data "google_secret_manager_secret_version" "ansible-devops-admin" {
  secret  = "ansible_devops_admin"
  project = local.vms_dev
}

resource "kubernetes_secret" "ansible-devops-admin" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-syndication-etl",
  ])

  metadata {
    name      = "ansible-devops-admin-${var.environment}"
    namespace = each.key
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.ansible-devops-admin.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.ansible-devops-admin.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "ritchie_aws_competitor_feed_access_key_id" {
  secret  = "rb_aws_competitive_intelligence_etl_access_key_id"
  project = local.project
}

data "google_secret_manager_secret_version" "ritchie_aws_competitor_feed_secret_value" {
  secret  = "rb_aws_auctions_scraper_etl_secret_access_key"
  project = local.project
}

resource "kubernetes_secret" "de-rb-competitive-intelligence-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-rb-competitive-intelligence-dev"
    namespace = kubernetes_namespace.workload_identity["rb-competitive-intelligence-etl"].metadata[0].name
  }

  data = {
    access_key_id = data.google_secret_manager_secret_version.ritchie_aws_competitor_feed_access_key_id.secret_data
    secret_value  = data.google_secret_manager_secret_version.ritchie_aws_competitor_feed_secret_value.secret_data
  }
}

data "google_secret_manager_secret_version" "open_exchange_rates_app_id" {
  secret  = "open_exchange_rates_app_id"
  project = local.project
}

resource "kubernetes_secret" "de-open-exchange-rates-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-open-exchange-rates-dev"
    namespace = kubernetes_namespace.workload_identity["exchange-rates-etl"].metadata[0].name
  }

  data = {
    app_id = data.google_secret_manager_secret_version.open_exchange_rates_app_id.secret_data
  }
}

# identity user pipeline
data "google_secret_manager_secret_version" "mssql-user-identity-pipeline-secret" {
  secret  = "identity_user_pipeline_sql_user"
  project = local.management_dev
}


resource "kubernetes_secret" "mssql-user-identity-pipeline-secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "identity-user-pipeline",
  ])

  metadata {
    name      = "identity-user-pipeline-sql-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    WIN_USER     = lookup(jsondecode(data.google_secret_manager_secret_version.mssql-user-identity-pipeline-secret.secret_data), "WIN_USER")
    WIN_PASSWORD = lookup(jsondecode(data.google_secret_manager_secret_version.mssql-user-identity-pipeline-secret.secret_data), "WIN_PASSWORD")
  }
}

data "google_secret_manager_secret_version" "sentry-slack-webhook" {
  secret  = "infrastructure_slack_webhook"
  project = local.project
}

resource "kubernetes_secret" "sentry-slack-webhook" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "slack-sentry-webhook-url"
    namespace = "sentry-alerts"
  }

  data = {
    url = data.google_secret_manager_secret_version.sentry-slack-webhook.secret_data
  }
}

resource "kubernetes_secret" "check-unused-disks-slack-webhook" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "infrastructure-webhook-url"
    namespace = "check-unused-disks"
  }

  data = {
    url = data.google_secret_manager_secret_version.sentry-slack-webhook.secret_data
  }
}

data "google_secret_manager_secret_version" "public-repo-slack-webhook" {
  secret  = "infrastructure_slack_webhook"
  project = local.project
}

resource "kubernetes_secret" "public-repo-slack-webhook" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "slack-public-repos-webhook-url"
    namespace = "public-repo-alerts"
  }

  data = {
    url = data.google_secret_manager_secret_version.public-repo-slack-webhook.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_ims_fleet_refresh_etl" {
  secret  = "mssql_ims_fleet_refresh_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-ims-fleet-refresh-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-ims-fleet-refresh-dev"
    namespace = kubernetes_namespace.workload_identity["ims-fleet-refresh"].metadata[0].name
  }

  data = {
    username = "svc_ims_fleet_refresh_etl"
    password = data.google_secret_manager_secret_version.mssql_ims_fleet_refresh_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "enterprise-service-cleanup-slack-sales-webhook" {
  secret  = "enterprise_service_cleanup_slack_sales_webhook"
  project = local.project
}

resource "kubernetes_secret" "enterprise-slack-sales" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "slack-sales-webhook-url"
    namespace = "enterprise-service-cleanup"
  }

  data = {
    url = data.google_secret_manager_secret_version.enterprise-service-cleanup-slack-sales-webhook.secret_data
  }
}

data "google_secret_manager_secret_version" "enterprise-service-cleanup-slack-appraisals-webhook" {
  secret  = "enterprise_service_cleanup_slack_appraisals_webhook"
  project = local.project
}

resource "kubernetes_secret" "enterprise-slack-appraisals" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "slack-appraisals-webhook-url"
    namespace = "enterprise-service-cleanup"
  }

  data = {
    url = data.google_secret_manager_secret_version.enterprise-service-cleanup-slack-appraisals-webhook.secret_data
  }
}

data "google_secret_manager_secret_version" "enterprise-service-cleanup-slack-analytics-webhook" {
  secret  = "enterprise_service_cleanup_slack_analytics_webhook"
  project = local.project
}

resource "kubernetes_secret" "enterprise-slack-analytics" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "slack-analytics-webhook-url"
    namespace = "enterprise-service-cleanup"
  }

  data = {
    url = data.google_secret_manager_secret_version.enterprise-service-cleanup-slack-analytics-webhook.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_svc_fleet_manager_sync" {
  secret  = "mssql_svc_fleet_manager_sync"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-fleet-manager-sync-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-fleet-manager-sync-dev"
    namespace = kubernetes_namespace.workload_identity["fleet-manager-sync"].metadata[0].name
  }

  data = {
    username = "svc_fleet_manager_sync"
    password = data.google_secret_manager_secret_version.mssql_svc_fleet_manager_sync.secret_data
  }
}

data "google_secret_manager_secret_version" "sentry-analytics-bearer-token" {
  secret  = "sentry_analytics_bearer_token"
  project = local.project
}

resource "kubernetes_secret" "sentry-analytics-bearer-token" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "sentry-analytics-bearer-token"
    namespace = "sentry-alerts"
  }

  data = {
    url = data.google_secret_manager_secret_version.sentry-analytics-bearer-token.secret_data
  }
}

data "google_secret_manager_secret_version" "sentry-appraisals-bearer-token" {
  secret  = "sentry_appraisals_bearer_token"
  project = local.project
}

resource "kubernetes_secret" "sentry-appraisals-bearer-token" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "sentry-appraisals-bearer-token"
    namespace = "sentry-alerts"
  }

  data = {
    url = data.google_secret_manager_secret_version.sentry-appraisals-bearer-token.secret_data
  }
}

data "google_secret_manager_secret_version" "sentry-sales-bearer-token" {
  secret  = "sentry_sales_bearer_token"
  project = local.project
}

resource "kubernetes_secret" "sentry-sales-bearer-token" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "sentry-sales-bearer-token"
    namespace = "sentry-alerts"
  }

  data = {
    url = data.google_secret_manager_secret_version.sentry-sales-bearer-token.secret_data
  }
}

data "google_secret_manager_secret_version" "sentry-services-bearer-token" {
  secret  = "sentry_services_bearer_token"
  project = local.project
}

resource "kubernetes_secret" "sentry-saservicesles-bearer-token" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "sentry-services-bearer-token"
    namespace = "sentry-alerts"
  }

  data = {
    url = data.google_secret_manager_secret_version.sentry-services-bearer-token.secret_data
  }
}

data "google_secret_manager_secret_version" "sales_fleet_manager_api_auth_pipeline" {
  secret  = "sales_fleet_manager_auth_token"
  project = local.services
}

data "google_secret_manager_secret_version" "sales_fleet_manager_integrations_api_auth" {
  secret  = "sales_fleet_manager_integrations_auth_token"
  project = local.services
}

resource "kubernetes_secret" "de-sales-fleet-manager-api-auth-ingest-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-sales-fleet-manager-api-auth-ingest-dev"
    namespace = kubernetes_namespace.workload_identity["ims-fleet-ingest"].metadata[0].name
  }

  data = {
    token = data.google_secret_manager_secret_version.sales_fleet_manager_api_auth_pipeline.secret_data
  }
}

resource "kubernetes_secret" "de-sales-fleet-manager-api-auth-refresh-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-sales-fleet-manager-api-auth-refresh-dev"
    namespace = kubernetes_namespace.workload_identity["ims-fleet-refresh"].metadata[0].name
  }

  data = {
    token = data.google_secret_manager_secret_version.sales_fleet_manager_api_auth_pipeline.secret_data
  }
}

resource "kubernetes_secret" "de-sales-fleet-manager-api-auth-token" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "equipment-classification-etl",
    "fleet-manager-publish-insights-taxonomy-and-localization-etl",
    "restore-etl",
  ])

  metadata {
    name      = "de-sales-fleet-manager-api-auth-token-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    token = data.google_secret_manager_secret_version.sales_fleet_manager_api_auth_pipeline.secret_data
  }
}

resource "kubernetes_secret" "de-sales-fleet-manager-integrations-auth-token" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "fleet-manager-fleet-metrics",
  ])

  metadata {
    name      = "de-sales-fleet-manager-integrations-auth-token-${var.environment}"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    token = data.google_secret_manager_secret_version.sales_fleet_manager_integrations_api_auth.secret_data
  }
}

data "google_secret_manager_secret_version" "platform_user_config_api_auth_pipeline" {
  secret  = "platform_user_config_auth_token"
  project = local.services
}

resource "kubernetes_secret" "de-platform-user-config-api-auth-refresh-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "ims-fleet-ingest",
    "ims-fleet-refresh",
    "fleet-manager-shard-server-mapping",
    "rental-insights-etl",
  ])

  metadata {
    name      = "de-platform-user-config-api-auth-refresh-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    token = data.google_secret_manager_secret_version.platform_user_config_api_auth_pipeline.secret_data
  }
}

data "google_secret_manager_secret_version" "platform_classification_api_auth_pipeline" {
  secret  = "platform_classification_auth_token"
  project = local.services
}

resource "kubernetes_secret" "de-platform-classification-api-auth" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "fleet-manager-supercategory-etl",
    "platform-classification",
  ])

  metadata {
    name      = "de-platform-classification-api-auth-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    token = data.google_secret_manager_secret_version.platform_classification_api_auth_pipeline.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_airflow_monitor_system_ims" {
  secret  = "mssql_airflow_monitor_system_ims"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-airflow-monitor-system-ims-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-airflow-monitor-system-ims-dev"
    namespace = kubernetes_namespace.workload_identity["airflow-monitoring-system-ims"].metadata[0].name
  }

  data = {
    username = "svc_airflow_monitor_system_ims"
    password = data.google_secret_manager_secret_version.mssql_airflow_monitor_system_ims.secret_data
  }
}

data "google_secret_manager_secret_version" "pgbouncer-server-ca-secret" {
  secret  = "pgbouncer_default_ca_server_ca"
  project = local.services
}


resource "kubernetes_secret" "pgbouncer-server-ca-secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "ims-fleet-ingest",
    "ims-fleet-refresh",
    "rouse-rb-taxonomy-mapping-etl",
    "fleet-manager-pg-export",
    "fleet-manager-pg-export-multi-tenant",
    "fleet-manager-backfill",
    "change-history-etl",
    "equipment-classification-etl",
    "equipment-valuations-etl",
    "auction-data-etl",
    "equipment-details-etl",
    "equipment-values-residuals-etl",
    "fleet-manager-cat-product-group",
    "fleet-manager-customers-metrics-reporting",
    "fleet-manager-sales-subcategory",
    "fleet-manager-supercategory-etl",
    "fleet-manager-sync",
    "fleet-manager-users-etl",
    "marketing-metrics",
    "mpe-kpi-metrics-etl",
    "mpe-transactions-etl",
    "modelyear-range-etl",
    "sales-prevailing-exchange-rates-etl",
    "transaction-volume-auction-etl",
    "value-trends-etl",
    "web-leads-etl",
    "appraisal-valuation-canary-etl",
    "auctions-etl",
    "classification-photos",
    "client-etl",
    "countries-etl",
    "create-empty-lookup-values-tables",
    "csmm-relevancy-etl",
    "equipment-catalog-etl",
    "equipment-configuration-variants-etl",
    "equipment-files-etl",
    "equipment-notes-history-etl",
    "equipment-photos-etl",
    "equipment-photos-sync-monitor",
    "equipment-sync-monitor",
    "equipment-syndication-etl",
    "equipment-values-history-etl",
    "manage-equipment-etl",
    "marketable-life-etl",
    "migrations-etl",
    "equipment-view-publisher",
    "fleet-manager-consolidated-data",
    "fleet-manager-expected-time-to-sell-etl",
    "fleet-manager-fleet-customers-monitoring",
    "fleet-manager-fleet-listings",
    "fleet-manager-fleet-metrics",
    "fleet-manager-publish-insights-taxonomy-and-localization-etl",
    "proposal-history-etl",
    "rfm-archiving-etl",
    "sales-erp-sync",
    "sales-google-analytics-page-views",
    "sales-invoice-emails",
    "sales-transactions",
    "sales-values-lookup",
    "ss-export",
    "transaction-volume-retail-etl",
    "restore-etl",
    "fleet-manager-valuations-history-etl",
    "health-checks",
    "generate-site-map-etl",
    "legacy-provisioning-etl",
    "rental-insights-etl",
    "mixer-export-etl",
    "mixer-values-lookup-configuration-sync",
    "rb-list-views-etl",
    "rb-list-web-leads-etl",
    "rb-taxonomy-etl",
    "fleet-manager-multi-checks",
    "values-lookup-schema-etl",
    "fleet-manager-partition-management",
    "sales-rfm-user-config-reporting",
    "selling-channel-reporting-etl",
    "smartequip-parts-book-etl",
    "user-config-rule-change-log-reporting",
    "united-xvalues-export-etl",
    "custom-grids-benchmark-transactional-detail-etl",
    "rental-metrics-aggs",
  ])

  metadata {
    name      = "pgbouncer-server-ca-cert"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    SSL_ROOT_CERT = data.google_secret_manager_secret_version.pgbouncer-server-ca-secret.secret_data
  }
}

data "google_secret_manager_secret_version" "pgbouncer-rfm02-dev-client-cert-secret" {
  secret  = "pgbouncer_rfm02_dev_client_cert"
  project = local.services
}


resource "kubernetes_secret" "pgbouncer-rfm02-dev-client-cert-secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "ims-fleet-ingest",
    "ims-fleet-refresh",
    "rouse-rb-taxonomy-mapping-etl",
    "fleet-manager-pg-export",
    "fleet-manager-pg-export-multi-tenant",
    "fleet-manager-backfill",
    "change-history-etl",
    "equipment-classification-etl",
    "equipment-valuations-etl",
    "auction-data-etl",
    "equipment-details-etl",
    "equipment-values-residuals-etl",
    "fleet-manager-cat-product-group",
    "fleet-manager-customers-metrics-reporting",
    "fleet-manager-sales-subcategory",
    "fleet-manager-supercategory-etl",
    "fleet-manager-sync",
    "fleet-manager-users-etl",
    "marketing-metrics",
    "mpe-kpi-metrics-etl",
    "mpe-transactions-etl",
    "modelyear-range-etl",
    "sales-prevailing-exchange-rates-etl",
    "transaction-volume-auction-etl",
    "value-trends-etl",
    "web-leads-etl",
    "appraisal-valuation-canary-etl",
    "auctions-etl",
    "classification-photos",
    "client-etl",
    "countries-etl",
    "csmm-relevancy-etl",
    "create-empty-lookup-values-tables",
    "equipment-catalog-etl",
    "equipment-configuration-variants-etl",
    "equipment-files-etl",
    "equipment-notes-history-etl",
    "equipment-photos-etl",
    "equipment-photos-sync-monitor",
    "equipment-sync-monitor",
    "equipment-syndication-etl",
    "equipment-values-history-etl",
    "manage-equipment-etl",
    "marketable-life-etl",
    "migrations-etl",
    "equipment-view-publisher",
    "fleet-manager-consolidated-data",
    "fleet-manager-expected-time-to-sell-etl",
    "fleet-manager-fleet-customers-monitoring",
    "fleet-manager-fleet-listings",
    "fleet-manager-fleet-metrics",
    "fleet-manager-publish-insights-taxonomy-and-localization-etl",
    "proposal-history-etl",
    "rfm-archiving-etl",
    "sales-erp-sync",
    "sales-google-analytics-page-views",
    "sales-invoice-emails",
    "sales-transactions",
    "sales-values-lookup",
    "ss-export",
    "transaction-volume-retail-etl",
    "restore-etl",
    "fleet-manager-valuations-history-etl",
    "health-checks",
    "generate-site-map-etl",
    "legacy-provisioning-etl",
    "rental-insights-etl",
    "mixer-export-etl",
    "mixer-values-lookup-configuration-sync",
    "rb-list-views-etl",
    "rb-list-web-leads-etl",
    "rb-taxonomy-etl",
    "fleet-manager-multi-checks",
    "values-lookup-schema-etl",
    "fleet-manager-partition-management",
    "sales-rfm-user-config-reporting",
    "selling-channel-reporting-etl",
    "smartequip-parts-book-etl",
    "user-config-rule-change-log-reporting",
    "united-xvalues-export-etl",
    "custom-grids-benchmark-transactional-detail-etl",
    "rental-metrics-aggs",
  ])

  metadata {
    name      = "pgbouncer-rfm02-dev-client-cert"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    SSL_CERT = data.google_secret_manager_secret_version.pgbouncer-rfm02-dev-client-cert-secret.secret_data
  }
}

data "google_secret_manager_secret_version" "pgbouncer-rfm02-dev-client-key-secret" {
  secret  = "pgbouncer_rfm02_dev_client_key"
  project = local.services
}


resource "kubernetes_secret" "pgbouncer-rfm02-dev-client-key-secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "ims-fleet-ingest",
    "ims-fleet-refresh",
    "rouse-rb-taxonomy-mapping-etl",
    "fleet-manager-pg-export",
    "fleet-manager-pg-export-multi-tenant",
    "fleet-manager-backfill",
    "change-history-etl",
    "equipment-classification-etl",
    "equipment-valuations-etl",
    "auction-data-etl",
    "equipment-details-etl",
    "equipment-values-residuals-etl",
    "fleet-manager-cat-product-group",
    "fleet-manager-customers-metrics-reporting",
    "fleet-manager-sales-subcategory",
    "fleet-manager-supercategory-etl",
    "fleet-manager-sync",
    "fleet-manager-users-etl",
    "marketing-metrics",
    "mpe-kpi-metrics-etl",
    "mpe-transactions-etl",
    "modelyear-range-etl",
    "sales-prevailing-exchange-rates-etl",
    "transaction-volume-auction-etl",
    "value-trends-etl",
    "web-leads-etl",
    "appraisal-valuation-canary-etl",
    "auctions-etl",
    "classification-photos",
    "client-etl",
    "countries-etl",
    "create-empty-lookup-values-tables",
    "csmm-relevancy-etl",
    "equipment-catalog-etl",
    "equipment-configuration-variants-etl",
    "equipment-files-etl",
    "equipment-notes-history-etl",
    "equipment-photos-etl",
    "equipment-photos-sync-monitor",
    "equipment-sync-monitor",
    "equipment-syndication-etl",
    "equipment-values-history-etl",
    "equipment-view-publisher",
    "fleet-manager-consolidated-data",
    "manage-equipment-etl",
    "marketable-life-etl",
    "migrations-etl",
    "fleet-manager-expected-time-to-sell-etl",
    "fleet-manager-fleet-customers-monitoring",
    "fleet-manager-fleet-listings",
    "fleet-manager-fleet-metrics",
    "fleet-manager-publish-insights-taxonomy-and-localization-etl",
    "proposal-history-etl",
    "rfm-archiving-etl",
    "sales-erp-sync",
    "sales-google-analytics-page-views",
    "sales-invoice-emails",
    "sales-transactions",
    "sales-values-lookup",
    "ss-export",
    "transaction-volume-retail-etl",
    "restore-etl",
    "fleet-manager-valuations-history-etl",
    "health-checks",
    "generate-site-map-etl",
    "legacy-provisioning-etl",
    "rental-insights-etl",
    "mixer-export-etl",
    "mixer-values-lookup-configuration-sync",
    "rb-list-views-etl",
    "rb-list-web-leads-etl",
    "rb-taxonomy-etl",
    "fleet-manager-multi-checks",
    "values-lookup-schema-etl",
    "fleet-manager-partition-management",
    "sales-rfm-user-config-reporting",
    "selling-channel-reporting-etl",
    "smartequip-parts-book-etl",
    "user-config-rule-change-log-reporting",
    "united-xvalues-export-etl",
    "custom-grids-benchmark-transactional-detail-etl",
    "rental-metrics-aggs",
  ])

  metadata {
    name      = "pgbouncer-rfm02-dev-client-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    SSL_KEY = data.google_secret_manager_secret_version.pgbouncer-rfm02-dev-client-key-secret.secret_data
  }
}

data "google_secret_manager_secret_version" "pfx-cert-password" {
  secret  = "pfx_cert_password"
  project = local.project
}

resource "kubernetes_secret" "pfx-cert-password" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "octopus-cert-renewal",
    "winrm-setup",
  ])

  metadata {
    name      = "pfx-cert-password"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    password = data.google_secret_manager_secret_version.pfx-cert-password.secret_data
  }
}

data "google_secret_manager_secret_version" "octopus-api-key" {
  secret  = "octopus_api_key"
  project = local.project
}

resource "kubernetes_secret" "octopus-api-key" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "octopus-cert-renewal",
  ])

  metadata {
    name      = "octopus-api-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    api_key = data.google_secret_manager_secret_version.octopus-api-key.secret_data
  }
}


data "google_secret_manager_secret_version" "mssql_svc_marketing_metrics_etl" {
  secret  = "mssql_svc_marketing_metrics_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-marketing-metrics-etl-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-marketing-metrics-etl-dev"
    namespace = kubernetes_namespace.workload_identity["marketing-metrics"].metadata[0].name
  }

  data = {
    username = "svc_marketing_metrics_etl"
    password = data.google_secret_manager_secret_version.mssql_svc_marketing_metrics_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "web_taylor_martin_auctions_scraper_etl" {
  secret  = "web_taylor_martin_auctions_scraper_etl"
  project = local.project
}

resource "kubernetes_secret" "de-taylor-martin-auctions-scraper-etl-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-taylor-martin-auctions-scraper-etl-dev"
    namespace = kubernetes_namespace.workload_identity["auctions-scraper-etl"].metadata[0].name
  }

  data = {
    username = "<EMAIL>"
    password = data.google_secret_manager_secret_version.web_taylor_martin_auctions_scraper_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "web_big_iron_auctions_scraper_etl" {
  secret  = "web_big_iron_auctions_scraper_etl"
  project = local.project
}

resource "kubernetes_secret" "de-web-big-iron-auctions-scraper-etl-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-web-big-iron-auctions-scraper-etl-dev"
    namespace = kubernetes_namespace.workload_identity["auctions-scraper-etl"].metadata[0].name
  }

  data = {
    email    = "<EMAIL>"
    password = data.google_secret_manager_secret_version.web_big_iron_auctions_scraper_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "web_ip_vendor_auctions_scraper_etl" {
  secret  = "web_ip_vendor_auctions_scraper_etl"
  project = local.project
}

resource "kubernetes_secret" "de-web-ip-vendor-auctions-scraper-etl-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-web-ip-vendor-auctions-scraper-etl-dev"
    namespace = kubernetes_namespace.workload_identity["auctions-scraper-etl"].metadata[0].name
  }

  data = {
    username = "resirouseservices"
    password = data.google_secret_manager_secret_version.web_ip_vendor_auctions_scraper_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_ietl_archive_history_data_etl" {
  secret  = "mssql_ietl_archive_history_data_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-ietl-archive-history-data-etl-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-ietl-archive-history-data-etl-dev"
    namespace = kubernetes_namespace.workload_identity["ietl-archive-history-data-etl"].metadata[0].name
  }

  data = {
    username = "svc_ietl_archive_history_data_etl"
    password = data.google_secret_manager_secret_version.mssql_ietl_archive_history_data_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_ietl_copy_analytics_etl" {
  secret  = "mssql_ietl_copy_analytics_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-ietl-copy-analytics-etl-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-ietl-copy-analytics-etl-dev"
    namespace = kubernetes_namespace.workload_identity["ietl-copy-analytics-etl"].metadata[0].name
  }

  data = {
    username = "svc_ietl_copy_analytics_etl"
    password = data.google_secret_manager_secret_version.mssql_ietl_copy_analytics_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_ietl_sold_channel_keywords_etl" {
  secret  = "mssql_ietl_sold_channel_keywords_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-ietl-sold-channel-keywords-etl-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-ietl-sold-channel-keywords-etl-dev"
    namespace = kubernetes_namespace.workload_identity["ietl-sold-channel-keywords-etl"].metadata[0].name
  }

  data = {
    username = "svc_ietl_sold_channel_keywords_etl"
    password = data.google_secret_manager_secret_version.mssql_ietl_sold_channel_keywords_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_riei_combo_classification_etl" {
  secret  = "mssql_riei_combo_classification_etl"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-riei-combo-classification-etl-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-riei-combo-classification-etl-dev"
    namespace = kubernetes_namespace.workload_identity["riei-combo-classification-etl"].metadata[0].name
  }

  data = {
    username = "svc_riei_combo_classification_etl"
    password = data.google_secret_manager_secret_version.mssql_riei_combo_classification_etl.secret_data
  }
}

data "google_secret_manager_secret_version" "ws_svc_ietl_inputfile_transform" {
  secret  = "ws_svc_ietl_inputfile_transform"
  project = local.project
}

resource "kubernetes_secret" "de-ws-ietl-input-file-etl-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-ws-ietl-input-file-etl-dev"
    namespace = kubernetes_namespace.workload_identity["ietl-input-file-etl"].metadata[0].name
  }

  data = {
    domain   = "rasgcp"
    username = "svc_ietl_inputfile_t"
    password = data.google_secret_manager_secret_version.ws_svc_ietl_inputfile_transform.secret_data
  }
}

data "google_secret_manager_secret_version" "mssql_svc_sales_salesforce_reporting" {
  secret  = "mssql_svc_sales_salesforce_reporting"
  project = local.project
}

resource "kubernetes_secret" "de-mssql-svc-sales-salesforce-reporting-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-mssql-svc-sales-salesforce-reporting-dev"
    namespace = kubernetes_namespace.workload_identity["sales-salesforce-reporting"].metadata[0].name
  }

  data = {
    username = "svc_sales_salesforce_reporting"
    password = data.google_secret_manager_secret_version.mssql_svc_sales_salesforce_reporting.secret_data
  }
}

data "google_secret_manager_secret_version" "mascus_leads_api_credentials_secret" {
  secret  = "mascus_leads_api_credentials"
  project = local.project
}

resource "kubernetes_secret" "mascus_leads_api_credentials_secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "rb-list-views-etl",
    "rb-list-web-leads-etl",
  ])

  metadata {
    name      = "mascus-leads-api-credentials"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.mascus_leads_api_credentials_secret.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.mascus_leads_api_credentials_secret.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "mascus_api_credentials_secret" {
  secret  = "mascus_api_credentials"
  project = local.services
}

resource "kubernetes_secret" "mascus_api_credentials_secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "rb-taxonomy-etl",
  ])

  metadata {
    name      = "mascus-api-credentials"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.mascus_api_credentials_secret.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.mascus_api_credentials_secret.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "record360-03-dev-record360rw_credentials_secret" {
  secret  = "record360-03-dev-record360rw"
  project = local.project
}

resource "kubernetes_secret" "record360-03-dev-record360rw_credentials_secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "appraisals-api",
  ])

  metadata {
    name      = "record360-03-dev-record360rw"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.record360-03-dev-record360rw_credentials_secret.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.record360-03-dev-record360rw_credentials_secret.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "kafka_appraisals_secret" {
  secret  = "kafka-appraisals"
  project = local.project
}

resource "kubernetes_secret" "kafka_appraisals_secret_secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "appraisals-api",
  ])

  metadata {
    name      = "kafka-appraisals"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.kafka_appraisals_secret.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.kafka_appraisals_secret.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "salesforce_client_api_secret" {
  secret  = "salesforce_client_api"
  project = local.project
  version = 5
}

resource "kubernetes_secret" "salesforce_client_api_secret_secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "appraisals-api",
  ])

  metadata {
    name      = "salesforce-client-api"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.salesforce_client_api_secret.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.salesforce_client_api_secret.secret_data), "client_secret")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.salesforce_client_api_secret.secret_data), "client_password")
    username = lookup(jsondecode(data.google_secret_manager_secret_version.salesforce_client_api_secret.secret_data), "client_username")
  }
}


data "google_secret_manager_secret_version" "pgbouncer-platform01-dev-client-cert-secret" {
  secret  = "pgbouncer_platform01_dev_client_cert"
  project = local.services
}

resource "kubernetes_secret" "pgbouncer-platform01-dev-client-cert-secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "client-etl",
    "equipment-syndication-etl",
    "fleet-manager-backfill",
    "fleet-manager-partition-management",
    "fleet-manager-users-etl",
    "ims-fleet-refresh",
    "rb-list-views-etl",
    "rb-list-web-leads-etl",
    "sales-rfm-user-config-reporting",
  ])

  metadata {
    name      = "pgbouncer-platform01-dev-client-cert"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    SSL_CERT = data.google_secret_manager_secret_version.pgbouncer-platform01-dev-client-cert-secret.secret_data
  }
}

data "google_secret_manager_secret_version" "pgbouncer-platform01-dev-client-key-secret" {
  secret  = "pgbouncer_platform01_dev_client_key"
  project = local.services
}

resource "kubernetes_secret" "pgbouncer-platform01-dev-client-key-secret" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "client-etl",
    "equipment-syndication-etl",
    "fleet-manager-backfill",
    "fleet-manager-partition-management",
    "fleet-manager-users-etl",
    "ims-fleet-refresh",
    "rb-list-views-etl",
    "rb-list-web-leads-etl",
    "sales-rfm-user-config-reporting",
  ])

  metadata {
    name      = "pgbouncer-platform01-dev-client-key"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    SSL_KEY = data.google_secret_manager_secret_version.pgbouncer-platform01-dev-client-key-secret.secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_kafka_rb" {
  for_each = toset([
    "susttest",
    "projtest",
    "uat",
  ])
  secret  = "dbuser_kafka_rb_${each.key}"
  project = local.services
}

resource "kubernetes_secret" "dbuser_kafka_rb" {
  provider = kubernetes.composer-jobs-v3
  for_each = data.google_secret_manager_secret_version.dbuser_kafka_rb

  metadata {
    name      = "rb-kafka-${each.key}"
    namespace = kubernetes_namespace.workload_identity["rb-taxonomy-etl"].metadata[0].name
  }

  data = {
    kafka_api_key          = lookup(jsondecode(each.value.secret_data), "kafka_api_key")
    kafka_api_secret       = lookup(jsondecode(each.value.secret_data), "kafka_api_secret")
    schema_registry_key    = lookup(jsondecode(each.value.secret_data), "schema_registry_key")
    schema_registry_secret = lookup(jsondecode(each.value.secret_data), "schema_registry_secret")
  }
}

data "google_secret_manager_secret_version" "sales_rb_list_views_etl_auth0_credentials" {
  secret  = "sales_rb_list_views_etl_auth0_credentials"
  project = local.services
}

resource "kubernetes_secret" "de-rb-list-views-auth0-credentials-prod" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-rb-list-views-auth0-credentials-dev"
    namespace = kubernetes_namespace.workload_identity["rb-list-views-etl"].metadata[0].name
  }

  data = {
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rb_list_views_etl_auth0_credentials.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rb_list_views_etl_auth0_credentials.secret_data), "client_secret")
  }
}

resource "kubernetes_secret" "listing-sync-monitor-auth0-credentials-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "listing-sync-monitor-auth0-credentials"
    namespace = kubernetes_namespace.workload_identity["equipment-sync-monitor"].metadata[0].name
  }

  data = {
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rb_list_views_etl_auth0_credentials.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.sales_rb_list_views_etl_auth0_credentials.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "sales_google_analytics_page_views_etl_auth0_credentials" {
  secret  = "sales_google_analytics_page_views_etl_auth0_credentials"
  project = local.services
}

resource "kubernetes_secret" "de-sales-google-analytics-page-views-auth0-credentials-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-sales-google-analytics-page-views-auth0-credentials-dev"
    namespace = kubernetes_namespace.workload_identity["sales-google-analytics-page-views"].metadata[0].name
  }

  data = {
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.sales_google_analytics_page_views_etl_auth0_credentials.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.sales_google_analytics_page_views_etl_auth0_credentials.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "valuations_cms_auth0_credentials" {
  secret  = "valuations_cms_auth0_credentials"
  project = local.services
}

resource "kubernetes_secret" "de-cms-auth0-credentials-dev" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "expanded-equipment-etl",
    "appraisal-cms-canary-etl",
    "smartequip-parts-book-etl",
  ])

  metadata {
    name      = "de-cms-auth0-credentials-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    client_id     = lookup(jsondecode(data.google_secret_manager_secret_version.valuations_cms_auth0_credentials.secret_data), "client_id")
    client_secret = lookup(jsondecode(data.google_secret_manager_secret_version.valuations_cms_auth0_credentials.secret_data), "client_secret")
  }
}

data "google_secret_manager_secret_version" "appraisals-dev-rw" {
  secret  = "appraisals-dev-rw"
  project = local.project
}

resource "kubernetes_secret" "appraisals-dev-rw" {
  provider = kubernetes.composer-jobs-v3

  for_each = toset([
    "appraisals-api",
    "inspections-etl"
  ])

  metadata {
    name      = "appraisals-dev-rw"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals-dev-rw.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.appraisals-dev-rw.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "dcs-api-gfs04-dev-user" {
  secret  = "dcs-api-gfs04-dev-user"
  project = local.project
}

resource "kubernetes_secret" "dcs-api-gfs04-dev-user" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "appraisals-dcs-api",
  ])
  metadata {
    name      = "dcs-gfs04-dev-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.dcs-api-gfs04-dev-user.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.dcs-api-gfs04-dev-user.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "dcs_postgres_dcs_api_user_dev" {
  secret  = "dcs_postgres_dcs_api_user_dev"
  project = local.project
}

resource "kubernetes_secret" "appraisals-dcs-api-db-user" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "appraisals-dcs-api",
  ])
  metadata {
    name      = "dcs-postgres-dcs-api-user"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.dcs_postgres_dcs_api_user_dev.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.dcs_postgres_dcs_api_user_dev.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "analytics_classify_export_credentials" {
  secret  = "analytics_classify_export_credentials"
  project = local.services
}

resource "kubernetes_secret" "analytics-classify-import-ss-credentials-dev" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "analytics-ss-import",
  ])

  metadata {
    name      = "analytics-classify-import-ss-credentials-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_classify_export_credentials.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.analytics_classify_export_credentials.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "valuations_wells_fargo_ssh_key" {
  secret  = "valuations_wells_fargo_ssh_key"
  project = local.management_dev
}

resource "kubernetes_secret" "wellsfargo-data-export-ssh-key-dev" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "wellsfargo-data-export",
  ])

  metadata {
    name      = "wellsfargo-data-export-ssh-key-dev"
    namespace = kubernetes_namespace.workload_identity[each.key].metadata[0].name
  }
  data = {
    SSH_KEY = data.google_secret_manager_secret_version.valuations_wells_fargo_ssh_key.secret_data
  }
}

data "google_secret_manager_secret_version" "alloydb-dev-admin" {
  secret  = "alloydb-dev-admin"
  project = local.management_dev
}

resource "kubernetes_secret" "alloydb-dev-admin" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "postgresql-create-db-users",
    "pglogical-monitoring-rfm101"
  ])

  metadata {
    name      = "alloydb-dev-admin"
    namespace = each.key
  }
  data = {
    password = data.google_secret_manager_secret_version.alloydb-dev-admin.secret_data
    username = "admin"
  }
}

data "google_secret_manager_secret_version" "alloydb_rfm1001_dev_admin" {
  secret  = "alloydb-rfm1001-admin-dev"
  project = local.services_dev
}

resource "kubernetes_secret" "alloydb_rfm1001_dev_admin" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "postgresql-create-db-users",
  ])

  metadata {
    name      = "alloydb-rfm1001-admin-dev"
    namespace = each.key
  }
  data = {
    password = data.google_secret_manager_secret_version.alloydb_rfm1001_dev_admin.secret_data
    username = "admin"
  }
}

data "google_secret_manager_secret_version" "airflow_api_user" {
  secret  = "astronomer-airflow-api-key-${var.environment}"
  project = local.terraform_admin
}

resource "kubernetes_secret" "airflow_api_user" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "rebasing-automated-run"
  ])

  metadata {
    name      = "airflow-api-key-${var.environment}"
    namespace = each.key
  }
  data = {
    password = data.google_secret_manager_secret_version.airflow_api_user.secret_data
    username = "admin"
  }
}

data "google_secret_manager_secret_version" "dbuser_replication_lag_user" {
  secret  = "dbuser_replication_lag_user"
  project = local.services_dev
}

resource "kubernetes_secret" "dbuser_replication_lag_user" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "pglogical-monitoring-rfm101"
  ])

  metadata {
    name      = "dbuser-replication-lag-user"
    namespace = each.key
  }
  data = {
    password = data.google_secret_manager_secret_version.dbuser_replication_lag_user.secret_data
    username = "replication_lag_user"
  }
}

data "google_secret_manager_secret_version" "platform-valuation-base-image-webhook-trigger" {
  secret  = "platform-valuation-base-image-webhook-trigger"
  project = local.project
}

resource "kubernetes_secret" "platform-valuation-base-image-webhook-trigger" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "ml-for-rouse-etl",
    "appraisal-valuation-etl"
  ])

  metadata {
    name      = "platform-valuation-base-image-webhook-trigger"
    namespace = each.key
  }
  data = {
    webhook = data.google_secret_manager_secret_version.platform-valuation-base-image-webhook-trigger.secret_data
  }
}

data "google_secret_manager_secret_version" "platform-valuation-base-image-version" {
  secret  = "platform-valuation-base-image-version"
  project = local.images
}

data "google_secret_manager_secret_version" "platform-valuation-secondary-boot-config" {
  secret  = "platform-valuation-secondary-boot-config"
  project = local.services
}

data "google_secret_manager_secret_version" "platform-valuation-ci-trigger" {
  secret  = "platform-valuation-ci-trigger"
  project = local.project
}

resource "kubernetes_secret" "platform-valuation-ci-trigger" {
  provider = kubernetes.composer-jobs-v3
  for_each = toset([
    "platform-valuation-node-pools"
  ])

  metadata {
    name      = "platform-valuation-ci-trigger"
    namespace = each.key
  }
  data = {
    webhook = data.google_secret_manager_secret_version.platform-valuation-ci-trigger.secret_data
  }
}

data "google_secret_manager_secret_version" "sales_fleet_manager_postgres" {
  secret  = "sales_fleet_manager_postgres"
  project = local.project
}

resource "kubernetes_secret" "sales-fleet-manager-postgres" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "sales-fleet-manager-postgres"
    namespace = "sales-fleet-manager-postgres"
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.sales_fleet_manager_postgres.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.sales_fleet_manager_postgres.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "rfm_sales_fleet_manager_user" {
  secret  = "dbuser_sales_fleet_manager"
  project = local.services
}

resource "kubernetes_secret" "sales-fleet-manager-user" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "sales-fleet-manager-user"
    namespace = "sales-fleet-manager-postgres"
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.rfm_sales_fleet_manager_user.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.rfm_sales_fleet_manager_user.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "rfm-analytics-api-framework" {
  secret = "analytics-api-framework"
  project = local.qa_tools
}

resource "kubernetes_secret" "rfm-analytics-api-credentials" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "analytics-api-framework"
    namespace = "rental-insights-data-integrity-checks"
  }

  data = {
    analytics_credentials = data.google_secret_manager_secret_version.rfm-analytics-api-framework.secret_data
  }
}


data "google_secret_manager_secret_version" "rfm-qa-dev-secrets" {
  for_each = toset([
    "api-client-id",
    "api-client-secret",
    "sales-password-dev"
  ])
  secret = each.key
  project = local.rfm_qa_dev
}

resource "kubernetes_secret" "rfm-qa-dev-api-client-id" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "api-client-id"
    namespace = "rental-insights-data-integrity-checks"
  }

  data = {
    client_id = data.google_secret_manager_secret_version.rfm-qa-dev-secrets["api-client-id"].secret_data
  }
}

resource "kubernetes_secret" "rfm-qa-dev-api-client-secret" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "api-client-secret"
    namespace = "rental-insights-data-integrity-checks"
  }

  data = {
    client_secret = data.google_secret_manager_secret_version.rfm-qa-dev-secrets["api-client-secret"].secret_data
  }
}

resource "kubernetes_secret" "rfm-qa-dev-sales-password-dev" {
  provider = kubernetes.composer-jobs-v3
  metadata {
    name      = "sales-password-dev"
    namespace = "rental-insights-data-integrity-checks"
  }

  data = {
    sales_credentials = data.google_secret_manager_secret_version.rfm-qa-dev-secrets["sales-password-dev"].secret_data
  }
}

# Shardless Secrets
data "google_secret_manager_secret_version" "confluent-kafka-dev-sa-key" {
  secret  = "confluent-kafka-dev-sa-key"
  project = local.services
}

data "google_secret_manager_secret_version" "confluent-kafka-dev-sa-schema-reg-key" {
  secret  = "confluent-kafka-dev-sa-schema-reg-key"
  project = local.services
}

data "google_secret_manager_secret_version" "confluent-kafka-dev-sa-schema-reg-secret" {
  secret  = "confluent-kafka-dev-sa-schema-reg-secret"
  project = local.services
}

data "google_secret_manager_secret_version" "confluent-kafka-dev-sa-secret" {
  secret  = "confluent-kafka-dev-sa-secret"
  project = local.services
}

data "google_secret_manager_secret_version" "enterprise_domain_service_confluent_kafka_dev_sa_secret" {
  secret  = "confluent-kafka-dev-sa-secret"
  project = local.services
}

data "google_secret_manager_secret_version" "platform_classification_api_auth" {
  secret  = "platform_classification_auth_token"
  project = local.services
}

data "google_secret_manager_secret_version" "platform_user_config_api_auth" {
  secret  = "platform_user_config_auth_token"
  project = local.services
}

data "google_secret_manager_secret_version" "sales_classification_api_auth" {
  secret  = "sales_classification_auth_token"
  project = local.services
}

data "google_secret_manager_secret_version" "sales_fleet_manager_api_auth" {
  secret  = "sales_fleet_manager_auth_token"
  project = local.services
}

data "google_secret_manager_secret_version" "sales_rfm_api_static_auth_token" {
  secret  = "sales_rfm_api_static_auth_token"
  project = local.services
}

data "google_secret_manager_secret_version" "ims-shard-map-gcp" {
  secret  = "ims_shard_map"
  project = local.services
}

data "google_secret_manager_secret_version" "alloy-shard-map" {
  secret  = "alloy_shard_map"
  project = local.services
}


data "google_secret_manager_secret_version" "sales_fleet_manager_pagespeed_api_key_pipeline" {
  secret  = "sales_fleet_manager_pagespeed_api_key"
  project = local.services
}

data "google_secret_manager_secret_version" "sales_fleet_manager_dbuser_rmf_core_api" {
  secret  = "dbuser_sales_fleet_manager"
  project = local.services
}

data "google_secret_manager_secret_version" "sales_fleet_manager_dbuser_rfm_ingest" {
  secret  = "dbuser_rfm_ims_fleet_ingest_etl"
  project = local.services
}

data "google_secret_manager_secret_version" "sales_fleet_manager_dbuser_rfm_refresh" {
  secret  = "dbuser_rfm_ims_fleet_refresh_etl"
  project = local.services
}

resource "kubernetes_secret" "de-webshop-pagespeed-metrics-pagespeed-api-key" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-webshop-pagespeed-metrics-pagespeed-api-key-${var.environment}"
    namespace = kubernetes_namespace.workload_identity["webshop-pagespeed-metrics"].metadata[0].name
  }

  data = {
    token = data.google_secret_manager_secret_version.sales_fleet_manager_pagespeed_api_key_pipeline.secret_data
  }
}

# data integrity checks airflow jobs private
resource "kubernetes_secret" "rfm-analytics-api-credentials-airflow-private" {
  provider = kubernetes.airflow-jobs-private
  metadata {
    name      = "analytics-api-framework"
    namespace = "rental-insights-data-integrity-checks"
  }

  data = {
    analytics_credentials = data.google_secret_manager_secret_version.rfm-analytics-api-framework.secret_data
  }
}

resource "kubernetes_secret" "rfm-qa-dev-api-client-id-airflow-private" {
  provider = kubernetes.airflow-jobs-private
  metadata {
    name      = "api-client-id"
    namespace = "rental-insights-data-integrity-checks"
  }

  data = {
    client_id = data.google_secret_manager_secret_version.rfm-qa-dev-secrets["api-client-id"].secret_data
  }
}

resource "kubernetes_secret" "rfm-qa-dev-api-client-secret-airflow-private" {
  provider = kubernetes.airflow-jobs-private
  metadata {
    name      = "api-client-secret"
    namespace = "rental-insights-data-integrity-checks"
  }

  data = {
    client_secret = data.google_secret_manager_secret_version.rfm-qa-dev-secrets["api-client-secret"].secret_data
  }
}

resource "kubernetes_secret" "rfm-qa-dev-sales-password-dev-airflow-private" {
  provider = kubernetes.airflow-jobs-private
  metadata {
    name      = "sales-password-dev"
    namespace = "rental-insights-data-integrity-checks"
  }

  data = {
    sales_credentials = data.google_secret_manager_secret_version.rfm-qa-dev-secrets["sales-password-dev"].secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_ras_messages_global_client_metrics" {
  secret  = "dbuser_ras_messages_global_client_metrics"
  project = local.services
}

resource "kubernetes_secret" "de_client_metrics_ras_messages_global" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "de-client-metrics-ras-messages-global-${var.environment}"
    namespace = "client-metrics"
  }

  data = {
    username = lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_ras_messages_global_client_metrics.secret_data), "username")
    password = lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_ras_messages_global_client_metrics.secret_data), "password")
  }
}

data "google_secret_manager_secret_version" "sales_fleet_manager_tools_auth0_client_id" {
  secret  = "sales_fleet_manager_tools_auth0_client_id"
  project = local.services
}

data "google_secret_manager_secret_version" "sales_fleet_manager_tools_auth0_client_secret" {
  secret  = "sales_fleet_manager_tools_auth0_client_secret"
  project = local.services
}

data "google_secret_manager_secret_version" "platform_user_config_sso_integration_token" {
  secret  = "platform_user_config_sso_integration_token"
  project = local.services
}

data "google_secret_manager_secret_version" "platform_user_config_auth_token" {
  secret  = "platform_user_config_auth_token"
  project = local.services
}

resource "kubernetes_secret" "role-sync-monitor-credentials-dev" {
  provider = kubernetes.composer-jobs-v3

  metadata {
    name      = "role-sync-monitor-credentials-dev"
    namespace = kubernetes_namespace.workload_identity["role-permission-sync-monitor"].metadata[0].name
  }

  data = {
    user_management_auth0_client_id     = data.google_secret_manager_secret_version.sales_fleet_manager_tools_auth0_client_id.secret_data
    user_management_auth0_client_secret = data.google_secret_manager_secret_version.sales_fleet_manager_tools_auth0_client_secret.secret_data
    user_service_sso_token              = data.google_secret_manager_secret_version.platform_user_config_sso_integration_token.secret_data
    user_service_static_token           = data.google_secret_manager_secret_version.platform_user_config_auth_token.secret_data
  }
}

data "google_secret_manager_secret_version" "dbuser_smartequip" {
  for_each = toset([
    "appcontent_smartequip_etl",
    "appdata_smartequip_etl",
  ])
  secret  = "dbuser_smartequip_${each.key}"
  project = local.project
}

resource "kubernetes_secret" "dbuser_smartequip" {
  provider = kubernetes.airflow-jobs-private

  for_each = {
    appcontent_smartequip_etl = {
      namespace   = "appcontent-smartequip-etl",
      secret_name = "de-mysql-appcontent-smartequip-etl"
    }
    appdata_smartequip_etl = {
      namespace   = "appdata-smartequip-etl",
      secret_name = "de-mysql-appdata-smartequip-etl"
    }
  }

  metadata {
    name      = each.value["secret_name"]
    namespace = kubernetes_namespace.private_cluster_workload_identity[each.value["namespace"]].metadata[0].name
  }

  data = {
    password = lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_smartequip[each.key].secret_data), "password")
    username = lookup(jsondecode(data.google_secret_manager_secret_version.dbuser_smartequip[each.key].secret_data), "username")
  }
}

