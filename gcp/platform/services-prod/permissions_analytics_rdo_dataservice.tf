resource "google_secret_manager_secret_iam_member" "analytics_rdo_data_service_secrets" {
  for_each = toset([
      data.google_secret_manager_secret_version.analytics_rdo_dataservice_secret_key.secret,
      data.google_secret_manager_secret_version.analytics_rdo_dataservice_auth_token.secret,
  ])
  project   = local.project
  secret_id =  each.key
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${google_service_account.workload_identity["analytics-rdo-dataservice"].email}"
}

resource "google_project_iam_member" "analytics_rdo_data_service_permissions" {
  for_each = toset([
    "roles/cloudtrace.agent",
    "roles/cloudprofiler.agent",
    "roles/errorreporting.writer",
    "roles/logging.logWriter",
  ])
  project = local.project
  role    = each.key
  member  = "serviceAccount:${google_service_account.workload_identity["analytics-rdo-dataservice"].email}"
}