resource "google_bigquery_dataset" "bigquery_sales_notification_service_history" {
  dataset_id    = "sales_notification_service_history"
  friendly_name = "sales_notification_service_history"
  location      = "US"
  description   = "Dataset storing notification records"

  labels = {
    division = "sales"
  }

  access {
    role           = "WRITER"
    user_by_email  = google_service_account.workload_identity["sales-notification-service"].email
  }

  access {
    role           = "OWNER"
    group_by_email = "<EMAIL>"
  }
}

resource "google_bigquery_table" "notification_history" {
  dataset_id          = google_bigquery_dataset.bigquery_sales_notification_service_history.dataset_id
  deletion_protection = false

  table_id = "notification_history"

  labels = {
    division = "sales"
  }

  time_partitioning {
    type  = "MONTH"
    field = "notification_processed_at"
  }

  schema = jsonencode([
    {
      "name": "client_id_rouse_sales",
      "type": "INTEGER",
      "mode": "NULLABLE",
      "description": "The Rouse Sales client ID."
    },
    {
      "name": "identity_user_id",
      "type": "INTEGER",
      "mode": "NULLABLE",
      "description": "User ID in user service"
    },
    {
      "name": "user_frequency",
      "type": "STRING",
      "mode": "NULLABLE",
      "description": "User configured frequency for notifications"
    },
    {
      "name": "user_send_day",
      "type": "STRING",
      "mode": "NULLABLE",
      "description": "User configured Send day for weekly notifications"
    },
    {
      "name": "user_send_time",
      "type": "STRING",
      "mode": "NULLABLE",
      "description": "User configure time of notification delivery hh:mm:ss format"
    },
    {
      "name": "notification_type",
      "type": "STRING",
      "mode": "NULLABLE",
      "description": "Type of the notification, defined by User service"
    },
    {
      "name": "message",
      "type": "STRING",
      "mode": "NULLABLE",
      "description": "Body of the notification or email sent"
    },
    {
      "name": "recipient_email",
      "type": "STRING",
      "mode": "NULLABLE",
      "description": "For fleet digest notification"
    },
    {
      "name": "notification_created_at",
      "type": "TIMESTAMP",
      "mode": "NULLABLE",
      "description": "Timestamp of when the notification document was created"
    },
    {
      "name": "status",
      "type": "STRING",
      "mode": "NULLABLE",
      "description": "Processing status of the notification"
    },
    {
      "name": "processing_attempts",
      "type": "INTEGER",
      "mode": "NULLABLE",
      "description": "Number of attempts made to process notification"
    },
    {
      "name": "notification_processed_at",
      "type": "TIMESTAMP",
      "mode": "NULLABLE",
      "description": "Processing status of the notification"
    }
  ])
}