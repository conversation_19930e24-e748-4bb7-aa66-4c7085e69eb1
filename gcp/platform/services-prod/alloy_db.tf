locals {
  kubernetes_network_id = "projects/${data.google_project.project.number}/global/networks/${module.services_kubernetes_network.network_name}"
}

resource "google_alloydb_cluster" "rfm101-prod" {
  cluster_id       = "rfm101-prod"
  location         = "us-central1"
  database_version = "POSTGRES_15"

  network_config {
    network            = "projects/services-prod-e6fffc/global/networks/services-kubernetes-prod-network"
    allocated_ip_range = "service-network-alloydb"
  }

}

resource "google_alloydb_instance" "rfm101-prod" {
  cluster       = google_alloydb_cluster.rfm101-prod.name
  instance_id   = "rfm101-prod"
  instance_type = "PRIMARY"

  database_flags = {
    "alloydb.enable_pglogical"                      = "on",
    "alloydb.logical_decoding"                      = "on",
    "google_columnar_engine.enabled"                = "true",
    "google_columnar_engine.enable_vectorized_join" = "on",
    "work_mem"                                      = "1572864",
    "default_statistics_target"                     = "200",
    "max_parallel_workers"                          = "32",
    "max_parallel_workers_per_gather"               = "16",
    "max_sync_workers_per_subscription"             = "4",
    "max_logical_replication_workers"               = "8",
    "wal_receiver_timeout"                          = "600000",
    "checkpoint_timeout"                            = "1800",
    "max_wal_size"                                  = "64000",
    "enable_nestloop"                               = "on",
    "log_min_duration_statement"                    = "7000",
    "alloydb.enable_auto_explain"                   = "on",
    "auto_explain.log_min_duration"                 = "7000",
    "alloydb.iam_authentication"                    = "on",
  }

  machine_config {
    cpu_count = 16
  }
  lifecycle {
    ignore_changes = [
      database_flags["google_columnar_engine.relations"],
    ]
  }
  client_connection_config {
    ssl_config {
      ssl_mode = "ALLOW_UNENCRYPTED_AND_ENCRYPTED"
    }
  }
}

data "google_secret_manager_secret_version" "rfm101-prod-admin" {
  secret  = "rfm101-prod-admin"
  project = local.project
}

resource "google_alloydb_user" "alloydb_admin_user" {
  cluster        = google_alloydb_cluster.rfm101-prod.name
  user_id        = "admin"
  user_type      = "ALLOYDB_BUILT_IN"
  password       = data.google_secret_manager_secret_version.rfm101-prod-admin.secret_data
  database_roles = ["alloydbsuperuser", "pg_read_all_stats", ]
  depends_on     = [google_alloydb_instance.rfm101-prod]
}


resource "google_alloydb_cluster" "rdo101_cluster" {
  cluster_id       = "rdo101-${var.environment}"
  location         = var.region
  database_version = "POSTGRES_15"

  network_config {
    network            = local.kubernetes_network_id
    allocated_ip_range = "service-network-alloydb"
  }
  
  maintenance_update_policy {
    maintenance_windows {
          day = "MONDAY"

          start_time {
              hours   = 2
              minutes = 0
              nanos   = 0
              seconds = 0
            }
    }
  }
    
  automated_backup_policy {
    location      = "us-central1"
    backup_window = "14400s"
    enabled       = false
    weekly_schedule {
      days_of_week = ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"]
      start_times {
        hours   = 5 # 5:00 UTC == 10:00 PM PT
        minutes = 0
        seconds = 0
        nanos   = 0
      }
    }
    quantity_based_retention {
      count = 4
    }
  }

  continuous_backup_config {
    enabled              = true
    recovery_window_days = 7
  }
}

resource "google_alloydb_instance" "rdo101" {
  cluster           = google_alloydb_cluster.rdo101_cluster.name
  instance_id       = "rdo101-${var.environment}"
  instance_type     = "PRIMARY"
  availability_type = "REGIONAL"

  database_flags = {
    "google_columnar_engine.enabled"                = "true",
    "google_columnar_engine.enable_vectorized_join" = "on",
    "statement_timeout"                             = "0",
    "alloydb.iam_authentication"                    = "on",
  }

  network_config {
    enable_public_ip = false
  }

  machine_config {
    cpu_count = 8
  }

  client_connection_config {
    ssl_config {
      ssl_mode = "ENCRYPTED_ONLY"
    }
  }

  lifecycle {
    ignore_changes = [
      machine_config[0].cpu_count,
      network_config
    ]
  }
}

data "google_secret_manager_secret_version" "rdo101_admin" {
  secret  = "rdo101-admin"
  project = local.project
}

resource "google_alloydb_user" "rdo101_admin_user" {
  cluster        = google_alloydb_cluster.rdo101_cluster.name
  user_id        = "admin"
  user_type      = "ALLOYDB_BUILT_IN"
  password       = data.google_secret_manager_secret_version.rdo101_admin.secret_data
  database_roles = ["alloydbsuperuser", "pg_read_all_stats", "pg_write_all_data", "root"]
  depends_on     = [google_alloydb_instance.rdo101]
}

resource "google_compute_address" "pgbouncer_rdo101_ip" {
  name         = "rdo101-pgbouncer-primary"
  region       = var.region
  project      = local.project
  address_type = "INTERNAL"
  subnetwork   = module.services_kubernetes_network.subnet_link
}

data "google_secret_manager_secret_version" "rdo101_root" {
  secret  = "rdo101-root"
  project = local.project
}

resource "google_alloydb_user" "rdo101_root_user" {
  cluster        = google_alloydb_cluster.rdo101_cluster.name
  user_id        = "root"
  user_type      = "ALLOYDB_BUILT_IN"
  password       = data.google_secret_manager_secret_version.rdo101_root.secret_data
  database_roles = ["alloydbsuperuser", "pg_read_all_stats"]
  depends_on     = [google_alloydb_instance.rdo101]
}

data "google_secret_manager_secret_version" "rdo101_rdo_api" {
  secret  = "rdo101-rdo_api"
  project = local.project
}

resource "google_alloydb_user" "rdo101_rdo_api_user" {
  cluster    = google_alloydb_cluster.rdo101_cluster.name
  user_id    = "rdo_api"
  user_type  = "ALLOYDB_BUILT_IN"
  password   = data.google_secret_manager_secret_version.rdo101_rdo_api.secret_data
  depends_on = [google_alloydb_instance.rdo101]

  lifecycle {
    ignore_changes = [
      database_roles
    ]
  }
}
