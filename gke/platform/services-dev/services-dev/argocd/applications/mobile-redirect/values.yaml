web-application:
  containerPort: 80

  service:
    type: NodePort
    port: 80
    targetPort: 80

  serviceAccountName: sales-redirect-service

  replicaCount: 2

  livenessProbe:
    httpGet:
      path: /health
      port: 80
    periodSeconds: 30
    successThreshold: 1
    timeoutSeconds: 1
    failureThreshold: 2

  readinessProbe:
    httpGet:
      path: /health
      port: 80
    periodSeconds: 30
    successThreshold: 1
    timeoutSeconds: 1
    failureThreshold: 2

  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 4
    targetCPUUtilizationPercentage: 80

  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 200m
      memory: 256Mi

  volumeMounts:
    - mountPath: /etc/nginx/nginx.conf
      name: nginx-config
      subPath: nginx.conf

  volumes:
    - name: nginx-config
      configMap:
        name: mobile-redirect-web-application

  nodeSelector:
    auth-type: workload-identity
    ingress: external

  tolerations:
    - key: application
      operator: "Equal"
      value: "web"
      effect: "NoSchedule"
    - key: workload-identity
      operator: "Equal"
      value: "enabled"
      effect: "NoSchedule"
    - key: ingress
      operator: "Equal"
      value: "external"
      effect: "NoSchedule"

  config:
    enabled: true
    name: mobile-redirect-nginx-config
    data:
      nginx.conf: |
        user nginx;
        worker_processes auto;
        error_log /var/log/nginx/error.log warn;
        pid /var/run/nginx.pid;

        events {
            worker_connections 1024;
        }

        http {
            include /etc/nginx/mime.types;
            default_type application/octet-stream;

            log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                            '$status $body_bytes_sent "$http_referer" '
                            '"$http_user_agent" "$http_x_forwarded_for"';

            access_log /var/log/nginx/access.log main;

            sendfile on;
            keepalive_timeout 65;

            # Mobile detection regex
            map $http_user_agent $is_mobile {
                default 0;
                "~*android|avantgo|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od|ad)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino" 1;
            }

            server {
                listen 80;
                server_name ~^redirect\.(?<stage>\w+)\.rouseservices\.com$;

                # Health check endpoint
                location /health {
                    return 200 "OK\n";
                    add_header Content-Type text/plain;
                }

                # Main redirect logic
                location / {
                    # Preserve query string
                    if ($is_mobile) {
                        return 302 https://mobile-assets.$stage.rouseservices.com$request_uri;
                    }
                    return 302 https://rfm.$stage.rousesales.com$request_uri;
                }

                # Specific path handling for fleet-digest
                location /fleet-digest {
                    if ($is_mobile) {
                        return 302 https://mobile-assets.$stage.rouseservices.com/fleet-digest$is_args$args;
                    }
                    return 302 https://rfm.$stage.rousesales.com/fleet-digest$is_args$args;
                }
            }
        }

  appVersion: 1.25.3
  image:
    repository: nginx
    tag: 1.25.3-alpine

  ingress:
    annotations:
      kubernetes.io/ingress.class: "nginx"
    enabled: true
    hosts:
      - host: "redirect.develop.rouseservices.com"
        paths:
          - path: /
      - host: "redirect.alpha.rouseservices.com"
        paths:
          - path: /
      - host: "redirect.beta.rouseservices.com"
        paths:
          - path: /
      - host: "redirect.charlie.rouseservices.com"
        paths:
          - path: /
      - host: "redirect.echo.rouseservices.com"
        paths:
          - path: /
      - host: "redirect.foxtrot.rouseservices.com"
        paths:
          - path: /
      - host: "redirect.insi.rouseservices.com"
        paths:
          - path: /
      - host: "redirect.stage.rouseservices.com"
        paths:
          - path: /
      - host: "redirect.ppe.rouseservices.com"
        paths:
          - path: /
    tls:
      - hosts:
          - "redirect.develop.rouseservices.com"
        secretName: develop-rouseservices-com
      - hosts:
          - "redirect.alpha.rouseservices.com"
        secretName: alpha-rouseservices-com
      - hosts:
          - "redirect.beta.rouseservices.com"
        secretName: beta-rouseservices-com
      - hosts:
          - "redirect.charlie.rouseservices.com"
        secretName: charlie-rouseservices-com
      - hosts:
          - "redirect.echo.rouseservices.com"
        secretName: echo-rouseservices-com
      - hosts:
          - "redirect.foxtrot.rouseservices.com"
        secretName: foxtrot-rouseservices-com
      - hosts:
          - "redirect.insi.rouseservices.com"
        secretName: insi-rouseservices-com
      - hosts:
          - "redirect.stage.rouseservices.com"
        secretName: stage-rouseservices-com
      - hosts:
          - "redirect.ppe.rouseservices.com"
        secretName: ppe-rouseservices-com

  branch_name: "dev"
  github_repo_url: ""
  project_name: "mobile-redirect"
  environment_name: "dev" 
