web-application:
  containerPort: 80

  service:
    type: NodePort
    port: 80
    targetPort: 80

  serviceAccountName: sales-redirect-service

  replicaCount: 2

  livenessProbe:
    httpGet:
      path: /health
      port: 80
    periodSeconds: 30
    successThreshold: 1
    timeoutSeconds: 1
    failureThreshold: 2

  readinessProbe:
    httpGet:
      path: /health
      port: 80
    periodSeconds: 30
    successThreshold: 1
    timeoutSeconds: 1
    failureThreshold: 2

  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 4
    targetCPUUtilizationPercentage: 80

  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 200m
      memory: 256Mi

  volumeMounts:
    - mountPath: /etc/nginx/nginx.conf
      name: nginx-config
      subPath: nginx.conf

  volumes:
    - name: nginx-config
      configMap:
        name: mobile-redirect-web-application

  nodeSelector:
    auth-type: workload-identity

  tolerations:
    - key: application
      operator: "Equal"
      value: "web"
      effect: "NoSchedule"
    - key: workload-identity
      operator: "Equal"
      value: "enabled"
      effect: "NoSchedule"

  config:
    enabled: true
    name: mobile-redirect-nginx-config
    data:
      nginx.conf: |
        user nginx;
        worker_processes auto;
        error_log /var/log/nginx/error.log warn;
        pid /var/run/nginx.pid;

        events {
            worker_connections 1024;
        }

        http {
            include /etc/nginx/mime.types;
            default_type application/octet-stream;

            log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                            '$status $body_bytes_sent "$http_referer" '
                            '"$http_user_agent" "$http_x_forwarded_for"';

            access_log /var/log/nginx/access.log main;

            sendfile on;
            keepalive_timeout 65;

            # Mobile detection regex
            map $http_user_agent $is_mobile {
                default 0;
                "~*android|avantgo|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od|ad)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino" 1;
            }

            # Main server
            server {
                listen 80;
                server_name redirect.rouseservices.com;

                # Health check endpoint
                location /health {
                    return 200 "OK\n";
                    add_header Content-Type text/plain;
                }

                # Main redirect logic
                location / {
                    # Preserve query string
                    if ($is_mobile) {
                        return 302 https://mobile-assets.rouseservices.com$request_uri;
                    }
                    return 302 https://portal.rouseservices.com$request_uri;
                }

                # Specific path handling for fleet-digest
                location /fleet-digest {
                    if ($is_mobile) {
                        return 302 https://mobile-assets.rouseservices.com/fleet-digest$is_args$args;
                    }
                    return 302 https://portal.rouseservices.com/fleet-digest$is_args$args;
                }
            }
        }

  appVersion: 1.25.3
  image:
    repository: nginx
    tag: 1.25.3-alpine

  ingress:
    annotations:
      kubernetes.io/ingress.class: "nginx"
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    enabled: true
    hosts:
      - host: "redirect.rouseservices.com"
        paths:
          - path: /
    tls:
      - secretName: rouseservices-com-tls

  branch_name: "prod"
  github_repo_url: ""
  project_name: "mobile-redirect"
  environment_name: "prod" 