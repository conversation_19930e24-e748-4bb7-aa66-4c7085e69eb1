pgbouncer:
  replicaCount: 2
  resources:
    requests:
      cpu: "1"
      memory: "128Mi"

  internalPort: 5432
  servicePort: 5432
  antiAffinity: soft
  tolerations:
    - key: application
      operator: "Equal"
      value: "web"
      effect: "NoSchedule"
    - key: workload-identity
      operator: "Equal"
      value: "enabled"
      effect: "NoSchedule"
  nodeSelector:
    auth-type: workload-identity

  poolMode: session

  budget:
    minAvailable: 0

  image:
    repository: us-docker.pkg.dev/images-4a3fb6/gcr.io/pgbouncer
    tag: "2.0"
    pullPolicy: Always

  imagePullSecrets: []

  databases:
    "*":
      host: &ip **********
      port: &port 5432
    rfm__mt:
      host: *ip
      port: *port
      max_db_connections: 300
      pool_size: 250
      reserve_pool_size: 50

  # Admin users
  users:
    root: true
    sales_fleet_manager_tools: true

  # [users] block configuration
  userConfig:
    ims_fleet_ingest_etl: "max_user_connections=50"
    rfm_user_config_api: "max_user_connections=50"
    sales_fleet_manager: "max_user_connections=750"
    equipment_rfm_api: "max_user_connections=300"

  settings:
    auth_type: md5

  serverTLSSSLMode: verify-ca
  clientTLSSSLMode: verify-ca

  existingUsersSecret: "pgbouncer-pgbouncer-secret-userlist-txt"
  existingServerKey: "pgbouncer-cert"
  existingServerCert: "pgbouncer-cert"
  existingServerCa: "pgbouncer-cert"
  existingClientKey: "pgbouncer-cert-client"
  existingClientCert: "pgbouncer-cert-client"
  existingClientCa: "pgbouncer-cert-client"

  connectionLimits:
    maxClientConn: 20000
    maxUserConnections: 0 # unlimited
    defaultPoolSize: 100
    maxDbConnections: 400 # (1000 - replicas * 400)
    minPoolSize: 10
    reservePoolSize: 0
    reservePoolTimeout: 0

  spec:
    labels: {}
    annotations: {}

  labels: {}

  helm2selector: false

  logConnections: 1
  logDisconnections: 1
  logStats: 0
  logPoolerErrors: 1
  verbose: 0
  statsPeriod: 60
  serverLifetime: 3600
  serverIdleTimeout: 600

  customSettings:
    unix_socket_dir: "/tmp"
    max_prepared_statements: "7"

  service:
    enabled: true
    type: LoadBalancer
    name: ""
    loadBalancerIP: **************
    annotations:
      networking.gke.io/load-balancer-type: "Internal"

  global:
    namespacedDatabases: false

  pgbouncerExporter:
    enabled: false
    monitorUserSecret: "pgbouncer-pgbouncer-monitoring-user"
    port: 9127
    image:
      repository: prometheuscommunity/pgbouncer-exporter
      tag: v0.4.1
      pullPolicy: Always
    log:
      level: error
      format: logfmt
