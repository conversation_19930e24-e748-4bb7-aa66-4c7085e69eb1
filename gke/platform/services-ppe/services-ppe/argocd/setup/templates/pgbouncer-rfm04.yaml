apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: pgbouncer-rfm04-ppe
  namespace: argocd
spec:
  project: devops
  source:
    repoURL: https://github.com/RouseServices/infrastructure-v2.git
    targetRevision: HEAD
    path: gke/platform/services-ppe/services-ppe/argocd/applications/enterprise-pgbouncer-rfm04
    helm:
      releaseName: pgbouncer-rfm04-primary
      valueFiles:
        - rfm04r0-values.yaml
  destination:
    name: services-ppe
    namespace: enterprise-pgbouncer-rfm04
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=false
