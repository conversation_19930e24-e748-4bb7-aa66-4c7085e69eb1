apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: pgbouncer-rfm04-replica-ppe
  namespace: argocd
spec:
  project: devops
  source:
    repoURL: https://github.com/RouseServices/infrastructure-v2.git
    targetRevision: HEAD
    path: gke/platform/services-ppe/services-ppe/argocd/applications/enterprise-pgbouncer-rfm04
    helm:
      releaseName: pgbouncer-rfm04-replica
      valueFiles:
        - rfm04r1-values.yaml
  destination:
    name: services-ppe
    namespace: enterprise-pgbouncer-rfm04
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=false
