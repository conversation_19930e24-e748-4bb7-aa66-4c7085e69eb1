#!/usr/bin/env python

"""Train entity embedding
Usage:
    ./4_validation_public_cache.py [options]

    Preprocess raw data compute lookback windows and save to local or GCP 

Options:

    --env=ENV                  Specify enviroment: local, dev or prod. Ex: --env=local [default: local]
    --algo=ALGO                Specify algorithm: full or lite. Ex: --algo=lite [default: lite]
    --sample=N                 sample factor public cache table. Ex: --sample=0.10 (This takes 30 seconds)
    --model-version=ENV        Specify model version in public cache. Ex: --model-version=prod/1.7.0
    --frozen-month=YYYY-MM-DD  Specify frozen month. Ex: --frozen-month=2025-02-28 (Usually last day of the month)
    --upload                   Upload validation to GCP
"""

from typing import Dict,Tuple
import numpy as np
import pandas as pd
import semantic_version

from common.predictor import BatchPredictor
from common.ds_cache import GCPObjectHandler, LocalObjectHandler

from loguru import logger
import os
import sys
# import datetime as dt
#from datetime import datetime as dt
from datetime import datetime as dt, timedelta

from time import perf_counter

from google.cloud import bigquery
import os

from tabulate import tabulate

from docopt import docopt

from common.query_builder import QueryBuilder, PUBLIC_CACHE_TABLE
from common.utils import check_env_compliance, get_enviroment_name

# NOTE: for debug. run export LOG_LEVEL=DEBUG in terminal <== for debug messages  (default is INFO)
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

# Set up the logger
logger.remove()  # Remove any previously added
logger.add(sys.stdout, level=LOG_LEVEL)

PROJECT_ID = 'appraisals-data-dev-c55fa4'

LOG_NAMES_FOR_TMP_DISABLE = [
"common.lookback",
"common.predictor",
"common.embed_cat",
"common.preprocessing",
"common.ds_cache",
"common.encoder"
]

def check_model_version_exist_in_public_cache(model_version: str) -> None:

    QUERY = f"""
        SELECT 
            DISTINCT model_version
        FROM `{PUBLIC_CACHE_TABLE}`
    """

    bigquery_client = bigquery.Client(project=PROJECT_ID)
    query_result = bigquery_client.query(QUERY)

    model_version_list = []

    for row in query_result:
        model_version_list.append(row.model_version)

    logger.info(f"model_version_list: {model_version_list}")

    assert model_version in model_version_list, f"env: {model_version} not in public cache table: {model_version_list}"

def check_env_and_model_version_compatibility(env, model_version) -> None:

    if get_enviroment_name(env) != 'prod':
        return

    env_version = env.split("/")[-1]
    model_version_number = model_version.split("/")[-1]

    env_parsed = semantic_version.Version(env_version)
    model_parsed = semantic_version.Version(model_version_number)

    assert env_parsed >= model_parsed, \
        f"--env must be greather than --model-version ({env_version}) is not >= {model_version_number})."
     
def get_published_date_based_on_prv_frozeen_month(_prv_frozen_month: str) -> str:

    prv_frozen_date = dt.strptime(_prv_frozen_month, "%Y-%m-%d")

    # NOTE: assumption frozen month is the last day of the month or near the end of the month
    first_day_of_next_month = prv_frozen_date + timedelta(days=3)
    
    logger.debug(f"First day of next month: {first_day_of_next_month}")
    
    # TODO: Review this later. Consider use calendar package or lubridate for python
    _next_next_month = (first_day_of_next_month.month + 1) % 13
    if _next_next_month == 0:
        _next_next_month = 1
        first_day_of_next_month = first_day_of_next_month.replace(year=first_day_of_next_month.year + 1)
    
    logger.debug(f"next next month: {_next_next_month}")
    
    _first_day_of_next_next_month_str = f"{first_day_of_next_month.year}-{_next_next_month:02d}-01"
    _first_day_of_next_next_month = dt.strptime(_first_day_of_next_next_month_str, "%Y-%m-%d")
    
    logger.debug(f"First day of next next month: {_first_day_of_next_next_month}")
    
    last_day_of_next_month = _first_day_of_next_next_month - timedelta(days=1)
    publish_date = last_day_of_next_month.strftime("%Y-%m-%d")

    return publish_date

def _load_data_from_public_cache_table(env:str,sample_factor: float = 0.10) -> pd.DataFrame:
    
    usa_sample_12year = can_sample_12year = gbr_sample_12year = int(25_000 * sample_factor)

    usa_sample_target_category = int(1_500 * sample_factor)

    usa_category_size = int(5_000 * sample_factor)
    can_category_size = int(2_000 * sample_factor)
    uk_category_size = int(3_000 * sample_factor)
    others_countries_size = int(1_000 * sample_factor)

    age_size = int(1_500 * sample_factor)

    query_builder = QueryBuilder()

    SPECIAL_QUERIES = query_builder.build_special_queries(env=env, country='usa', limit=usa_sample_target_category)
    USA_HIGH_SAMPLE_SIZE_MINI_EXC, USA_HIGH_SAMPLE_SIZE_TRUCKS, USA_OLD_CRANES_QUERY, USA_TARGET_CATEGORY_QUERY = SPECIAL_QUERIES 

    data_dict = {
        'usa':  query_builder.build_query( env=env, country='usa', limit=usa_sample_12year),
        'can': query_builder.build_query( env=env, country='can', limit=can_sample_12year),
        'gbr': query_builder.build_query(env=env, country='gbr', limit=gbr_sample_12year),
        'fra': query_builder.build_query(env=env, country='fra', limit=others_countries_size),
        'deu': query_builder.build_query(env=env, country='deu', limit=others_countries_size),
        'esp': query_builder.build_query(env=env, country='esp', limit=others_countries_size),
        'ita': query_builder.build_query(env=env, country='ita', limit=others_countries_size),
        'nld': query_builder.build_query(env=env, country='nld', limit=others_countries_size),
        'jpn': query_builder.build_query(env=env, country='jpn', limit=others_countries_size),
        'aus': query_builder.build_query(env=env, country='aus', limit=others_countries_size),

        # NOTE: usa
        'usa_target_category': USA_TARGET_CATEGORY_QUERY,
        'usa_old_cranes': USA_OLD_CRANES_QUERY,
        'usa_truck_tractors': query_builder.build_query(env=env, country='usa', limit=usa_category_size, category='truck tractors'),
        'usa_excavators': query_builder.build_query(env=env, country='usa', limit=usa_category_size, category='excavators'),
        'usa_wheel_loaders': query_builder.build_query(env=env, country='usa', limit=usa_category_size, category='wheel loaders'),
        'usa_telehandlers': query_builder.build_query(env=env, country='usa', limit=usa_category_size, category='telehandlers'),
        'usa_dozers': query_builder.build_query(env=env, country='usa', limit=usa_category_size, category='dozers'),
        'usa_forklift_trucks': query_builder.build_query(env=env, country='usa', limit=usa_category_size, category='forklift trucks'),
        'usa_compact_track_loaders': query_builder.build_query(env=env, country='usa', limit=usa_category_size, category='compact track loaders'),
        'usa_articulating_boom_lifts': query_builder.build_query(env=env, country='usa', limit=usa_category_size, category='articulating boom lifts'),
        'usa_articulated_dump_trucks': query_builder.build_query(env=env, country='usa', limit=usa_category_size, category='articulated dump trucks'),
        'usa_scissor_lifts': query_builder.build_query(env=env, country='usa', limit=usa_category_size, category='scissor lifts'),
        'usa_pickup_trucks': query_builder.build_query(env=env, country='usa', limit=usa_category_size, category='pickup trucks'),
        'usa_dump_trucks': query_builder.build_query(env=env, country='usa', limit=usa_category_size, category='dump trucks'),
        'usa_water_trucks': query_builder.build_query(env=env, country='usa', limit=usa_category_size, category='water trucks'),
        'usa_tractors': query_builder.build_query(env=env, country='usa', limit=usa_category_size, category='tractors'),
        'usa_storage_containers': query_builder.build_query(env=env, country='usa', limit=usa_category_size, category='storage containers'),
        'usa_high_sample_size_mini_exc': USA_HIGH_SAMPLE_SIZE_MINI_EXC,
        'usa_high_sample_size_trucks': USA_HIGH_SAMPLE_SIZE_TRUCKS,
        #'usa_new_equip':  USA_NEW_EQUIP_QUERY,
        'usa_age_0': query_builder.build_query_for_age(env=env, country='usa', limit=age_size, age=0, verbose=False),
        'usa_age_1': query_builder.build_query_for_age(env=env, country='usa', limit=age_size, age=1, verbose=False),
        'usa_age_2': query_builder.build_query_for_age(env=env, country='usa', limit=age_size, age=2, verbose=False),
        'usa_age_3': query_builder.build_query_for_age(env=env, country='usa', limit=age_size, age=3, verbose=False),
        'usa_age_4': query_builder.build_query_for_age(env=env, country='usa', limit=age_size, age=4, verbose=False),
        'usa_age_5': query_builder.build_query_for_age(env=env, country='usa', limit=age_size, age=5, verbose=False),
        'usa_construction': query_builder.build_cuts_query_for_price_index(env=env, country='usa', limit=usa_category_size,
                                                                             cuts='constructions', verbose=False),

        # NOTE: can
        'can_truck_tractors': query_builder.build_query(env=env, country='can', limit=can_category_size, category='truck tractors'),
        'can_excavators': query_builder.build_query( env=env, country='can', limit=can_category_size, category='excavators'),
        'can_scissor_lifts': query_builder.build_query( env=env, country='can', limit=can_category_size, category='scissor lifts'),
        'can_wheel_loaders': query_builder.build_query( env=env, country='can', limit=can_category_size, category='wheel loaders'),
        'can_telehandlers': query_builder.build_query( env=env, country='can', limit=can_category_size, category='telehandlers'),
        'can_dozers': query_builder.build_query( env=env, country='can', limit=can_category_size, category='dozers'),
        'can_pickup_trucks': query_builder.build_query( env=env, country='can', limit=can_category_size, category='pickup trucks'),
        'can_dump_trucks': query_builder.build_query( env=env, country='can', limit=can_category_size, category='dump trucks'),
        'can_water_trucks': query_builder.build_query( env=env, country='can', limit=can_category_size, category='water trucks'),

        'can_construction': query_builder.build_cuts_query_for_price_index(env=env, country='can', limit=usa_category_size, 
                                                                             cuts='constructions'),
        'can_age_0': query_builder.build_query_for_age(env=env, country='can', limit=age_size, age=0, verbose=False),
        'can_age_1': query_builder.build_query_for_age(env=env, country='can', limit=age_size, age=1, verbose=False),
        'can_age_2': query_builder.build_query_for_age(env=env, country='can', limit=age_size, age=2, verbose=False),
        'can_age_3': query_builder.build_query_for_age(env=env, country='can', limit=age_size, age=3, verbose=False),
        'can_age_4': query_builder.build_query_for_age(env=env, country='can', limit=age_size, age=4, verbose=False),
        'can_age_5': query_builder.build_query_for_age(env=env, country='can', limit=age_size, age=5, verbose=False),

        # NOTE: gbr
        'gbr_truck_tractors': query_builder.build_query(env=env, country='gbr', limit=uk_category_size, category='truck tractors'),
        'gbr_excavators': query_builder.build_query( env=env, country='gbr', limit=uk_category_size, category='excavators'),
        'gbr_wheel_loaders': query_builder.build_query( env=env, country='gbr', limit=uk_category_size, category='wheel loaders'),
        'gbr_telehandlers': query_builder.build_query( env=env, country='gbr', limit=uk_category_size, category='telehandlers'),
        'gbr_scissor_lifts': query_builder.build_query( env=env, country='gbr', limit=uk_category_size, category='scissor lifts'),
        'gbr_dozers': query_builder.build_query( env=env, country='gbr', limit=uk_category_size, category='dozers'),
        'gbr_forklift_trucks': query_builder.build_query( env=env, country='gbr', limit=uk_category_size, category='forklift trucks'),
        'gbr_compact_track_loaders': query_builder.build_query( env=env, country='gbr', limit=uk_category_size, category='compact track loaders'),
        'gbr_articulating_boom_lifts': query_builder.build_query( env=env, country='gbr', limit=uk_category_size, category='articulating boom lifts'),
        'gbr_articulated_dump_trucks': query_builder.build_query( env=env, country='gbr', limit=uk_category_size, category='articulated dump trucks'),
        'gbr_construction': query_builder.build_cuts_query_for_price_index(env=env, country='gbr', limit=uk_category_size,
                                                                            cuts='constructions'),  
        'gbr_age_0': query_builder.build_query_for_age(env=env, country='gbr', limit=age_size, age=0, verbose=False),
        'gbr_age_1': query_builder.build_query_for_age(env=env, country='gbr', limit=age_size, age=1, verbose=False),
        'gbr_age_2': query_builder.build_query_for_age(env=env, country='gbr', limit=age_size, age=2, verbose=False),
        'gbr_age_3': query_builder.build_query_for_age(env=env, country='gbr', limit=age_size, age=3, verbose=False),
        'gbr_age_4': query_builder.build_query_for_age(env=env, country='gbr', limit=age_size, age=4, verbose=False),
        'gbr_age_5': query_builder.build_query_for_age(env=env, country='gbr', limit=age_size, age=5, verbose=False),
    }

    bigquery_client = bigquery.Client(project=PROJECT_ID)
    data_list = []
    for query_type, query in data_dict.items():

        data = bigquery_client.query(query).to_dataframe()
        data['query_type'] = query_type

        logger.info(f"query_name: {query_type}: {data.shape[0]}")

        if data.shape[0] < 200:
            logger.warning(f"{query_type} has shape: {data.shape}")

        if data.shape[0] > 0:
            data_list.append(data)

    public_cache_data = pd.concat(data_list, ignore_index=True)

    public_cache_data['item_country'] = public_cache_data['item_country'].str.lower().str.strip()

    public_cache_data['retail_prediction'] = public_cache_data['retail_prediction'].astype(float)
    public_cache_data['auction_prediction'] = public_cache_data['auction_prediction'].astype(float)

    logger.info(f"public_cache_data.shape: {public_cache_data.shape}")
    logger.warning(f"public_cache_data unique countries: {public_cache_data.item_country.unique()}")

    return public_cache_data
 
def _load_continent_subcontinent_data() -> pd.DataFrame:

    QUERY = """
    SELECT DISTINCT 
        country_code as item_country,
        sub_continent as subcontinent,
        continent
    FROM 
        `appraisals-data-prod-707493.data_science_models.transactions`

    WHERE 

    continent IS NOT NULL AND sub_continent IS NOT NULL

    ORDER BY
        continent,
        sub_continent,
        country_code;
    """

    bigquery_client = bigquery.Client(project=PROJECT_ID)
    contry_to_continent_subcontinent_map = bigquery_client.query(QUERY).to_dataframe()
 
    contry_to_continent_subcontinent_map['item_country'] = contry_to_continent_subcontinent_map['item_country'].str.lower().str.strip()

    return contry_to_continent_subcontinent_map

def _enrich_data_and_get_predictions(target_data: pd.DataFrame, predictor) -> pd.DataFrame:
    
    retail_df = target_data.copy()
    auction_df = target_data.copy()
    
    retail_df['sale_type'] = "retail"
    auction_df['sale_type'] = "unreserved auction"
    
    synthetic_data = pd.concat([retail_df, auction_df], axis=0).reset_index(drop=True)
    
    y_pred, lookback_test = predictor.predict(synthetic_data)
    y_pred = y_pred.astype(int)

    # NOTE: Split the predictions back into 'retail' and 'unreserved auction'
    n = len(target_data)
    target_data['new_auction_prediction'] = y_pred[n:]
    target_data['new_retail_prediction'] = y_pred[:n]
    
    lookback_test = lookback_test[:n]

    return target_data, lookback_test
 
def _calculate_and_print_ratios(sub_data: pd.DataFrame, label: str, 
                                warning: str = None, verbose: bool = True) -> Dict[str, Tuple[int, float, float]]:
    
    curr_year = dt.now().year
    median_age = curr_year - sub_data['mfg_year'].median()
    prv_median_retail_price = sub_data['retail_prediction'].median()
    prv_median_auction_price = sub_data['auction_prediction'].median()

    # NOTE: mean pred ratio
    retails_preds_sum_ratio = sub_data['new_retail_prediction'].sum() / sub_data['retail_prediction'].sum()
    auctions_preds_sum_ratio = sub_data['new_auction_prediction'].sum() / sub_data['auction_prediction'].sum()
    
    retail_ratio_individual = sub_data['new_retail_prediction'] / sub_data['retail_prediction']
    auction_ratio_individual = sub_data['new_auction_prediction'] / sub_data['auction_prediction']
    
    # NOTE: Nth pred ratio percentile
    retail_10_percentile = np.percentile(retail_ratio_individual, 20)
    auction_10_percentile = np.percentile(auction_ratio_individual, 20)
    retail_90_percentile = np.percentile(retail_ratio_individual, 80)
    auction_90_percentile = np.percentile(auction_ratio_individual, 80)

    results = {
            f"{label}": (
                sub_data.shape[0],
                median_age,
                prv_median_retail_price,
                retail_10_percentile,
                retails_preds_sum_ratio,
                retail_90_percentile,
                prv_median_auction_price,
                auction_10_percentile,
                auctions_preds_sum_ratio,
                auction_90_percentile
            )
        }

    if verbose:
        print(f"{label} data shape: {sub_data.shape}")

        if warning:
            print(warning)
        
        print(" =====  retail")
        print(f"retail_10_percentile: {retail_10_percentile:.3f}")
        print(f"retails_preds_sum_ratio: {retails_preds_sum_ratio:.3f}")
        print(f"retail_90_percentile: {retail_90_percentile:.3f}")

        print()
        print(" =====  auction")
        print(f"auction_10_percentile: {auction_10_percentile:.3f}")
        print(f"auctions_preds_sum_ratio: {auctions_preds_sum_ratio:.3f}")
        print(f"auction_90_percentile: {auction_90_percentile:.3f}")
        print(" ================")

    return results

def _compute_prediction_ratios(target_data_with_preds: pd.DataFrame, verbose: bool = True) -> Dict[str, Tuple[int, float, float]]:

    result_dict = {}

    result_dict.update(_calculate_and_print_ratios(target_data_with_preds, "Global", verbose=verbose))

    warning_msg = None

    filters_warnings = {
        "USA": {'filter': target_data_with_preds.query_type == 'usa', 'warning': None},
       
        "CAN": {'filter': target_data_with_preds.query_type == 'can', 
                'warning':warning_msg},
        
        "UK": {'filter': target_data_with_preds.query_type == 'gbr', 
               'warning': warning_msg},
        
        "FRA": {'filter': target_data_with_preds.query_type == 'fra',
                'warning': warning_msg},

        "ITA": {'filter': target_data_with_preds.query_type == 'ita',
                'warning': warning_msg},

        "NLD": {'filter': target_data_with_preds.query_type == 'nld',
                'warning': warning_msg},

        "AUS": {'filter': target_data_with_preds.query_type == 'aus',
                'warning': warning_msg},

        "JPN": {'filter': target_data_with_preds.item_country == 'jpn',
                'warning': warning_msg},

        "DEU": {'filter': target_data_with_preds.item_country == 'deu',
                'warning': warning_msg},

        "ESP": {'filter': target_data_with_preds.item_country == 'esp',
                'warning': warning_msg},

        "USA_TARGET_CATEGORY": {'filter': target_data_with_preds.query_type == 'usa_target_category',
                                'warning':None},

        "USA_OLD_CRANES": {'filter': target_data_with_preds.query_type == 'usa_old_cranes',
                           'warning': None},

        "TRUCK_TRACTORS_USA": {'filter': (target_data_with_preds.query_type == 'usa_truck_tractors'),
                'warning': warning_msg},

        "EXCAVATORS_USA": {'filter': (target_data_with_preds.query_type == 'usa_excavators'),
                'warning': warning_msg},

        "WHEEL_LOADERS_USA": {'filter': (target_data_with_preds.query_type == 'usa_wheel_loaders'),
                'warning': warning_msg},

        "TELEHANDLERS_USA": {'filter': (target_data_with_preds.query_type == 'usa_telehandlers'),
                'warning': warning_msg},

        "DOZERS_USA": {'filter': (target_data_with_preds.query_type == 'usa_dozers'),
                'warning': warning_msg},

        "FORKLIFT_TRUCK_USA": {'filter': (target_data_with_preds.query_type == 'usa_forklift_trucks'),
                'warning': warning_msg},

        "COMPACT_TRACK_LOADERS_USA": {'filter': (target_data_with_preds.query_type == 'usa_compact_track_loaders'),
                'warning': warning_msg},

        "ARTICULATING_BOOM_LIFTS_USA": {'filter': (target_data_with_preds.query_type == 'usa_articulating_boom_lifts'),
                'warning': warning_msg},

        "ARTICULATED_DUMP_TRUCKS_USA": {'filter': (target_data_with_preds.query_type == 'usa_articulated_dump_trucks'),
                'warning': warning_msg},

        "SCISSOR_LIFT_USA": {'filter': (target_data_with_preds.query_type == 'usa_scissor_lifts'),
                'warning': warning_msg},
        
        "USA_PICKUP_TRUCKS": {'filter': (target_data_with_preds.query_type == 'usa_pickup_trucks'),
                'warning': warning_msg},

        "USA_DUMP_TRUCKS": {'filter': (target_data_with_preds.query_type == 'usa_dump_trucks'),
                'warning': warning_msg},
        
        "USA_WATER_TRUCKS": {'filter': (target_data_with_preds.query_type == 'usa_water_trucks'),
                'warning': warning_msg},

        "USA_TRACTORS": {'filter': (target_data_with_preds.query_type == 'usa_tractors'),
                'warning': warning_msg},

        "USA_STORAGE_CONTAINERS": {'filter': (target_data_with_preds.query_type == 'usa_storage_containers'),
                'warning': warning_msg},

        "USA_CONSTRUCTION": {'filter': (target_data_with_preds.query_type == 'usa_construction'),
                'warning': warning_msg},

        "usa_age_0": {'filter': target_data_with_preds.query_type == 'usa_age_0', 
                'warning': None},

        "usa_age_1": {'filter': target_data_with_preds.query_type == 'usa_age_1', 
                'warning': None},

        "usa_age_2": {'filter': target_data_with_preds.query_type == 'usa_age_2', 
                'warning': None},

        "usa_age_3": {'filter': target_data_with_preds.query_type == 'usa_age_3', 
                'warning': None},

        "usa_age_4": {'filter': target_data_with_preds.query_type == 'usa_age_4',
                'warning': None},

        "usa_age_5": {'filter': target_data_with_preds.query_type == 'usa_age_5',
                'warning': None},

        "TRUCK_TRACTORS_CAN": {'filter': (target_data_with_preds.query_type == 'can_truck_tractors'),
                'warning': warning_msg},

        "EXCAVATORS_CAN": {'filter': (target_data_with_preds.query_type == 'can_excavators'),
                'warning': warning_msg},

        "SCISSOR_LIFT_CAN": {'filter': (target_data_with_preds.query_type == 'can_scissor_lifts'),
                'warning': warning_msg},

        "WHEEL_LOADERS_CAN": {'filter': (target_data_with_preds.query_type == 'can_wheel_loaders'),
                'warning': warning_msg},

        "TELEHANDLERS_CAN": {'filter': (target_data_with_preds.query_type == 'can_telehandlers'),
                'warning': warning_msg},

        "DOZERS_CAN": {'filter': (target_data_with_preds.query_type == 'can_dozers'),
                'warning': warning_msg},

        "PICKUP_TRUCK_CAN": {'filter': (target_data_with_preds.query_type == 'can_pickup_trucks'),
                'warning': warning_msg},

        "DUMP_TRUCK_CAN": {'filter': (target_data_with_preds.query_type == 'can_dump_trucks'),
                'warning': warning_msg},

        "WATER_TRUCK_CAN": {'filter': (target_data_with_preds.query_type == 'can_water_trucks'),
                'warning': warning_msg},

        "CAN_CONSTRUCTION": {'filter': (target_data_with_preds.query_type == 'can_construction'),
                'warning': warning_msg},

        "can_age_0": {'filter': target_data_with_preds.query_type == 'can_age_0', 
                'warning': None},   

        "can_age_1": {'filter': target_data_with_preds.query_type == 'can_age_1', 
                'warning': None},  

        "can_age_2": {'filter': target_data_with_preds.query_type == 'can_age_2', 
                'warning': None},

        "can_age_3": {'filter': target_data_with_preds.query_type == 'can_age_3',
                'warning': None},

        "can_age_4": {'filter': target_data_with_preds.query_type == 'can_age_4',
                'warning': None},
        
        "can_age_5": {'filter': target_data_with_preds.query_type == 'can_age_5',
                'warning': None},
        
        "TRUCK_TRACTORS_UK": {'filter': (target_data_with_preds.query_type == 'gbr_truck_tractors'),
                'warning': warning_msg},

        "EXCAVATORS_UK": {'filter': (target_data_with_preds.query_type == 'gbr_excavators'),
                'warning': warning_msg},

        "WHEEL_LOADERS_UK": {'filter': (target_data_with_preds.query_type == 'gbr_wheel_loaders'),
                'warning': warning_msg},

        "TELEHANDLERS_UK": {'filter': (target_data_with_preds.query_type == 'gbr_telehandlers'),
                'warning': warning_msg},

        "SCISSOR_LIFT_UK": {'filter': (target_data_with_preds.query_type == 'gbr_scissor_lifts'),
                'warning': warning_msg},

        "DOZERS_UK": {'filter': (target_data_with_preds.query_type == 'gbr_dozers'),
                'warning': warning_msg},

        "FORKLIFT_TRUCK_UK": {'filter': (target_data_with_preds.query_type == 'gbr_forklift_trucks'),
                'warning': warning_msg},

        "COMPACT_TRACK_LOADERS_UK": {'filter': (target_data_with_preds.query_type == 'gbr_compact_track_loaders'),
                'warning': warning_msg},

        "ARTICULATING_BOOM_LIFTS_UK": {'filter': (target_data_with_preds.query_type == 'gbr_articulating_boom_lifts'),
                'warning': warning_msg},

        "ARTICULATED_DUMP_TRUCKS_UK": {'filter': (target_data_with_preds.query_type == 'gbr_articulated_dump_trucks'),
                'warning': warning_msg},

        "UK_CONSTRUCTION": {'filter': (target_data_with_preds.query_type == 'gbr_construction'),
                'warning': warning_msg},

        "uk_age_0": {'filter': target_data_with_preds.query_type == 'gbr_age_0', 
                'warning': None},

        "uk_age_1": {'filter': target_data_with_preds.query_type == 'gbr_age_1', 
                'warning': None},

        "uk_age_2": {'filter': target_data_with_preds.query_type == 'gbr_age_2', 
                'warning': None},

        "uk_age_3": {'filter': target_data_with_preds.query_type == 'gbr_age_3', 
                'warning': None},
            
        "uk_age_4": {'filter': target_data_with_preds.query_type == 'gbr_age_4', 
                'warning': None},

        "uk_age_5": {'filter': target_data_with_preds.query_type == 'gbr_age_5', 
                'warning': None},
                
        # "HIGH_SAMPLE_SIZE_USA_NOT_RND": {'filter': target_data_with_preds.query_type == 'usa_high_sample_size',
        #                    'warning': None},

        "HIGH_SAMPLE_SIZE_USA_MINI_EXC": {'filter': target_data_with_preds.query_type == 'usa_high_sample_size_mini_exc',
                                'warning': None},

        "HIGH_SAMPLE_SIZE_USA_TRUCKS": {'filter': target_data_with_preds.query_type == 'usa_high_sample_size_trucks',
                                'warning': None},

        "with_abcost": {'filter': target_data_with_preds.abcost > 0, 'warning': None},
        
        "non_abcost": {'filter': target_data_with_preds.abcost < 0, 
                       'warning': None},

        # "usa_new_equip": {'filter': target_data_with_preds.query_type == 'usa_new_equip', 
        #                'warning': None},

        "model year <= 10year old": {'filter': target_data_with_preds.mfg_year >= 2015, 'warning': None},
        "model year  > 10year old": {'filter': target_data_with_preds.mfg_year < 2015, 'warning': None}
    }

    for label, data in filters_warnings.items():

        logger.info(f"label: {label}")

        filtered_data = target_data_with_preds[data['filter']]
        
        if filtered_data.shape[0] > 0:

            logger.info(f"filtered_data.shape: {filtered_data.shape}")
            result_dict.update(_calculate_and_print_ratios(filtered_data, label, data['warning'], verbose=verbose))

        else:
            logger.warning(f"No data for filter: {label}")

    return result_dict

def get_prediction_and_ratios(target_data: pd.DataFrame,predictor) -> pd.DataFrame:

    start = perf_counter()

    # NOTE: Suppress logging for enrich_data_and_get_predictions
    logger.warning("Disabling logging for enrich_data_and_get_predictions")
    for log_name in LOG_NAMES_FOR_TMP_DISABLE:
        logger.disable(log_name)
    
    target_data_with_preds, lookback_data = _enrich_data_and_get_predictions(target_data, predictor) # <== this wont show log msgs

    sample_size_prv_agebin, sample_size_agebin, sample_size_nxt_agebin  = predictor.sample_size_cache.predict(lookback_data)
    #target_data_with_preds['sum_sample_sizes'] = sample_size_prv_agebin + sample_size_agebin + sample_size_nxt_agebin
    target_data_with_preds['sample_sizes_on_training'] = sample_size_agebin 

    # NOTE: Enabling logs
    for log_name in LOG_NAMES_FOR_TMP_DISABLE:
        logger.enable(log_name)

    logger.warning("Logging re-enabled")

    n_new_neg_retail = target_data_with_preds[target_data_with_preds.new_retail_prediction < 0].shape[0]
    n_new_neg_auction = target_data_with_preds[target_data_with_preds.new_auction_prediction < 0].shape[0]

    logger.warning(f"Number of negative NEW predictions => retail: {n_new_neg_retail}; auction: {n_new_neg_auction}")

    target_data_with_preds = target_data_with_preds[target_data_with_preds.new_retail_prediction > 0.0]
    target_data_with_preds = target_data_with_preds[target_data_with_preds.new_auction_prediction > 0.0]    

    prediction_end = perf_counter()
    logger.info(f"prediction took {prediction_end - start:0.2f} seconds")

    assert 'new_retail_prediction' in target_data_with_preds.columns, 'new_retail_prediction not in target_data_with_preds'
    assert 'new_auction_prediction' in target_data_with_preds.columns, 'new_auction_prediction not in target_data_with_preds'

    return target_data_with_preds

def main(env: str, algo: str, model_version: str, sample_factor: float = 0.05, frozen_month: str = None) ->  pd.DataFrame:

    start = perf_counter()
    logger.info(f"===> Validation public cache main(): env: {env}; algo: {algo}; sample: {sample_factor} ...")

    assert 'prod' in model_version, f"model_version: {model_version} is not a prod version"
    
    check_env_compliance(env)
    check_env_compliance(model_version)
    check_env_and_model_version_compatibility(env, model_version)
    check_model_version_exist_in_public_cache(model_version)

    public_cache_data = _load_data_from_public_cache_table(model_version,sample_factor)
    
    country_code_to_continent_subcontinent_map = _load_continent_subcontinent_data()

    logger.info(f"country_code_to_contnine_subcontinent_map.shape: {country_code_to_continent_subcontinent_map.shape}")
    logger.info(f"country_code_to_contnine_subcontinent_map.columns: {country_code_to_continent_subcontinent_map.columns}")

    load_end = perf_counter()
    logger.info(f"load_data() took {load_end - start:0.2f} seconds")

    # NOTE: add continent and subcontinent columns 
    target_data = pd.merge(public_cache_data, country_code_to_continent_subcontinent_map, 
                           on='item_country', how='left')

    _prv_frozen_month = str(target_data['auc_end_time'].max())
    
    logger.info(f"Model artifacts {model_version} fozen month: {_prv_frozen_month}")

    _next_frozen_month = frozen_month if frozen_month is not None else get_published_date_based_on_prv_frozeen_month(_prv_frozen_month)
    logger.info(f"ENV artifacts {model_version} frozen month: {_next_frozen_month}")

    # NOTE: removing retail_prediction and auction_prediction missings from public cache
    _n = target_data.shape[0]
    target_data = target_data.dropna(subset=['retail_prediction', 'auction_prediction'])

    logger.warning(f"Remove {_n - target_data.shape[0]} rows with missing retail_prediction or auction_prediction")

    target_data['retail_prediction'] = target_data['retail_prediction'].astype(int)
    target_data['auction_prediction'] = target_data['auction_prediction'].astype(int)

    n_neg_retail = target_data[target_data.retail_prediction < 0].shape[0]
    n_neg_auction = target_data[target_data.auction_prediction < 0].shape[0]

    logger.warning(f"Number of negative predictions => retail: {n_neg_retail}; auction: {n_neg_auction}")

    target_data = target_data[target_data.retail_prediction > 0.0]
    target_data = target_data[target_data.auction_prediction > 0.0]

    # NOTE: In public cache we decide to use as input usd and conversion rate as 1.0 no matter is the country (Billy)
    # THIS VALUE IS REPLACED IN PREDICTOR WHEN RUNS LOOKBACK.transform() method.
    target_data['conversion_rate'] = 1.000
    target_data['currency_code'] = 'usd'

    predictor = BatchPredictor(env, algo)

    logger.info(f"Getting predicitions for FROZEN MONTH: {_next_frozen_month}...")
    target_data['auc_end_time'] = _next_frozen_month

    target_data_with_preds = get_prediction_and_ratios(target_data,predictor)
    assert 'sample_sizes_on_training' in target_data_with_preds.columns, 'sample_sizes not in target_data_with_preds'

    # NOTE: compute the performance metrics for the new predictions
    logger.info('Comparing new predictions with old predictions')
    pred_ratios = _compute_prediction_ratios(target_data_with_preds)

    logger.info("Compute on train samples")
    target_data_on_training_with_preds = target_data_with_preds[target_data_with_preds['sample_sizes_on_training'] > 12]

    pred_ratios_on_training = _compute_prediction_ratios(target_data_on_training_with_preds)

    return target_data_with_preds, pred_ratios, pred_ratios_on_training

def format_table_ratios(preds_ratio: dict)-> pd.DataFrame:
    
    tabular_data =  pd.DataFrame.from_dict(preds_ratio, orient='index', 
                                           columns=['count', 'median_age',
                                                    'prv_median_retail_preds', 'retail_15th_perc' ,'retail_ratio', 'retail_85th_perc',
                                                    'prv_median_auction_preds', 'auction_15th_perc','auction_ratio','auction_75th_perc']
                                        )
    
    tabular_data['group'] = tabular_data.index
    # NOTE: reordering columns
    tabular_data = tabular_data[['group'] + [col for col in tabular_data if col != 'group']]

    return tabular_data 

def _generate_report_metadata(env: str, algo: str,frozen_month: str) ->str:

        metadata = (
            "metadata: for 4_validation_public_cache.py\n"
            f"env: {env}, algo: {algo}\n"
            f"frozen_month: {frozen_month}"
        )

        return metadata

if __name__ == '__main__':

    args = docopt(__doc__)

    _env = args.get('--env','local')
    _algo = args.get('--algo','lite')
    _sample_factor = float(args.get('--sample','0.10'))
    _model_version = args.get('--model-version','prod/1.7.0')
    _frozen_month = args.get('--frozen-month',None)

    # NOTE: [Un]Comment for debug in vscode
    # _env='prod/1.25.0'
    # _model_version = 'prod/1.24.1'
    # _algo = 'lite'
    # _sample = 1.00 

    assert _sample_factor > 0.0 and _sample_factor <= 2.00, f"sample factor must be in (0.0, 2.00]. Got {_sample_factor}"

    results = main(_env, _algo, _model_version, _sample_factor, _frozen_month)
    #target_data_with_preds_prv, pred_ratios_prv,target_data_with_preds, pred_ratios = results
    target_data_with_preds, pred_ratios, pred_ratios_on_training = results

    logger.info("Validation public cache completed successfully...")
    
    prv_frozen_month = str(target_data_with_preds['auc_end_time'].max())
    next_frozen_month  = _frozen_month if _frozen_month is not None else get_published_date_based_on_prv_frozeen_month(prv_frozen_month)

    logger.info(f"FROZEN MONTH of {_model_version}: {next_frozen_month} ")
    table_ratios = format_table_ratios(pred_ratios)
    print(tabulate(table_ratios, headers=table_ratios.columns,floatfmt=".2f", showindex=False, tablefmt="github"))

    logger.info(f"FROZEN MONTH of {_model_version} restrict on seen on training data: {next_frozen_month}")
    table_ratios_on_training = format_table_ratios(pred_ratios_on_training)
    print(tabulate(table_ratios_on_training, headers=table_ratios_on_training.columns,floatfmt=".2f", showindex=False, 
                        tablefmt="github"))

    logger.info(f"Saving/Uploading validation data...")
    _upload_validation = args.get('--upload',False)

    table_ratios = format_table_ratios(pred_ratios)
    table_ratios_on_training = format_table_ratios(pred_ratios_on_training)
 
    handler =  GCPObjectHandler(root_path=_env)if _upload_validation else LocalObjectHandler()

    metadata = _generate_report_metadata(_env, _algo,next_frozen_month)
    handler.save(metadata, 'validation/metadata_public_cache.txt')
    
    handler.save(target_data_with_preds, 'validation/target_data_with_preds.pkl')
    handler.save(target_data_with_preds, 'validation/target_data_with_preds.csv')
    handler.save(table_ratios, 'validation/table_ratios.csv')
    handler.save(table_ratios, 'validation/table_ratios_on_training.csv')    
